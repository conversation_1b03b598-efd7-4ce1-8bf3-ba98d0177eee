package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler;

import static org.mockito.Mockito.times;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.bpmnengineadapter.BPMNEngineRunTimeServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.TemplateService;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TriggerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.TriggerDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.TriggerType;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter.TransactionEntity;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.camunda.bpm.model.bpmn.instance.StartEvent;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.Silent.class)
public class V3SignalProcessTest {

  @Mock private BPMNEngineRunTimeServiceRest bpmnEngineRunTimeServiceRest;
  @Mock private V3RunTimeHelper runtimeHelper;

  @Mock private ProcessDetailsRepository processDetailsRepository;
  @Mock private TriggerDetailsRepository triggerDetailsRepository;
  @Mock private TemplateService templateService;

  @Mock private MetricLogger metricLogger;
  @Mock private WASContextHandler contextHandler;

  @InjectMocks private V3SignalProcess v3SignalProcess;

  private TemplateDetails templateDetails;

  private List<DefinitionDetails> definitionDetailsList;

  @Before
  public void prepareMockData() {
    definitionDetailsList = new ArrayList<>();
    templateDetails = TriggerHandlerTestData.getBPMNTemplateDetails();
    definitionDetailsList.add(TriggerHandlerTestData.getDefinitionDetails(templateDetails));
  }

  @Test
  public void signalProcessTest() {
    TransactionEntity transactionEntity =
		TransactionEntityFactory.getInstanceOf(
			TriggerHandlerTestData.prepareV3TriggerMessage("deleted").getTriggerMessage(),
				contextHandler);
    Map<String, Object> initialStartEventExtensionProperties = TriggerHandlerTestData.getStartEventExtensionPropertiesOfTheTemplate();
    Mockito.doReturn(true).when(bpmnEngineRunTimeServiceRest).correlateMessage(Mockito.any());
    Optional<List<ProcessDetails>> processDetailsOptionalList =
        Optional.of(
            TriggerHandlerTestData.getProcessDetailsList(
                transactionEntity.getEntityId(), definitionDetailsList.stream().findFirst().get()));
    ProcessDetails processDetails = processDetailsOptionalList.get().stream().findFirst().get();
    processDetails.setDefinitionDetails(definitionDetailsList.stream().findFirst().get());

    Optional<List<TriggerDetails>> triggerDetailsOptionalList =
        Optional.ofNullable(
            TriggerHandlerTestData.getTriggerDetaisList(templateDetails, "deleted"));
    Mockito.doReturn(triggerDetailsOptionalList)
        .when(triggerDetailsRepository)
        .findByTemplateDetails(templateDetails);
    boolean signalProcessResult =
        v3SignalProcess.signalProcess(
            transactionEntity, definitionDetailsList, processDetails, initialStartEventExtensionProperties);
    Assert.assertTrue(signalProcessResult);
  }

  @Test
  public void signalProcessWhenPOIsApprovedTest() {
    TransactionEntity transactionEntity =
    		TransactionEntityFactory.getInstanceOf(
    			TriggerHandlerTestData.prepareV3TriggerMessage("approved").getTriggerMessage(),
    				contextHandler);
    Map<String, Object> initialStartEventExtensionProperties = TriggerHandlerTestData.getStartEventExtensionPropertiesOfTheTemplate();
    Mockito.doReturn(true).when(bpmnEngineRunTimeServiceRest).correlateMessage(Mockito.any());
    Optional<List<ProcessDetails>> processDetailsOptionalList =
        Optional.of(
            TriggerHandlerTestData.getProcessDetailsList(
                transactionEntity.getEntityId(), definitionDetailsList.stream().findFirst().get()));
    ProcessDetails processDetails = processDetailsOptionalList.get().stream().findFirst().get();
    processDetails.setDefinitionDetails(definitionDetailsList.stream().findFirst().get());

    Optional<List<TriggerDetails>> triggerDetailsOptionalList =
        Optional.ofNullable(
            TriggerHandlerTestData.getTriggerDetaisList(templateDetails, "approved"));
    Mockito.doReturn(triggerDetailsOptionalList)
        .when(triggerDetailsRepository)
        .findByTemplateDetails(templateDetails);
    boolean signalProcessResult =
        v3SignalProcess.signalProcess(
            transactionEntity, definitionDetailsList, processDetails, initialStartEventExtensionProperties);
    Assert.assertTrue(signalProcessResult);
  }

  @Test
  public void signalProcessEventsTest() {
    TransactionEntity transactionEntity =
    		TransactionEntityFactory.getInstanceOf(
        			TriggerHandlerTestData.prepareV3TriggerMessage("deleted").getTriggerMessage(),
        				contextHandler);
    Map<String, Object> initialStartEventExtensionProperties = TriggerHandlerTestData.getStartEventExtensionPropertiesOfTheTemplate();
    Mockito.doReturn(true).when(bpmnEngineRunTimeServiceRest).correlateMessageEvent(Mockito.any());
    Optional<List<ProcessDetails>> processDetailsOptionalList =
        Optional.of(
            TriggerHandlerTestData.getProcessDetailsList(
                transactionEntity.getEntityId(), definitionDetailsList.stream().findFirst().get()));
    ProcessDetails processDetails = processDetailsOptionalList.get().stream().findFirst().get();
    processDetails.setDefinitionDetails(definitionDetailsList.stream().findFirst().get());

    Optional<List<TriggerDetails>> triggerDetailsOptionalList =
        Optional.ofNullable(
            TriggerHandlerTestData.getTriggerDetaisList(templateDetails, "deleted"));
    Mockito.doReturn(triggerDetailsOptionalList)
        .when(triggerDetailsRepository)
        .findByTemplateDetails(templateDetails);

    Mockito.when(contextHandler.get(WASContextEnums.IS_EVENT)).thenReturn(Boolean.TRUE.toString());
    boolean signalProcessResult =
        v3SignalProcess.signalProcess(
            transactionEntity, definitionDetailsList, processDetails, initialStartEventExtensionProperties);

    Assert.assertTrue(signalProcessResult);
  }

  @Test
  public void signalProcessbyId() {
    TransactionEntity transactionEntity =
    		TransactionEntityFactory.getInstanceOf(
			TriggerHandlerTestData.prepareV3TriggerMessage("deleted").getTriggerMessage(),
				contextHandler);
    Map<String, Object> initialStartEventExtensionProperties = TriggerHandlerTestData.getStartEventExtensionPropertiesOfTheTemplate();

    Mockito.doReturn(true).when(bpmnEngineRunTimeServiceRest).correlateMessage(Mockito.any());
    Optional<List<ProcessDetails>> processDetailsOptionalList =
        Optional.of(
            TriggerHandlerTestData.getProcessDetailsList(
                transactionEntity.getEntityId(), definitionDetailsList.stream().findFirst().get()));
    ProcessDetails processDetails = processDetailsOptionalList.get().stream().findFirst().get();
    processDetails.setDefinitionDetails(definitionDetailsList.stream().findFirst().get());

    Optional<List<TriggerDetails>> triggerDetailsOptionalList =
        Optional.ofNullable(
            TriggerHandlerTestData.getTriggerDetaisList(templateDetails, "deleted"));
    Mockito.doReturn(triggerDetailsOptionalList)
        .when(triggerDetailsRepository)
        .findByTemplateDetails(templateDetails);
    boolean signalProcessResult =
        v3SignalProcess.signalProcessById(
            transactionEntity,
            definitionDetailsList.stream().findFirst(),
            processDetails,
            initialStartEventExtensionProperties);

    Assert.assertTrue(signalProcessResult);
  }

  @SuppressWarnings("unchecked")
  @Test
  public void signalProcessbyIdGlobalVariableScoping() {
    TransactionEntity transactionEntity =
    		TransactionEntityFactory.getInstanceOf(
			TriggerHandlerTestData.prepareV3TriggerMessage("globalScoping").getTriggerMessage(),
				contextHandler);
    Map<String, Object> initialStartEventExtensionProperties = TriggerHandlerTestData.getStartEventExtensionPropertiesOfTheTemplate();

    Mockito.doReturn(true).when(bpmnEngineRunTimeServiceRest).correlateMessage(Mockito.any());
    Optional<List<ProcessDetails>> processDetailsOptionalList =
        Optional.of(
            TriggerHandlerTestData.getProcessDetailsList(
                transactionEntity.getEntityId(), definitionDetailsList.stream().findFirst().get()));
    ProcessDetails processDetails = processDetailsOptionalList.get().stream().findFirst().get();
    processDetails.setDefinitionDetails(definitionDetailsList.stream().findFirst().get());

    Optional<List<TriggerDetails>> triggerDetailsOptionalList =
        Optional.ofNullable(
            TriggerHandlerTestData.getTriggerDetaisList(templateDetails, "created"));
    Mockito.doReturn(triggerDetailsOptionalList)
        .when(triggerDetailsRepository)
        .findByTemplateDetails(templateDetails);

    Mockito.when(
            runtimeHelper.getVariablesMap(
                transactionEntity,
                transactionEntity.getVariables().getGlobal(),
                false,
                initialStartEventExtensionProperties,
                definitionDetailsList.stream().findFirst().get(),
                false,
                null,
                false))
        .thenReturn(
            ObjectConverter.fromJson(
                "{\"variables\":{\"TotalAmt\":{\"type\":\"Double\",\"value\":8000}}}",
                HashMap.class));

    boolean signalProcessResult =
        v3SignalProcess.signalProcessById(
            transactionEntity,
            definitionDetailsList.stream().findFirst(),
            processDetails,
            initialStartEventExtensionProperties);

    Assert.assertTrue(signalProcessResult);
  }

  @Test
  public void signalProcessbyIdNoVariableScoping() {
    TransactionEntity transactionEntity =
    		TransactionEntityFactory.getInstanceOf(
    				TriggerHandlerTestData.prepareV3TriggerMessage("globalScoping").getTriggerMessage(),
    					contextHandler);
    Map<String, Object> initialStartEventExtensionProperties = TriggerHandlerTestData.getStartEventExtensionPropertiesOfTheTemplate();

    Optional<List<ProcessDetails>> processDetailsOptionalList =
        Optional.of(
            TriggerHandlerTestData.getProcessDetailsList(
                transactionEntity.getEntityId(), definitionDetailsList.stream().findFirst().get()));
    ProcessDetails processDetails = processDetailsOptionalList.get().stream().findFirst().get();
    processDetails.setDefinitionDetails(definitionDetailsList.stream().findFirst().get());

    Optional<List<TriggerDetails>> triggerDetailsOptionalList =
        Optional.ofNullable(
            TriggerHandlerTestData.getTriggerDetaisList(templateDetails, "created"));
    Mockito.doReturn(triggerDetailsOptionalList)
        .when(triggerDetailsRepository)
        .findByTemplateDetails(templateDetails);

    // setting global/local variables as null
    transactionEntity.getVariables().setGlobal(Collections.emptyMap());
    transactionEntity.getVariables().setLocal(Collections.emptyMap());

    Mockito.doReturn(true).when(bpmnEngineRunTimeServiceRest).correlateMessage(Mockito.any());

    boolean signalProcessResult =
        v3SignalProcess.signalProcessById(
            transactionEntity,
            definitionDetailsList.stream().findFirst(),
            processDetails,
            initialStartEventExtensionProperties);

    Assert.assertTrue(signalProcessResult);
  }

  @SuppressWarnings("unchecked")
  @Test
  public void signalProcessbyIdLocalVariableScoping() {
    TransactionEntity transactionEntity =
    		TransactionEntityFactory.getInstanceOf(
			TriggerHandlerTestData.prepareV3TriggerMessage("localScoping").getTriggerMessage(),
				contextHandler);
    Map<String, Object> initialStartEventExtensionProperties = TriggerHandlerTestData.getStartEventExtensionPropertiesOfTheTemplate();

    Mockito.doReturn(true).when(bpmnEngineRunTimeServiceRest).correlateMessage(Mockito.any());
    Optional<List<ProcessDetails>> processDetailsOptionalList =
        Optional.of(
            TriggerHandlerTestData.getProcessDetailsList(
                transactionEntity.getEntityId(), definitionDetailsList.stream().findFirst().get()));
    ProcessDetails processDetails = processDetailsOptionalList.get().stream().findFirst().get();
    processDetails.setDefinitionDetails(definitionDetailsList.stream().findFirst().get());

    Optional<List<TriggerDetails>> triggerDetailsOptionalList =
        Optional.ofNullable(
            TriggerHandlerTestData.getTriggerDetaisList(templateDetails, "created"));
    Mockito.doReturn(triggerDetailsOptionalList)
        .when(triggerDetailsRepository)
        .findByTemplateDetails(templateDetails);

    Mockito.when(
            runtimeHelper.getVariablesMap(
                transactionEntity,
                transactionEntity.getVariables().getLocal(),
                false,
                initialStartEventExtensionProperties,
                definitionDetailsList.stream().findFirst().get(),
                false,
                null,
                false))
        .thenReturn(
            ObjectConverter.fromJson(
                "{\"variables\":{\"TotalAmt\":{\"type\":\"Double\",\"value\":8000}}}",
                HashMap.class));

    boolean signalProcessResult =
        v3SignalProcess.signalProcessById(
            transactionEntity,
            definitionDetailsList.stream().findFirst(),
            processDetails,
            initialStartEventExtensionProperties);

    Assert.assertTrue(signalProcessResult);
  }

  @SuppressWarnings("unchecked")
  @Test
  public void signalProcessbyIdLocalGlobalVariableScoping() {
    TransactionEntity transactionEntity =
    		TransactionEntityFactory.getInstanceOf(
			TriggerHandlerTestData.prepareV3TriggerMessage("localGlobalScoping").getTriggerMessage(),
				contextHandler);
    Map<String, Object> initialStartEventExtensionProperties = TriggerHandlerTestData.getStartEventExtensionPropertiesOfTheTemplate();

    Mockito.doReturn(true).when(bpmnEngineRunTimeServiceRest).correlateMessage(Mockito.any());
    Optional<List<ProcessDetails>> processDetailsOptionalList =
        Optional.of(
            TriggerHandlerTestData.getProcessDetailsList(
                transactionEntity.getEntityId(), definitionDetailsList.stream().findFirst().get()));
    ProcessDetails processDetails = processDetailsOptionalList.get().stream().findFirst().get();
    processDetails.setDefinitionDetails(definitionDetailsList.stream().findFirst().get());

    Optional<List<TriggerDetails>> triggerDetailsOptionalList =
        Optional.ofNullable(
            TriggerHandlerTestData.getTriggerDetaisList(templateDetails, "created"));
    Mockito.doReturn(triggerDetailsOptionalList)
        .when(triggerDetailsRepository)
        .findByTemplateDetails(templateDetails);

    Mockito.when(
            runtimeHelper.getVariablesMap(
                transactionEntity,
                transactionEntity.getVariables().getLocal(),
                false,
                initialStartEventExtensionProperties,
                definitionDetailsList.stream().findFirst().get(),
                false,
                null,
                false))
        .thenReturn(
            ObjectConverter.fromJson(
                "{\"variables\":{\"TotalAmt\":{\"type\":\"Double\",\"value\":8000}}}",
                HashMap.class));

    Mockito.when(
            runtimeHelper.getVariablesMap(
                transactionEntity,
                transactionEntity.getVariables().getGlobal(),
                false,
                initialStartEventExtensionProperties,
                definitionDetailsList.stream().findFirst().get(),
                false,
                null,
                false))
        .thenReturn(
            ObjectConverter.fromJson(
                "{\"variables\":{\"TotalAmt\":{\"type\":\"Double\",\"value\":8000}}}",
                HashMap.class));

    boolean signalProcessResult =
        v3SignalProcess.signalProcessById(
            transactionEntity,
            definitionDetailsList.stream().findFirst(),
            processDetails,
            initialStartEventExtensionProperties);

    Assert.assertTrue(signalProcessResult);
  }

  @Test
  public void signalProcessbyIdInvalidTriggerName() {
    TransactionEntity transactionEntity =
    		TransactionEntityFactory.getInstanceOf(
			TriggerHandlerTestData.prepareV3TriggerMessage("deleted").getTriggerMessage(),
				contextHandler);
    Map<String, Object> initialStartEventExtensionProperties = TriggerHandlerTestData.getStartEventExtensionPropertiesOfTheTemplate();

    Optional<List<ProcessDetails>> processDetailsOptionalList =
        Optional.of(
            TriggerHandlerTestData.getProcessDetailsList(
                transactionEntity.getEntityId(), definitionDetailsList.stream().findFirst().get()));
    ProcessDetails processDetails = processDetailsOptionalList.get().stream().findFirst().get();
    processDetails.setDefinitionDetails(definitionDetailsList.stream().findFirst().get());

    Optional<List<TriggerDetails>> triggerDetailsOptionalList =
        Optional.ofNullable(
            TriggerHandlerTestData.getTriggerDetaisList(templateDetails, "created"));
    Mockito.doReturn(triggerDetailsOptionalList)
        .when(triggerDetailsRepository)
        .findByTemplateDetails(templateDetails);
    boolean signalProcessResult =
        v3SignalProcess.signalProcessById(
            transactionEntity,
            definitionDetailsList.stream().findFirst(),
            processDetails,
            initialStartEventExtensionProperties);

    Assert.assertFalse(signalProcessResult);
  }

  @Test
  public void signalProcessByIdNullTriggerName() {
    TransactionEntity transactionEntity =
		TransactionEntityFactory.getInstanceOf(
				TriggerHandlerTestData.prepareV3TriggerMessage("deleted").getTriggerMessage(),
					contextHandler);
    Map<String, Object> initialStartEventExtensionProperties = TriggerHandlerTestData.getStartEventExtensionPropertiesOfTheTemplate();

    Optional<List<ProcessDetails>> processDetailsOptionalList =
        Optional.of(
            TriggerHandlerTestData.getProcessDetailsList(
                transactionEntity.getEntityId(), definitionDetailsList.stream().findFirst().get()));
    ProcessDetails processDetails = processDetailsOptionalList.get().stream().findFirst().get();
    processDetails.setDefinitionDetails(definitionDetailsList.stream().findFirst().get());

    Optional<List<TriggerDetails>> triggerDetailsOptionalList =
        Optional.of(TriggerHandlerTestData.getTriggerDetaisList(templateDetails, null));
    Mockito.doReturn(triggerDetailsOptionalList)
        .when(triggerDetailsRepository)
        .findByTemplateDetails(templateDetails);
    boolean signalProcessResult =
        v3SignalProcess.signalProcessById(
            transactionEntity,
            definitionDetailsList.stream().findFirst(),
            processDetails,
            initialStartEventExtensionProperties);

    Assert.assertFalse(signalProcessResult);
  }

  @Test
  public void signalProcessByIdNullEntityChangeType() {
    TransactionEntity transactionEntity =
		TransactionEntityFactory.getInstanceOf(
				TriggerHandlerTestData.prepareV3TriggerMessage("deleted").getTriggerMessage(),
					contextHandler);
    Map<String, Object> initialStartEventExtensionProperties = TriggerHandlerTestData.getStartEventExtensionPropertiesOfTheTemplate();

    Optional<List<ProcessDetails>> processDetailsOptionalList =
        Optional.of(
            TriggerHandlerTestData.getProcessDetailsList(
                transactionEntity.getEntityId(), definitionDetailsList.stream().findFirst().get()));
    ProcessDetails processDetails = processDetailsOptionalList.get().stream().findFirst().get();
    processDetails.setDefinitionDetails(definitionDetailsList.stream().findFirst().get());

    Optional<List<TriggerDetails>> triggerDetailsOptionalList =
        Optional.of(TriggerHandlerTestData.getTriggerDetaisList(templateDetails, "updated"));
    Mockito.doReturn(triggerDetailsOptionalList)
        .when(triggerDetailsRepository)
        .findByTemplateDetails(templateDetails);
    transactionEntity.getEventHeaders().setEntityChangeType(null);
    boolean signalProcessResult =
        v3SignalProcess.signalProcessById(
            transactionEntity,
            definitionDetailsList.stream().findFirst(),
            processDetails,
            initialStartEventExtensionProperties);

    Assert.assertFalse(signalProcessResult);
  }

  @Test
  public void signalProcessbyIdFalse() {
    TransactionEntity transactionEntity =
		TransactionEntityFactory.getInstanceOf(
				TriggerHandlerTestData.prepareV3TriggerMessage("deleted").getTriggerMessage(),
					contextHandler);
    Map<String, Object> initialStartEventExtensionProperties = TriggerHandlerTestData.getStartEventExtensionPropertiesOfTheTemplate();

    Optional<List<ProcessDetails>> processDetailsOptionalList =
        Optional.of(
            TriggerHandlerTestData.getProcessDetailsList(
                transactionEntity.getEntityId(), definitionDetailsList.stream().findFirst().get()));
    ProcessDetails processDetails = processDetailsOptionalList.get().stream().findFirst().get();
    processDetails.setDefinitionDetails(definitionDetailsList.stream().findFirst().get());

    Mockito.doReturn(Optional.empty())
        .when(triggerDetailsRepository)
        .findByTemplateDetails(templateDetails);

    boolean signalProcessResult =
        v3SignalProcess.signalProcessById(
            transactionEntity,
            definitionDetailsList.stream().findFirst(),
            processDetails,
            initialStartEventExtensionProperties);

    Mockito.verify(runtimeHelper, Mockito.times(1))
        .markProcessError(Mockito.any(), Mockito.any());

    Assert.assertFalse(signalProcessResult);
  }

  @Test
  public void signalProcessbyIdFalseProcessNotMarkedInErrorStateTest() {
    TransactionEntity transactionEntity =
		TransactionEntityFactory.getInstanceOf(
				TriggerHandlerTestData.prepareV3TriggerMessage("deleted").getTriggerMessage(),
					contextHandler);
    transactionEntity.getEventHeaders().setBlockProcessOnSignalFailure(false);
    Map<String, Object> initialStartEventExtensionProperties = TriggerHandlerTestData.getStartEventExtensionPropertiesOfTheTemplate();

    Optional<List<ProcessDetails>> processDetailsOptionalList =
        Optional.of(
            TriggerHandlerTestData.getProcessDetailsList(
                transactionEntity.getEntityId(), definitionDetailsList.stream().findFirst().get()));
    ProcessDetails processDetails = processDetailsOptionalList.get().stream().findFirst().get();
    processDetails.setDefinitionDetails(definitionDetailsList.stream().findFirst().get());

    Mockito.doReturn(Optional.empty())
        .when(triggerDetailsRepository)
        .findByTemplateDetails(templateDetails);

    boolean signalProcessResult =
        v3SignalProcess.signalProcessById(
            transactionEntity,
            definitionDetailsList.stream().findFirst(),
            processDetails,
            initialStartEventExtensionProperties);

    Mockito.verify(processDetailsRepository, Mockito.times(0))
        .updateProcessStatus(Mockito.any(), Mockito.any());

    Assert.assertFalse(signalProcessResult);
  }

  @Test
  public void signalProcessTriggerNameUppercase() {
    TransactionEntity transactionEntity =
		TransactionEntityFactory.getInstanceOf(
				TriggerHandlerTestData.prepareV3TriggerMessage("deleted").getTriggerMessage(),
					contextHandler);
    Map<String, Object> initialStartEventExtensionProperties = TriggerHandlerTestData.getStartEventExtensionPropertiesOfTheTemplate();

    Mockito.doReturn(true).when(bpmnEngineRunTimeServiceRest).correlateMessage(Mockito.any());
    Optional<List<ProcessDetails>> processDetailsOptionalList =
        Optional.of(
            TriggerHandlerTestData.getProcessDetailsList(
                transactionEntity.getEntityId(), definitionDetailsList.stream().findFirst().get()));
    ProcessDetails processDetails = processDetailsOptionalList.get().stream().findFirst().get();
    processDetails.setDefinitionDetails(definitionDetailsList.stream().findFirst().get());

    Optional<List<TriggerDetails>> triggerDetailsOptionalList =
        Optional.ofNullable(
            TriggerHandlerTestData.getTriggerDetaisList(templateDetails, "DELETED"));
    Mockito.doReturn(triggerDetailsOptionalList)
        .when(triggerDetailsRepository)
        .findByTemplateDetails(templateDetails);
    boolean signalProcessResult =
        v3SignalProcess.signalProcessById(
            transactionEntity,
            definitionDetailsList.stream().findFirst(),
            processDetails,
            initialStartEventExtensionProperties);

    Mockito.verify(metricLogger, Mockito.times(0))
        .logErrorMetric(Mockito.any(), Mockito.any(), Mockito.any());
    Assert.assertTrue(signalProcessResult);
  }

  @Test
  public void signalProcessTriggerNameMixedcase() {
    TransactionEntity transactionEntity =
		TransactionEntityFactory.getInstanceOf(
				TriggerHandlerTestData.prepareV3TriggerMessage("deleted").getTriggerMessage(),
					contextHandler);
    Map<String, Object> initialStartEventExtensionProperties = TriggerHandlerTestData.getStartEventExtensionPropertiesOfTheTemplate();

    Mockito.doReturn(true).when(bpmnEngineRunTimeServiceRest).correlateMessage(Mockito.any());
    Optional<List<ProcessDetails>> processDetailsOptionalList =
        Optional.of(
            TriggerHandlerTestData.getProcessDetailsList(
                transactionEntity.getEntityId(), definitionDetailsList.stream().findFirst().get()));
    ProcessDetails processDetails = processDetailsOptionalList.get().stream().findFirst().get();
    processDetails.setDefinitionDetails(definitionDetailsList.stream().findFirst().get());

    Optional<List<TriggerDetails>> triggerDetailsOptionalList =
        Optional.ofNullable(
            TriggerHandlerTestData.getTriggerDetaisList(templateDetails, "DeLeTeD_VoIdEd"));
    Mockito.doReturn(triggerDetailsOptionalList)
        .when(triggerDetailsRepository)
        .findByTemplateDetails(templateDetails);
    boolean signalProcessResult =
        v3SignalProcess.signalProcessById(
            transactionEntity,
            definitionDetailsList.stream().findFirst(),
            processDetails,
            initialStartEventExtensionProperties);

    Mockito.verify(metricLogger, Mockito.times(0))
        .logErrorMetric(Mockito.any(), Mockito.any(), Mockito.any());
    Assert.assertTrue(signalProcessResult);
  }

  @Test
  public void signalProcessDuplicateTriggerNames() {
    TransactionEntity transactionEntity =
		TransactionEntityFactory.getInstanceOf(
				TriggerHandlerTestData.prepareV3TriggerMessage("deleted").getTriggerMessage(),
					contextHandler);
    Map<String, Object> initialStartEventExtensionProperties = TriggerHandlerTestData.getStartEventExtensionPropertiesOfTheTemplate();

    Mockito.doReturn(true).when(bpmnEngineRunTimeServiceRest).correlateMessage(Mockito.any());
    Optional<List<ProcessDetails>> processDetailsOptionalList =
        Optional.of(
            TriggerHandlerTestData.getProcessDetailsList(
                transactionEntity.getEntityId(), definitionDetailsList.stream().findFirst().get()));
    ProcessDetails processDetails = processDetailsOptionalList.get().stream().findFirst().get();
    processDetails.setDefinitionDetails(definitionDetailsList.stream().findFirst().get());

    Optional<List<TriggerDetails>> triggerDetailsOptionalList =
        Optional.ofNullable(
            TriggerHandlerTestData.getTriggerDetaisList(templateDetails, "DeLeTeD_VoIdEd"));
    triggerDetailsOptionalList
        .get()
        .add(
            TriggerDetails.builder()
                .recordType(RecordType.INVOICE)
                .templateDetails(templateDetails)
                .triggerName("DeLeTeD_VoIdEd")
                .triggerType(TriggerType.WFTASK)
                .build());
    Mockito.doReturn(triggerDetailsOptionalList)
        .when(triggerDetailsRepository)
        .findByTemplateDetails(templateDetails);
    boolean signalProcessResult =
        v3SignalProcess.signalProcessById(
            transactionEntity,
            definitionDetailsList.stream().findFirst(),
            processDetails,
            initialStartEventExtensionProperties);

    Mockito.verify(metricLogger, Mockito.times(0))
        .logErrorMetric(Mockito.any(), Mockito.any(), Mockito.any());
    Assert.assertTrue(signalProcessResult);
  }

  @Test
  public void signalProcessMultipleTriggerNames() {
    TransactionEntity transactionEntity =
		TransactionEntityFactory.getInstanceOf(
				TriggerHandlerTestData.prepareV3TriggerMessage("deleted").getTriggerMessage(),
					contextHandler);
    Map<String, Object> initialStartEventExtensionProperties = TriggerHandlerTestData.getStartEventExtensionPropertiesOfTheTemplate();

    Mockito.doReturn(true).when(bpmnEngineRunTimeServiceRest).correlateMessage(Mockito.any());
    Optional<List<ProcessDetails>> processDetailsOptionalList =
        Optional.of(
            TriggerHandlerTestData.getProcessDetailsList(
                transactionEntity.getEntityId(), definitionDetailsList.stream().findFirst().get()));
    ProcessDetails processDetails = processDetailsOptionalList.get().stream().findFirst().get();
    processDetails.setDefinitionDetails(definitionDetailsList.stream().findFirst().get());

    Optional<List<TriggerDetails>> triggerDetailsOptionalList =
        Optional.ofNullable(
            TriggerHandlerTestData.getTriggerDetaisList(templateDetails, "DeLeTeD_VoIdEd"));
    triggerDetailsOptionalList
        .get()
        .add(
            TriggerDetails.builder()
                .recordType(RecordType.INVOICE)
                .templateDetails(templateDetails)
                .triggerName("Deleted_Disabled")
                .triggerType(TriggerType.WFTASK)
                .build());
    Mockito.doReturn(triggerDetailsOptionalList)
        .when(triggerDetailsRepository)
        .findByTemplateDetails(templateDetails);

    try (MockedStatic<WorkflowLogger> mocked = Mockito.mockStatic(WorkflowLogger.class)) {
      boolean signalProcessResult =
          v3SignalProcess.signalProcessById(
              transactionEntity,
              definitionDetailsList.stream().findFirst(),
              processDetails,
              initialStartEventExtensionProperties);

      mocked.verify(times(1), () -> WorkflowLogger.warn(Mockito.any()));
      Assert.assertTrue(signalProcessResult);
    }
  }

  @Test
  public void signalProcessNoMatchingTriggerNames() {

    TransactionEntity transactionEntity =
    	TransactionEntityFactory.getInstanceOf(
			TriggerHandlerTestData.prepareV3TriggerMessage("approved").getTriggerMessage(),
				contextHandler);
    Map<String, Object> initialStartEventExtensionProperties = TriggerHandlerTestData.getStartEventExtensionPropertiesOfTheTemplate();

    Optional<List<ProcessDetails>> processDetailsOptionalList =
        Optional.of(
            TriggerHandlerTestData.getProcessDetailsList(
                transactionEntity.getEntityId(), definitionDetailsList.stream().findFirst().get()));
    ProcessDetails processDetails = processDetailsOptionalList.get().stream().findFirst().get();
    processDetails.setDefinitionDetails(definitionDetailsList.stream().findFirst().get());

    Optional<List<TriggerDetails>> triggerDetailsOptionalList =
        Optional.ofNullable(
            TriggerHandlerTestData.getTriggerDetaisList(templateDetails, "DeLeTeD_VoIdEd"));
    triggerDetailsOptionalList
        .get()
        .add(
            TriggerDetails.builder()
                .recordType(RecordType.INVOICE)
                .templateDetails(templateDetails)
                .triggerName("Deleted_Disabled")
                .triggerType(TriggerType.WFTASK)
                .build());
    Mockito.doReturn(triggerDetailsOptionalList)
        .when(triggerDetailsRepository)
        .findByTemplateDetails(templateDetails);

    try (MockedStatic<WorkflowLogger> mocked = Mockito.mockStatic(WorkflowLogger.class)) {
      boolean signalProcessResult =
          v3SignalProcess.signalProcessById(
              transactionEntity,
              definitionDetailsList.stream().findFirst(),
              processDetails,
              initialStartEventExtensionProperties);

      mocked.verify(times(1), () -> WorkflowLogger.warn(Mockito.any()));
      Assert.assertFalse(signalProcessResult);
    }
  }

  //Test the logic when trigger names are substrings of each other QBOES-8569
  //The trigger name which matches the entity type exactly should be returned
  @Test
  public void signalProcessTestWithSameMessageName() {
    TransactionEntity transactionEntity =
    		TransactionEntityFactory.getInstanceOf(
			TriggerHandlerTestData.prepareV3TriggerMessage("deleted").getTriggerMessage(),
				contextHandler);
    Map<String, Object> initialStartEventExtensionProperties = TriggerHandlerTestData.getStartEventExtensionPropertiesOfTheTemplate();
    Mockito.doReturn(true).when(bpmnEngineRunTimeServiceRest).correlateMessage(Mockito.any());
    Optional<List<ProcessDetails>> processDetailsOptionalList =
        Optional.of(
            TriggerHandlerTestData.getProcessDetailsList(
                transactionEntity.getEntityId(), definitionDetailsList.stream().findFirst().get()));
    ProcessDetails processDetails = processDetailsOptionalList.get().stream().findFirst().get();
    processDetails.setDefinitionDetails(definitionDetailsList.stream().findFirst().get());

    List<TriggerDetails> triggerDetailsList = new ArrayList<>();
    TriggerHandlerTestData.getTriggerDetailsListWithSubstrings(templateDetails, "deleted_test_1", triggerDetailsList);
    TriggerHandlerTestData.getTriggerDetailsListWithSubstrings(templateDetails, "deleted_test_2", triggerDetailsList);
    TriggerHandlerTestData.getTriggerDetailsListWithSubstrings(templateDetails, "deleted", triggerDetailsList);

    Optional<List<TriggerDetails>> triggerDetailsOptionalList = Optional.ofNullable(triggerDetailsList);

    Mockito.doReturn(triggerDetailsOptionalList)
        .when(triggerDetailsRepository)
        .findByTemplateDetails(templateDetails);
    boolean signalProcessResult =
        v3SignalProcess.signalProcess(
            transactionEntity, definitionDetailsList, processDetails, initialStartEventExtensionProperties);
    Assert.assertTrue(signalProcessResult);
  }

  //Test the logic when trigger names are substrings of each other QBOES-8569
  //If the trigger name which matches the entity type exactly is not present then return using findFirst
  @Test
  public void signalProcessTestWithMessageNamesAsSubstring() {
    TransactionEntity transactionEntity =
		TransactionEntityFactory.getInstanceOf(
			TriggerHandlerTestData.prepareV3TriggerMessage("deleted").getTriggerMessage(),
					contextHandler);
    Map<String, Object> initialStartEventExtensionProperties = TriggerHandlerTestData.getStartEventExtensionPropertiesOfTheTemplate();
    Mockito.doReturn(true).when(bpmnEngineRunTimeServiceRest).correlateMessage(Mockito.any());
    Optional<List<ProcessDetails>> processDetailsOptionalList =
        Optional.of(
            TriggerHandlerTestData.getProcessDetailsList(
                transactionEntity.getEntityId(), definitionDetailsList.stream().findFirst().get()));
    ProcessDetails processDetails = processDetailsOptionalList.get().stream().findFirst().get();
    processDetails.setDefinitionDetails(definitionDetailsList.stream().findFirst().get());

    List<TriggerDetails> triggerDetailsList = new ArrayList<>();
    TriggerHandlerTestData.getTriggerDetailsListWithSubstrings(templateDetails, "deleted_test_1", triggerDetailsList);
    TriggerHandlerTestData.getTriggerDetailsListWithSubstrings(templateDetails, "deleted_test_2", triggerDetailsList);
    TriggerHandlerTestData.getTriggerDetailsListWithSubstrings(templateDetails, "deleted_test_3", triggerDetailsList);

    Optional<List<TriggerDetails>> triggerDetailsOptionalList = Optional.ofNullable(triggerDetailsList);

    Mockito.doReturn(triggerDetailsOptionalList)
        .when(triggerDetailsRepository)
        .findByTemplateDetails(templateDetails);
    boolean signalProcessResult =
        v3SignalProcess.signalProcess(
            transactionEntity, definitionDetailsList, processDetails, initialStartEventExtensionProperties);
    Assert.assertTrue(signalProcessResult);
  }

  //Test the logic when trigger names are substrings of each other QBOES-8569
  //If the trigger name does not match the entity type
  @Test
  public void signalProcessTestWithUnlikeMessageName() {
    TransactionEntity transactionEntity =
    		TransactionEntityFactory.getInstanceOf(
    				TriggerHandlerTestData.prepareV3TriggerMessage("deleted").getTriggerMessage(),
    						contextHandler);
    Map<String, Object> initialStartEventExtensionProperties = TriggerHandlerTestData.getStartEventExtensionPropertiesOfTheTemplate();

    Optional<List<ProcessDetails>> processDetailsOptionalList =
        Optional.of(
            TriggerHandlerTestData.getProcessDetailsList(
                transactionEntity.getEntityId(), definitionDetailsList.stream().findFirst().get()));
    ProcessDetails processDetails = processDetailsOptionalList.get().stream().findFirst().get();
    processDetails.setDefinitionDetails(definitionDetailsList.stream().findFirst().get());

    List<TriggerDetails> triggerDetailsList = new ArrayList<>();
    TriggerHandlerTestData.getTriggerDetailsListWithSubstrings(templateDetails, "test_1", triggerDetailsList);

    Optional<List<TriggerDetails>> triggerDetailsOptionalList = Optional.ofNullable(triggerDetailsList);

    Mockito.doReturn(triggerDetailsOptionalList)
        .when(triggerDetailsRepository)
        .findByTemplateDetails(templateDetails);
    boolean signalProcessResult =
        v3SignalProcess.signalProcess(
            transactionEntity, definitionDetailsList, processDetails, initialStartEventExtensionProperties);
    Assert.assertFalse(signalProcessResult);
  }

  @Test
  public void signalProcessbyIdDynamicTriggerName() {
    TransactionEntity transactionEntity =
        new TransactionEntity(
            TriggerHandlerTestData.prepareV3TriggerMessage("deleted").getTriggerMessage()) {
          @Override
          public String getEntityChangeType() {
            return "deleted1245";
          }
        };
    Map<String, Object> initialStartEventExtensionProperties = TriggerHandlerTestData.getStartEventExtensionPropertiesOfTheTemplate();
    Mockito.doReturn(true).when(bpmnEngineRunTimeServiceRest).correlateMessage(Mockito.any());
    Optional<List<ProcessDetails>> processDetailsOptionalList =
        Optional.of(
            TriggerHandlerTestData.getProcessDetailsList(
                transactionEntity.getEntityId(), definitionDetailsList.stream().findFirst().get()));
    ProcessDetails processDetails = processDetailsOptionalList.get().stream().findFirst().get();
    processDetails.setDefinitionDetails(definitionDetailsList.stream().findFirst().get());

    Optional<List<TriggerDetails>> triggerDetailsOptionalList =
        Optional.ofNullable(
            TriggerHandlerTestData.getTriggerDetaisList(templateDetails, "deleted${realmId}"));
    Mockito.doReturn(triggerDetailsOptionalList)
        .when(triggerDetailsRepository)
        .findByTemplateDetails(templateDetails);
    Mockito.when(
            runtimeHelper.checkStaticMessageExistsInCorrelationMsg(
                "deleted1245", "deleted${realmId}"))
        .thenReturn(true);
    boolean signalProcessResult =
        v3SignalProcess.signalProcessById(
            transactionEntity,
            definitionDetailsList.stream().findFirst(),
            processDetails,
            initialStartEventExtensionProperties);

    Assert.assertTrue(signalProcessResult);
    triggerDetailsOptionalList =
        Optional.ofNullable(
            TriggerHandlerTestData.getTriggerDetaisList(templateDetails, "${realmId}deleted"));
    Mockito.doReturn(triggerDetailsOptionalList)
        .when(triggerDetailsRepository)
        .findByTemplateDetails(templateDetails);

    Mockito.when(
        runtimeHelper.checkStaticMessageExistsInCorrelationMsg(
             "deleted1245", "${realmId}deleted")).thenReturn(true)
        .thenReturn(true);
    signalProcessResult =
        v3SignalProcess.signalProcessById(
            transactionEntity,
            definitionDetailsList.stream().findFirst(),
            processDetails,
            initialStartEventExtensionProperties);
    Assert.assertTrue(signalProcessResult);

    triggerDetailsOptionalList =
        Optional.ofNullable(
            TriggerHandlerTestData.getTriggerDetaisList(templateDetails, "${realmId}deleted${userId}"));

    Mockito.when(
        runtimeHelper.checkStaticMessageExistsInCorrelationMsg(
            "deleted1245","${realmId}deleted${userId}")).thenReturn(true);
    Mockito.doReturn(triggerDetailsOptionalList)
        .when(triggerDetailsRepository)
        .findByTemplateDetails(templateDetails);
    signalProcessResult =
        v3SignalProcess.signalProcessById(
            transactionEntity,
            definitionDetailsList.stream().findFirst(),
            processDetails, initialStartEventExtensionProperties);
    Assert.assertTrue(signalProcessResult);

    triggerDetailsOptionalList =
        Optional.ofNullable(
            TriggerHandlerTestData.getTriggerDetaisList(templateDetails, "${realmId}"));
    Mockito.doReturn(triggerDetailsOptionalList)
        .when(triggerDetailsRepository)
        .findByTemplateDetails(templateDetails);
    Mockito.when(
        runtimeHelper.checkStaticMessageExistsInCorrelationMsg(
             "deleted1245","${realmId}")).thenReturn(true);
    signalProcessResult =
        v3SignalProcess.signalProcessById(
            transactionEntity,
            definitionDetailsList.stream().findFirst(),
            processDetails, initialStartEventExtensionProperties);
    Assert.assertTrue(signalProcessResult);
  }
  @Test
  public void signalProcessbyIdInvalidDynamicTriggerName() {
    TransactionEntity transactionEntity =
        new TransactionEntity(
            TriggerHandlerTestData.prepareV3TriggerMessage("deleted").getTriggerMessage()) {
          @Override
          public String getEntityChangeType() {
            return "deleted1245";
          }
        };
    Map<String, Object> initialStartEventExtensionProperties = TriggerHandlerTestData.getStartEventExtensionPropertiesOfTheTemplate();
    Optional<List<ProcessDetails>> processDetailsOptionalList =
        Optional.of(
            TriggerHandlerTestData.getProcessDetailsList(
                transactionEntity.getEntityId(), definitionDetailsList.stream().findFirst().get()));
    ProcessDetails processDetails = processDetailsOptionalList.get().stream().findFirst().get();
    processDetails.setDefinitionDetails(definitionDetailsList.stream().findFirst().get());

    Optional<List<TriggerDetails>> triggerDetailsOptionalList =
        Optional.ofNullable(
            TriggerHandlerTestData.getTriggerDetaisList(templateDetails, "event${realmId}"));
    Mockito.doReturn(triggerDetailsOptionalList)
        .when(triggerDetailsRepository)
        .findByTemplateDetails(templateDetails);
    Mockito.when(
            runtimeHelper.checkStaticMessageExistsInCorrelationMsg(
                "deleted1245", "event${realmId}"))
        .thenReturn(false);
    boolean signalProcessResult =
        v3SignalProcess.signalProcessById(
            transactionEntity,
            definitionDetailsList.stream().findFirst(),
            processDetails,
            initialStartEventExtensionProperties);
    Assert.assertFalse(signalProcessResult);

    Mockito.when(
            runtimeHelper.checkStaticMessageExistsInCorrelationMsg(
                "deleted1245", "event${realmId}"))
        .thenThrow(new NullPointerException());
    signalProcessResult =
        v3SignalProcess.signalProcessById(
            transactionEntity,
            definitionDetailsList.stream().findFirst(),
            processDetails,
            initialStartEventExtensionProperties);
    Assert.assertFalse(signalProcessResult);
  }
}
