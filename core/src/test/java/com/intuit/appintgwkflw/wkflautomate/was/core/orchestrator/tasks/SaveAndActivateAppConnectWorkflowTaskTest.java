package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.AuthDetailsService;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.impl.AuthDetailsServiceImpl;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.AppConnectService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.AppConnectServiceImpl;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.AuthDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.response.AppConnectSaveWorkflowResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.async.execution.request.State;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;

public class SaveAndActivateAppConnectWorkflowTaskTest {

  private final ArgumentCaptor<String> definitionIdCaptor = ArgumentCaptor.forClass(String.class);
  private final SaveAndActivateAppConnectWorkflowTask mockCreateAppConnectWorkflowTask =
      Mockito.mock(SaveAndActivateAppConnectWorkflowTask.class);
  private SaveAndActivateAppConnectWorkflowTask createAppConnectWorkflowTask;
  private State input;
  private AppConnectService appConnectService;
  private AuthDetailsService authDetailsService;
  private BpmnModelInstance bpmnModelInstance;

  @Before
  public void setup() {
    input = new State();
    bpmnModelInstance = Mockito.mock(BpmnModelInstance.class);
    input.addValue(AsyncTaskConstants.SUBSCRIPTION_ID_KEY, "sub-id");
    input.addValue(AsyncTaskConstants.BPMN_INSTANCE_KEY, bpmnModelInstance);
    authDetailsService = Mockito.mock(AuthDetailsServiceImpl.class);
    appConnectService = Mockito.mock(AppConnectServiceImpl.class);
    AppConnectSaveWorkflowResponse appConnectSaveWorkflowResponse =
        new AppConnectSaveWorkflowResponse();
    appConnectSaveWorkflowResponse.setId("123");
    createAppConnectWorkflowTask =
        new SaveAndActivateAppConnectWorkflowTask(
            appConnectService, authDetailsService, bpmnModelInstance, false, false, false,null, "display-name");
    Mockito.when(appConnectService.createWorkflow(eq("sub-id"), anyString(), eq(bpmnModelInstance), anyString()))
        .thenReturn(appConnectSaveWorkflowResponse);
  }

  @Test
  public void testExecute() {
    input.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, "def-id");
    Assert.assertEquals(
        "123",
        createAppConnectWorkflowTask.execute(input).getValue(AsyncTaskConstants.WORKFLOW_ID_KEY));
  }

  @Test
  public void testExecute_isMigration() {
    input.addValue(AsyncTaskConstants.SUBSCRIPTION_ID_KEY, "sub-id");
    input.addValue(AsyncTaskConstants.REALM_ID_KEY, "realm-id");
    input.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, "def-id");

    AppConnectSaveWorkflowResponse appConnectSaveWorkflowResponse =
            new AppConnectSaveWorkflowResponse();
    appConnectSaveWorkflowResponse.setId("123");

    createAppConnectWorkflowTask =
            new SaveAndActivateAppConnectWorkflowTask(
                    appConnectService, authDetailsService, bpmnModelInstance, false, true, true,null, "display-name");
    Mockito.when(appConnectService.createWorkflow(eq("sub-id"), anyString(), eq(bpmnModelInstance), anyString()))
            .thenReturn(appConnectSaveWorkflowResponse);

    Assert.assertEquals(
        "123",
        createAppConnectWorkflowTask.execute(input).getValue(AsyncTaskConstants.WORKFLOW_ID_KEY));

    createAppConnectWorkflowTask =
            new SaveAndActivateAppConnectWorkflowTask(
                    appConnectService, authDetailsService, bpmnModelInstance, false, false, true,null, "display-name");
    Mockito.when(appConnectService.createWorkflow(eq("sub-id"), anyString(), eq(bpmnModelInstance), anyString()))
            .thenReturn(appConnectSaveWorkflowResponse);

    Assert.assertEquals(
        "123",
        createAppConnectWorkflowTask.execute(input).getValue(AsyncTaskConstants.WORKFLOW_ID_KEY));
  }

  @Test
  public void testExecute_NoSubscriptionId() {
    input.addValue(AsyncTaskConstants.SUBSCRIPTION_ID_KEY, "");
    input.addValue(AsyncTaskConstants.REALM_ID_KEY, "realm-id");
    input.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, "def-id");
    AuthDetails authDetails = new AuthDetails();
    authDetails.setSubscriptionId("sub-id");
    Mockito.when(authDetailsService.getAuthDetailsFromRealmId(anyString())).thenReturn(authDetails);
    Assert.assertEquals(
        "123",
        createAppConnectWorkflowTask.execute(input).getValue(AsyncTaskConstants.WORKFLOW_ID_KEY));
  }

  @Test
  public void testExecute_SingleDefinition() {
    input.addValue(AsyncTaskConstants.IS_SINGLE_DEFINITION, true);
    Assert.assertEquals(
        "123",
        createAppConnectWorkflowTask.execute(input).getValue(AsyncTaskConstants.WORKFLOW_ID_KEY));
    Mockito.verify(appConnectService)
        .createWorkflow(anyString(), definitionIdCaptor.capture(), any(BpmnModelInstance.class), anyString());
    Assert.assertNotNull(definitionIdCaptor.getValue());
  }

  @Test
  public void testExecuteForUpdate_withoutEntityOps() {
    input.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, "def-id");
    AppConnectSaveWorkflowResponse workflowResponse = new AppConnectSaveWorkflowResponse();

    Mockito.when(
            appConnectService.activateDeactivateActionWorkflow(eq("345"), eq("sub-id"), eq(true)))
        .thenReturn(workflowResponse);
    createAppConnectWorkflowTask =
        new SaveAndActivateAppConnectWorkflowTask(
            appConnectService, authDetailsService, bpmnModelInstance, true, true, false,"345", "display-name");
    Assert.assertEquals(
        "345",
        createAppConnectWorkflowTask.execute(input).getValue(AsyncTaskConstants.WORKFLOW_ID_KEY));
    Mockito.verify(appConnectService, Mockito.times(1)).updateWorkflow(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.any(BpmnModelInstance.class), Mockito.anyString());
    Mockito.verify(appConnectService, Mockito.never()).registerToken(Mockito.anyString(), Mockito.anyString(), Mockito.anyString());
  }

  @Test
  public void testExecuteForUpdate_withEntityOps() {
    input.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, "def-id");
    input.addValue(WorkflowConstants.ENTITY_TYPE, "testEntity");
    input.addValue(AsyncTaskConstants.ENTITY_OPERATION, "create,update");
    input.addValue(AsyncTaskConstants.REALM_ID_KEY, "1234567890");
    input.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, "defId");
    input.addValue(AsyncTaskConstants.IS_SINGLE_DEFINITION, true);

    AppConnectSaveWorkflowResponse workflowResponse = new AppConnectSaveWorkflowResponse();
    Mockito.when(
            appConnectService.activateDeactivateActionWorkflow(eq("345"), eq("sub-id"), eq(true)))
        .thenReturn(workflowResponse);
    createAppConnectWorkflowTask =
        new SaveAndActivateAppConnectWorkflowTask(
            appConnectService, authDetailsService, bpmnModelInstance, true, true, false,"345", "display-name");
    State execution = createAppConnectWorkflowTask.execute(input);
    Assert.assertEquals(
        "345",
        execution.getValue(AsyncTaskConstants.WORKFLOW_ID_KEY));
    Mockito.verify(appConnectService, Mockito.times(1)).updateWorkflow(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.any(BpmnModelInstance.class), Mockito.anyString());
    Mockito.verify(appConnectService, Mockito.times(1)).registerToken(Mockito.any(), Mockito.anyString(), Mockito.anyString());
    Assert.assertEquals("defId", input.getValue(AsyncTaskConstants.DEFINITION_ID_KEY));
  }

  @Test
  public void testDisableUpdateForMCR() {
    input.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, "def-id");
    input.addValue(AsyncTaskConstants.REALM_ID_KEY, "1234567890");
    input.addValue(AsyncTaskConstants.IS_APPCONNECT_UPDATE_DISABLED, true);

    createAppConnectWorkflowTask =
        new SaveAndActivateAppConnectWorkflowTask(
            appConnectService, authDetailsService, bpmnModelInstance, false, true, false,"345", "display-name");
    State execution = createAppConnectWorkflowTask.execute(input);
    Assert.assertEquals(
        "345",
        execution.getValue(AsyncTaskConstants.WORKFLOW_ID_KEY));
    Mockito.verify(appConnectService, Mockito.times(0)).updateWorkflow(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.any(BpmnModelInstance.class), Mockito.anyString());

  }

  @Test
  public void testExecute_Error() {
    input.addValue(AsyncTaskConstants.SUBSCRIPTION_ID_KEY, "");
    input.addValue(AsyncTaskConstants.REALM_ID_KEY, "realm-id");
    input.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, "def-id");
    State state = createAppConnectWorkflowTask.execute(input);
    Assert.assertNotNull(state);
    Assert.assertTrue(state.getValue(AsyncTaskConstants.APP_CONNECT_TASK_FAILURE));
    Assert.assertNotNull(state.getValue(AsyncTaskConstants.APP_CONNECT_EXCEPTION));
    Assert.assertNotNull(state.getValue(AsyncTaskConstants.APP_CONNECT_ERROR_MESSAGE));
  }

  @Test
  public void testExecute_Invalid_DefinitionId_Exception() {
    State state = new State();
    state.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, "");
    state.addValue(AsyncTaskConstants.WORKFLOW_ID_KEY, "wrkflw-id");
    Mockito.when(mockCreateAppConnectWorkflowTask.execute(state))
        .thenThrow(new WorkflowGeneralException(WorkflowError.INVALID_DEFINITION_ID));
    try {
      mockCreateAppConnectWorkflowTask.execute(state);
      Assert.fail();
    } catch (WorkflowGeneralException e) {
      Assert.assertEquals(WorkflowError.INVALID_DEFINITION_ID.getErrorMessage(), e.getMessage());
    }
  }

  @Test
  public void testExecute_Invalid_BPMN_Model_Instance_Exception() {
    State state = new State();
    state.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, "2134");
    state.addValue(AsyncTaskConstants.WORKFLOW_ID_KEY, "wrkflw-id");
    state.addValue(AsyncTaskConstants.BPMN_INSTANCE_KEY, "");
    Mockito.when(mockCreateAppConnectWorkflowTask.execute(state))
        .thenThrow(new WorkflowGeneralException(WorkflowError.INVALID_BPMN_MODEL_INSTANCE));
    try {
      mockCreateAppConnectWorkflowTask.execute(state);
      Assert.fail();
    } catch (WorkflowGeneralException e) {
      Assert.assertEquals(
          WorkflowError.INVALID_BPMN_MODEL_INSTANCE.getErrorMessage(), e.getMessage());
    }
  }

  @Test
  public void testExecute_Invalid_RealmId_Exception() {
    State state = new State();
    state.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, "2134");
    state.addValue(AsyncTaskConstants.SUBSCRIPTION_ID_KEY, "");
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, "");
    Mockito.when(mockCreateAppConnectWorkflowTask.execute(state))
        .thenThrow(new WorkflowGeneralException(WorkflowError.INVALID_REALM_ID));
    try {
      mockCreateAppConnectWorkflowTask.execute(state);
      Assert.fail();
    } catch (WorkflowGeneralException e) {
      Assert.assertEquals(WorkflowError.INVALID_REALM_ID.getErrorMessage(), e.getMessage());
    }
  }

  @Test
  public void testExecute_WorkflowId_Exception() {
    State state = new State();
    state.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, "2134");
    state.addValue(AsyncTaskConstants.WORKFLOW_ID_KEY, "");
    Mockito.when(mockCreateAppConnectWorkflowTask.execute(state))
        .thenThrow(new WorkflowGeneralException(WorkflowError.INVALID_WORKFLOW_ID_INPUT));
    try {
      mockCreateAppConnectWorkflowTask.execute(state);
      Assert.fail();
    } catch (WorkflowGeneralException e) {
      Assert.assertEquals(
          WorkflowError.INVALID_WORKFLOW_ID_INPUT.getErrorMessage(), e.getMessage());
    }
  }

  @Test
  public void testExecute_Create_Update_Workflow_fail_Exception() {
    input.addValue(AsyncTaskConstants.BPMN_ENGINE_DEPLOY_REQUEST_KEY, "bpmn-req");
    input.addValue(AsyncTaskConstants.BPMN_ENGINE_DEPLOY_RESPONSE_KEY, "bpmn-res");
    input.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, "def-id");
    input.addValue(AsyncTaskConstants.REALM_ID_KEY, "rlm-id");
    input.addValue(AsyncTaskConstants.DEPLOYMENT_ID_KEY, "deploy-id");
    input.addValue(AsyncTaskConstants.SUBSCRIPTION_ID_KEY, "");
    Mockito.when(mockCreateAppConnectWorkflowTask.execute(input))
        .thenThrow(new WorkflowGeneralException(WorkflowError.CREATE_UPDATE_WORKFLOW_FAIL));
    try {
      mockCreateAppConnectWorkflowTask.execute(input);
      Assert.fail();
    } catch (Exception e) {
      Assert.assertEquals(
          WorkflowError.CREATE_UPDATE_WORKFLOW_FAIL.getErrorMessage(), e.getMessage());
    }
  }
}
