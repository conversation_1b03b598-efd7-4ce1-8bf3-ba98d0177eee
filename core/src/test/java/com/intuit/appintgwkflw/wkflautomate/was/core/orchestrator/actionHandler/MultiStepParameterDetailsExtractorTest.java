package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import static org.mockito.ArgumentMatchers.any;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionActivityDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.impl.ProcessDetailsRepoService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

/**
 * <AUTHOR>
 */
public class MultiStepParameterDetailsExtractorTest {

  private CustomWorkflowConfig customWorkflowConfig;

  @Mock private DefinitionActivityDetailsRepository definitionActivityDetailsRepository;
  @Mock private ProcessDetailsRepository processDetailsRepository;
  @Mock private ProcessDetailsRepoService processDetailsRepoService;

  @InjectMocks MultiStepParameterDetailsExtractor multiStepParameterDetailsExtractor;

  private WorkerActionRequest workerActionRequest;

  private static final String parametersSchema =
      TestHelper.readResourceAsString("schema/testData/parameters.json");
  private static final Map<String, String> schema = new HashMap<>();
  public static String PLACEHOLDER_VALUES_PATH = "placeholder/multi_test_placeholder.json";


  static {
    schema.put(WorkFlowVariables.PARAMETERS_KEY.getName(), parametersSchema);
    schema.put(WorkFlowVariables.HANDLER_DETAILS_KEY.getName(), "{}");
    schema.put("entityType", "invoice");
    schema.put(WorkflowConstants.DEFINITION_KEY, "2121312213");
    schema.put(WorkflowConstants.INTUIT_REALMID, "362727827");
  }

  @Before
  public void setUp() throws Exception {
    MockitoAnnotations.openMocks(this);
    customWorkflowConfig = TestHelper.loadCustomConfig();
    TemplateDetails templateDetails =
        TemplateDetails.builder()
            .id(DefinitionTestConstants.TEMPLATE_ID)
            .templateName("customApproval")
            .ownerId(Long.valueOf("9999"))
            .allowMultipleDefinitions(true)
            .build();

    DefinitionDetails mockedDefinitionDetails =
        DefinitionDetails.builder()
            .definitionId("d1")
            .templateDetails(templateDetails)
            .build();
    Mockito.when(processDetailsRepoService.findByProcessIdWithoutDefinitionData(any()))
        .thenReturn(Optional.of(mockedDefinitionDetails));
  }

  private MultiStepParameterDetailsExtractor getMultiStepParameterDetailsExtractor() {
    return new MultiStepParameterDetailsExtractor(
        customWorkflowConfig,
        definitionActivityDetailsRepository,
        processDetailsRepository,
        processDetailsRepoService
    );
  }

  @Test
  public void extractAppconnectParameterDetailsCustomWorkflow() {
    String handlerId = "hid";
    workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("createTask")
            .definitionKey("sendForApproval")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .ownerId(Long.valueOf("9999"))
            .inputVariables(schema)
            .handlerId(handlerId)
            .build();

    Mockito.when(definitionActivityDetailsRepository.findActivityDetailsByDefinitionIdAndParentId(
            any(), any(), any()
        ))
        .thenReturn(
            Optional.of(
                DefinitionActivityDetail.builder()
                    .userAttributes(TestHelper.readResourceAsString(PLACEHOLDER_VALUES_PATH))
                    .build()
            )
        );

    Map<String, HandlerDetails.ParameterDetails> parameterDetails =
        getMultiStepParameterDetailsExtractor().getParameterDetails(workerActionRequest).get();
    Assert.assertEquals(
        Arrays.asList(
            "Approval due for Invoice [[DocNumber]]"),
        parameterDetails.get("TaskName").getFieldValue());
  }

  @Test
  public void extractAppconnectParameterDetailsCustomWorkflowFailed() {
    Mockito.when(processDetailsRepoService.findByProcessIdWithoutDefinitionData(any()))
        .thenReturn(Optional.ofNullable(null));

    String handlerId = "hid";
    workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("createTask")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .ownerId(Long.valueOf("9999"))
            .inputVariables(schema)
            .handlerId(handlerId)
            .build();
    try {
      Map<String, HandlerDetails.ParameterDetails> parameterDetails =
          getMultiStepParameterDetailsExtractor().getParameterDetails(workerActionRequest).get();
      Assert.fail("The above method should throw exception");
    } catch (WorkflowGeneralException e) {
      Assert.assertTrue(
          e.getMessage().contains(WorkflowError.DEFINITION_NOT_FOUND.getErrorMessage()));
    }
  }


  @Test
  public void extractAppconnectParameterDetailsCustomWorkflowFailedActivityDetailsNotFound() {
    Mockito.when(definitionActivityDetailsRepository.findActivityDetailsByDefinitionIdAndParentId(
            any(), any(), any()
        )).thenReturn(Optional.empty());

    String handlerId = "hid";
    workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("createTask")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .ownerId(Long.valueOf("9999"))
            .inputVariables(schema)
            .handlerId(handlerId)
            .build();
    try {
      Map<String, HandlerDetails.ParameterDetails> parameterDetails =
          getMultiStepParameterDetailsExtractor().getParameterDetails(workerActionRequest).get();
      Assert.fail("The above method should throw exception");
    } catch (WorkflowGeneralException e) {
      Assert.assertTrue(
          e.getMessage().contains(WorkflowError.DEFINITION_ACTIVITY_DETAILS_NOT_FOUND.getErrorMessage()));
    }
  }


}