package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services;


import com.intuit.appintgwkflw.wkflautomate.was.common.config.AppConnectConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.mocks.AppConnectServiceMockImpl;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.AuthDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.response.AppConnectSaveWorkflowResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.response.AppConnectUnsubscribeResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.response.CreateSubscriptionResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.response.GetSubscriptionResponse;
import java.util.ArrayList;
import java.util.Collections;

import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.commons.utils.IoUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

/** Created by ssingh14 on 27/04/20. */
public class AppConnectServiceMockImplTest {

  private static final String REALM_ID = "realmId";
  private static final String DEFINITION_ID = "defId";
  private static final String SUBSCRIPTION_ID = "subId";
  private static final String DEFINITION_NAME = "defName";
  private static final String WORKFLOW_ID = "wkid";
  private static final String SUB_ID = "123";
  private final BpmnModelInstance bpmnModelInstance =
      Bpmn.readModelFromStream(
          IoUtil.stringAsInputStream(
              TestHelper.readResourceAsString("bpmn/invoiceapprovalTest.bpmn")));

  @InjectMocks private AppConnectServiceMockImpl appConnectService;

  @Mock
  public static AppConnectConfig appConnectConfig;

  @Before
  public void setup() {
    MockitoAnnotations.initMocks(this);
    Mockito.doReturn("12345").when(appConnectConfig).getWorkflowId();
  }

  @Test
  public void testGetSubscriptionForApp() {
    GetSubscriptionResponse response = new GetSubscriptionResponse();
    String id = "123";
    response.setId(id);
    String getSubscriptionResponseId = appConnectService.getSubscriptionForApp(REALM_ID);
    Assert.assertNotNull(getSubscriptionResponseId);
    Assert.assertEquals(id, getSubscriptionResponseId);
  }

  @Test
  public void testCreateSubscriptionForApp() {
    CreateSubscriptionResponse response = new CreateSubscriptionResponse();
    String id = "123";
    response.setId(id);
    String createSubscriptionResponseId = appConnectService.createSubscriptionForApp(REALM_ID);
    Assert.assertNotNull(createSubscriptionResponseId);
    Assert.assertEquals(id, createSubscriptionResponseId);
  }

  @Test
  public void testCreateWorkflow() {
    AppConnectSaveWorkflowResponse appConnectSaveWorkflowResponse =
        new AppConnectSaveWorkflowResponse();
    String id = "12345";
    appConnectSaveWorkflowResponse.setId(id);
    AppConnectSaveWorkflowResponse returnedResponse =
        appConnectService.createWorkflow(
            SUBSCRIPTION_ID, DEFINITION_ID, bpmnModelInstance, DEFINITION_NAME);
    Assert.assertNotNull(returnedResponse);
    Assert.assertEquals(id, returnedResponse.getId());
  }

  @Test
  public void testDisableWorkflowWithOfflineTicket() {
    String workflowId = "xyzx";
    AppConnectSaveWorkflowResponse appConnectSaveWorkflowResponse =
        new AppConnectSaveWorkflowResponse();
    String id = "12345";
    appConnectSaveWorkflowResponse.setId(id);

    try {
      AppConnectSaveWorkflowResponse appConnectSaveWorkflowResponseRes =
          appConnectService.disableAppConnectWorkflow(
              workflowId, AuthDetails.builder().subscriptionId(SUBSCRIPTION_ID).build());
      Assert.assertEquals(appConnectSaveWorkflowResponse, appConnectSaveWorkflowResponseRes);
    } catch (Exception e) {
      Assert.fail();
    }
  }

  @Test
  public void testDeleteWorkflowOfflineTicket() {
    AppConnectSaveWorkflowResponse appConnectSaveWorkflowResponse =
        new AppConnectSaveWorkflowResponse();
    String id = "response-id";
    appConnectSaveWorkflowResponse.setId(id);
    try {
      appConnectService.deleteWorkflow(
          "wkid", SUBSCRIPTION_ID, AuthDetails.builder().subscriptionId("123").build());
    } catch (Exception e) {
      Assert.fail();
    }
  }

  @Test
  public void testUnsubscribe() {
    String subscriptionId = "123";
    AppConnectUnsubscribeResponse getAppConnectUnsubscribeResponse =
        appConnectService.unsubscribe(AuthDetails.builder().subscriptionId("123").build(), false);
    Assert.assertNotNull(getAppConnectUnsubscribeResponse);
    Assert.assertEquals("true", getAppConnectUnsubscribeResponse.getSuccess());
    Assert.assertEquals("unsubscribed", getAppConnectUnsubscribeResponse.getStatusMessage());
  }

  @Test
  public void testActivateWorkflow() {
    String workflowId = "xyzx";
    AppConnectSaveWorkflowResponse appConnectSaveWorkflowResponse =
        new AppConnectSaveWorkflowResponse();
    String id = "12345";
    appConnectSaveWorkflowResponse.setId(id);
    try {
      AppConnectSaveWorkflowResponse appConnectSaveWorkflowResponseRes =
          appConnectService.activateDeactivateActionWorkflow(workflowId, SUBSCRIPTION_ID, true);
      Assert.assertEquals(appConnectSaveWorkflowResponse, appConnectSaveWorkflowResponseRes);
    } catch (Exception e) {
      Assert.fail();
    }
  }

  @Test
  public void testUpdateWorkflow() {
    String workflowId = "xyzx";
    try {
      appConnectService.updateWorkflow(
          workflowId, SUBSCRIPTION_ID, DEFINITION_ID, bpmnModelInstance, DEFINITION_NAME);
    } catch (Exception e) {
      Assert.fail();
    }
  }

  @Test
  public void testRegisterToken() {
    try {
      appConnectService.registerToken("1234567890", "entityType", "entityOperation");
    } catch (Exception e) {
      Assert.fail();
    }
  }

  @Test
  public void testDeleteWorkflow() {
    AppConnectSaveWorkflowResponse appConnectSaveWorkflowResponse =
        new AppConnectSaveWorkflowResponse();
    String id = "response-id";
    appConnectSaveWorkflowResponse.setId(id);
    try {
      appConnectService.deleteWorkflow("wkid", SUBSCRIPTION_ID);
    } catch (Exception e) {
      Assert.fail();
    }
  }

  @Test(expected = NullPointerException.class)
  public void testDeleteWorkflowIdNull() {
    appConnectService.deleteWorkflow(null, SUBSCRIPTION_ID);
  }

  @Test(expected = NullPointerException.class)
  public void testDeleteWorkflowSubscriptionId() {
    appConnectService.deleteWorkflow(WORKFLOW_ID, null);
  }

  @Test(expected = NullPointerException.class)
  public void testDeleteWorkflowIdNullWithAuthdetails() {
    appConnectService.deleteWorkflow(
        null, SUBSCRIPTION_ID, AuthDetails.builder().subscriptionId(SUB_ID).build());
  }

  @Test(expected = NullPointerException.class)
  public void testDeleteWorkflowSubscriptionIdWithAuthdetails() {
    appConnectService.deleteWorkflow(
        WORKFLOW_ID, null, AuthDetails.builder().subscriptionId(SUB_ID).build());
  }

  @Test(expected = NullPointerException.class)
  public void testDeleteWorkflowsNull() {
    appConnectService.deleteWorkflows(Collections.singletonList(WORKFLOW_ID), null);
  }

  @Test(expected = NullPointerException.class)
  public void testUpdateWorkflowNullWorkflowId() {
    appConnectService.updateWorkflow(null, "", "", bpmnModelInstance, DEFINITION_NAME);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testRegisterTokenNullEntityType() {
    appConnectService.registerToken("1234567890", null, "entityOperation");
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testRegisterTokenNullEntityOperation() {
    appConnectService.registerToken("1234567890", "entityType", null);
  }

  @Test(expected = NullPointerException.class)
  public void testUpdateWorkflowNullSubscriptionId() {
    appConnectService.updateWorkflow("", null, "", bpmnModelInstance, DEFINITION_NAME);
  }

  @Test(expected = NullPointerException.class)
  public void testUpdateWorkflowNullDefinitionId() {
    appConnectService.updateWorkflow("", "", null, bpmnModelInstance, DEFINITION_NAME);
  }

  @Test(expected = NullPointerException.class)
  public void testUpdateWorkflowNullBpmModelInstance() {
    appConnectService.updateWorkflow("", "", "", null, DEFINITION_NAME);
  }

  @Test
  public void testDeleteWorkflows() {
    AppConnectSaveWorkflowResponse appConnectSaveWorkflowResponse =
        new AppConnectSaveWorkflowResponse();
    String id = "response-id";
    appConnectSaveWorkflowResponse.setId(id);
    try {
      appConnectService.deleteWorkflows(
          new ArrayList<String>(), AuthDetails.builder().subscriptionId("123").build());
    } catch (Exception e) {
      Assert.fail();
    }
  }

  @Test
  public void testGetAppConnectWorkflows() {
    AppConnectSaveWorkflowResponse appConnectSaveWorkflowResponse =
        new AppConnectSaveWorkflowResponse();
    String id = "response-id";
    appConnectSaveWorkflowResponse.setId(id);
    Assert.assertTrue(
        appConnectService
                .getAppConnectWorkflows(
                    REALM_ID, AuthDetails.builder().subscriptionId("123").build())
                .size()
            > 0);
  }
}
