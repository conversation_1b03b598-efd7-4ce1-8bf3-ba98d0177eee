package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.helpers;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.DefinitionEventProcessorAttributeConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.DefinitionEventProcessorConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DefinitionEventType;
import java.util.HashMap;
import java.util.Map;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

/**
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class DefinitionEventProcessorHelperTest {

  @InjectMocks DefinitionEventProcessorHelper definitionEventProcessorHelper;

  @Mock DefinitionEventProcessorConfig definitionEventProcessorConfig;

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
    ReflectionTestUtils.setField(
        definitionEventProcessorHelper,
        "definitionEventProcessorConfig",
        definitionEventProcessorConfig);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testHandlerIdWithNoConfig() {
    definitionEventProcessorHelper.getHandlerId(
        "customStart", DefinitionEventType.CUSTOM_REMINDER_FETCH_TRANSACTIONS);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testHandlerIdWithNoConfigAttribute() {
    Map<DefinitionEventType, DefinitionEventProcessorAttributeConfig> map = new HashMap<>();
    map.put(DefinitionEventType.CUSTOM_REMINDER_FETCH_TRANSACTIONS, null);
    Mockito.when(definitionEventProcessorConfig.getDefinitionEventType()).thenReturn(map);
    definitionEventProcessorHelper.getHandlerId(
        "customStart", DefinitionEventType.CUSTOM_REMINDER_FETCH_TRANSACTIONS);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testHandlerIdWithNoConfigAttributeHandlerIds() {
    Map<DefinitionEventType, DefinitionEventProcessorAttributeConfig> map = new HashMap<>();
    map.put(
        DefinitionEventType.CUSTOM_REMINDER_FETCH_TRANSACTIONS,
        new DefinitionEventProcessorAttributeConfig());
    Mockito.when(definitionEventProcessorConfig.getDefinitionEventType()).thenReturn(map);
    definitionEventProcessorHelper.getHandlerId(
        "customStart", DefinitionEventType.CUSTOM_REMINDER_FETCH_TRANSACTIONS);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testHandlerIdWithWrongConfigAttributeHandlerIds() {
    Map<DefinitionEventType, DefinitionEventProcessorAttributeConfig> map = new HashMap<>();
    DefinitionEventProcessorAttributeConfig definitionEventProcessorAttributeConfig =
        new DefinitionEventProcessorAttributeConfig();
    definitionEventProcessorAttributeConfig.setHandlerIds(Map.of("abc", "abc"));
    map.put(
        DefinitionEventType.CUSTOM_REMINDER_FETCH_TRANSACTIONS,
        definitionEventProcessorAttributeConfig);
    Mockito.when(definitionEventProcessorConfig.getDefinitionEventType()).thenReturn(map);
    definitionEventProcessorHelper.getHandlerId(
        "customStart", DefinitionEventType.CUSTOM_REMINDER_FETCH_TRANSACTIONS);
  }

  @Test
  public void testHandlerId() {
    Map<DefinitionEventType, DefinitionEventProcessorAttributeConfig> map = new HashMap<>();
    DefinitionEventProcessorAttributeConfig definitionEventProcessorAttributeConfig =
        new DefinitionEventProcessorAttributeConfig();
    definitionEventProcessorAttributeConfig.setHandlerIds(
        Map.of(
            WorkflowConstants.CUSTOM_START_EVENT,
            "/intuit-workflows/api/custom-reminder-start-process.json"));
    map.put(
        DefinitionEventType.CUSTOM_REMINDER_FETCH_TRANSACTIONS,
        definitionEventProcessorAttributeConfig);
    Mockito.when(definitionEventProcessorConfig.getDefinitionEventType()).thenReturn(map);
    Assert.assertEquals(
        "/intuit-workflows/api/custom-reminder-start-process.json",
        definitionEventProcessorHelper.getHandlerId(
            WorkflowConstants.CUSTOM_START_EVENT,
            DefinitionEventType.CUSTOM_REMINDER_FETCH_TRANSACTIONS));
  }
}
