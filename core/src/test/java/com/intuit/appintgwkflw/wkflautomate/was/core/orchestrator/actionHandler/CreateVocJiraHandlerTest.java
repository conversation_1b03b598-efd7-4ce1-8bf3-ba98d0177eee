package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;


import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.SlackConstant.LATEST;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.SlackConstant.OLDEST;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.INTUIT_REALMID;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.jira.service.JiraServiceManager;
import com.intuit.appintgwkflw.wkflautomate.was.core.slack.service.SlackServiceManager;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.WorkerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.JiraConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.SlackConstant;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.TaskHandlerName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.jira.response.JiraResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import com.slack.api.model.Message;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.util.CollectionUtils;

@RunWith(MockitoJUnitRunner.class)
public class CreateVocJiraHandlerTest {

  @InjectMocks
  private CreateVocJiraHandler createVocJiraHandler;

  @Mock
  private WorkerUtil workerUtil;


  @Mock
  private SlackServiceManager slackServiceManager;

  @Mock
  private JiraServiceManager jiraServiceManager;
  private static Map<String, String> schema = new HashMap<>();

  @Test
  public void testGetName() {
    Assert.assertEquals(TaskHandlerName.WAS_CREATE_VOC_JIRA, createVocJiraHandler.getName());

  }

  static {
    schema.put(OLDEST, "12345");
    schema.put(LATEST, "12345");
    schema.put(SlackConstant.CHANNEL_ID, "channelId");
    schema.put(INTUIT_REALMID, "1234");
    schema.put(JiraConstants.PROJECT, "project");
    schema.put(JiraConstants.ISSUE_TYPE, "story");
    schema.put(JiraConstants.COUNTRY_FIELD, "US");
    schema.put(JiraConstants.LABEL, "test");
    schema.put(JiraConstants.CUSTOM_FILED_13505, "test");
    schema.put(JiraConstants.CUSTOM_FILED_17002, "test");
    schema.put(JiraConstants.COMPONENT, "comp");
  }

  @Test
  public void testExecutionSuccessWithNoSlackMessages() {
    String handlerId = "hId";
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId(handlerId)
            .taskId("externalTaskId")
            .ownerId(1234L)
            .isCalledProcess(false).build();
    Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);
    List<Message> messageList = new ArrayList<>();
    // add messages here
    Mockito.when(slackServiceManager.readAllMessages(Mockito.any())).thenReturn(messageList);
    Map<String, Object> map = createVocJiraHandler.executeAction(workerActionRequest);
    // check jira keys
    Assert.assertFalse(CollectionUtils.isEmpty(map));
    List<String> jiraKeys = (List<String>) map.get(JiraConstants.JIRA_KEYS);
    Assert.assertTrue(CollectionUtils.isEmpty(jiraKeys));
    Mockito.verify(slackServiceManager, Mockito.times(1)).readAllMessages(Mockito.any());
    Mockito.verify(jiraServiceManager, Mockito.times(0)).createJira(Mockito.any());
    Mockito.verify(slackServiceManager, Mockito.times(0))
        .getMessageChatLink(Mockito.any(), Mockito.any());
  }

  @Test
  public void testExecutionSuccess() {
    String handlerId = "hId";
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId(handlerId)
            .taskId("externalTaskId")
            .ownerId(1234L)
            .isCalledProcess(false).build();
    Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);
    List<Message> messageList = new ArrayList<>();
    messageList.add(slackMessage(SLACK_MESSAGE_TEXT));
    // add messages here
    Mockito.when(slackServiceManager.readAllMessages(Mockito.any())).thenReturn(messageList);
    Mockito.when(slackServiceManager.getMessageChatLink(Mockito.any(), Mockito.any()))
        .thenReturn("test.slack.com");
    JiraResponse jiraResponse = new JiraResponse();
    jiraResponse.setKey("QBOES-12345");
    Mockito.when(jiraServiceManager.createJira(Mockito.any())).thenReturn(
        jiraResponse);
    Map<String, Object> map = createVocJiraHandler.executeAction(workerActionRequest);
    // check jira keys
    Assert.assertFalse(CollectionUtils.isEmpty(map));
    List<String> jiraKeys = (List<String>) map.get(JiraConstants.JIRA_KEYS);
    Assert.assertTrue(jiraKeys.size() == 1);
    Mockito.verify(slackServiceManager, Mockito.times(1)).readAllMessages(Mockito.any());
    Mockito.verify(slackServiceManager, Mockito.times(1))
        .getMessageChatLink(Mockito.any(), Mockito.any());
    Mockito.verify(jiraServiceManager, Mockito.times(1)).createJira(Mockito.any());
  }

  @Test
  public void testExecutionSuccessSlackTextMessageLessThan250() {
    String handlerId = "hId";
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId(handlerId)
            .taskId("externalTaskId")
            .ownerId(1234L)
            .isCalledProcess(false).build();
    Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);
    List<Message> messageList = new ArrayList<>();
    messageList.add(slackMessage("Response:My name is abc"));
    // add messages here
    Mockito.when(slackServiceManager.readAllMessages(Mockito.any())).thenReturn(messageList);
    JiraResponse jiraResponse = new JiraResponse();
    jiraResponse.setKey("QBOES-12345");
    Mockito.when(jiraServiceManager.createJira(Mockito.any())).thenReturn(
        jiraResponse);
    Map<String, Object> map = createVocJiraHandler.executeAction(workerActionRequest);
    // check jira keys
    Assert.assertFalse(CollectionUtils.isEmpty(map));
    List<String> jiraKeys = (List<String>) map.get(JiraConstants.JIRA_KEYS);
    Assert.assertTrue(jiraKeys.size() == 1);
    Mockito.verify(slackServiceManager, Mockito.times(1)).readAllMessages(Mockito.any());
    Mockito.verify(jiraServiceManager, Mockito.times(1)).createJira(Mockito.any());
  }

  @Test
  public void testExecutionSuccessSlackTextMessageUnformatted() {
    String handlerId = "hId";
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId(handlerId)
            .taskId("externalTaskId")
            .ownerId(1234L)
            .isCalledProcess(false).build();
    Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);
    List<Message> messageList = new ArrayList<>();
    messageList.add(slackMessage("My name is abc"));
    // add messages here
    Mockito.when(slackServiceManager.readAllMessages(Mockito.any())).thenReturn(messageList);
    JiraResponse jiraResponse = new JiraResponse();
    jiraResponse.setKey("QBOES-12345");
    Map<String, Object> map = createVocJiraHandler.executeAction(workerActionRequest);
    // check jira keys
    Assert.assertFalse(CollectionUtils.isEmpty(map));
    List<String> jiraKeys = (List<String>) map.get(JiraConstants.JIRA_KEYS);
    Assert.assertTrue(CollectionUtils.isEmpty(jiraKeys));
    Mockito.verify(slackServiceManager, Mockito.times(1)).readAllMessages(Mockito.any());
    Mockito.verify(jiraServiceManager, Mockito.times(0)).createJira(Mockito.any());
  }

  @Test
  public void testExecutionSuccessSlackTextMessageNull() {
    String handlerId = "hId";
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId(handlerId)
            .taskId("externalTaskId")
            .ownerId(1234L)
            .isCalledProcess(false).build();
    Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);
    List<Message> messageList = new ArrayList<>();
    messageList.add(slackMessage(null));
    // add messages here
    Mockito.when(slackServiceManager.readAllMessages(Mockito.any())).thenReturn(messageList);
    JiraResponse jiraResponse = new JiraResponse();
    jiraResponse.setKey("QBOES-12345");
    Map<String, Object> map = createVocJiraHandler.executeAction(workerActionRequest);
    // check jira keys
    Assert.assertFalse(CollectionUtils.isEmpty(map));
    List<String> jiraKeys = (List<String>) map.get(JiraConstants.JIRA_KEYS);
    Assert.assertTrue(CollectionUtils.isEmpty(jiraKeys));
    Mockito.verify(slackServiceManager, Mockito.times(1)).readAllMessages(Mockito.any());
    Mockito.verify(jiraServiceManager, Mockito.times(0)).createJira(Mockito.any());
  }

  @Test
  public void testExecutionSuccessJiraServiceDown() {
    String handlerId = "hId";
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId(handlerId)
            .taskId("externalTaskId")
            .ownerId(1234L)
            .isCalledProcess(false).build();
    Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);
    List<Message> messageList = new ArrayList<>();
    messageList.add(slackMessage(SLACK_MESSAGE_TEXT));
    // add messages here
    Mockito.when(slackServiceManager.readAllMessages(Mockito.any())).thenReturn(messageList);
    Mockito.when(jiraServiceManager.createJira(Mockito.any())).thenReturn(
        null);
    Map<String, Object> map = createVocJiraHandler.executeAction(workerActionRequest);
    // check jira keys
    Assert.assertFalse(CollectionUtils.isEmpty(map));
    List<String> jiraKeys = (List<String>) map.get(JiraConstants.JIRA_KEYS);
    Assert.assertTrue(CollectionUtils.isEmpty(jiraKeys));
    Mockito.verify(slackServiceManager, Mockito.times(1)).readAllMessages(Mockito.any());
    Mockito.verify(jiraServiceManager, Mockito.times(1)).createJira(Mockito.any());
  }

  @Test
  public void testExecutionSuccessUnformattedDueDate() {
    String handlerId = "hId";
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId(handlerId)
            .taskId("externalTaskId")
            .ownerId(1234L)
            .isCalledProcess(false).build();
    Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);
    List<Message> messageList = new ArrayList<>();
    messageList.add(slackMessage(
        SLACK_MESSAGE_TEXT.replace("Date : May 24, 2024 13:22:32", "Date : test abc")));
    // add messages here
    Mockito.when(slackServiceManager.readAllMessages(Mockito.any())).thenReturn(messageList);
    Mockito.when(jiraServiceManager.createJira(Mockito.any())).thenReturn(
        null);
    Map<String, Object> map = createVocJiraHandler.executeAction(workerActionRequest);
    // check jira keys
    Assert.assertFalse(CollectionUtils.isEmpty(map));
    List<String> jiraKeys = (List<String>) map.get(JiraConstants.JIRA_KEYS);
    Assert.assertTrue(CollectionUtils.isEmpty(jiraKeys));
    Mockito.verify(slackServiceManager, Mockito.times(1)).readAllMessages(Mockito.any());
    Mockito.verify(jiraServiceManager, Mockito.times(1)).createJira(Mockito.any());
  }

  @Test
  public void testExecutionSuccessJiraServiceDownThrowException() {
    String handlerId = "hId";
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId(handlerId)
            .taskId("externalTaskId")
            .ownerId(1234L)
            .isCalledProcess(false).build();
    Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);
    List<Message> messageList = new ArrayList<>();
    messageList.add(slackMessage(SLACK_MESSAGE_TEXT));
    // add messages here
    Mockito.when(slackServiceManager.readAllMessages(Mockito.any())).thenReturn(messageList);
    JiraResponse jiraResponse = new JiraResponse();
    jiraResponse.setKey("QBOES-12345");
    Mockito.when(jiraServiceManager.createJira(Mockito.any()))
        .thenThrow(new WorkflowGeneralException(
            WorkflowError.JIRA_API_CALL_FAILURE, "Some internal error"));
    Map<String, Object> map = createVocJiraHandler.executeAction(workerActionRequest);
    // check jira keys
    Assert.assertFalse(CollectionUtils.isEmpty(map));
    List<String> jiraKeys = (List<String>) map.get(JiraConstants.JIRA_KEYS);
    Assert.assertTrue(CollectionUtils.isEmpty(jiraKeys));
    Mockito.verify(slackServiceManager, Mockito.times(1)).readAllMessages(Mockito.any());
    Mockito.verify(jiraServiceManager, Mockito.times(1)).createJira(Mockito.any());
  }

  private Message slackMessage(String text) {
    Message message = new Message();
    message.setText(text);
    return message;
  }

  private final String SLACK_MESSAGE_TEXT = "From : Workflow Survey\n"
      + "User Realm ID : 9130355314946946\n"
      + "Name: Noah asdasdasdasda\n"
      + "Company Email : <EMAIL>\n"
      + "Region : CA\n"
      + "SKU: ADVANCED\n"
      + "Date : May 24, 2024 13:22:32\n"
      + "---------------------------------------------\n"
      + "Rating : 9\n"
      + "Survey Question : Do you have any suggestions for new templates?\n"
      + "Response: Create the ability to send Sales Reciepts to customers much like how Invoices can be sent after their creation through a workflow automation. This would assist in \"sending reciepts\" to customers after using a POS system and asking for email reciepts.";
}
