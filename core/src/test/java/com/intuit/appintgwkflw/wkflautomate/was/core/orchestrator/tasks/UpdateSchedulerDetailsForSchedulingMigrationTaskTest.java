package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.SchedulerDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.async.execution.request.State;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

/** <AUTHOR> */
@RunWith(MockitoJUnitRunner.class)
public class UpdateSchedulerDetailsForSchedulingMigrationTaskTest {
    @Mock
    private DefinitionDetails definitionDetails;

    @Mock
    private SchedulerDetailsRepository schedulerDetailsRepository;

    @Test
    public void testExecute_Success() {
        when(definitionDetails.getDefinitionId()).thenReturn("testDefinitionId");
        TemplateDetails templateDetails = TemplateDetails.builder().templateName("customScheduledActions").build();
        when(definitionDetails.getTemplateDetails()).thenReturn(templateDetails);
        when(schedulerDetailsRepository.setMigrationFlagForDefinitionId(any(), any())).thenReturn(1);
        State state = new State();
        UpdateSchedulerDetailsForSchedulingMigrationTask task = new UpdateSchedulerDetailsForSchedulingMigrationTask(definitionDetails, schedulerDetailsRepository, true);
        State resultState = task.execute(state);

        verify(schedulerDetailsRepository, times(1)).setMigrationFlagForDefinitionId("testDefinitionId", true);
        assertEquals(state, resultState);
    }

    @Test
    public void testExecute_UPDATE_SCHEDULE_DETAILS_EXCEPTION() {
        State state = new State();
        when(definitionDetails.getDefinitionId()).thenReturn("testDefinitionId");
        TemplateDetails templateDetails = TemplateDetails.builder().templateName("customScheduledActions").build();
        when(definitionDetails.getTemplateDetails()).thenReturn(templateDetails);
        when(schedulerDetailsRepository.setMigrationFlagForDefinitionId(any(), any())).thenReturn(0);
        UpdateSchedulerDetailsForSchedulingMigrationTask task = new UpdateSchedulerDetailsForSchedulingMigrationTask(definitionDetails, schedulerDetailsRepository, true);
        try {
            task.execute(state);
        } catch (WorkflowGeneralException e) {
            assertEquals(WorkflowError.UPDATE_SCHEDULE_DETAILS_EXCEPTION, e.getWorkflowError());
        }
    }

    @Test
    public void testOnError() {
        State state = new State();
        UpdateSchedulerDetailsForSchedulingMigrationTask task = new UpdateSchedulerDetailsForSchedulingMigrationTask(definitionDetails, schedulerDetailsRepository, true);
        State resultState = task.onError(state);

        assertEquals(true, resultState.getValue(AsyncTaskConstants.UPDATE_DEFINITION_TASK_FAILURE));
    }
}
