package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config;


import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.ConfigTemplate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.test.util.ReflectionTestUtils;

public class PrecannedTemplateConfigTest {

  private PrecannedTemplateConfig precannedTemplateConfig;

  @Before
  public void init() {
    ConfigTemplate configTemplate1 = new ConfigTemplate();
    configTemplate1.setId("templateId1");
    configTemplate1.setName("templateName1");

    ConfigTemplate configTemplate2 = new ConfigTemplate();
    configTemplate2.setId("templateId2");
    configTemplate2.setName("templateName2");

    //configTemplates.
    precannedTemplateConfig = new PrecannedTemplateConfig();
    ReflectionTestUtils.setField(precannedTemplateConfig, "templates", List.of(configTemplate1, configTemplate2) );

  }

  @Test
  public void testPrecannedConfig() {
    Map<String, ConfigTemplate> map = precannedTemplateConfig.getTemplateConfigMap();
    Assert.assertEquals("templateName1", map.get("templateId1").getName());
    Assert.assertEquals("templateName2", map.get("templateId2").getName());
  }
}
