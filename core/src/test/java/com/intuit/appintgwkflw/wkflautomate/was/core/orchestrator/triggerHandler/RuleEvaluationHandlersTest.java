package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

public class RuleEvaluationHandlersTest {

  private static final String QBO_APP_ID = "Intuit.appintgwkflw.wkflautomate.qbowasapiclient";

  @Mock private DefaultRuleHandler defaultRuleHandler;

  @Before
  public void init() {

    MockitoAnnotations.initMocks(this);
    Mockito.when(defaultRuleHandler.getName()).thenReturn(QBO_APP_ID);
    RuleEvaluationHandlers.addHandler(defaultRuleHandler.getName(), defaultRuleHandler);
  }

  @Test
  public void getTestNUll() {
    RuleEvaluationHandler handler = RuleEvaluationHandlers.getHandler(null);
    Assert.assertNull(handler);
  }

  @Test
  public void getTestRuleHandler() {
    RuleEvaluationHandler handler = RuleEvaluationHandlers.getHandler(QBO_APP_ID);
    Assert.assertNotNull(handler);
  }
  @Test
  public void containsFalse() {
    Assert.assertFalse(RuleEvaluationHandlers.contains(null));
  }
  @Test
  public void containsTrue() {
    Assert.assertTrue(RuleEvaluationHandlers.contains(QBO_APP_ID));
  }


}

