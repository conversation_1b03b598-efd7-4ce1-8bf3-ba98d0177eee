package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import static org.mockito.ArgumentMatchers.any;

import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

/** <AUTHOR> */
public class TestWorkflowTaskHandlerTest {

  @Mock private AppConnectWorkflowTaskHandler appConnectWorkflowTaskHandler;

  @InjectMocks private TestAppconnectWorkflowTaskHandler actionHandler;

  @Mock
  private MetricLogger metricLogger;
  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
    ReflectionTestUtils.setField(actionHandler, "metricLogger", metricLogger);
  }

  @Test
  public void testExecuteAction() {
    WorkerActionRequest inputRequest = WorkerActionRequest.builder().build();
    actionHandler.executeAction(inputRequest);
    Mockito.verify(appConnectWorkflowTaskHandler).executeAction(inputRequest);
  }

  @Test
  public void testLogErrorMetric() {
    Mockito.when(appConnectWorkflowTaskHandler.executeAction(Mockito.any()))
        .thenThrow(WorkflowGeneralException.class);
    try {
      actionHandler.execute(WorkerActionRequest.builder().build());
      Assert.fail("Exception should be thrown");
    } catch (WorkflowGeneralException workflowGeneralException) {
      Mockito.verify(metricLogger, Mockito.times(1)).logErrorMetric(any(), any(), any());
    }
  }
}
