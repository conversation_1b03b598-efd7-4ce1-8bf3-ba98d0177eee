package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import static org.mockito.ArgumentMatchers.any;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.impl.ProcessDetailsRepoService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.service.TranslationService;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

public class CustomWorkflowNonUserConfiguredParameterDetailsExtractorTest {
  private WorkerActionRequest workerActionRequest;
  private TranslationService translationService;
  private final String parametersSchema =
      TestHelper.readResourceAsString("schema/testData/non_user_configured_parameters.json");
  private final String handlerSchema =
      TestHelper.readResourceAsString("schema/testData/handlers.json");
  private final String POHandlerSchema = TestHelper.readResourceAsString("schema/testData/POhandlers.json");
  private final Map<String, String> schema = new HashMap<>();
  private final Map<String, String> POSchema = new HashMap<>();

  @Mock private ProcessDetailsRepoService processDetailsRepoService;

  @Before
  public void setUp() throws Exception {

    schema.put(WorkFlowVariables.PARAMETERS_KEY.getName(), parametersSchema);
    schema.put(WorkFlowVariables.HANDLER_DETAILS_KEY.getName(), handlerSchema);
    schema.put("entityType", "invoice");
    schema.put(WorkflowConstants.DEFINITION_KEY, "2121312213");
    schema.put(WorkflowConstants.INTUIT_REALMID, "362727827");
    schema.put(WorkFlowVariables.TASK_DETAILS_KEY.getName(), getTaskDetails(false));

    POSchema.put(WorkFlowVariables.PARAMETERS_KEY.getName(), parametersSchema);
    POSchema.put(WorkFlowVariables.HANDLER_DETAILS_KEY.getName(), POHandlerSchema);
    POSchema.put("entityType", "Purchase order");
    POSchema.put(WorkflowConstants.DEFINITION_KEY, "2121312213");
    POSchema.put(WorkflowConstants.INTUIT_REALMID, "362727827");
    POSchema.put(WorkFlowVariables.TASK_DETAILS_KEY.getName(), getTaskDetails(false));

    MockitoAnnotations.openMocks(this);
    translationService = TestHelper.initTranslationService();
  }

  @Test
  public void extractNonUserConfiguredAppConnectParameterDetailsCustomWorkflowWithoutDefinition() {

    Mockito.when(processDetailsRepoService.findByProcessIdWithoutDefinitionData(any()))
        .thenReturn(Optional.empty());
    String handlerId = "hid";
    workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("sendRejectNotification_txnApproval")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .ownerId(Long.valueOf("9999"))
            .inputVariables(schema)
            .handlerId(handlerId)
            .build();
    CustomWorkflowNonUserConfiguredParameterDetailsExtractor
        customWorkflowNonUserConfiguredParameterDetailsExtractor =
            new CustomWorkflowNonUserConfiguredParameterDetailsExtractor(
                processDetailsRepoService, TestHelper.initTranslationService());
    try {
      customWorkflowNonUserConfiguredParameterDetailsExtractor
          .getParameterDetails(workerActionRequest)
          .get();
      Assert.fail();
    } catch (WorkflowGeneralException workflowGeneralException) {
      Assert.assertEquals(
          workflowGeneralException.getWorkflowError(), WorkflowError.DEFINITION_NOT_FOUND);
    }
  }

  @Test
  public void expectRootProcessIdInWorkflowActionRequest() {
    String rootProcessInstanceId = "rootProcessInstanceId";

    workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("sendRejectNotification_txnApproval")
            .processDefinitionId("dId")
            .rootProcessInstanceId(rootProcessInstanceId)
            .processInstanceId("iId")
            .ownerId(Long.valueOf("9999"))
            .inputVariables(Map.of())
            .handlerId("handlerId")
            .build();

    Assert.assertEquals(workerActionRequest.fetchParentProcessInstanceId(), rootProcessInstanceId);
  }

  @Test
  public void expectProcessIdInWorkflowActionRequest() {
    String processInstanceId = "processId";

    workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("sendRejectNotification_txnApproval")
            .processDefinitionId("dId")
            .processInstanceId(processInstanceId)
            .ownerId(Long.valueOf("9999"))
            .inputVariables(Map.of())
            .handlerId("handlerId")
            .build();

    Assert.assertEquals(workerActionRequest.fetchParentProcessInstanceId(), processInstanceId);
  }

  @Test
  public void
      extractNonUserConfiguredAppConnectParameterDetailsCustomWorkflowWithoutParameterSchema() {
    setDefinitionForLocale("en_US", true);
    String handlerId = "hid";
    workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("sendRejectNotification_txnApproval")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .ownerId(Long.valueOf("9999"))
            .inputVariables(Map.of())
            .handlerId(handlerId)
            .build();
    CustomWorkflowNonUserConfiguredParameterDetailsExtractor
        customWorkflowNonUserConfiguredParameterDetailsExtractor =
            new CustomWorkflowNonUserConfiguredParameterDetailsExtractor(
                processDetailsRepoService, TestHelper.initTranslationService());

    Assert.assertEquals(
        true,
        customWorkflowNonUserConfiguredParameterDetailsExtractor
            .getParameterDetails(workerActionRequest)
            .isEmpty());
  }

  @Test
  public void extractNonUserConfiguredAppConnectParameterDetailsForOnDemandApproval() {
    DefinitionDetails mockedDefinitionDetails =
        DefinitionDetails.builder()
            .build();
    Mockito.when(processDetailsRepoService.findByProcessIdWithoutDefinitionData(any()))
        .thenReturn(Optional.of(mockedDefinitionDetails));
    String handlerId = "hid";
    Map<String, String> inputVariables = new HashMap<>();
    inputVariables.put(WorkflowConstants.INTUIT_WAS_LOCALE,"fr_ca");
    inputVariables.put(WorkflowConstants.ON_DEMAND_APPROVAL,Boolean.TRUE.toString());
    inputVariables.putAll(POSchema);

    workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("sendRejectNotification_txnApproval")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .ownerId(Long.valueOf("9999"))
            .inputVariables(inputVariables)
            .handlerId(handlerId)
            .build();
    CustomWorkflowNonUserConfiguredParameterDetailsExtractor
        customWorkflowNonUserConfiguredParameterDetailsExtractor =
        new CustomWorkflowNonUserConfiguredParameterDetailsExtractor(
            processDetailsRepoService, TestHelper.initTranslationService());

    Assert.assertFalse(customWorkflowNonUserConfiguredParameterDetailsExtractor
            .getParameterDetails(workerActionRequest)
            .isEmpty());

  }


  @Test
  public void extractNonUserConfiguredAppConnectParameterDetailsCustomWorkflow() {
    Map<String, String> expectedData =
        Map.of(
            "Message",
            "Hi,\n"
                + "\n"
                + "Invoice [[DocNumber]] was denied approval by [[ApproverName]].[[Comment]]\n"
                + "\n"
                + "Warm regards,\n"
                + "[[CompanyName]]",
            "Subject",
            "Invoice [[DocNumber]] is denied approval");
    processActionRequest("en_US", expectedData, true);
    processActionRequest("en_US", expectedData, false);
    processActionRequestWithoutLocale(expectedData, true);
    processActionRequestWithoutLocale(expectedData, false);
  }

  @Test
  public void extractNonUserConfiguredAppConnectParameterDetailsCustomWorkflowForFr_CA() {
    processActionRequest(
        "fr_CA",
        Map.of(
            "Message",
            "Bonjour,\n"
                + "\n"
                + "Facture [[DocNumber]] s’est vu refuser l’approbation par [[ApproverName]].[[Comment]]\n"
                + "\n"
                + "Cordialement,\n"
                + "[[CompanyName]]",
            "Subject",
            "L’approbation de Facture [[DocNumber]] a été refusée"),
        true);
  }

  private void processActionRequest(
      String locale, Map<String, String> expectedData, boolean setPlaceHolderValue) {
    setDefinitionForLocale(locale, setPlaceHolderValue);
    String handlerId = "hid";
    workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("sendRejectNotification_txnApproval")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .ownerId(Long.valueOf("9999"))
            .inputVariables(schema)
            .handlerId(handlerId)
            .build();
    CustomWorkflowNonUserConfiguredParameterDetailsExtractor
        customWorkflowNonUserConfiguredParameterDetailsExtractor =
            new CustomWorkflowNonUserConfiguredParameterDetailsExtractor(
                processDetailsRepoService, TestHelper.initTranslationService());
    Map<String, HandlerDetails.ParameterDetails> parameterDetails =
        customWorkflowNonUserConfiguredParameterDetailsExtractor
            .getParameterDetails(workerActionRequest)
            .get();
    for (String key : expectedData.keySet()) {
      Assert.assertEquals(expectedData.get(key), parameterDetails.get(key).getFieldValue().get(0));
    }
  }

  private void processActionRequestWithoutLocale(
      Map<String, String> expectedData, boolean setPlaceHolderValue) {
    setDefinition(setPlaceHolderValue);
    String handlerId = "hid";
    workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("sendRejectNotification_txnApproval")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .ownerId(Long.valueOf("9999"))
            .inputVariables(schema)
            .handlerId(handlerId)
            .build();
    CustomWorkflowNonUserConfiguredParameterDetailsExtractor
        customWorkflowNonUserConfiguredParameterDetailsExtractor =
            new CustomWorkflowNonUserConfiguredParameterDetailsExtractor(
                processDetailsRepoService, TestHelper.initTranslationService());
    Map<String, HandlerDetails.ParameterDetails> parameterDetails =
        customWorkflowNonUserConfiguredParameterDetailsExtractor
            .getParameterDetails(workerActionRequest)
            .get();
    for (String key : expectedData.keySet()) {
      Assert.assertEquals(expectedData.get(key), parameterDetails.get(key).getFieldValue().get(0));
    }
  }

  private String getTaskDetails(boolean required) {
    return "{\n" + "  \"required\":" + "\"" + required + "\"" + "}";
  }

  private String getPlaceholderValueForGivenLocale(String locale) {
    return "{\n"
        + "\"user_meta_data\": {\n"
        + "    \"intuit_was_locale\":"
        + "\""
        + locale
        + "\"\n"
        + "}}";
  }

  private String getPlaceholderValue() {
    return "{\n" + "  \"user_meta_data\": {\n" + "  }\n" + "}";
  }

  private void setDefinitionForLocale(String locale, boolean setPlaceHolderValues) {
    TemplateDetails templateDetails =
        TemplateDetails.builder()
            .id(DefinitionTestConstants.TEMPLATE_ID)
            .templateName("customReminder")
            .ownerId(Long.valueOf("9999"))
            .allowMultipleDefinitions(true)
            .build();
    DefinitionDetails mockedDefinitionDetails =
        DefinitionDetails.builder()
            .templateDetails(templateDetails)
            .placeholderValue(
                setPlaceHolderValues ? getPlaceholderValueForGivenLocale(locale) : "{}")
            .build();
    Mockito.when(processDetailsRepoService.findByProcessIdWithoutDefinitionData(any()))
        .thenReturn(Optional.of(mockedDefinitionDetails));
  }

  private void setDefinition(boolean setPlaceHolderValues) {
    TemplateDetails templateDetails =
        TemplateDetails.builder()
            .id(DefinitionTestConstants.TEMPLATE_ID)
            .templateName("customReminder")
            .ownerId(Long.valueOf("9999"))
            .allowMultipleDefinitions(true)
            .build();
    DefinitionDetails mockedDefinitionDetails =
        DefinitionDetails.builder()
            .templateDetails(templateDetails)
            .placeholderValue(setPlaceHolderValues ? getPlaceholderValue() : "{}")
            .build();
    Mockito.when(processDetailsRepoService.findByProcessIdWithoutDefinitionData(any()))
        .thenReturn(Optional.of(mockedDefinitionDetails));
  }

  @Test
  public void extractNonUserConfiguredAppConnectParameterDetailsCustomWorkflowWithoutDefinitionForPO() {
    Mockito.when(processDetailsRepoService.findByProcessIdWithoutDefinitionData(any()))
            .thenReturn(Optional.empty());
    String handlerId = "hid";
    workerActionRequest =
            WorkerActionRequest.builder()
                    .activityId("sendRejectNotification_txnApproval")
                    .processDefinitionId("dId")
                    .processInstanceId("iId")
                    .ownerId(Long.valueOf("9999"))
                    .inputVariables(POSchema)
                    .handlerId(handlerId)
                    .build();
    CustomWorkflowNonUserConfiguredParameterDetailsExtractor
            customWorkflowNonUserConfiguredParameterDetailsExtractor =
            new CustomWorkflowNonUserConfiguredParameterDetailsExtractor(
                    processDetailsRepoService, TestHelper.initTranslationService());
    try {
      customWorkflowNonUserConfiguredParameterDetailsExtractor
              .getParameterDetails(workerActionRequest)
              .get();
      Assert.fail();
    } catch (WorkflowGeneralException workflowGeneralException) {
      Assert.assertEquals(
              workflowGeneralException.getWorkflowError(), WorkflowError.DEFINITION_NOT_FOUND);
    }
  }

  @Test
  public void extractNonUserConfiguredAppConnectParameterDetailsCustomWorkflowForPO() {
    Map<String, String> expectedData =
            Map.of(
                    "Message",
                    "Hi,\n"
                            + "\n"
                            + "Purchase order [[DocNumber]] was denied approval by [[ApproverName]].[[Comment]]\n"
                            + "\n"
                            + "Warm regards,\n"
                            + "[[CompanyName]]",
                    "Subject",
                    "Purchase order [[DocNumber]] is denied approval");
    processActionRequestForPO("en_US", expectedData, true);
    processActionRequestForPO("en_US", expectedData, false);
    processActionRequestWithoutLocaleForPO(expectedData, true);
    processActionRequestWithoutLocaleForPO(expectedData, false);
  }

  private void processActionRequestForPO(
          String locale, Map<String, String> expectedData, boolean setPlaceHolderValue) {
    setDefinitionForLocale(locale, setPlaceHolderValue);
    String handlerId = "hid";
    workerActionRequest =
            WorkerActionRequest.builder()
                    .activityId("sendRejectNotification_txnApproval")
                    .processDefinitionId("dId")
                    .processInstanceId("iId")
                    .ownerId(Long.valueOf("9999"))
                    .inputVariables(POSchema)
                    .handlerId(handlerId)
                    .build();
    CustomWorkflowNonUserConfiguredParameterDetailsExtractor
            customWorkflowNonUserConfiguredParameterDetailsExtractor =
            new CustomWorkflowNonUserConfiguredParameterDetailsExtractor(
                    processDetailsRepoService, TestHelper.initTranslationService());
    Map<String, HandlerDetails.ParameterDetails> parameterDetails =
            customWorkflowNonUserConfiguredParameterDetailsExtractor
                    .getParameterDetails(workerActionRequest)
                    .get();
    for (String key : expectedData.keySet()) {
      Assert.assertEquals(expectedData.get(key), parameterDetails.get(key).getFieldValue().get(0));
    }
  }

  private void processActionRequestWithoutLocaleForPO(
          Map<String, String> expectedData, boolean setPlaceHolderValue) {
    setDefinition(setPlaceHolderValue);
    String handlerId = "hid";
    workerActionRequest =
            WorkerActionRequest.builder()
                    .activityId("sendRejectNotification_txnApproval")
                    .processDefinitionId("dId")
                    .processInstanceId("iId")
                    .ownerId(Long.valueOf("9999"))
                    .inputVariables(POSchema)
                    .handlerId(handlerId)
                    .build();
    CustomWorkflowNonUserConfiguredParameterDetailsExtractor
            customWorkflowNonUserConfiguredParameterDetailsExtractor =
            new CustomWorkflowNonUserConfiguredParameterDetailsExtractor(
                    processDetailsRepoService, TestHelper.initTranslationService());
    Map<String, HandlerDetails.ParameterDetails> parameterDetails =
            customWorkflowNonUserConfiguredParameterDetailsExtractor
                    .getParameterDetails(workerActionRequest)
                    .get();
    for (String key : expectedData.keySet()) {
      Assert.assertEquals(expectedData.get(key), parameterDetails.get(key).getFieldValue().get(0));
    }
  }
}

