package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.eventHandler;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.executor.actions.SchedulingScheduledActionsProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.executor.actions.factory.WorkflowScheduleActionProcessorFactory;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.DeleteEventSchedulingTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.EventScheduleHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.SchedulerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.EventHeaderConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.Status;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.WorkflowNameEnum;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType;
import com.intuit.foundation.workflow.scheduling.Execution;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.ZonedDateTime;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class SchedulingEventHandlerTest {
    @InjectMocks private SchedulingEventHandler schedulingEventHandler;
    @Mock
    private MetricLogger metricsLogger;
    @Mock private WASContextHandler handler;
    @Mock private DefinitionDetailsRepository definitionDetailsRepository;
    @Mock private EventScheduleHelper eventScheduleHelper;
    private SchedulingScheduledActionsProcessor schedulingScheduledActionsProcessor;

    @Before
    public void init() {
        MockitoAnnotations.initMocks(this);
        schedulingScheduledActionsProcessor = Mockito.mock(SchedulingScheduledActionsProcessor.class);
        WorkflowScheduleActionProcessorFactory.addProcessor(
                WorkflowNameEnum.SCHEDULED_ACTIONS, schedulingScheduledActionsProcessor);
        ReflectionTestUtils.setField(schedulingEventHandler, "definitionDetailsRepository", definitionDetailsRepository);
        ReflectionTestUtils.setField(schedulingEventHandler, "wasContextHandler", handler);
        ReflectionTestUtils.setField(schedulingEventHandler, "metricLogger", metricsLogger);
        ReflectionTestUtils.setField(schedulingEventHandler, "eventScheduleHelper", eventScheduleHelper);
    }

    @Test
    public void testTransformEvent() throws Exception {
        Execution schedulingEvent = new Execution();
        schedulingEvent.setExecutionId("executionId");
        schedulingEvent.setPriority(1);
        schedulingEvent.setReferenceId("57928475");
        schedulingEvent.setScheduleId("scheduleId");
        schedulingEvent.setScheduledTime(ZonedDateTime.now());
        schedulingEvent.setMetadata("{}");
        schedulingEvent.setOffering("qbo");
        schedulingEvent.setUseCase("usecase");
        Execution event = schedulingEventHandler.transform(ObjectConverter.toJson(schedulingEvent));

        Assert.assertNotNull(event);
        Assert.assertEquals("executionId",event.getExecutionId());
        Assert.assertEquals(1, event.getPriority().intValue());
    }


    @Test(expected = WorkflowGeneralException.class)
    public void testTransformNullEvent() throws Exception {
        schedulingEventHandler.transform(null);
    }

    @Test(expected = WorkflowGeneralException.class)
    public void testTransformIncorrectEvent() throws Exception {
        schedulingEventHandler.transform("hello");
    }

    @Test(expected = WorkflowGeneralException.class)
    public void testExecuteMissingExecutionId() throws Exception {
        Execution schedulingEvent = new Execution();
        schedulingEvent.setExecutionId(null);
        schedulingEvent.setPriority(1);
        schedulingEvent.setReferenceId("57928475");
        schedulingEvent.setScheduleId("scheduleId");
        schedulingEvent.setScheduledTime(ZonedDateTime.now());
        schedulingEvent.setMetadata("{}");
        schedulingEvent.setOffering("qbo");
        schedulingEvent.setUseCase("usecase");
        schedulingEventHandler.execute(schedulingEvent, getHeadersWithOwnerId());
    }

    @Test(expected = WorkflowGeneralException.class)
    public void testExecuteIncorrectReferenceId() throws Exception {
        Execution schedulingEvent = new Execution();
        schedulingEvent.setExecutionId("executionId");
        schedulingEvent.setPriority(1);
        schedulingEvent.setReferenceId("57928475");
        schedulingEvent.setScheduleId("scheduleId");
        schedulingEvent.setScheduledTime(ZonedDateTime.now());
        schedulingEvent.setMetadata("{}");
        schedulingEvent.setOffering("qbo");
        schedulingEvent.setUseCase("usecase");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put(EventHeaderConstants.OWNER_ID, "1234");


        schedulingEventHandler.execute(schedulingEvent, headers);
        Mockito.verify(metricsLogger)
                .logErrorMetric(
                        Mockito.eq(MetricName.SCHEDULING_EVENT), Mockito.eq(Type.EVENT_METRIC), Mockito.any());
    }

    @Test
    public void testExecuteNoDefinitionFound() throws Exception {
        Execution schedulingEvent = new Execution();
        schedulingEvent.setExecutionId("executionId");
        schedulingEvent.setPriority(1);
        schedulingEvent.setReferenceId("57928475:customScheduledActions_customStart");
        schedulingEvent.setScheduleId("scheduleId");
        schedulingEvent.setScheduledTime(ZonedDateTime.now());
        schedulingEvent.setMetadata("{}");
        schedulingEvent.setOffering("qbo");
        schedulingEvent.setUseCase("usecase");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put(EventHeaderConstants.OWNER_ID, "1234");
        DeleteEventSchedulingTask deleteEventSchedulingTask = Mockito.mock(DeleteEventSchedulingTask.class);
        when(definitionDetailsRepository.findTopByDefinitionKeyAndOwnerIdOrderByVersionDesc(Mockito.any(), Mockito.any())).thenReturn(null);
        when(eventScheduleHelper.prepareSchedulingDeleteTask(Mockito.any(), Mockito.any())).thenReturn(deleteEventSchedulingTask);
        when(handler.get(Mockito.any())).thenReturn("1234");
        schedulingEventHandler.execute(schedulingEvent, headers);
        Mockito.verify(schedulingScheduledActionsProcessor, Mockito.times(0)).process((SchedulerDetails) Mockito.any(), Mockito.any());
    }

    @Test
    public void testExecuteDefinitionStatusDisabled() throws Exception {
        Execution schedulingEvent = new Execution();
        schedulingEvent.setExecutionId("executionId");
        schedulingEvent.setPriority(1);
        schedulingEvent.setReferenceId("57928475:customScheduledActions_customStart");
        schedulingEvent.setScheduleId("scheduleId");
        schedulingEvent.setScheduledTime(ZonedDateTime.now());
        schedulingEvent.setMetadata("{}");
        schedulingEvent.setOffering("qbo");
        schedulingEvent.setUseCase("usecase");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put(EventHeaderConstants.OWNER_ID, "1234");
        DefinitionDetails definitionDetails = new DefinitionDetails();
        definitionDetails.setStatus(Status.valueOf("DISABLED"));
        when(definitionDetailsRepository.findTopByDefinitionKeyAndOwnerIdOrderByVersionDesc(Mockito.any(), Mockito.any())).thenReturn(definitionDetails);
        when(handler.get(WASContextEnums.OWNER_ID)).thenReturn("23849237498");
        schedulingEventHandler.execute(schedulingEvent, headers);
        Mockito.verify(schedulingScheduledActionsProcessor, Mockito.times(0)).process((SchedulerDetails) Mockito.any(), Mockito.any());
    }

    @Test
    public void testExecuteSuccess() throws Exception {
        Execution schedulingEvent = new Execution();
        schedulingEvent.setExecutionId("executionId");
        schedulingEvent.setPriority(1);
        schedulingEvent.setReferenceId("57928475:customScheduledActions_customStart");
        schedulingEvent.setScheduleId("scheduleId");
        schedulingEvent.setScheduledTime(ZonedDateTime.now());
        schedulingEvent.setMetadata("{}");
        schedulingEvent.setOffering("qbo");
        schedulingEvent.setUseCase("usecase");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put(EventHeaderConstants.OWNER_ID, "1234");
        DefinitionDetails definitionDetails = new DefinitionDetails();
        definitionDetails.setStatus(Status.valueOf("ENABLED"));
        TemplateDetails templateDetails = Mockito.mock(TemplateDetails.class);
        when(templateDetails.getTemplateName()).thenReturn("customScheduledActions");
        definitionDetails.setTemplateDetails(templateDetails);

        when(definitionDetailsRepository.findTopByDefinitionKeyAndOwnerIdOrderByVersionDesc(Mockito.any(), Mockito.any())).thenReturn(definitionDetails);
        when(schedulingScheduledActionsProcessor.process((DefinitionDetails)  Mockito.any(), (Execution) Mockito.any())).thenReturn(Collections.emptyMap());
        when(handler.get(WASContextEnums.OWNER_ID)).thenReturn("23849237498");

        schedulingEventHandler.execute(schedulingEvent, headers);
        Mockito.verify(schedulingScheduledActionsProcessor, Mockito.times(1)).process((DefinitionDetails) Mockito.any(), Mockito.any());
    }

    @Test(expected = WorkflowGeneralException.class)
    public void testExecuteFailure() throws Exception {
        Execution schedulingEvent = new Execution();
        schedulingEvent.setExecutionId("executionId");
        schedulingEvent.setPriority(1);
        schedulingEvent.setReferenceId("57928475:customScheduledActions_customStart");
        schedulingEvent.setScheduleId("scheduleId");
        schedulingEvent.setScheduledTime(ZonedDateTime.now());
        schedulingEvent.setMetadata("{}");
        schedulingEvent.setOffering("qbo");
        schedulingEvent.setUseCase("usecase");Map<String, String> headers = new HashMap<String, String>();
        headers.put(EventHeaderConstants.OWNER_ID, "1234");
        DefinitionDetails definitionDetails = new DefinitionDetails();
        definitionDetails.setStatus(Status.valueOf("ENABLED"));
        TemplateDetails templateDetails = Mockito.mock(TemplateDetails.class);
        when(templateDetails.getTemplateName()).thenReturn("customScheduledActions");
        definitionDetails.setTemplateDetails(templateDetails);

        when(definitionDetailsRepository.findTopByDefinitionKeyAndOwnerIdOrderByVersionDesc(Mockito.any(), Mockito.any())).thenReturn(definitionDetails);
        when(schedulingScheduledActionsProcessor.process((DefinitionDetails)  Mockito.any(), Mockito.any())).thenThrow(new WorkflowGeneralException("test"));
        when(handler.get(WASContextEnums.OWNER_ID)).thenReturn("23849237498");

        schedulingEventHandler.execute(schedulingEvent, headers);
        Mockito.verify(metricsLogger)
                .logErrorMetric(
                        Mockito.eq(MetricName.SCHEDULING_EVENT), Mockito.eq(Type.EVENT_METRIC), Mockito.any());
    }

    @Test(expected = WorkflowGeneralException.class)
    public void testExecuteFailureRetryable() throws Exception {
        Execution schedulingEvent = new Execution();
        schedulingEvent.setExecutionId("executionId");
        schedulingEvent.setPriority(1);
        schedulingEvent.setReferenceId("57928475:customScheduledActions_customStart");
        schedulingEvent.setScheduleId("scheduleId");
        schedulingEvent.setScheduledTime(ZonedDateTime.now());
        schedulingEvent.setMetadata("{}");
        schedulingEvent.setOffering("qbo");
        schedulingEvent.setUseCase("usecase");Map<String, String> headers = new HashMap<String, String>();
        headers.put(EventHeaderConstants.OWNER_ID, "1234");
        DefinitionDetails definitionDetails = new DefinitionDetails();
        definitionDetails.setStatus(Status.valueOf("ENABLED"));
        TemplateDetails templateDetails = Mockito.mock(TemplateDetails.class);
        when(templateDetails.getTemplateName()).thenReturn("customScheduledActions");
        definitionDetails.setTemplateDetails(templateDetails);

        when(definitionDetailsRepository.findTopByDefinitionKeyAndOwnerIdOrderByVersionDesc(Mockito.any(), Mockito.any())).thenReturn(definitionDetails);
        when(schedulingScheduledActionsProcessor.process((DefinitionDetails)  Mockito.any(), Mockito.any())).thenThrow(new WorkflowRetriableException("test"));
        when(handler.get(WASContextEnums.OWNER_ID)).thenReturn("23849237498");

        schedulingEventHandler.execute(schedulingEvent, headers);
    }

    @Test
    public void testGetName(){
        Assert.assertEquals(EventEntityType.SCHEDULING, schedulingEventHandler.getName());
    }

    private Map<String, String> getHeadersWithOwnerId() {
        Map<String, String> headers = new HashMap<String, String>();
        headers.put(EventHeaderConstants.OWNER_ID, "1234");

        return headers;
    }
}
