package com.intuit.appintgwkflw.wkflautomate.was.rxhooks;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;

import com.intuit.appintgwkflw.wkflautomate.was.common.util.WASContext;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.impl.RxExecutionChain;
import com.intuit.async.execution.request.State;
import com.intuit.v4.Authorization;
import org.jboss.logging.MDC;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import io.reactivex.plugins.RxJavaPlugins;

/** <AUTHOR> */
public class RxHooksTest {

  @Before
  public void init() {
    // to copy MDC to child threads
    RxJavaPlugins.setScheduleHandler(new MdcPropagatingOnScheduleFunction());
  }

  @Test
  public void testMdcOnChildThread() {
    MDC.put(WASContextEnums.AUTHORIZATION_HEADER.getValue(), "TEST");
    State chain = new RxExecutionChain(new State(), new TaskA()).execute();
    Assert.assertEquals("TEST", chain.getValue("HEADERFROMDC"));
  }

  @Test
  public void testThreadLocal() {
    Authorization authorization = new Authorization().realm("123");
    WASContext.setAuthContext(authorization);
    WASContext.setNonRealmSystemUserContext(true);
    WASContext.setOfferingId("qbo");
    try {
      new RxExecutionChain(new State(), new TaskB(), new TaskB()).next(new TaskB(), new TaskB())
          .next(new TaskB()).execute();
    } catch (Exception e) {
      Assert.fail("The above method should not fail");
    } finally {
      WASContext.clear();
    }
  }
}

class TaskA implements Task {

  @Override
  public State execute(State inputRequest) {
    State output = new State();
    output.addValue("HEADERFROMDC", MDC.get(WASContextEnums.AUTHORIZATION_HEADER.getValue()));
    return output;
  }
}

class TaskB implements Task{

  @Override
  public State execute(State state) {
    Assert.assertNotNull(WASContext.getOwnerId());
    Assert.assertEquals(123L, WASContext.getOwnerId().longValue());
    Assert.assertTrue(WASContext.isNonRealmSystemUser());
    Assert.assertTrue(WASContext.getOfferingId().isPresent());
    Assert.assertEquals("qbo", WASContext.getOfferingId().get());
    return new State();
  }
}
