package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import static org.mockito.ArgumentMatchers.any;

import java.util.Collections;
import java.util.HashMap;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.TaskHandlerName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;

/** <AUTHOR> */
public class TestHandlerTest {

  @InjectMocks private TestHandler testHandler;

  @Mock
  private MetricLogger metricLogger;

  @Before
  public void init() {
    MockitoAnnotations.openMocks(this);
    WorkflowTaskHandlers.addHandler(testHandler.getName(), testHandler);
    ReflectionTestUtils.setField(testHandler, "metricLogger", metricLogger);
  }

  @Test
  public void getTestAction() {
    WorkflowTaskHandler handler =
        WorkflowTaskHandlers.getHandler(TaskHandlerName.TEST_ACTION_HANDLER);
    Assert.assertNotNull(handler);
  }
  
  @Test
  public void testGetName() {
    Assert.assertNotNull(testHandler);
    Assert.assertNotNull(testHandler.executeAction(new HashMap<>().put("test", Collections.emptyMap())));
    Assert.assertEquals(TaskHandlerName.TEST_ACTION_HANDLER, testHandler.getName());
    Assert.assertNotNull(testHandler
			.executeAction(new HashMap<>().put("extensionProperties", Collections.singletonMap("delay", "false"))));
  }

  @Test
  public void containsTrue() {
    Assert.assertTrue(
        (Boolean)
            WorkflowTaskHandlers.getHandler(TaskHandlerName.TEST_ACTION_HANDLER)
                .executeAction(new HashMap<>().put("test", Collections.emptyMap()))
                .get(WorkFlowVariables.RESPONSE.getName()));
  }
  
  

  @Test
  public void testLogErrorMetric() {
    testHandler.logErrorMetric(new WorkflowGeneralException(WorkflowError.INVALID_INPUT), null);
    Mockito.verify(metricLogger, Mockito.times(1)).logErrorMetric(any(), any(), any());
  }
}
