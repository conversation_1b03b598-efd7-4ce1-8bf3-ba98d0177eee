package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import static org.mockito.ArgumentMatchers.any;

import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.SchedulerDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;

import com.intuit.async.execution.request.State;
import java.util.List;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class UpdateSchedulerDetailsInDataStoreTaskTest {
  private State input;
  private SchedulerDetailsRepository schedulerDetailsRepository;
  private UpdateSchedulerDetailsInDataStoreTask updateSchedulerDetailsInDataStoreTask;
  private final String definitionId = "def123";
  private final List<String> schedulerIds = List.of("sch123", "sch1234");

  @Before
  public void setup() {
    input = new State();
    schedulerDetailsRepository = Mockito.mock(SchedulerDetailsRepository.class);
    updateSchedulerDetailsInDataStoreTask =
        new UpdateSchedulerDetailsInDataStoreTask(schedulerDetailsRepository);
  }

  @Test
  public void testExecute() {
    input.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, definitionId);
    input.addValue(AsyncTaskConstants.EVENT_SCHEDULE_IDS, schedulerIds);
    Mockito.when(
            schedulerDetailsRepository.updateDefinitionIdForSchedulers(definitionId, schedulerIds))
        .thenReturn(2);
    updateSchedulerDetailsInDataStoreTask.execute(input);
    Mockito.verify(schedulerDetailsRepository, Mockito.times(1))
        .updateDefinitionIdForSchedulers(definitionId, schedulerIds);
  }

  @Test
  public void testExecute_WithNoSchedulerIds() {
    input.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, definitionId);
    updateSchedulerDetailsInDataStoreTask.execute(input);
    Mockito.verify(schedulerDetailsRepository, Mockito.times(0))
        .updateDefinitionIdForSchedulers(any(), any());
  }

  @Test
  public void testExecute_WithEmptySchedulerIds() {
    input.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, definitionId);
    input.addValue(AsyncTaskConstants.EVENT_SCHEDULE_IDS, List.of());
    updateSchedulerDetailsInDataStoreTask.execute(input);
    Mockito.verify(schedulerDetailsRepository, Mockito.times(0))
        .updateDefinitionIdForSchedulers(any(), any());
  }

  @Test
  public void testExecute_WithException() {
    input.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, definitionId);
    input.addValue(AsyncTaskConstants.EVENT_SCHEDULE_IDS, schedulerIds);
    Mockito.when(
            schedulerDetailsRepository.updateDefinitionIdForSchedulers(definitionId, schedulerIds))
        .thenThrow(new RuntimeException("DB failed"));
    State state = updateSchedulerDetailsInDataStoreTask.execute(input);
    Mockito.verify(schedulerDetailsRepository, Mockito.times(1))
        .updateDefinitionIdForSchedulers(definitionId, schedulerIds);
    Assert.assertTrue(state.getValue(AsyncTaskConstants.UPDATE_SCHEDULE_DETAILS_TASK_FAILURE));
  }

  @Test
  public void testExecute_WithDefIdKeyBlankOrNull() {
    State state = updateSchedulerDetailsInDataStoreTask.execute(input);
    Mockito.verify(schedulerDetailsRepository, Mockito.times(0))
        .updateDefinitionIdForSchedulers(any(), any());
    Assert.assertTrue(state.getValue(AsyncTaskConstants.UPDATE_SCHEDULE_DETAILS_TASK_FAILURE));

    // add blank only
    input.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, "");
    state = updateSchedulerDetailsInDataStoreTask.execute(input);
    Mockito.verify(schedulerDetailsRepository, Mockito.times(0))
        .updateDefinitionIdForSchedulers(any(), any());
    Assert.assertTrue(state.getValue(AsyncTaskConstants.UPDATE_SCHEDULE_DETAILS_TASK_FAILURE));
  }
}
