package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.fetcher;

import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.AppConnectConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.retry.SqsRetryConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.httpClient.AppConnectWASClient;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.AuthDetailsService;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.AuthDetails;

import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.fetcher.RecordQueryConnectorRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.fetcher.RecordQueryConnectorResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.HttpStatus;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(MockitoJUnitRunner.class)
public class AppconnectRecordListFetcherTest {
  @InjectMocks private AppconnectRecordListFetcher recordListFetcher;
  @Mock private AppConnectConfig appConnectConfig;
  @Mock private AppConnectWASClient appConnectWASClient;
  @Mock private AuthDetailsService authDetailsService;
  @Mock private WASContextHandler wasContextHandler;
  @Mock private SqsRetryConfig sqsRetryConfig;
  private static final String APPCONNECT_DUZZIT_RESPONSE =
      TestHelper.readResourceAsString("duzzitOutputs/appconnectCRResponse.json");

  private final String ownerId = "123456789";
  private final RecordQueryConnectorResponse recordQueryConnectorResponse =
      ObjectConverter.fromJson(APPCONNECT_DUZZIT_RESPONSE, RecordQueryConnectorResponse.class);

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
    ReflectionTestUtils.setField(recordListFetcher, "authDetailsService", authDetailsService);
    ReflectionTestUtils.setField(recordListFetcher, "appConnectConfig", appConnectConfig);
    ReflectionTestUtils.setField(recordListFetcher, "appConnectWASClient", appConnectWASClient);
    ReflectionTestUtils.setField(recordListFetcher, "wasContextHandler", wasContextHandler);
    ReflectionTestUtils.setField(recordListFetcher, "sqsRetryConfig", sqsRetryConfig);
    Mockito.when(appConnectConfig.getConnectorEndpoint())
        .thenReturn("https://e2e.api.intuit.com/appconnect/connector");
    Mockito.when(authDetailsService.getAuthDetailsFromRealmId(ownerId))
        .thenReturn(getAuthDetails());
    Mockito.when(wasContextHandler.get(WASContextEnums.INTUIT_TID))
        .thenReturn(UUID.randomUUID().toString());
    Mockito.when(wasContextHandler.get(WASContextEnums.OWNER_ID)).thenReturn(ownerId);
    Mockito.when(sqsRetryConfig.getStatusCode()).thenReturn(Set.of(501, 502, 503));
  }

  @Test
  public void fetchTransactions() {
    Mockito.when(appConnectWASClient.httpResponse(Mockito.any()))
        .thenReturn(
            WASHttpResponse.builder()
                .isSuccess2xx(true)
                .status(HttpStatus.ACCEPTED)
                .response(recordQueryConnectorResponse)
                .build());
    RecordQueryConnectorResponse response =
        recordListFetcher.fetchRecords(
            new RecordQueryConnectorRequest("connectorId-123", getAppconnectDuzzitRequest()));
    verify(authDetailsService, times(1)).getAuthDetailsFromRealmId(ownerId);
    verify(appConnectWASClient, times(1)).httpResponse(Mockito.any());
    Assert.assertTrue(response.getRecordList().size() > 1);
  }

  @Test
  public void fetchTransactions_withAppconnectError() {
    Mockito.when(appConnectWASClient.httpResponse(Mockito.any()))
        .thenReturn(
            WASHttpResponse.builder()
                .isSuccess2xx(false)
                .status(HttpStatus.ACCEPTED)
                .response(recordQueryConnectorResponse)
                .build());
    try {
      recordListFetcher.fetchRecords(
          new RecordQueryConnectorRequest("connectorId-123", getAppconnectDuzzitRequest()));
    } catch (WorkflowGeneralException workflowGeneralException) {
      verify(authDetailsService, times(1)).getAuthDetailsFromRealmId(ownerId);
      verify(appConnectWASClient, times(1)).httpResponse(Mockito.any());
      Assert.assertEquals(
          WorkflowError.APPCONNECT_DUZZIT_CALL_FAILURE,
          workflowGeneralException.getWorkflowError());
    }
  }

  @Test
  public void fetchTransactions_withResponseNull() {
    Mockito.when(appConnectWASClient.httpResponse(Mockito.any()))
        .thenReturn(
            WASHttpResponse.builder()
                .isSuccess2xx(true)
                .status(HttpStatus.ACCEPTED)
                .response(null)
                .build());
    try {
      recordListFetcher.fetchRecords(
          new RecordQueryConnectorRequest("connectorId-123", getAppconnectDuzzitRequest()));
    } catch (WorkflowGeneralException workflowGeneralException) {
      verify(authDetailsService, times(1)).getAuthDetailsFromRealmId(ownerId);
      verify(appConnectWASClient, times(1)).httpResponse(Mockito.any());
      Assert.assertEquals(
          WorkflowError.APPCONNECT_DUZZIT_CALL_FAILURE,
          workflowGeneralException.getWorkflowError());
    }
  }

  @Test
  public void fetchTransactions_successNull() {
    recordQueryConnectorResponse.setSuccess(null);
    Mockito.when(appConnectWASClient.httpResponse(Mockito.any()))
        .thenReturn(
            WASHttpResponse.builder()
                .isSuccess2xx(true)
                .status(HttpStatus.ACCEPTED)
                .response(null)
                .build());
    try {
      recordListFetcher.fetchRecords(
          new RecordQueryConnectorRequest("connectorId-123", getAppconnectDuzzitRequest()));
    } catch (WorkflowGeneralException workflowGeneralException) {
      verify(authDetailsService, times(1)).getAuthDetailsFromRealmId(ownerId);
      verify(appConnectWASClient, times(1)).httpResponse(Mockito.any());
      Assert.assertEquals(
          WorkflowError.APPCONNECT_DUZZIT_CALL_FAILURE,
          workflowGeneralException.getWorkflowError());
    }
  }

  @Test
  public void fetchTransactions_successEmpty() {
    recordQueryConnectorResponse.setSuccess("");
    Mockito.when(appConnectWASClient.httpResponse(Mockito.any()))
        .thenReturn(
            WASHttpResponse.builder()
                .isSuccess2xx(true)
                .status(HttpStatus.ACCEPTED)
                .response(null)
                .build());
    try {
      recordListFetcher.fetchRecords(
          new RecordQueryConnectorRequest("connectorId-123", getAppconnectDuzzitRequest()));
    } catch (WorkflowGeneralException workflowGeneralException) {
      verify(authDetailsService, times(1)).getAuthDetailsFromRealmId(ownerId);
      verify(appConnectWASClient, times(1)).httpResponse(Mockito.any());
      Assert.assertEquals(
          WorkflowError.APPCONNECT_DUZZIT_CALL_FAILURE,
          workflowGeneralException.getWorkflowError());
    }
  }

  @Test
  public void fetchTransactions_successFalse() {
    recordQueryConnectorResponse.setSuccess("FALSE");
    Mockito.when(appConnectWASClient.httpResponse(Mockito.any()))
        .thenReturn(
            WASHttpResponse.builder()
                .isSuccess2xx(true)
                .status(HttpStatus.ACCEPTED)
                .response(null)
                .build());
    try {
      recordListFetcher.fetchRecords(
          new RecordQueryConnectorRequest("connectorId-123", getAppconnectDuzzitRequest()));
    } catch (WorkflowGeneralException workflowGeneralException) {
      verify(authDetailsService, times(1)).getAuthDetailsFromRealmId(ownerId);
      verify(appConnectWASClient, times(1)).httpResponse(Mockito.any());
      Assert.assertEquals(
          WorkflowError.APPCONNECT_DUZZIT_CALL_FAILURE,
          workflowGeneralException.getWorkflowError());
    }
  }

  private AuthDetails getAuthDetails() {
    AuthDetails authDetails = new AuthDetails();
    authDetails.setSubscriptionId("1234");
    return authDetails;
  }

  private Map<String, String> getAppconnectDuzzitRequest() {
    Map<String, String> requestMap = new HashMap<>();
    requestMap.put(WorkflowConstants.FILTER_RECORD_TYPE, RecordType.INVOICE.getRecordType());
    requestMap.put(
        WorkflowConstants.FILTER_CONDITION,
        "{   \"rules\": [     {       \"parameterName\": \"TxnAmount\",       \"conditionalExpression\": \"GTE 0\"    } , {       \"parameterName\": \"TxnDueDays\",       \"conditionalExpression\": \"AF 0\"    }   ] }");
    requestMap.put(WorkflowConstants.FILTER_CLOSE_TASK_CONDITIONS, "txn_sent");
    return requestMap;
  }

  @Test
  public void fetchTransactions_withAppconnect500XError() {
    Mockito.when(appConnectWASClient.httpResponse(Mockito.any()))
        .thenReturn(
            WASHttpResponse.builder()
                .isSuccess2xx(false)
                .status(HttpStatus.BAD_GATEWAY)
                .error("TEST ERROR")
                .response(recordQueryConnectorResponse)
                .build());
    try {
      recordListFetcher.fetchRecords(
          new RecordQueryConnectorRequest("connectorId-123", getAppconnectDuzzitRequest()));
    } catch (WorkflowGeneralException workflowGeneralException) {
      verify(authDetailsService, times(1)).getAuthDetailsFromRealmId(ownerId);
      verify(appConnectWASClient, times(1)).httpResponse(Mockito.any());
      Assert.assertEquals(
          WorkflowError.APPCONNECT_DUZZIT_RETRYABLE_EXCEPION,
          workflowGeneralException.getWorkflowError());
    }
  }
}
