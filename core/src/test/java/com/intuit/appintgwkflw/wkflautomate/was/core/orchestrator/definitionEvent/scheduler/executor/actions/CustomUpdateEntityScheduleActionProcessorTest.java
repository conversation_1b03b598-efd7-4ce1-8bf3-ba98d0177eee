package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.executor.actions;

import static org.mockito.Mockito.times;

import java.util.Collections;

import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.executor.actions.CustomReminderScheduleActionProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.executor.actions.CustomSendEntityScheduleActionProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.executor.actions.CustomUpdateEntityScheduleActionProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.SchedulerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.SchedulerAction;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.trigger.Trigger;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.listner.EventScheduleMessageData;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(MockitoJUnitRunner.class)
public class CustomUpdateEntityScheduleActionProcessorTest {

  @InjectMocks
  private CustomUpdateEntityScheduleActionProcessor customUpdateEntityScheduleActionProcessor;
  @Mock
  private CustomReminderScheduleActionProcessor customReminderScheduleActionProcessor;
  private static final String CUSTOM_REMINDER_WORKFLOW_DEF =
      TestHelper.readResourceAsString("bpmn/customWorkflowDefinition.bpmn");

  private static final String CUSTOM_REMINDER_WORKFLOW =
      TestHelper.readResourceAsString("bpmn/customReminderTest.bpmn");

  private static final long ownerId = 123456789;

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
    ReflectionTestUtils.setField(
        customUpdateEntityScheduleActionProcessor, "customReminderScheduleActionProcessor",
        customReminderScheduleActionProcessor);
  }

  @Test
  public void testProcess() {
    SchedulerDetails schedulerDetails = getSchedulerDetails(CUSTOM_REMINDER_WORKFLOW_DEF);
    EventScheduleMessageData eventScheduleMessageData = getMessageData();
    Mockito.when(customReminderScheduleActionProcessor
        .process(schedulerDetails, eventScheduleMessageData)).thenReturn(Collections.emptyMap());
    customUpdateEntityScheduleActionProcessor.process(
        schedulerDetails, eventScheduleMessageData);
    Mockito.verify(customReminderScheduleActionProcessor, times(1))
        .process(schedulerDetails, eventScheduleMessageData);
  }

  private SchedulerDetails getSchedulerDetails(String data) {
    return SchedulerDetails.builder()
        .definitionDetails(getDefinitionDetails(data))
        .schedulerId("1234")
        .schedulerAction(SchedulerAction.CUSTOM_REMINDER_CUSTOM_START)
        .ownerId(ownerId)
        .build();
  }

  private EventScheduleMessageData getMessageData() {
    EventScheduleMessageData eventScheduleMessageData = new EventScheduleMessageData();
    eventScheduleMessageData.setMessageId("mes123");
    eventScheduleMessageData.setScheduleId("1234");
    return eventScheduleMessageData;
  }

  private DefinitionDetails getDefinitionDetails(String data) {
    DefinitionDetails definitionDetails = new DefinitionDetails();
    definitionDetails.setDefinitionId("def123");
    definitionDetails.setOwnerId(Long.valueOf(ownerId));
    definitionDetails.setRecordType(RecordType.INVOICE);
    definitionDetails.setDefinitionData(data.getBytes());
    TemplateDetails templateDetails = new TemplateDetails();
    templateDetails.setOfferingId("off123");
    definitionDetails.setTemplateDetails(templateDetails);
    return definitionDetails;
  }
}
