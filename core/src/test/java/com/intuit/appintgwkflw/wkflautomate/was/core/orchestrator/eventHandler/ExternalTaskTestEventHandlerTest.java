package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.eventHandler;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowEventException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.EventHeaderConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventHeaderEntity;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.WorkflowMetaData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.externaltask.ExternalTaskAssigned;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.externaltask.ExternalTaskCompleted;
import com.intuit.appintgwkflw.wkflautomate.was.event.api.EventPublisherCapability;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import static org.mockito.ArgumentMatchers.any;


@RunWith(MockitoJUnitRunner.class)
public class ExternalTaskTestEventHandlerTest {

  private ExternalTaskTestEventHandler handler;

  @Mock
  EventPublisherCapability eventPublisherCapability;

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
    handler = new ExternalTaskTestEventHandler(eventPublisherCapability);
  }

  @Test
  public void testTransform() throws Exception {
    ExternalTaskAssigned e =
        ExternalTaskAssigned.builder()
            .businessEntityType("businessEntityType")
            .businessEntityId("businessEntityId")
            .taskName("taskname")
            .variables(Collections.EMPTY_MAP)
            .build();

    ExternalTaskAssigned result = handler.transform(ObjectConverter.toJson(e));
    Assert.assertNotNull(result);
    Assert.assertEquals("taskname", result.getTaskName());
    Assert.assertEquals("businessEntityId", result.getBusinessEntityId());
    Assert.assertEquals("businessEntityType", result.getBusinessEntityType());
    Assert.assertEquals(Collections.EMPTY_MAP, result.getVariables());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testTransformNull() throws Exception {
    handler.transform(null);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testTransformIncorrectJson() throws Exception {
    handler.transform("hello");
  }

  @Test
  public void testExecuteSuccess() throws Exception {
    WorkflowMetaData workflowMetaData =
        WorkflowMetaData.builder()
            .workflowName("workflowName")
            .processInstanceId("processInstanceId")
            .workflowOwnerId("workflowownerId")
            .build();
    ExternalTaskAssigned task =
        ExternalTaskAssigned.builder()
            .workflowMetadata(workflowMetaData)
            .businessEntityType("businessEntityType")
            .businessEntityId("businessEntityId")
            .taskName("taskname")
            .variables(Collections.EMPTY_MAP)
            .build();

    Map<String, String> headers = new HashMap<>();
    headers.put(EventHeaderConstants.ENTITY_ID, "taskId:workerId");
    headers.put(EventHeaderConstants.INTUIT_TID, "tid");
    handler.execute(task, headers);
    Mockito.verify(eventPublisherCapability).publish(any(), any());
  }

  @Test
  public void testExecuteSuccess_withTargetTestAlias() throws Exception {
    WorkflowMetaData workflowMetaData =
            WorkflowMetaData.builder()
                    .workflowName("workflowName")
                    .processInstanceId("processInstanceId")
                    .workflowOwnerId("workflowownerId")
                    .build();
    ExternalTaskAssigned task =
            ExternalTaskAssigned.builder()
                    .workflowMetadata(workflowMetaData)
                    .businessEntityType("businessEntityType")
                    .businessEntityId("businessEntityId")
                    .taskName("taskname")
                    .variables(Collections.EMPTY_MAP)
                    .build();

    Map<String, String> headers = new HashMap<>();
    headers.put(EventHeaderConstants.ENTITY_ID, "taskId:workerId");
    headers.put(EventHeaderConstants.INTUIT_TID, "tid");
    headers.put(EventHeaderConstants.TARGET_ASSET_ALIAS, "[{'targetAssetAlias':'value'}]");
    handler.execute(task, headers);
    Mockito.verify(eventPublisherCapability).publish(any(), any());
  }

  @Test(expected = WorkflowEventException.class)
  public void testExecuteException() throws Exception {
    WorkflowMetaData workflowMetaData =
            WorkflowMetaData.builder()
                    .workflowName("workflowName")
                    .processInstanceId("processInstanceId")
                    .workflowOwnerId("workflowownerId")
                    .build();
    ExternalTaskAssigned task =
            ExternalTaskAssigned.builder()
                    .workflowMetadata(workflowMetaData)
                    .businessEntityType("businessEntityType")
                    .businessEntityId("businessEntityId")
                    .taskName("taskname")
                    .variables(Collections.EMPTY_MAP)
                    .build();

    Map<String, String> headers = new HashMap<>();
    headers.put(EventHeaderConstants.ENTITY_ID, "taskId:workerId");
    headers.put(EventHeaderConstants.INTUIT_TID, "tid");
    Mockito.doThrow(
            new WorkflowEventException("Error")).
            when(eventPublisherCapability).publish(any(EventHeaderEntity.class), any(ExternalTaskCompleted.class));
    handler.execute(task, headers);
  }

}
