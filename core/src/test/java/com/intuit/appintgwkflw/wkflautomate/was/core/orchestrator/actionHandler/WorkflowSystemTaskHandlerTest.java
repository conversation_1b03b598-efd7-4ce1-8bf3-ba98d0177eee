package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.common.WorkflowExternalTaskManager;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.WorkflowTaskConfig;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.WorkflowTaskConfigDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import java.util.HashMap;
import java.util.Map;
import org.camunda.bpm.engine.variable.impl.VariableMapImpl;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class WorkflowSystemTaskHandlerTest {

  @Mock private WorkflowExternalTaskManager taskManager;

  @Mock private PublishEventHandler publishEventHandler;

  @Mock private WorkflowTaskConfig workflowTaskConfig;

  @InjectMocks private WorkflowSystemTaskHandler workflowSystemTaskHandler;

  @Before
  public void init() {

    Map<TaskType, WorkflowTaskConfigDetails> configDetailsMap = Mockito.mock(Map.class);
    Mockito.when(workflowTaskConfig.getTaskConfig()).thenReturn(configDetailsMap);

  }

  @Test
  public void executePublishHandler_systemTaskDisabled(){
    WorkflowTaskRequest.WorkflowTaskRequestBuilder builder = WorkflowTaskRequest.builder();
    WorkerActionRequest workerActionRequest = WorkerActionRequest.builder().activityId("act1")
        .extensionProperties(new HashMap<>()).variableMap(new VariableMapImpl()).build();

    WorkflowTaskConfigDetails workflowTaskConfigDetails = new WorkflowTaskConfigDetails();
    workflowTaskConfigDetails.setDisable(true);
    Map<TaskType, WorkflowTaskConfigDetails> configDetailsMap = Mockito.mock(Map.class);
    Mockito.when(workflowTaskConfig.getTaskConfig()).thenReturn(configDetailsMap);
    Mockito.when(configDetailsMap.get(TaskType.SYSTEM_TASK)).thenReturn(workflowTaskConfigDetails);

    Mockito.when(publishEventHandler.executeAction(Mockito.any())).thenReturn(Map.of(
        WorkFlowVariables.PENDING_EXTEND_LOCK.getName(), Boolean.TRUE));
    Map<String, Object> response = workflowSystemTaskHandler.execute(builder, workerActionRequest);
    Assert.assertTrue(response.containsKey(WorkFlowVariables.PENDING_EXTEND_LOCK.getName()));

    Mockito.verify(publishEventHandler, Mockito.times(1)).executeAction(Mockito.any());
  }

  @Test
  public void testSystemTask(){
    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().skipCallback(false)
        .publishExternalTaskEvent(false).publishWorkflowStateTransitionEvent(true)
        .invokeDownstreamOnRetry(true).build();

    Mockito.when(taskManager.execute(Mockito.any())).thenReturn(
        WorkflowTaskResponse.builder().responseMap(Map.of("key", "value")).build());
    Map<String, Object> response = workflowSystemTaskHandler.execute(WorkflowTaskRequest.builder(),
        Mockito.mock(WorkerActionRequest.class));

    Mockito.verify(taskManager, Mockito.times(1)).execute(Mockito.eq(taskRequest));
    Assert.assertTrue(response.containsKey(WorkFlowVariables.PENDING_EXTEND_LOCK.getName()));
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testSystemTask_Failure(){

    Mockito.when(taskManager.execute(Mockito.any())).thenThrow(
        WorkflowGeneralException.class);
    workflowSystemTaskHandler.execute(WorkflowTaskRequest.builder(),
        Mockito.mock(WorkerActionRequest.class));
  }
}
