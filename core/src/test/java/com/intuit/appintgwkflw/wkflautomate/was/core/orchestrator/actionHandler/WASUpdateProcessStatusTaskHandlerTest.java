package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.UPDATE_PROCESS_STATUS_FAILURE;
import static org.mockito.ArgumentMatchers.any;

import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.helper.DomainEventService;
import com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.impl.ProcessDomainEventHandler;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DomainEvent;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DomainEventHeaders;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.impl.ProcessDetailsRepoService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.EntityChangeAction;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ProcessStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventHeaderEntity;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import com.intuit.foundation.workflow.workflowautomation.Process;
import com.intuit.system.interfaces.BaseEntity;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

/**
 * <AUTHOR>
 */
public class WASUpdateProcessStatusTaskHandlerTest {

  @InjectMocks private WASUpdateProcessStatusTaskHandler processStatusTaskHandler;

  @Mock private ProcessDomainEventHandler processDomainEventHandler;

  @Mock private ProcessDetailsRepoService processDetailsRepoService;

  @Mock private DomainEventService domainEventService;
  @Mock private MetricLogger metricLogger;

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
    ReflectionTestUtils.setField(processStatusTaskHandler, "metricLogger", metricLogger);
  }

  @Test
  public void testUpdateStatusSuccess() {
    String processInstanceID = "iId";
    DomainEvent<BaseEntity> domainEvent = new DomainEvent<>();
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId(processInstanceID)
            .taskId("taskId")
            .inputVariables(new HashMap<>())
            .build();
    domainEvent.setHeaders(
        DomainEventHeaders.builder()
            .accountId("account-id")
            .offeringId("")
            .entityChangeAction(EntityChangeAction.UPDATE)
            .build());
    Mockito.when(processDetailsRepoService.findByProcessId(processInstanceID))
        .thenReturn(Optional.of(
                ProcessDetails.builder()
                        .processId("iTd")
                        .definitionDetails(
                                DefinitionDetails.builder()
                                        .definitionId("dId")
                                        .createdByUserId(1L)
                                        .build()
                        ).build()
        ));

    Mockito.when(domainEventService.isDomainEventPublishEnabled()).thenReturn(true);
    Mockito.when(domainEventService.prepareEntityHeaders(workerActionRequest))
        .thenReturn(EventHeaderEntity.builder().build());
    Mockito.when(processDetailsRepoService.updateStatusAndPublishDomainEvent(any(), any()))
        .thenReturn(1);
    Mockito.when(domainEventService.updateStatus(any(), any(), any(),any(), any())).thenReturn(1);

    Map<String, Object> response = processStatusTaskHandler.executeAction(workerActionRequest);
    Assert.assertNotNull(response);
    Assert.assertEquals(response.size(), 1);
    Assert.assertEquals(
        response.entrySet().stream().findFirst().get().getValue(), Boolean.TRUE.toString());
  }

  @Test
  public void testUpdateStatusSuccessWithoutDomainEvents() {
    String processInstanceID = "iId";
    DomainEvent<? extends BaseEntity> domainEvent = new DomainEvent<>();
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId(processInstanceID)
            .taskId("taskId")
            .inputVariables(new HashMap<>())
            .build();
    domainEvent.setHeaders(
        DomainEventHeaders.builder()
            .accountId("account-id")
            .offeringId("")
            .entityChangeAction(EntityChangeAction.UPDATE)
            .build());
    Mockito.when(processDetailsRepoService.findByProcessId(processInstanceID))
        .thenReturn(Optional.of(
                ProcessDetails.builder()
                        .processId("iTd")
                        .definitionDetails(
                                DefinitionDetails.builder()
                                        .definitionId("dId")
                                        .createdByUserId(1L)
                                        .build()
                        )
                        .build()
        ));
    Mockito.when(domainEventService.prepareEntityHeaders(workerActionRequest))
        .thenReturn(EventHeaderEntity.builder().build());

    Mockito.when(domainEventService.isDomainEventPublishEnabled()).thenReturn(false);
    Mockito.when(processDetailsRepoService.updateStatus(processInstanceID, ProcessStatus.ENDED))
        .thenReturn(1);

    Mockito.when(domainEventService.updateStatus(any(), any(), any(),any(), any())).thenReturn(1);
    Map<String, Object> response = processStatusTaskHandler.executeAction(workerActionRequest);
    Assert.assertNotNull(response);
    Assert.assertEquals(response.size(), 1);
    Assert.assertEquals(
        response.entrySet().stream().findFirst().get().getValue(), Boolean.TRUE.toString());
  }

  /** Added UT if processId is not present in WAS DB. (Orphan Process) */
  @Test
  public void testInvalidProcess() {
    String processInstanceID = "iId";
    DomainEvent<Process> domainEvent = new DomainEvent<>();
    domainEvent.setHeaders(
        DomainEventHeaders.builder()
            .accountId("account-id")
            .offeringId("")
            .entityChangeAction(EntityChangeAction.UPDATE)
            .build());
    Mockito.when(processDetailsRepoService.findByProcessId(processInstanceID))
        .thenReturn(Optional.ofNullable(null));

    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId(processInstanceID)
            .taskId("taskId")
            .inputVariables(new HashMap<>())
            .build();

    Map<String, Object> response = processStatusTaskHandler.executeAction(workerActionRequest);
    Assert.assertNotNull(response);
  }

  @Test
  public void testUpdateStatusFailure() {
    String processInstanceID = "iId";
    DomainEvent<Process> domainEvent = new DomainEvent<>();
    domainEvent.setHeaders(
        DomainEventHeaders.builder()
            .accountId("account-id")
            .offeringId("")
            .entityChangeAction(EntityChangeAction.UPDATE)
            .build());
    Mockito.when(processDetailsRepoService.findByProcessId(processInstanceID))
        .thenReturn(Optional.of(ProcessDetails.builder().processId("iTd").build()));
    Mockito.when(
        processDetailsRepoService.updateStatusAndPublishDomainEvent(
            ProcessDetails.builder().processId("iTd").build(), domainEvent))
        .thenReturn(0);
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId(processInstanceID)
            .taskId("taskId")
            .inputVariables(new HashMap<>())
            .build();

    try {
      processStatusTaskHandler.executeAction(workerActionRequest);
      Assert.fail();
    } catch (Exception e) {
      Assert.assertTrue(
          e.getMessage()
              .contains(String.format(UPDATE_PROCESS_STATUS_FAILURE.getErrorMessage(), "iId")));
    }
  }

  @Test
  public void testGetName() {
    Assert.assertEquals(
        "was_updateProcessStatus", processStatusTaskHandler.getName().getTaskHandlerName());
  }

  @Test
  public void testLogMetric() {
    String processInstanceID = "iId";
    DomainEvent<Process> domainEvent = new DomainEvent<>();
    domainEvent.setHeaders(
        DomainEventHeaders.builder()
            .accountId("account-id")
            .offeringId("")
            .entityChangeAction(EntityChangeAction.UPDATE)
            .build());
    Mockito.when(processDetailsRepoService.findByProcessId(processInstanceID))
        .thenReturn(Optional.of(ProcessDetails.builder().processId("iTd").build()));
    Mockito.when(
            processDetailsRepoService.updateStatusAndPublishDomainEvent(
                ProcessDetails.builder().processId("iTd").build(), domainEvent))
        .thenReturn(0);
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId(processInstanceID)
            .taskId("taskId")
            .inputVariables(new HashMap<>())
            .build();
    try {
      processStatusTaskHandler.execute(workerActionRequest);
      Assert.fail("Exception should be thrown");
    } catch (WorkflowGeneralException workflowGeneralException) {
      Mockito.verify(metricLogger, Mockito.times(1)).logErrorMetric(any(), any(), any());
    }
  }
}
