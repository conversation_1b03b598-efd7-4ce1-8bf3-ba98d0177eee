package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.capability;

import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler.TransactionEntityFactory;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler.TriggerHandlerTestData;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.AuthHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ActivityDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.BpmnComponentType;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter.TransactionEntity;
import org.javatuples.Pair;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import javax.persistence.NoResultException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

@RunWith(MockitoJUnitRunner.class)
public class DefinitionKeyQueryCapabilityTest {

    @InjectMocks
    private DefinitionKeyQueryCapability definitionKeyQueryCapability;

    @Mock
    private AuthHelper authHelper;

    @Mock
    private DefinitionDetailsRepository definitionDetailsRepository;

    @Mock
    private WASContextHandler contextHandler;

    @Mock
    private ActivityDetailsRepository activityDetailsRepository;

    private TransactionEntity transactionEntity;

    private final TemplateDetails bpmnTemplateDetail = TriggerHandlerTestData.getBPMNTemplateDetails();

    private final DefinitionDetails definitionDetails = TriggerHandlerTestData.getDefinitionDetails(bpmnTemplateDetail);

    private static final String ACTIVITY_ATTRIBUTES = "{\"modelAttributes\": {\"stepDetails\": \"{\\\"startEvent\\\":[\\\"startEvent\\\",\\\"Account-Manager\\\"] }\", \"taskDetails\": \"{ \\\"required\\\": true }\", \"activityName\": \"startEvent\", \"handlerDetails\": \"{\\\"taskHandler\\\":\\\"was\\\",\\\"recordType\\\":\\\"test\\\"}\", \"startableEvents\": \"[\\\"created\\\", \\\"updated\\\"]\", \"currentStepDetails\": \"{ \\\"required\\\": true }\", \"processVariablesDetails\": \"[{\\\"variableName\\\":\\\"userId\\\",\\\"variableType\\\":\\\"String\\\"},{\\\"variableName\\\":\\\"assigneeId\\\",\\\"variableType\\\":\\\"String\\\"}]\", \"ignoreProcessVariablesDetails\": \"true\"}}";

    @Before
    public void init() {
        Mockito.when(authHelper.getOwnerId()).thenReturn("1234");
        transactionEntity =
                TransactionEntityFactory.getInstanceOf(
                        TriggerHandlerTestData.prepareV3TriggerMessage("test").getTriggerMessage(),
                        contextHandler);
        transactionEntity.getEventHeaders().setDefinitionKey("def_key");
        definitionDetails.setDefinitionData(new byte[]{1, 2, 3});
    }

    @Test
    public void test_getTemplateDetails() {
        Mockito.when(definitionDetailsRepository.findEnabledDefinitionForDefinitionKey(
                1234L, "def_key")).thenReturn(definitionDetails);

        List<TemplateDetails> result = definitionKeyQueryCapability.getTemplateDetails(transactionEntity);
        Assert.assertEquals(1, result.size());
    }

    @Test
    public void test_getTemplateDetails_EmptyList() {
        Mockito.when(definitionDetailsRepository.findEnabledDefinitionForDefinitionKey(
                1234L, "def_key")).thenReturn(null);

        List<TemplateDetails> result = definitionKeyQueryCapability.getTemplateDetails(transactionEntity);
        Assert.assertEquals(0, result.size());
    }

    @Test
    public void test_getTemplateDetails_NoResultError() {
        Mockito.when(definitionDetailsRepository.findEnabledDefinitionForDefinitionKey(
                1234L, "def_key")).thenThrow(NoResultException.class);

        WorkflowGeneralException workflowGeneralException =
                Assertions.assertThrows(WorkflowGeneralException.class,
                        () -> definitionKeyQueryCapability.getTemplateDetails(transactionEntity));
        Assert.assertEquals(WorkflowError.ENABLED_DEFINITION_NOT_FOUND, workflowGeneralException.getWorkflowError());
    }

    @Test
    public void test_getTemplateData() {
        Mockito.when(definitionDetailsRepository.findEnabledDefinitionForDefinitionKey(
                1234L, "def_key")).thenReturn(definitionDetails);
        Assert.assertEquals(definitionDetails.getDefinitionData(), definitionKeyQueryCapability.getTemplateData(transactionEntity));
    }

    @Test
    public void test_getTemplateData_DefinitionDataNull() {
        Mockito.when(definitionDetailsRepository.findEnabledDefinitionForDefinitionKey(
                1234L, "def_key")).thenReturn(definitionDetails);
        definitionDetails.setDefinitionData(null);
        definitionDetails.getTemplateDetails().setTemplateData(new byte[]{0, 1, 2});
        Assert.assertEquals(definitionDetails.getTemplateDetails().getTemplateData(), definitionKeyQueryCapability.getTemplateData(transactionEntity));
    }

    @Test
    public void test_getDmnTemplateDetails() {
        definitionDetails.setDefinitionId("id");
        definitionDetails.setDefinitionKey("def_key");
        Mockito.when(definitionDetailsRepository.findByParentId("id"))
                .thenReturn(Optional.of(Collections.singletonList(definitionDetails)));
        Pair<String, byte[]> result = definitionKeyQueryCapability.getDmnTemplateDetails("def_key", Collections.singletonList(definitionDetails));
        Assert.assertEquals(definitionDetails.getTemplateDetails().getTemplateName(), result.getValue0());
        Assert.assertEquals(definitionDetails.getDefinitionData(), result.getValue1());

    }

    @Test
    public void test_getDmnTemplateDetails_emptyList() {
        definitionDetails.setDefinitionId("id");
        definitionDetails.setDefinitionKey("def_key");
        Mockito.when(definitionDetailsRepository.findByParentId("id"))
                .thenReturn(Optional.of(Collections.emptyList()));
        WorkflowGeneralException workflowGeneralException =
                Assertions.assertThrows(WorkflowGeneralException.class,
                        () -> definitionKeyQueryCapability.getDmnTemplateDetails("def_key", Collections.singletonList(definitionDetails)));
        Assert.assertEquals(WorkflowError.DMN_NOT_FOUND_ERROR, workflowGeneralException.getWorkflowError());
    }

    @Test
    public void test_getDmnTemplateDetails_OptionalEmptyResponse() {
        definitionDetails.setDefinitionId("id");
        definitionDetails.setDefinitionKey("def_key");
        WorkflowGeneralException workflowGeneralException =
                Assertions.assertThrows(WorkflowGeneralException.class,
                        () -> definitionKeyQueryCapability.getDmnTemplateDetails("def_key_empty", Collections.singletonList(definitionDetails)));
        Assert.assertEquals(WorkflowError.DMN_NOT_FOUND_ERROR, workflowGeneralException.getWorkflowError());

    }

    @Test
    public void test_getEnabledDefinitions() {

        Mockito.when(definitionDetailsRepository.findDefinitionsByDefinitionKey("def_key", 1234L, false))
                .thenReturn(Collections.singletonList(definitionDetails));
        List<DefinitionDetails> result = definitionKeyQueryCapability.getEnabledDefinitions(transactionEntity, false);
        Assert.assertEquals(1, result.size());
        Assert.assertEquals(definitionDetails.getDefinitionId(), result.get(0).getDefinitionId());
    }

    @Test
    public void testFetchInitialStartEventActivityDetail() {

        ActivityDetail activityDetail = new ActivityDetail();
        activityDetail.setAttributes(ACTIVITY_ATTRIBUTES);
        activityDetail.setActivityType(BpmnComponentType.START_EVENT.name());
        List<ActivityDetail> activityDetails = Collections.singletonList(activityDetail);


        Mockito.when(definitionDetailsRepository.findDefinitionsWithCustomFilterAndSelection(ArgumentMatchers.anyMap(),
                        ArgumentMatchers.anyList()))
                .thenReturn(Optional.of(Collections.singletonList(definitionDetails)));

        Mockito.when(activityDetailsRepository.findByTemplateIdAndActivityType(ArgumentMatchers.anyString(), ArgumentMatchers.anyString()))
                .thenReturn(activityDetails);

        // Act
        ActivityDetail activityDetail1 = definitionKeyQueryCapability
                .fetchInitialStartEventActivityDetail(transactionEntity);

        // Assert
        Assert.assertEquals(activityDetail, activityDetail1);

        Mockito.verify(authHelper).getOwnerId();
        Mockito.verify(activityDetailsRepository).findByTemplateIdAndActivityType(ArgumentMatchers.anyString(), ArgumentMatchers.anyString());
        Mockito.verify(definitionDetailsRepository).findDefinitionsWithCustomFilterAndSelection(ArgumentMatchers.anyMap(),
                        ArgumentMatchers.anyList());

    }

    @Test
    public void testFetchInitialStartEventActivityDetailWithNoTemplateId() {

        Mockito.when(definitionDetailsRepository.findDefinitionsWithCustomFilterAndSelection(ArgumentMatchers.anyMap(),
                        ArgumentMatchers.anyList()))
                .thenReturn(Optional.empty());

        // Act
        Assert.assertThrows(WorkflowGeneralException.class, () -> definitionKeyQueryCapability
                .fetchInitialStartEventActivityDetail(transactionEntity));


        Mockito.verify(authHelper, Mockito.times(1)).getOwnerId();
        Mockito.verify(definitionDetailsRepository).findDefinitionsWithCustomFilterAndSelection(ArgumentMatchers.anyMap(),
                ArgumentMatchers.anyList());
    }

}
