package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

public class TriggerHandlersTest {

  private static final String QBO_APP_ID = "Intuit.appintgwkflw.wkflautomate.qbowasapiclient";

  @Mock private DefaultTriggerHandler defaultTriggerHandler;

  @Before
  public void init() {

    MockitoAnnotations.initMocks(this);
    Mockito.when(defaultTriggerHandler.getName()).thenReturn(QBO_APP_ID);
    TriggerHandlers.addHandler(defaultTriggerHandler.getName(), defaultTriggerHandler);
  }

  @Test
  public void getTestNUll() {
    TriggerHandler handler = TriggerHandlers.getHandler(null);
    Assert.assertNull(handler);
  }

  @Test
  public void getTestRuleHandler() {
    TriggerHandler handler = TriggerHandlers.getHandler(QBO_APP_ID);
    Assert.assertNotNull(handler);
  }
  @Test
  public void containsFalse() {
    Assert.assertFalse(TriggerHandlers.contains(null));
  }
  @Test
  public void containsTrue() {
    Assert.assertTrue(TriggerHandlers.contains(QBO_APP_ID));
  }


}


