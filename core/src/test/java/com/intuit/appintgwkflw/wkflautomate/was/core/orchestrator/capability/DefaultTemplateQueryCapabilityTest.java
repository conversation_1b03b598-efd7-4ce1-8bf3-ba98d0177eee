package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.capability;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.doReturn;

import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.TemplateService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler.TransactionEntityFactory;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler.TriggerHandlerTestData;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.AuthHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ActivityDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.TemplateDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.BpmnComponentType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ModelType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter.TransactionEntity;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import org.javatuples.Pair;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DefaultTemplateQueryCapabilityTest {

  private static final String UPDATED_EVENT = "updated";

  private static final String REALM_ID = "12345";

  private static final String TEMPLATE_NAME = "invoiceapproval";

  private final TemplateDetails bpmnTemplateDetail = TriggerHandlerTestData.getBPMNTemplateDetails();

  private static final String ACTIVITY_ATTRIBUTES = "{\"modelAttributes\": {\"stepDetails\": \"{\\\"startEvent\\\":[\\\"startEvent\\\",\\\"Account-Manager\\\"] }\", \"taskDetails\": \"{ \\\"required\\\": true }\", \"activityName\": \"startEvent\", \"handlerDetails\": \"{\\\"taskHandler\\\":\\\"was\\\",\\\"recordType\\\":\\\"test\\\"}\", \"startableEvents\": \"[\\\"created\\\", \\\"updated\\\"]\", \"currentStepDetails\": \"{ \\\"required\\\": true }\", \"processVariablesDetails\": \"[{\\\"variableName\\\":\\\"userId\\\",\\\"variableType\\\":\\\"String\\\"},{\\\"variableName\\\":\\\"assigneeId\\\",\\\"variableType\\\":\\\"String\\\"}]\", \"ignoreProcessVariablesDetails\": \"true\"}}";

  private static final String ACTIVITY_ATTRIBUTES_WITHOUT_STARTABLE_EVENTS = "{\"modelAttributes\": {\"stepDetails\": \"{\\\"startEvent\\\":[\\\"startEvent\\\",\\\"Account-Manager\\\"] }\", \"taskDetails\": \"{ \\\"required\\\": true }\", \"activityName\": \"startEvent\", \"handlerDetails\": \"{\\\"taskHandler\\\":\\\"was\\\",\\\"recordType\\\":\\\"test\\\"}\", \"currentStepDetails\": \"{ \\\"required\\\": true }\", \"processVariablesDetails\": \"[{\\\"variableName\\\":\\\"userId\\\",\\\"variableType\\\":\\\"String\\\"},{\\\"variableName\\\":\\\"assigneeId\\\",\\\"variableType\\\":\\\"String\\\"}]\", \"ignoreProcessVariablesDetails\": \"true\"}}";

  @Mock
  private DefinitionDetailsRepository definitionDetailsRepository;

  @Mock
  private AuthHelper authHelper;

  private TransactionEntity transactionEntityUpdated;

  @Mock
  private TemplateDetailsRepository templateDetailsRepository;

  @Mock
  private TemplateService templateService;

  @Mock
  private WASContextHandler contextHandler;

  @Mock
  private ActivityDetailsRepository activityDetailsRepository;

  @InjectMocks
  private DefaultTemplateQueryCapability defaultTemplateQueryCapability;

  @Before
  public void prepareMockData() {

    transactionEntityUpdated =
            TransactionEntityFactory.getInstanceOf(TriggerHandlerTestData.prepareV3TriggerMessage(UPDATED_EVENT).getTriggerMessage(),
            contextHandler);
    Mockito.when(authHelper.getOwnerId()).thenReturn("12345");
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testGetEnabledEligibleDefinitionsListNoWorkflowDefinition() {

    Mockito.when(
            definitionDetailsRepository.findAllEnabledDefinitionsForOwnerIdAndTemplateName(
                Long.parseLong(REALM_ID), ModelType.BPMN, "invoiceapproval", false))
        .thenReturn(Optional.empty());

    List<DefinitionDetails> definitionDetailsList =defaultTemplateQueryCapability
            .getEnabledEligibleDefinitionsList(REALM_ID, transactionEntityUpdated, false);
  }

  @Test
  public void testGetEnabledEligibleDefinitionsList() {

    bpmnTemplateDetail.setTemplateName(TEMPLATE_NAME);
    final DefinitionDetails definitionDetails =
        TriggerHandlerTestData.getDefinitionDetails(bpmnTemplateDetail);
    final List<DefinitionDetails> definitionDetailsList =
        Collections.singletonList(definitionDetails);
    Mockito.when(
            definitionDetailsRepository.findAllEnabledDefinitionsForOwnerIdAndTemplateName(
                Long.parseLong(REALM_ID), ModelType.BPMN, "invoiceapproval", false))
        .thenReturn(Optional.of(definitionDetailsList));
    List<DefinitionDetails> definitionDetailLists =
        defaultTemplateQueryCapability.getEnabledEligibleDefinitionsList(
            REALM_ID, transactionEntityUpdated, false);
    Assert.assertTrue(definitionDetailLists.size() > 0);
  }

  @Test
  public void testTemplateDetail() {
    final List<TemplateDetails> templateDetailsList = new ArrayList<>();
    final TemplateDetails templateDetails = new TemplateDetails();
    templateDetails.setTemplateName("invoiceapproval");
    templateDetailsList.add(templateDetails);
    Mockito.when(
            templateDetailsRepository.findTemplateDetailsExceptTemplateData(
                Mockito.any(), Mockito.any(), Mockito.any()))
        .thenReturn(Optional.of(templateDetailsList));
    assertNotNull(defaultTemplateQueryCapability.getTemplateDetails(RecordType.INVOICE, "approval"));
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testGetEnabledEligibleDefinitionsListWithException() {
    Mockito.when(
                    definitionDetailsRepository.findAllEnabledDefinitionsForOwnerIdAndTemplateName(
                            Long.parseLong(REALM_ID), ModelType.BPMN, "invoiceapproval", false))
            .thenReturn(Optional.empty());
    List<DefinitionDetails> definitionDetailsList = defaultTemplateQueryCapability.getEnabledEligibleDefinitionsList(
            REALM_ID, transactionEntityUpdated, false);
  }

  @Test
  public void getTemplateDetailsTestSuccess () {
    transactionEntityUpdated =
            TransactionEntityFactory.getInstanceOf(
                    TriggerHandlerTestData.prepareV3TriggerMessage("created").getTriggerMessage(),
            contextHandler);
    doReturn(Optional.ofNullable(Collections.singletonList(bpmnTemplateDetail)))
        .when(templateDetailsRepository)
        .findTemplateDetailsExceptTemplateData(Mockito.any(), Mockito.any(), Mockito.any());

    List<TemplateDetails> templateDetailsList =
        defaultTemplateQueryCapability.getTemplateDetails(transactionEntityUpdated);

    assertEquals(1, templateDetailsList.size());
  }

  @Test
  public void getTemplateDataTestSuccess () {
    transactionEntityUpdated =
            TransactionEntityFactory.getInstanceOf(
                    TriggerHandlerTestData.prepareV3TriggerMessage("created").getTriggerMessage(),
            contextHandler);
    bpmnTemplateDetail.setTemplateName("invoiceapproval");
    doReturn(bpmnTemplateDetail)
        .when(templateService)
        .getTemplateByName(Mockito.any());
    byte[] templateData =
        defaultTemplateQueryCapability.getTemplateData(transactionEntityUpdated);
    assertTrue(templateData.length > 0);
  }

  @Test
  public void getTemplateDataForOnDemand () {
    transactionEntityUpdated =
            TransactionEntityFactory.getInstanceOf(
                    TriggerHandlerTestData.prepareV3TriggerMessage("created", RecordType.INVOICE.toString()).getTriggerMessage(),
                    contextHandler);
    bpmnTemplateDetail.setTemplateName("invoiceapproval");
    doReturn(bpmnTemplateDetail)
            .when(templateService)
            .getTemplateByName(WorkflowConstants.CUSTOM_APPROVAL_TEMPLATE);
    transactionEntityUpdated.getV3EntityPayload().put(WorkflowConstants.ON_DEMAND_APPROVAL,true);
    byte[] templateData =
            defaultTemplateQueryCapability.getTemplateData(transactionEntityUpdated);
    assertTrue(templateData.length > 0);
  }

  @Test
  public void getDmnTemplateDetailsSuccess () {
    transactionEntityUpdated =
            TransactionEntityFactory.getInstanceOf(
                    TriggerHandlerTestData.prepareV3TriggerMessage("created").getTriggerMessage(),
            contextHandler);
    DefinitionDetails definitionDetails = TriggerHandlerTestData.getDefinitionDetails(bpmnTemplateDetail);
    definitionDetails.setDefinitionData(definitionDetails.getTemplateDetails().getTemplateData());
    bpmnTemplateDetail.setTemplateName("invoiceapproval");

    Mockito.when(templateDetailsRepository.findTopByParentIdOrderByVersionDesc(Mockito.any())).thenReturn(Optional.ofNullable(bpmnTemplateDetail));
    Pair<String, byte[]> templateDetails =
        defaultTemplateQueryCapability
            .getDmnTemplateDetails(transactionEntityUpdated.getEventHeaders().getProviderWorkflowId(), Collections.singletonList(definitionDetails));


    assertEquals("invoiceapproval",templateDetails.getValue0());
    assertTrue(templateDetails.getValue1().length > 0);
  }

  @Test
  public void getEnabledDefinitionsSuccess () {
    transactionEntityUpdated =
            TransactionEntityFactory.getInstanceOf(
                    TriggerHandlerTestData.prepareV3TriggerMessage("created").getTriggerMessage(),
            contextHandler);
    DefinitionDetails definitionDetails = TriggerHandlerTestData.getDefinitionDetails(bpmnTemplateDetail);
    definitionDetails.setDefinitionData(definitionDetails.getTemplateDetails().getTemplateData());
    definitionDetails.getTemplateDetails().setTemplateName("invoiceapproval");
    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();
    definitionDetailsList.add(definitionDetails);
    doReturn(Optional.ofNullable(definitionDetailsList))
        .when(definitionDetailsRepository)
        .findAllEnabledDefinitionsForOwnerIdAndTemplateName(anyLong(), Mockito.any(), Mockito.any(), Mockito.anyBoolean());

    List<DefinitionDetails> defList =
        defaultTemplateQueryCapability.getEnabledDefinitions(transactionEntityUpdated, true);

    assertEquals(1, defList.size());
  }

  @Test
  public void testFetchInitialStartEventActivityDetail_OnDemandApproval() {
    List<ActivityDetail> activityDetailList = new ArrayList<>();
    ActivityDetail activityDetail = new ActivityDetail();
    activityDetail.setId(1L);
    activityDetail.setActivityId("startEvent");
    activityDetail.setActivityType(BpmnComponentType.START_EVENT.getName());
    activityDetail.setAttributes(ACTIVITY_ATTRIBUTES);
    ActivityDetail activityDetail1 = new ActivityDetail();
    activityDetail1.setId(2L);
    activityDetail1.setActivityId("startEvent");
    activityDetail1.setActivityType(BpmnComponentType.START_EVENT.getName());
    activityDetail1.setAttributes(ACTIVITY_ATTRIBUTES_WITHOUT_STARTABLE_EVENTS);

    activityDetailList.add(activityDetail);
    activityDetailList.add(activityDetail1);

    Optional<String> templateId = Optional.of(bpmnTemplateDetail.getId());
    Mockito.when(templateDetailsRepository.findTemplateIdForTopByTemplateNameOrderByVersionDesc
    (ArgumentMatchers.anyString(), ArgumentMatchers.any(), ArgumentMatchers.any())).thenReturn(templateId);
    Mockito.when(activityDetailsRepository.findByTemplateIdAndActivityType(bpmnTemplateDetail.getId(), BpmnComponentType.START_EVENT.getName()))
            .thenReturn(activityDetailList);

    ActivityDetail result = defaultTemplateQueryCapability
            .fetchInitialStartEventActivityDetail(transactionEntityUpdated);

    Mockito.verify(templateDetailsRepository).findTemplateIdForTopByTemplateNameOrderByVersionDesc(ArgumentMatchers.anyString(), ArgumentMatchers.any(), ArgumentMatchers.any());
    Mockito.verify(activityDetailsRepository).findByTemplateIdAndActivityType(bpmnTemplateDetail.getId(), BpmnComponentType.START_EVENT.getName());
    Assert.assertEquals(activityDetail, result);
  }

  @Test
  public void testFetchInitialStartEventActivityDetailWithoutTemplateId() {
    Optional<String> templateId = Optional.empty();
    Mockito.when(templateDetailsRepository.findTemplateIdForTopByTemplateNameOrderByVersionDesc
            (ArgumentMatchers.anyString(), ArgumentMatchers.any(), ArgumentMatchers.any())).thenReturn(templateId);

    Assert.assertThrows(WorkflowGeneralException.class, () -> defaultTemplateQueryCapability
            .fetchInitialStartEventActivityDetail(transactionEntityUpdated));
  }

  @Test
  public void testFetchInitialStartEventActivityDetail_RegularTemplate() {
    transactionEntityUpdated.getV3EntityPayload().put(WorkflowConstants.ON_DEMAND_APPROVAL, false);

    List<ActivityDetail> activityDetailList = new ArrayList<>();
    ActivityDetail activityDetail = new ActivityDetail();
    activityDetail.setId(1L);
    activityDetail.setActivityId("startEvent");
    activityDetail.setActivityType(BpmnComponentType.START_EVENT.getName());
    activityDetail.setAttributes(ACTIVITY_ATTRIBUTES);
    ActivityDetail activityDetail1 = new ActivityDetail();
    activityDetail1.setId(2L);
    activityDetail1.setActivityId("startEvent");
    activityDetail1.setActivityType(BpmnComponentType.START_EVENT.getName());
    activityDetail1.setAttributes(ACTIVITY_ATTRIBUTES_WITHOUT_STARTABLE_EVENTS);

    activityDetailList.add(activityDetail);
    activityDetailList.add(activityDetail1);

    Mockito.when(templateDetailsRepository.findTemplateIdForTopByTemplateNameOrderByVersionDesc
            (ArgumentMatchers.anyString(), ArgumentMatchers.any(), ArgumentMatchers.any())).thenReturn(Optional.ofNullable(bpmnTemplateDetail.getId()));
    Mockito.when(activityDetailsRepository.findByTemplateIdAndActivityType(bpmnTemplateDetail.getId(), BpmnComponentType.START_EVENT.getName()))
            .thenReturn(activityDetailList);

    ActivityDetail result = defaultTemplateQueryCapability
            .fetchInitialStartEventActivityDetail(transactionEntityUpdated);

    Mockito.verify(activityDetailsRepository).findByTemplateIdAndActivityType(bpmnTemplateDetail.getId(), BpmnComponentType.START_EVENT.getName());
    Assert.assertEquals(activityDetail, result);
  }

}
