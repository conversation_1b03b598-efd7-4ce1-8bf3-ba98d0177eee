package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.eventHandler;

import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.taskcompletionhandler.ServiceTaskCompletionHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.taskcompletionhandler.TaskCompletionHandlers;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.EventHeaderConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.WorkflowMetaData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.externaltask.ExternalTaskAssigned;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.event.api.EventPublisherCapability;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@RunWith(MockitoJUnitRunner.class)
public class ServiceTaskEventHandlerTest {

  @Mock
  private ProcessDetailsRepository processDetailsRepository;
  @Mock
  private EventPublisherCapability eventPublisherCapability;
  @Mock private MetricLogger metricsLogger;

  private ServiceTaskEventHandler serviceTaskEventHandler;

  private Optional<ProcessDetails> processDetails;

  @Mock
  ServiceTaskCompletionHandler taskCompletionHandlerMock;

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
    serviceTaskEventHandler =
        new ServiceTaskEventHandler(processDetailsRepository, eventPublisherCapability, metricsLogger);
    TemplateDetails templateDetails = TemplateDetails.builder()
        .templateName("templateName")
        .build();

    DefinitionDetails definitionDetails = DefinitionDetails.builder()
        .recordType(RecordType.INVOICE)
        .templateDetails(templateDetails)
        .build();
    processDetails = Optional.of(
        ProcessDetails.builder()
            .definitionDetails(definitionDetails)
            .processId("p11")
            .ownerId(123L)
            .recordId("rId")
            .build());
    TaskCompletionHandlers.addHandler(EventEntityType.SERVICE_TASK, taskCompletionHandlerMock);
  }

  @Test
  public void testTransformEvent() throws Exception {
    ExternalTaskAssigned serviceTaskEvent = getServiceTaskPayload();
    ExternalTaskAssigned event = serviceTaskEventHandler.transform(ObjectConverter.toJson(serviceTaskEvent));

    Assert.assertNotNull(event);
    Assert.assertNotNull(event.getWorkflowMetadata());
    Assert.assertEquals("p11", event.getWorkflowMetadata().getProcessInstanceId());
    Assert.assertEquals("owww1", event.getWorkflowMetadata().getWorkflowOwnerId());
    Assert.assertEquals("abc", event.getWorkflowMetadata().getWorkflowName());
    Assert.assertEquals("a11", event.getTaskName());

    Assert.assertEquals("eng1", event.getBusinessEntityId());
    Assert.assertEquals("engagement", event.getBusinessEntityType());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testTransformNullEvent() {
    serviceTaskEventHandler.transform(null);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testTransformIncorrectEvent() throws Exception {
    serviceTaskEventHandler.transform("hello");
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testTransformMissingFieldEvent() throws Exception {
    serviceTaskEventHandler.transform(ObjectConverter.toJson(ExternalTaskAssigned.builder().taskName("a11").build()));
  }

  @Test
  public void testExecuteValidCase() {
    ExternalTaskAssigned event = getServiceTaskPayload();
    Mockito.when(processDetailsRepository.findById(event.getWorkflowMetadata().getProcessInstanceId()))
        .thenReturn(processDetails);
    serviceTaskEventHandler.execute(event, getEventHeaders());

    Mockito.verify(eventPublisherCapability, Mockito.times(1)).publish(Mockito.any(), Mockito.any());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testValidateMissingEntityIdCase() {
    serviceTaskEventHandler.transformAndValidate("hello", new HashMap<>());

    Mockito.verify(eventPublisherCapability, Mockito.times(0)).publish(Mockito.any(), Mockito.any());
    Mockito.verify(taskCompletionHandlerMock, Mockito.times(1))
            .handleFailure(Mockito.any(), Mockito.any());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testExecuteNullDBObject() throws Exception {
    Mockito.when(processDetailsRepository.findById("p11"))
        .thenReturn(Optional.ofNullable(null));

    ExternalTaskAssigned event = getServiceTaskPayload();
    serviceTaskEventHandler.execute(event, getEventHeaders());

    Mockito.verify(eventPublisherCapability, Mockito.times(1)).publish(Mockito.any(), Mockito.any());
  }

  @Test
  public void testGetName() {
    EventEntityType eventEntityType = serviceTaskEventHandler.getName();

    Assert.assertEquals(eventEntityType.getEntityType(), EventEntityType.SERVICE_TASK.getEntityType());
  }

  private ExternalTaskAssigned getServiceTaskPayload() {
    WorkflowMetaData workflowMetaData = WorkflowMetaData.builder()
        .workflowName("abc")
        .processInstanceId("p11")
        .workflowOwnerId("owww1")
        .build();
    Map<String, Object> variables = new HashMap<>();
    HandlerDetails handlerDetails = new HandlerDetails();
    handlerDetails.setHandlerScope("test");
    handlerDetails.setHandlerId("id");
    variables.put(WorkFlowVariables.HANDLER_DETAILS_KEY.getName(), ObjectConverter.toJson(handlerDetails));
    return ExternalTaskAssigned.builder().taskName("a11")
            .businessEntityId("eng1")
            .businessEntityType("engagement")
            .workflowMetadata(workflowMetaData)
            .variables(variables)
            .build();
  }

  private Map<String, String> getEventHeaders() {
    Map<String, String> headers = new HashMap<>();
    headers.put(EventHeaderConstants.ENTITY_ID, "123");
    return headers;
  }
}
