package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.publisher;

import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.executor.actions.CustomReminderScheduleActionProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.executor.actions.SchedulingReminderProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.executor.actions.SchedulingScheduledActionsProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.executor.actions.factory.WorkflowScheduleActionProcessorFactory;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.SchedulerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.WorkflowNameEnum;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.history.TriggerNowRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.listner.EventScheduleMessageData;
import com.intuit.async.execution.request.State;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.*;
import java.util.*;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants.TRIGGER_NOW_ASYNC_EXCEPTION_KEY;
import static org.junit.Assert.*;

public class SchedulingRunNowTaskTest {

    @Mock
    private WASContextHandler wasContextHandler;

    @Mock
    private MetricLogger metricLogger;

    private SchedulingRunNowTask schedulingRunNowTask;

    @Mock
    private CustomReminderScheduleActionProcessor customReminderScheduleProcessor;

    @Mock
    private SchedulingScheduledActionsProcessor schedulingScheduledActionsProcessor;

    @Mock
    private SchedulingReminderProcessor schedulingReminderProcessor;

    private State state;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        schedulingRunNowTask = new SchedulingRunNowTask(wasContextHandler, metricLogger);

        WorkflowScheduleActionProcessorFactory.addProcessor(
                WorkflowNameEnum.CUSTOM_REMINDER, customReminderScheduleProcessor);
        WorkflowScheduleActionProcessorFactory.addProcessor(
                WorkflowNameEnum.SCHEDULED_ACTIONS, schedulingScheduledActionsProcessor);
        WorkflowScheduleActionProcessorFactory.addProcessor(
                WorkflowNameEnum.REMINDER, schedulingReminderProcessor);

        state = new State();
        TriggerNowRequest triggerNowRequest = new TriggerNowRequest("def123");
        state.addValue(AsyncTaskConstants.TRIGGER_NOW_REQUEST, triggerNowRequest);
        DefinitionDetails definitionDetails = new DefinitionDetails();
        definitionDetails.setDefinitionId("testId");
        state.addValue(AsyncTaskConstants.DEFINITION_DETAILS, definitionDetails);
        state.addValue(AsyncTaskConstants.WORKFLOW_NAME_KEY, "customReminder");
        state.addValue(AsyncTaskConstants.IS_SCHEDULING_FLOW_ENABLED, false);
        state.addValue(AsyncTaskConstants.EVENT_SCHEDULE_IDS, Arrays.asList(new SchedulerDetails("scheduler1", null, "def123")));
    }

    @Test
    public void testExecute_withSchedulingFlowEnabled() {
        // Arrange
        state.addValue(AsyncTaskConstants.IS_SCHEDULING_FLOW_ENABLED, true);
        state.addValue(AsyncTaskConstants.EVENT_SCHEDULE_IDS, Arrays.asList("schedule1", "schedule2"));
        Map<String, String> mockResponse = new HashMap<>();
        mockResponse.put("key", "value");
        Mockito.when(schedulingReminderProcessor.process((DefinitionDetails) Mockito.any(),Mockito.any())).thenReturn(mockResponse);

        // Act
        State resultState = schedulingRunNowTask.execute(state);

        // Assert
        assertNotNull(resultState);
        assertEquals(mockResponse, resultState.getValue(AsyncTaskConstants.TRIGGER_NOW_ASYNC_RESPONSE_KEY));
        Mockito.verify(schedulingReminderProcessor, Mockito.times(2)).process((DefinitionDetails) Mockito.any(),Mockito.any());

    }

    @Test
    public void testExecute_withESSFlow() {
        // Arrange
        // Mock the WorkflowScheduleActionProcessorFactory behavior
        Map<String, String> mockResponse = new HashMap<>();
        mockResponse.put("key", "value");
        Mockito.when(customReminderScheduleProcessor.process((SchedulerDetails) Mockito.any(),Mockito.any())).thenReturn(mockResponse);
        // Act
        State resultState = schedulingRunNowTask.execute(state);

        // Assert
        ArgumentCaptor<EventScheduleMessageData> captor = ArgumentCaptor.forClass(EventScheduleMessageData.class);
        Mockito.verify(customReminderScheduleProcessor, Mockito.times(1)).process((SchedulerDetails) Mockito.any(), captor.capture());
        EventScheduleMessageData capturedData = captor.getValue();
        Assert.assertNull(capturedData.getScope());
        assertNotNull(resultState);
        assertEquals(mockResponse, resultState.getValue(AsyncTaskConstants.TRIGGER_NOW_ASYNC_RESPONSE_KEY));
        Mockito.verify(customReminderScheduleProcessor, Mockito.times(1)).process((SchedulerDetails) Mockito.any(), captor.capture());
    }

    @Test
    public void testHandleRunNowForESSForTestScope() {
        // Arrange
        TriggerNowRequest triggerNowRequest = new TriggerNowRequest("def123", "test");
        state.addValue(AsyncTaskConstants.TRIGGER_NOW_REQUEST, triggerNowRequest);
        // Mock the WorkflowScheduleActionProcessorFactory behavior
        Map<String, String> mockResponse = new HashMap<>();
        mockResponse.put("key", "value");
        Mockito.when(customReminderScheduleProcessor.process((SchedulerDetails) Mockito.any(),Mockito.any())).thenReturn(mockResponse);
        // Act
        State resultState = schedulingRunNowTask.execute(state);

        // Assert
        ArgumentCaptor<EventScheduleMessageData> captor = ArgumentCaptor.forClass(EventScheduleMessageData.class);
        Mockito.verify(customReminderScheduleProcessor, Mockito.times(1)).process((SchedulerDetails) Mockito.any(), captor.capture());
        EventScheduleMessageData capturedData = captor.getValue();
        Assert.assertEquals("test", capturedData.getScope());
        assertNotNull(resultState);
        assertEquals(mockResponse, resultState.getValue(AsyncTaskConstants.TRIGGER_NOW_ASYNC_RESPONSE_KEY));
        Mockito.verify(customReminderScheduleProcessor, Mockito.times(1)).process((SchedulerDetails) Mockito.any(), captor.capture());
    }

    @Test
    public void testRunNowWorkflowNameNotSupported() {

        state.addValue(AsyncTaskConstants.WORKFLOW_NAME_KEY, "NA");
        State resultState = schedulingRunNowTask.execute(state);
        Exception exception = state.getValue(TRIGGER_NOW_ASYNC_EXCEPTION_KEY);
        Assert.assertEquals(UnsupportedOperationException.class, exception.getClass());
    }


}