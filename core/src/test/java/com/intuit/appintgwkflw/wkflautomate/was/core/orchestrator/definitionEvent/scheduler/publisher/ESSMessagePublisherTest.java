package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.publisher;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;

import com.amazonaws.services.sqs.AmazonSQS;
import com.amazonaws.services.sqs.model.SendMessageResult;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.SQSConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowEventException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.executor.message.ScheduleMessageExecutor;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.processor.ESSMessageProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.SchedulerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.SchedulerDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.SchedulerAction;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.cloud.aws.messaging.listener.Acknowledgment;
import org.springframework.test.util.ReflectionTestUtils;

/**
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class ESSMessagePublisherTest {

  @InjectMocks
  private ESSMessagePublisher essMessagePublisher;

  @Mock
  DefinitionDetailsRepository definitionDetailsRepository;

  @Mock
  SchedulerDetailsRepository schedulerDetailsRepository;

  @Mock
  WASContextHandler wasContextHandler;

  @Mock
  AmazonSQS amazonSQS;

  private final String DEFINITION_ID = "defId";
  private final Long OWNER_ID = 123L;
  private final DefinitionDetails definitionDetails = DefinitionDetails.builder()
      .definitionId(DEFINITION_ID)
      .build();

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
    ReflectionTestUtils.setField(
        essMessagePublisher, "definitionDetailsRepository", definitionDetailsRepository);
    ReflectionTestUtils.setField(essMessagePublisher, "schedulerDetailsRepository",
        schedulerDetailsRepository);
    ReflectionTestUtils.setField(essMessagePublisher, "wasContextHandler",
        wasContextHandler);
    ReflectionTestUtils.setField(essMessagePublisher, "sqsQueueUrl", "sqsUrl");
    ReflectionTestUtils.setField(essMessagePublisher, "amazonSQS", amazonSQS);
    Mockito.when(wasContextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("" + OWNER_ID);
  }

  @Test
  public void testPublish() {
    Mockito.when(definitionDetailsRepository.findByDefinitionIdAndOwnerId(DEFINITION_ID, OWNER_ID))
        .thenReturn(
            Optional.of(definitionDetails));
    Mockito.when(schedulerDetailsRepository.findByDefinitionDetails(definitionDetails))
        .thenReturn(Optional.of(
            List.of(SchedulerDetails.builder().schedulerId("sched123")
                .definitionDetails(definitionDetails).schedulerAction(
                    SchedulerAction.CUSTOM_REMINDER_CUSTOM_START).build())));
    Mockito.when(amazonSQS.sendMessage(Mockito.any())).thenReturn(new SendMessageResult());
    essMessagePublisher.publish(DEFINITION_ID);
    Mockito.verify(definitionDetailsRepository, Mockito.times(1))
        .findByDefinitionIdAndOwnerId(
            DEFINITION_ID, OWNER_ID);

    Mockito.verify(schedulerDetailsRepository, Mockito.times(1))
        .findByDefinitionDetails(
            definitionDetails);
    Mockito.verify(amazonSQS, Mockito.times(1)).sendMessage(Mockito.any());
  }

  @Test
  public void testPublishNoDefinitionFound() {
    Mockito.when(definitionDetailsRepository.findByDefinitionIdAndOwnerId(DEFINITION_ID, OWNER_ID))
        .thenReturn(
            Optional.empty());
    try {
      essMessagePublisher.publish(DEFINITION_ID);
      Assert.fail("Exception Should be thrown");
    } catch (WorkflowGeneralException workflowGeneralException) {
      Mockito.verify(definitionDetailsRepository, Mockito.times(1))
          .findByDefinitionIdAndOwnerId(
              DEFINITION_ID, OWNER_ID);
      Mockito.verify(schedulerDetailsRepository, Mockito.times(0))
          .findByDefinitionDetails(
              definitionDetails);
      Mockito.verify(amazonSQS, Mockito.times(0)).sendMessage(Mockito.any());
    }
  }


  @Test
  public void testPublishNoScheduleDetails() {
    Mockito.when(definitionDetailsRepository.findByDefinitionIdAndOwnerId(DEFINITION_ID, OWNER_ID))
        .thenReturn(
            Optional.of(definitionDetails));
    Mockito.when(schedulerDetailsRepository.findByDefinitionDetails(definitionDetails))
        .thenReturn(Optional.empty());
    try {
      essMessagePublisher.publish(DEFINITION_ID);
      Assert.fail("Exception Should be thrown");
    } catch (WorkflowGeneralException workflowGeneralException) {
      Mockito.verify(definitionDetailsRepository, Mockito.times(1))
          .findByDefinitionIdAndOwnerId(
              DEFINITION_ID, OWNER_ID);
      Mockito.verify(schedulerDetailsRepository, Mockito.times(1))
          .findByDefinitionDetails(
              definitionDetails);
      Mockito.verify(amazonSQS, Mockito.times(0)).sendMessage(Mockito.any());
    }
  }
}

