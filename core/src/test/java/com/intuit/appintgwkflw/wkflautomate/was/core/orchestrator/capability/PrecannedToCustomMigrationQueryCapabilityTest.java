package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.capability;

import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler.TransactionEntityFactory;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler.TriggerHandlerTestData;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter.TransactionEntity;
import org.javatuples.Pair;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.Mockito.doReturn;

@RunWith(MockitoJUnitRunner.class)
public class PrecannedToCustomMigrationQueryCapabilityTest {

  private static final String UPDATED_EVENT = "updated";

  private static final String TEMPLATE_NAME = "invoiceapproval";

  private final TemplateDetails bpmnTemplateDetail = TriggerHandlerTestData.getBPMNTemplateDetails();

  @Mock
  private CustomWorkflowQueryCapability customWorkflowQueryCapability;

  @Mock
  private DefaultTemplateQueryCapability defaultTemplateQueryCapability;

  @InjectMocks
  private PrecannedToCustomMigrationQueryCapability precannedToCustomMigrationQueryCapability;

  @Mock
  private WASContextHandler contextHandler;

  private TransactionEntity transactionEntityUpdated;

  @Before
  public void prepareMockData() {

    transactionEntityUpdated =
    		TransactionEntityFactory.getInstanceOf(TriggerHandlerTestData.prepareV3TriggerMessage(UPDATED_EVENT).getTriggerMessage(),
            contextHandler);
  }

  @Test
  public void testTemplateDetail() {
    final List<TemplateDetails> templateDetailsList = new ArrayList<>();
    final TemplateDetails templateDetails = new TemplateDetails();
    templateDetails.setTemplateName("invoiceapproval");
    templateDetailsList.add(templateDetails);
    Mockito.when(
            customWorkflowQueryCapability.getTemplateDetails(transactionEntityUpdated)
    ).thenReturn(templateDetailsList);
    assertNotNull(precannedToCustomMigrationQueryCapability.getTemplateDetails(transactionEntityUpdated));
  }

  @Test
  public void getTemplateDetailsTestSuccess () {
    transactionEntityUpdated =
    		TransactionEntityFactory.getInstanceOf(
    				TriggerHandlerTestData.prepareV3TriggerMessage("created").getTriggerMessage(),
            contextHandler);
    doReturn(Collections.singletonList(bpmnTemplateDetail))
        .when(customWorkflowQueryCapability)
        .getTemplateDetails(Mockito.any());

    List<TemplateDetails> templateDetailsList =
        precannedToCustomMigrationQueryCapability.getTemplateDetails(transactionEntityUpdated);

    assertEquals(1, templateDetailsList.size());
  }

  @Test
  public void getTemplateDataTestSuccess () {
    transactionEntityUpdated =
    		TransactionEntityFactory.getInstanceOf(
    				TriggerHandlerTestData.prepareV3TriggerMessage("created").getTriggerMessage(),
            contextHandler);
    bpmnTemplateDetail.setTemplateName("invoiceapproval");
    doReturn(bpmnTemplateDetail.getTemplateData())
        .when(customWorkflowQueryCapability)
        .getTemplateData(Mockito.any());
    byte[] templateData =
        precannedToCustomMigrationQueryCapability.getTemplateData(transactionEntityUpdated);
    assertTrue(templateData.length > 0);
  }

  @Test
  public void getDmnTemplateDetailsSuccess () {
    transactionEntityUpdated =
    		TransactionEntityFactory.getInstanceOf(
    				TriggerHandlerTestData.prepareV3TriggerMessage("created").getTriggerMessage(),
            contextHandler);

    Mockito.when(
            customWorkflowQueryCapability.getDmnTemplateDetails(Mockito.any(), Mockito.any())
    ).thenReturn(Pair.with(TEMPLATE_NAME, bpmnTemplateDetail.getTemplateData()));

    Pair<String, byte[]> templateDetails =
        precannedToCustomMigrationQueryCapability
            .getDmnTemplateDetails(
                    transactionEntityUpdated.getEventHeaders().getProviderWorkflowId(),
                    List.of(new DefinitionDetails())
            );

    assertEquals("invoiceapproval",templateDetails.getValue0());
    assertTrue(templateDetails.getValue1().length > 0);
  }

  @Test
  public void getEnabledDefinitionsSuccess () {
    transactionEntityUpdated =
    	TransactionEntityFactory.getInstanceOf(
    			TriggerHandlerTestData.prepareV3TriggerMessage("created").getTriggerMessage(),
            contextHandler);

    doReturn(List.of(new DefinitionDetails()))
        .when(customWorkflowQueryCapability)
        .getEnabledDefinitions(Mockito.any(), Mockito.anyBoolean());

    doReturn(List.of(new DefinitionDetails()))
        .when(defaultTemplateQueryCapability)
        .getEnabledDefinitions(Mockito.any(), Mockito.anyBoolean());

    List<DefinitionDetails> defList =
        precannedToCustomMigrationQueryCapability.getEnabledDefinitions(transactionEntityUpdated, true);

    assertEquals(2, defList.size());
  }

  @Test
  public void testFetchInitialStartEventActivityDetail() {

    Mockito.when(customWorkflowQueryCapability.fetchInitialStartEventActivityDetail(transactionEntityUpdated))
            .thenReturn(new ActivityDetail());

    precannedToCustomMigrationQueryCapability.fetchInitialStartEventActivityDetail(transactionEntityUpdated);

    Mockito.verify(customWorkflowQueryCapability, Mockito.times(1))
            .fetchInitialStartEventActivityDetail(transactionEntityUpdated);
  }

}
