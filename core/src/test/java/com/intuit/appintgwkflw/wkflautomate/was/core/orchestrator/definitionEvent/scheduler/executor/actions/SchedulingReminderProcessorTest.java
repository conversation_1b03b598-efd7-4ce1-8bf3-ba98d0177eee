package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.executor.actions;

import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.IXPManager;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.AuthDetailsService;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.threadPool.DuzzitPaginationConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.threadPool.RecordDuzzitPaginationConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.fetcher.RecordListFetcher;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.publisher.TriggerEventPublisher;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.EventScheduleHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionActivityDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.*;
import com.intuit.appintgwkflw.wkflautomate.was.entity.fetcher.RecordQueryConnectorResponse;
import com.intuit.foundation.workflow.scheduling.Execution;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;

import static org.mockito.Mockito.times;

@RunWith(MockitoJUnitRunner.class)
public class SchedulingReminderProcessorTest {
    @InjectMocks
    private SchedulingReminderProcessor schedulingReminderProcessor;
    @Mock
    private RecordListFetcher recordListFetcher;
    @Mock private WASContextHandler wasContextHandler;
    @Mock private TriggerEventPublisher triggerEventPublisher;
    @Mock private EventScheduleHelper eventScheduleHelper;
    @Mock private IXPManager ixpManager;
    @Mock private DuzzitPaginationConfig paginationConfig;
    @Mock private AuthDetailsService authDetailsService;
    @Mock private DefinitionActivityDetailsRepository definitionActivityDetailsRepository;

    private static final long ownerId = 123456789;
    private static final String APPCONNECT_DUZZIT_RESPONSE =
            TestHelper.readResourceAsString("duzzitOutputs/appconnectCRResponse.json");
    private final RecordQueryConnectorResponse recordQueryConnectorResponse =
            ObjectConverter.fromJson(APPCONNECT_DUZZIT_RESPONSE, RecordQueryConnectorResponse.class);
    private static final String CUSTOM_REMINDER_WORKFLOW_DEF =
            TestHelper.readResourceAsString("bpmn/customWorkflowDefinition.bpmn");

    private static final String CUSTOM_REMINDER_WORKFLOW =
            TestHelper.readResourceAsString("bpmn/customReminderTest.bpmn");


    @Before
    public void init() {
        MockitoAnnotations.initMocks(this);
        ReflectionTestUtils.setField(
                schedulingReminderProcessor, "recordListFetcher", recordListFetcher);
        ReflectionTestUtils.setField(
                schedulingReminderProcessor, "wasContextHandler", wasContextHandler);
        ReflectionTestUtils.setField(
                schedulingReminderProcessor, "triggerEventPublisher", triggerEventPublisher);
        ReflectionTestUtils.setField(
                schedulingReminderProcessor, "eventScheduleHelper", eventScheduleHelper);
        ReflectionTestUtils.setField(
                schedulingReminderProcessor, "ixpManager", ixpManager);
        ReflectionTestUtils.setField(
                schedulingReminderProcessor, "paginationConfig", paginationConfig);
        ReflectionTestUtils.setField(
                schedulingReminderProcessor, "definitionActivityDetailsRepository",
                definitionActivityDetailsRepository);
        ReflectionTestUtils.setField(schedulingReminderProcessor, "authDetailsService", authDetailsService);
        Mockito.when(
                        eventScheduleHelper.isEventSchedulingEnabledForWorkflow(Mockito.any(), Mockito.any()))
                .thenReturn(true);
    }

    @Test
    public void testProcess() {
        Mockito.when(recordListFetcher.fetchRecords(Mockito.any()))
                .thenReturn(recordQueryConnectorResponse);
        schedulingReminderProcessor.process(
                getDefinitionDetails(CUSTOM_REMINDER_WORKFLOW_DEF), getSchedulingEvent());
        Mockito.verify(recordListFetcher, times(1)).fetchRecords(Mockito.any());
        Mockito.verify(triggerEventPublisher, times(5)).publishTriggerEvent(Mockito.any());
    }

    @Test
    public void testProcessWorkflowNotEnabled() {
        Mockito.when(
                        eventScheduleHelper.isEventSchedulingEnabledForWorkflow(Mockito.any(), Mockito.any()))
                .thenReturn(false);
        schedulingReminderProcessor.process(
                getDefinitionDetails(CUSTOM_REMINDER_WORKFLOW_DEF), getSchedulingEvent());
        Mockito.verify(recordListFetcher, times(0)).fetchRecords(Mockito.any());
    }

    @Test
    public void testProcessEmptyReponse() {
        Mockito.when(recordListFetcher.fetchRecords(Mockito.any()))
                .thenReturn(new RecordQueryConnectorResponse());
        schedulingReminderProcessor.process(
                getDefinitionDetails(CUSTOM_REMINDER_WORKFLOW_DEF), getSchedulingEvent());
        Mockito.verify(recordListFetcher, times(1)).fetchRecords(Mockito.any());
        Mockito.verify(triggerEventPublisher, times(0)).publishTriggerEvent(Mockito.any());
    }

    @Test
    public void testProcess_withoutParameterDetails() {
        Mockito.when(recordListFetcher.fetchRecords(Mockito.any()))
                .thenReturn(recordQueryConnectorResponse);
        schedulingReminderProcessor.process(
                getDefinitionDetails(CUSTOM_REMINDER_WORKFLOW), getSchedulingEvent());
        Mockito.verify(recordListFetcher, times(1)).fetchRecords(Mockito.any());
    }


    @Test
    public void testProcess_NewInputParam() {
        Mockito.when(recordListFetcher.fetchRecords(Mockito.any()))
                .thenReturn(recordQueryConnectorResponse);
        Mockito.when(ixpManager.getBoolean(Mockito.anyString(),
                Mockito.anyString())).thenReturn(true);

        Map<RecordType, RecordDuzzitPaginationConfig> recordConfig = new HashMap<>();
        recordConfig.put(RecordType.INVOICE, new RecordDuzzitPaginationConfig());
        Mockito.when(paginationConfig.getRecordConfig()).thenReturn(recordConfig);

        schedulingReminderProcessor.process(
                getDefinitionDetails(CUSTOM_REMINDER_WORKFLOW_DEF), getSchedulingEvent());
        Mockito.verify(recordListFetcher, times(1)).fetchRecords(Mockito.any());
        Mockito.verify(triggerEventPublisher, times(5)).publishTriggerEvent(Mockito.any());
    }


    @Test
    public void testProcess_NewInputParam_IXPFalse() {
        Mockito.when(recordListFetcher.fetchRecords(Mockito.any()))
                .thenReturn(recordQueryConnectorResponse);
        Mockito.when(ixpManager.getBoolean(Mockito.anyString(),
                Mockito.anyString())).thenReturn(false);

        Map<RecordType, RecordDuzzitPaginationConfig> recordConfig = new HashMap<>();
        recordConfig.put(RecordType.INVOICE, new RecordDuzzitPaginationConfig());
        Mockito.when(paginationConfig.getRecordConfig()).thenReturn(recordConfig);

        schedulingReminderProcessor.process(
                getDefinitionDetails(CUSTOM_REMINDER_WORKFLOW_DEF), getSchedulingEvent());
        Mockito.verify(recordListFetcher, times(1)).fetchRecords(Mockito.any());
        Mockito.verify(triggerEventPublisher, times(5)).publishTriggerEvent(Mockito.any());
    }

    @Test
    public void testProcess_NewInputParam_DifferentRecordType() {
        Mockito.when(recordListFetcher.fetchRecords(Mockito.any()))
                .thenReturn(recordQueryConnectorResponse);

        Map<RecordType, RecordDuzzitPaginationConfig> recordConfig = new HashMap<>();
        recordConfig.put(RecordType.BILL, new RecordDuzzitPaginationConfig());
        Mockito.when(paginationConfig.getRecordConfig()).thenReturn(recordConfig);

        schedulingReminderProcessor.process(
                getDefinitionDetails(CUSTOM_REMINDER_WORKFLOW_DEF), getSchedulingEvent());
        Mockito.verify(recordListFetcher, times(1)).fetchRecords(Mockito.any());
        Mockito.verify(triggerEventPublisher, times(5)).publishTriggerEvent(Mockito.any());
    }

    @Test
    public void testProcess_OtherScheduleAction() {
        Mockito.when(recordListFetcher.fetchRecords(Mockito.any()))
                .thenReturn(recordQueryConnectorResponse);
        schedulingReminderProcessor.process(
                getDefinitionDetails(CUSTOM_REMINDER_WORKFLOW_DEF), getSchedulingEvent());
        Mockito.verify(recordListFetcher, times(1)).fetchRecords(Mockito.any());
        Mockito.verify(triggerEventPublisher, times(5)).publishTriggerEvent(Mockito.any());
    }

    @Test
    public void test_getWorkflowName() {
        Assert.assertEquals(WorkflowNameEnum.REMINDER,
                schedulingReminderProcessor.getWorkflowName());
    }

    @Test
    public void testMultiConditionProcess_CustomStart() {
        Mockito.when(recordListFetcher.fetchRecords(Mockito.any()))
                .thenReturn(recordQueryConnectorResponse);

        TemplateDetails templateDetails = new TemplateDetails();
        templateDetails.setTemplateCategory(TemplateCategory.CUSTOM.toString());
        templateDetails.setDefinitionType(DefinitionType.SINGLE);

        DefinitionDetails definitionDetails = getDefinitionDetails(CUSTOM_REMINDER_WORKFLOW_DEF);
        definitionDetails.setDefinitionData(null);
        definitionDetails.setTemplateDetails(templateDetails);

        DefinitionActivityDetail definitionActivityDetail = new DefinitionActivityDetail();
        definitionActivityDetail.setUserAttributes("{\"selected\": false, \"parameters\": {\"isRecurring\": {\"fieldValue\": [\"false\"]}, \"recurFrequency\": {\"fieldValue\": [\"2\"]}, \"FilterCondition\": {\"fieldValue\": [\"{\\\"rules\\\":[{\\\"parameterType\\\":\\\"DAYS\\\",\\\"parameterName\\\":\\\"TxnDueDays\\\",\\\"conditionalExpression\\\":\\\"BF 2\\\",\\\"$sdk_validated\\\":true},{\\\"parameterType\\\":\\\"DOUBLE\\\",\\\"parameterName\\\":\\\"TxnAmount\\\",\\\"conditionalExpression\\\":\\\"BTW 20000,20010\\\",\\\"$sdk_validated\\\":true}]}\"]}, \"FilterRecordType\": {\"fieldValue\": [\"invoice\"]}, \"FilterCloseTaskConditions\": {\"fieldValue\": [\"txn_paid\"]}}}");
        definitionActivityDetail.setActivityId("callActivity-1");

        List<DefinitionActivityDetail> definitionActivityDetailsList = new ArrayList<>();
        definitionActivityDetailsList.add(definitionActivityDetail);

        Mockito.when(definitionActivityDetailsRepository.findAllByDefinitionDetails_DefinitionId(Mockito.any()))
                .thenReturn(Optional.of(definitionActivityDetailsList));

        schedulingReminderProcessor.process(definitionDetails, getSchedulingEvent());

        Mockito.verify(recordListFetcher, times(1)).fetchRecords(Mockito.any());
        Mockito.verify(triggerEventPublisher, times(5)).publishTriggerEvent(Mockito.any());
    }

    @Test
    public void testMultiConditionProcess_CustomWait() {
        Mockito.when(recordListFetcher.fetchRecords(Mockito.any()))
                .thenReturn(recordQueryConnectorResponse);

        TemplateDetails templateDetails = new TemplateDetails();
        templateDetails.setTemplateCategory(TemplateCategory.CUSTOM.toString());
        templateDetails.setDefinitionType(DefinitionType.SINGLE);

        DefinitionDetails definitionDetails = getDefinitionDetails(CUSTOM_REMINDER_WORKFLOW_DEF);
        definitionDetails.setDefinitionData(null);
        definitionDetails.setTemplateDetails(templateDetails);

        DefinitionActivityDetail definitionActivityDetail = new DefinitionActivityDetail();
        definitionActivityDetail.setUserAttributes("{\"selected\": false, \"parameters\": {\"isRecurring\": {\"fieldValue\": [\"false\"]}, \"recurFrequency\": {\"fieldValue\": [\"2\"]}, \"FilterCondition\": {\"fieldValue\": [\"{\\\"rules\\\":[{\\\"parameterType\\\":\\\"DAYS\\\",\\\"parameterName\\\":\\\"TxnDueDays\\\",\\\"conditionalExpression\\\":\\\"BF 2\\\",\\\"$sdk_validated\\\":true},{\\\"parameterType\\\":\\\"DOUBLE\\\",\\\"parameterName\\\":\\\"TxnAmount\\\",\\\"conditionalExpression\\\":\\\"BTW 20000,20010\\\",\\\"$sdk_validated\\\":true}]}\"]}, \"FilterRecordType\": {\"fieldValue\": [\"invoice\"]}, \"FilterCloseTaskConditions\": {\"fieldValue\": [\"txn_paid\"]}}}");
        definitionActivityDetail.setActivityId("callActivity-1");

        List<DefinitionActivityDetail> definitionActivityDetailsList = new ArrayList<>();
        definitionActivityDetailsList.add(definitionActivityDetail);

        Mockito.when(definitionActivityDetailsRepository.findAllByDefinitionDetails_DefinitionId(Mockito.any()))
                .thenReturn(Optional.of(definitionActivityDetailsList));

        Execution schedulingEvent = getSchedulingEvent();
        schedulingEvent.setReferenceId("customReminder_234234234734:customReminder_customWait");
        schedulingReminderProcessor.process(definitionDetails, schedulingEvent);

        Mockito.verify(recordListFetcher, times(1)).fetchRecords(Mockito.any());
        Mockito.verify(triggerEventPublisher, times(5)).publishTriggerEvent(Mockito.any());
    }

    @Test
    public void testMultiConditionProcess_CustomRecur() {
        Mockito.when(recordListFetcher.fetchRecords(Mockito.any()))
                .thenReturn(recordQueryConnectorResponse);

        TemplateDetails templateDetails = new TemplateDetails();
        templateDetails.setTemplateCategory(TemplateCategory.CUSTOM.toString());
        templateDetails.setDefinitionType(DefinitionType.SINGLE);

        DefinitionDetails definitionDetails = getDefinitionDetails(CUSTOM_REMINDER_WORKFLOW_DEF);
        definitionDetails.setDefinitionData(null);
        definitionDetails.setTemplateDetails(templateDetails);

        DefinitionActivityDetail definitionActivityDetail = new DefinitionActivityDetail();
        definitionActivityDetail.setUserAttributes("{\"selected\": false, \"parameters\": {\"isRecurring\": {\"fieldValue\": [\"true\"]}, \"recurFrequency\": {\"fieldValue\": [\"2\"]}, \"FilterCondition\": {\"fieldValue\": [\"{\\\"rules\\\":[{\\\"parameterType\\\":\\\"DAYS\\\",\\\"parameterName\\\":\\\"TxnDueDays\\\",\\\"conditionalExpression\\\":\\\"BF 2\\\",\\\"$sdk_validated\\\":true},{\\\"parameterType\\\":\\\"DOUBLE\\\",\\\"parameterName\\\":\\\"TxnAmount\\\",\\\"conditionalExpression\\\":\\\"BTW 20000,20010\\\",\\\"$sdk_validated\\\":true}]}\"]}, \"FilterRecordType\": {\"fieldValue\": [\"invoice\"]}, \"FilterCloseTaskConditions\": {\"fieldValue\": [\"txn_paid\"]}}}");
        definitionActivityDetail.setActivityId("callActivity-1");

        DefinitionActivityDetail noPathDefinitionActivityDetail = new DefinitionActivityDetail();
        noPathDefinitionActivityDetail.setUserAttributes("{\"selected\": false, \"parameters\": {\"isRecurring\": {\"fieldValue\": [\"false\"]}, \"recurFrequency\": {\"fieldValue\": []}, \"FilterCondition\": {\"fieldValue\": [\"{\\\"rules\\\":[{\\\"parameterType\\\":\\\"DAYS\\\",\\\"parameterName\\\":\\\"TxnDueDays\\\",\\\"conditionalExpression\\\":\\\"BF 2\\\",\\\"$sdk_validated\\\":true},{\\\"parameterType\\\":\\\"DOUBLE\\\",\\\"parameterName\\\":\\\"TxnAmount\\\",\\\"conditionalExpression\\\":\\\"NOT_BTW 20000,20010\\\",\\\"$sdk_validated\\\":true}]}\"]}, \"FilterRecordType\": {\"fieldValue\": [\"invoice\"]}, \"FilterCloseTaskConditions\": {\"fieldValue\": [\"txn_paid\"]}}}");
        noPathDefinitionActivityDetail.setActivityId("callActivity-2");

        List<DefinitionActivityDetail> definitionActivityDetailsList = new ArrayList<>();
        definitionActivityDetailsList.add(definitionActivityDetail);
        definitionActivityDetailsList.add(noPathDefinitionActivityDetail);

        Mockito.when(definitionActivityDetailsRepository.findAllByDefinitionDetails_DefinitionId(Mockito.any()))
                .thenReturn(Optional.of(definitionActivityDetailsList));
        Execution schedulingEvent = getSchedulingEvent();
        schedulingEvent.setReferenceId("customReminder_234234234734:customReminder_customWait");

        schedulingReminderProcessor.process(definitionDetails, schedulingEvent);

        // 2 wait calls (1 per call activity and 1 recur call)
        Mockito.verify(recordListFetcher, times(3)).fetchRecords(Mockito.any());
        Mockito.verify(triggerEventPublisher, times(15)).publishTriggerEvent(Mockito.any());
    }

    private Execution getSchedulingEvent() {
        Execution schedulingEvent = new Execution();
        schedulingEvent.setReferenceId("customReminder_49163781263812:customReminder_customStart");
        schedulingEvent.setExecutionId("execId");
        return schedulingEvent;
    }

    private DefinitionDetails getDefinitionDetails(String data) {
        DefinitionDetails definitionDetails = new DefinitionDetails();
        definitionDetails.setDefinitionId("def123");
        definitionDetails.setOwnerId(Long.valueOf(ownerId));
        definitionDetails.setRecordType(RecordType.INVOICE);
        definitionDetails.setDefinitionData(data.getBytes());
        TemplateDetails templateDetails = new TemplateDetails();
        templateDetails.setOfferingId("off123");
        definitionDetails.setTemplateDetails(templateDetails);
        return definitionDetails;
    }
}