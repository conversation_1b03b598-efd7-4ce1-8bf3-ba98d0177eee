package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants.TASK_STATUS_COMPLETE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants.TASK_STATUS_CREATED;
import static org.mockito.ArgumentMatchers.any;

import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.common.WorkflowCustomTaskHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.common.WorkflowExternalTaskManager;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ActivityDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.TaskHandlerName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskAttributes;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskCommand;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowActivityAttributes;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import org.camunda.bpm.engine.variable.VariableMap;
import org.camunda.bpm.engine.variable.impl.VariableMapImpl;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

/**
 * WorkflowTaskModelHandler handles the create call of Workflow Task.
 *
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class WorkflowCustomTaskHandlerTest {

  @Mock
  private ProcessDetailsRepository processDetailRepo;

  @Mock
  private ActivityDetailsRepository activityDetailRepo;

  @Mock
  private WorkflowExternalTaskManager taskManager;

  @InjectMocks
  private WorkflowCustomTaskHandler workflowTaskModelHandler;

  @Mock
  private NotificationTaskRequestModifier notificationTaskRequestModifier;

  @Mock
  private WorkflowCustomTaskHelper customTaskHelper;
  @Mock
  private WorkflowSystemTaskHandler workflowSystemTaskHandler;

  @Mock
  private FeatureFlagManager featureFlagManager;

  @Mock
  MetricLogger metricLogger;

  @Before
  public void init() {
    customTaskHelper = new WorkflowCustomTaskHelper(processDetailRepo, activityDetailRepo, null,
        null, null, null);
    ReflectionTestUtils.setField(workflowTaskModelHandler, "customTaskHelper", customTaskHelper);
    ReflectionTestUtils.setField(workflowTaskModelHandler, "metricLogger", metricLogger);
  }

  /**
   * No process instance found.
   */
  @Test(expected = WorkflowGeneralException.class)
  public void test_noProcessInstanceFound() {
    Map<String, String> extensionProperties = new HashMap<>();
    extensionProperties.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE, "HUMAN_TASK");
    extensionProperties.put("estimate", "1");
    extensionProperties.put("priority", "1");

    Map<String, String> variableMap = new HashMap<>();
    variableMap.put("assigneeRoles", "[Role1, Role2]");
    variableMap.put("assigneeId", "assignee1");
    variableMap.put(WorkflowConstants.ID_KEY, "record1");
    VariableMap variableMap1 = new VariableMapImpl();
    variableMap1.putAll(variableMap);
    WorkerActionRequest workerActionRequest = WorkerActionRequest.builder().activityId("act1")
        .extensionProperties(extensionProperties).processInstanceId("processInstance1")
        .taskId("taskId1")
        .ownerId(1l).workerId("worker1").variableMap(variableMap1).build();
    Mockito.when(processDetailRepo.findById(Mockito.anyString()))
        .thenReturn(Optional.ofNullable(null));
    workflowTaskModelHandler.executeAction(workerActionRequest);
  }

  /**
   * Successful execution of HumanTask.
   */
  @Test
  public void successExecution_HumanTask_Sync() {
    Map<String, String> extensionProperties = new HashMap<>();
    extensionProperties.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE, "HUMAN_TASK");
    extensionProperties.put("estimate", "1");
    extensionProperties.put("priority", "1");
    extensionProperties.put(WorkFlowVariables.EVENTS.getName(), "[\"created\"]");

    Map<String, String> variableMap = new HashMap<>();
    variableMap.put("assigneeRoles", "[Role1, Role2]");
    variableMap.put("assigneeId", "assignee1");
    VariableMap variableMap1 = new VariableMapImpl();
    variableMap1.putAll(variableMap);

    WorkerActionRequest workerActionRequest = WorkerActionRequest.builder().activityId("act1")
        .extensionProperties(extensionProperties).processInstanceId("processInstance1")
        .taskId("taskId1")
        .ownerId(1l).workerId("worker1").variableMap(variableMap1).build();

    TemplateDetails templateDetails = TemplateDetails.builder().offeringId("offering1").build();
    DefinitionDetails definitionDetails = DefinitionDetails.builder().version(1)
        .templateDetails(templateDetails)
        .recordType(RecordType.INVOICE).build();
    ProcessDetails processDetails = ProcessDetails.builder().recordId("record1")
        .definitionDetails(definitionDetails).ownerId(1l).build();
    Mockito.when(processDetailRepo.findById(Mockito.anyString()))
        .thenReturn(Optional.of(processDetails));

    Map<String, Object> runtimeDefAttributes = new HashMap<>();
    runtimeDefAttributes.put("journalNo", "${journalNo}");
    runtimeDefAttributes.put("assigneeId", "${expertId}");
    runtimeDefAttributes.put("customerId", "${userId}");

    Map<String, String> modelDefAttributes = new HashMap<>();
    modelDefAttributes.put("visibility", "true");
    modelDefAttributes.put("estimate", "3");
    modelDefAttributes.put("taskType", "QB_INVOICE_APPROVAL");

    WorkflowActivityAttributes activityAttributes = WorkflowActivityAttributes.builder()
        .modelAttributes(modelDefAttributes).runtimeAttributes(runtimeDefAttributes).build();

    ActivityDetail activityDetails = ActivityDetail.builder().id(10).activityId("actId1")
        .activityName("actName1").attributes(ObjectConverter.toJson(activityAttributes))
        .templateDetails(templateDetails).type(TaskType.HUMAN_TASK).build();
    Mockito.when(
        activityDetailRepo.findByTemplateDetailsAndActivityId(Mockito.any(TemplateDetails.class),
            Mockito.anyString())).thenReturn(Optional.of(activityDetails));

    Mockito.when(taskManager.execute(Mockito.any()))
        .thenReturn(WorkflowTaskResponse.builder().build());

    Map<String, Object> responseMap = workflowTaskModelHandler.executeAction(workerActionRequest);
    Assert.assertTrue((boolean) responseMap.get(WorkFlowVariables.PENDING_EXTEND_LOCK.getName()));

    Map<String, Object> runtimeAttributes = new HashMap<>(
        workerActionRequest.getVariableMap());

    WorkflowTaskRequest taskRequest = WorkflowTaskRequest
        .builder()
        .processInstanceId(workerActionRequest.getProcessInstanceId())
        .id(workerActionRequest.getTaskId())
        .activityId(activityDetails.getActivityId())
        .publishExternalTaskEvent(true).publishWorkflowStateTransitionEvent(true)
        .activityName(activityDetails.getActivityName())
        .command(TaskCommand.CREATE).status(TASK_STATUS_CREATED)
        .taskType(TaskType.HUMAN_TASK)
        .taskAttributes(TaskAttributes.builder()
        		.modelAttributes(activityAttributes.getModelAttributes())
        		.runtimeAttributes(activityAttributes.getRuntimeAttributes())
        		.variables(runtimeAttributes).build())
        .workerId(workerActionRequest.getWorkerId())
        .recordId(processDetails.getRecordId()).build();

    Mockito.verify(taskManager, Mockito.times(1)).execute(Mockito.eq(taskRequest));
  }

  /**
   * Successful execution of SystemTask.
   */
  @Test
  public void successExecution_SystemTask() {
    Map<String, String> extensionProperties = new HashMap<>();
    extensionProperties.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE, "SYSTEM_TASK");
    extensionProperties.put(WorkflowConstants.HANDLER_ID, "intuit/test1");

    Map<String, String> variableMap = new HashMap<>();
    VariableMap variableMap1 = new VariableMapImpl();
    variableMap1.putAll(variableMap);

    WorkerActionRequest workerActionRequest = WorkerActionRequest.builder().activityId("act1")
        .extensionProperties(extensionProperties).processInstanceId("processInstance1")
        .taskId("taskId1")
        .ownerId(1l).workerId("worker1").variableMap(variableMap1).build();

    TemplateDetails templateDetails = TemplateDetails.builder().offeringId("offering1").build();
    DefinitionDetails definitionDetails = DefinitionDetails.builder().version(1)
        .templateDetails(templateDetails)
        .recordType(RecordType.INVOICE).build();
    ProcessDetails processDetails = ProcessDetails.builder().recordId("record1")
        .definitionDetails(definitionDetails).ownerId(1l).build();
    Mockito.when(processDetailRepo.findById(Mockito.anyString()))
        .thenReturn(Optional.of(processDetails));

    Map<String, Object> runtimeDefAttributes = new HashMap<>();

    Map<String, String> modelDefAttributes = new HashMap<>();


    WorkflowActivityAttributes activityAttributes = WorkflowActivityAttributes.builder()
        .modelAttributes(modelDefAttributes).runtimeAttributes(runtimeDefAttributes).build();

    ActivityDetail activityDetails = ActivityDetail.builder().id(10).activityId("actId1")
        .activityName("actName1").attributes(ObjectConverter.toJson(activityAttributes))
        .templateDetails(templateDetails).type(TaskType.SYSTEM_TASK).build();
    Mockito.when(
        activityDetailRepo.findByTemplateDetailsAndActivityId(Mockito.any(TemplateDetails.class),
            Mockito.anyString())).thenReturn(Optional.of(activityDetails));

    Mockito.when(workflowSystemTaskHandler.execute(Mockito.any(), Mockito.any()))
        .thenReturn(Map.of(WorkFlowVariables.PENDING_EXTEND_LOCK.getName(), Boolean.TRUE));

    Map<String, Object> responseMap = workflowTaskModelHandler.executeAction(workerActionRequest);
    Assert.assertTrue((boolean) responseMap.get(WorkFlowVariables.PENDING_EXTEND_LOCK.getName()));

    Mockito.verify(workflowSystemTaskHandler,
        Mockito.times(1)).execute(Mockito.any(), Mockito.any());
  }

  /**
   * Successful execution of SystemTask.
   */
  @Test(expected = WorkflowGeneralException.class)
  public void failureExecution_SystemTask() {
    Map<String, String> extensionProperties = new HashMap<>();
    extensionProperties.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE, "SYSTEM_TASK");
    extensionProperties.put(WorkflowConstants.HANDLER_ID, "intuit/test1");

    Map<String, String> variableMap = new HashMap<>();
    VariableMap variableMap1 = new VariableMapImpl();
    variableMap1.putAll(variableMap);

    WorkerActionRequest workerActionRequest = WorkerActionRequest.builder().activityId("act1")
        .extensionProperties(extensionProperties).processInstanceId("processInstance1")
        .taskId("taskId1")
        .ownerId(1l).workerId("worker1").variableMap(variableMap1).inputVariables(variableMap).build();

    TemplateDetails templateDetails = TemplateDetails.builder().offeringId("offering1").build();
    DefinitionDetails definitionDetails = DefinitionDetails.builder().version(1)
        .templateDetails(templateDetails)
        .recordType(RecordType.INVOICE).build();
    ProcessDetails processDetails = ProcessDetails.builder().recordId("record1")
        .definitionDetails(definitionDetails).ownerId(1l).build();
    Mockito.when(processDetailRepo.findById(Mockito.anyString()))
        .thenReturn(Optional.of(processDetails));

    Map<String, Object> runtimeDefAttributes = new HashMap<>();

    Map<String, String> modelDefAttributes = new HashMap<>();


    WorkflowActivityAttributes activityAttributes = WorkflowActivityAttributes.builder()
        .modelAttributes(modelDefAttributes).runtimeAttributes(runtimeDefAttributes).build();

    ActivityDetail activityDetails = ActivityDetail.builder().id(10).activityId("actId1")
        .activityName("actName1").attributes(ObjectConverter.toJson(activityAttributes))
        .templateDetails(templateDetails).type(TaskType.SYSTEM_TASK).build();
    Mockito.when(
        activityDetailRepo.findByTemplateDetailsAndActivityId(Mockito.any(TemplateDetails.class),
            Mockito.anyString())).thenReturn(Optional.of(activityDetails));

    Mockito.when(workflowSystemTaskHandler.execute(Mockito.any(), Mockito.any()))
        .thenThrow(WorkflowGeneralException.class);

    workflowTaskModelHandler.executeAction(workerActionRequest);
  }

  /** For OINP task, create and complete execution should be on same thread. */
  @Test
  public void successExecution_OtherTask_WithoutFeatureFlag() {
    Map<String, String> extensionProperties = new HashMap<>();
    extensionProperties.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE, "OINP_TASK");
    extensionProperties.put("estimate", "1");
    extensionProperties.put("priority", "1");

    Map<String, String> variableMap = new HashMap<>();
    variableMap.put("assigneeId", "assignee1");
    variableMap.put("intuit_realmid", "67246382639");
    VariableMap variableMap1 = new VariableMapImpl();
    variableMap1.putAll(variableMap);

    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("act1")
            .extensionProperties(extensionProperties)
            .processInstanceId("processInstance1")
            .taskId("taskId1")
            .ownerId(1l)
            .workerId("worker1")
            .variableMap(variableMap1)
            .inputVariables(variableMap)
            .build();

    TemplateDetails templateDetails = TemplateDetails.builder().offeringId("offering1").build();
    DefinitionDetails definitionDetails =
        DefinitionDetails.builder()
            .version(1)
            .templateDetails(templateDetails)
            .recordType(RecordType.INVOICE)
            .build();
    ProcessDetails processDetails =
        ProcessDetails.builder()
            .recordId("record1")
            .definitionDetails(definitionDetails)
            .ownerId(1l)
            .build();
    Mockito.when(processDetailRepo.findById(Mockito.anyString()))
        .thenReturn(Optional.of(processDetails));

    Map<String, Object> runtimeDefAttributes = new HashMap<>();
    runtimeDefAttributes.put("journalNo", "${journalNo}");
    runtimeDefAttributes.put("assigneeId", "${expertId}");
    runtimeDefAttributes.put("customerId", "${userId}");

    Map<String, String> modelDefAttributes = new HashMap<>();
    modelDefAttributes.put("visibility", "true");
    modelDefAttributes.put("estimate", "3");
    modelDefAttributes.put("taskType", "QB_INVOICE_APPROVAL");

    WorkflowActivityAttributes activityAttributes =
        WorkflowActivityAttributes.builder()
            .modelAttributes(modelDefAttributes)
            .runtimeAttributes(runtimeDefAttributes)
            .build();

    ActivityDetail activityDetails =
        ActivityDetail.builder()
            .id(10)
            .activityId("actId1")
            .activityName("actName1")
            .attributes(ObjectConverter.toJson(activityAttributes))
            .templateDetails(templateDetails)
            .type(TaskType.HUMAN_TASK)
            .build();
    Mockito.when(
            activityDetailRepo.findByTemplateDetailsAndActivityId(
                Mockito.any(TemplateDetails.class), Mockito.anyString()))
        .thenReturn(Optional.of(activityDetails));

    Map<String, Object> responseMap = new HashMap<>();
    responseMap.put("abc", "abc");
    Mockito.when(taskManager.execute(Mockito.any()))
        .thenReturn(WorkflowTaskResponse.builder().responseMap(responseMap).build());
    Mockito.when(featureFlagManager.getBoolean(Mockito.any(), Mockito.anyBoolean(), Mockito.any(), Mockito.any())).thenReturn(false);
    Map<String, Object> response = workflowTaskModelHandler.executeAction(workerActionRequest);

    Assert.assertNotNull(response);
    Assert.assertNotNull(response.get("abc"));
    Mockito.verify(taskManager, Mockito.times(2)).execute(Mockito.any());
  }

  /** For OINP task, create and complete execution should be on same thread. */
  @Test
  public void successExecution_OtherTask_WithFeatureFlag() {
    Map<String, String> extensionProperties = new HashMap<>();
    extensionProperties.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE, "OINP_TASK");
    extensionProperties.put("estimate", "1");
    extensionProperties.put("priority", "1");

    Map<String, String> variableMap = new HashMap<>();
    variableMap.put("assigneeId", "assignee1");
    variableMap.put("intuit_userid", "-13246382639");
    variableMap.put("intuit_realmid", "67246382639");
    VariableMap variableMap1 = new VariableMapImpl();
    variableMap1.putAll(variableMap);

    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("actId1")
            .extensionProperties(extensionProperties)
            .processInstanceId("processInstance1")
            .taskId("taskId1")
            .ownerId(1l)
            .workerId("worker1")
            .variableMap(variableMap1)
            .inputVariables(variableMap)
            .build();

    TemplateDetails templateDetails = TemplateDetails.builder().offeringId("offering1").build();
    DefinitionDetails definitionDetails =
        DefinitionDetails.builder()
            .version(1)
            .templateDetails(templateDetails)
            .recordType(RecordType.INVOICE)
            .build();
    ProcessDetails processDetails =
        ProcessDetails.builder()
            .recordId("record1")
            .definitionDetails(definitionDetails)
            .ownerId(1l)
            .build();
    Mockito.when(processDetailRepo.findById(Mockito.anyString()))
        .thenReturn(Optional.of(processDetails));

    Map<String, Object> runtimeDefAttributes = new HashMap<>();
    runtimeDefAttributes.put("notificationData", "");
    runtimeDefAttributes.put("notificationMetaData", "{\"authId\": -1})");

    Map<String, String> modelDefAttributes = new HashMap<>();
    modelDefAttributes.put("activityName", "Send tray notification");
    modelDefAttributes.putAll(extensionProperties);

    Map<String, String> variables = new HashMap<>();
    variables.put("notificationData", "");
    VariableMap variableMap2 = new VariableMapImpl();
    variableMap2.putAll(variables);

    WorkflowTaskRequest.WorkflowTaskRequestBuilder taskRequestBuilder =
        WorkflowTaskRequest.builder()
            .processInstanceId(workerActionRequest.getProcessInstanceId())
            .id(workerActionRequest.getTaskId())
            .activityId(workerActionRequest.getActivityId())
            .activityName("actName1")
            .taskType(TaskType.NOTIFICATION_TASK)
            .taskAttributes(
                TaskAttributes.builder()
                    .modelAttributes(modelDefAttributes)
                    .runtimeAttributes(runtimeDefAttributes)
                    .variables(variableMap2)
                    .build());
    WorkflowActivityAttributes activityAttributes =
        WorkflowActivityAttributes.builder()
            .modelAttributes(modelDefAttributes)
            .runtimeAttributes(runtimeDefAttributes)
            .build();

    ActivityDetail activityDetails =
        ActivityDetail.builder()
            .id(10)
            .activityId("actId1")
            .activityName("actName1")
            .attributes(ObjectConverter.toJson(activityAttributes))
            .templateDetails(templateDetails)
            .type(TaskType.NOTIFICATION_TASK)
            .parentId(0)
            .build();
    Mockito.when(
            activityDetailRepo.findByTemplateDetailsAndActivityId(
                Mockito.any(TemplateDetails.class), Mockito.anyString()))
        .thenReturn(Optional.of(activityDetails));
    WorkflowTaskRequest createRequest =
        taskRequestBuilder
            .skipCallback(false)
            .publishExternalTaskEvent(false)
            .publishWorkflowStateTransitionEvent(false)
            .command(TaskCommand.CREATE)
            .status(TASK_STATUS_CREATED)
            .workerId(workerActionRequest.getWorkerId())
            .recordId(processDetails.getRecordId())
            .build();
    Mockito.when(
            notificationTaskRequestModifier.getTaskRequest(
                Mockito.any(WorkflowTaskRequest.class), Mockito.any(WorkerActionRequest.class)))
        .thenReturn(createRequest);
    TaskRequestHandlers.addHandler(TaskType.NOTIFICATION_TASK, notificationTaskRequestModifier);

    Mockito.when(featureFlagManager.getBoolean(Mockito.any(), Mockito.anyBoolean(), Mockito.any(), Mockito.any())).thenReturn(true);

    WorkflowTaskRequest completeRequest =
        taskRequestBuilder
            .skipCallback(false)
            .txnId("txn1")
            .publishExternalTaskEvent(false)
            .publishWorkflowStateTransitionEvent(false)
            .command(TaskCommand.COMPLETE)
            .status(TASK_STATUS_COMPLETE)
            .workerId(workerActionRequest.getWorkerId())
            .recordId(processDetails.getRecordId())
            .build();

    Map<String, Object> responseMap = new HashMap<>();
    responseMap.put("txnId", "txn1");
    Mockito.when(taskManager.execute(Mockito.eq(createRequest)))
        .thenReturn(WorkflowTaskResponse.builder().txnId("txn1").responseMap(responseMap).build());
    Mockito.when(taskManager.execute(Mockito.eq(completeRequest)))
        .thenReturn(WorkflowTaskResponse.builder().txnId("txn1").build());
    Map<String, Object> response = workflowTaskModelHandler.executeAction(workerActionRequest);
    Assert.assertEquals(response.get("txnId"), (Object) "txn1");
    Mockito.verify(taskManager, Mockito.times(1)).execute(Mockito.eq(createRequest));
    Mockito.verify(taskManager, Mockito.times(1)).execute(Mockito.eq(completeRequest));
  }

  /**
   * Expected exception in case Activity Definition not found.
   */
  @Test(expected = WorkflowGeneralException.class)
  public void execute_NoActivityDefFound() {

    Map<String, String> extensionProperties = new HashMap<>();
    extensionProperties.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE, "OINP_TASK");
    extensionProperties.put("estimate", "1");
    extensionProperties.put("priority", "1");

    Map<String, String> variableMap = new HashMap<>();
    variableMap.put("assigneeRoles", "[Role1, Role2]");
    variableMap.put("assigneeId", "assignee1");
    variableMap.put("recordId", "record1");
    VariableMap variableMap1 = new VariableMapImpl();
    variableMap1.putAll(variableMap);
    WorkerActionRequest workerActionRequest = WorkerActionRequest.builder().activityId("act1")
        .extensionProperties(extensionProperties).processInstanceId("processInstance1")
        .taskId("taskId1")
        .ownerId(1l).workerId("worker1").variableMap(variableMap1).build();

    TemplateDetails templateDetails = TemplateDetails.builder().offeringId("offering1").build();
    DefinitionDetails definitionDetails = DefinitionDetails.builder().version(1)
        .templateDetails(templateDetails)
        .recordType(RecordType.INVOICE).build();
    ProcessDetails processDetails = ProcessDetails.builder().recordId("record1")
        .definitionDetails(definitionDetails).ownerId(1l).build();
    Mockito.when(processDetailRepo.findById(Mockito.anyString()))
        .thenReturn(Optional.of(processDetails));

    Mockito.when(
        activityDetailRepo.findByTemplateDetailsAndActivityId(Mockito.any(TemplateDetails.class),
            Mockito.anyString())).thenReturn(Optional.ofNullable(null));

    workflowTaskModelHandler.executeAction(workerActionRequest);

  }


  /**
   * Successful execution of Async HumanTask.
   */
  @Test
  public void successExecution_HumanTask_async_tidNotPresent() {
    Map<String, String> extensionProperties = new HashMap<>();
    extensionProperties.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE, "HUMAN_TASK");
    extensionProperties.put("estimate", "1");
    extensionProperties.put("priority", "1");
    extensionProperties.put(ActivityConstants.EXTENSION_PROPERTY_TRANSACTION_MODE, "ASYNC");
    extensionProperties.put(WorkFlowVariables.EVENTS.getName(), "[\"created\",\"completed\"]");

    Map<String, String> variableMap = new HashMap<>();
    variableMap.put("assigneeRoles", "[Role1, Role2]");
    variableMap.put("assigneeId", "assignee1");
    variableMap.put(WorkflowConstants.ID_KEY, "record1");
    VariableMap variableMap1 = new VariableMapImpl();
    variableMap1.putAll(variableMap);

    WorkerActionRequest workerActionRequest = WorkerActionRequest.builder().activityId("act1")
        .extensionProperties(extensionProperties).processInstanceId("processInstance1")
        .taskId("taskId1")
        .ownerId(1l).workerId("worker1").variableMap(variableMap1).build();

    TemplateDetails templateDetails = TemplateDetails.builder().offeringId("offering1").build();
    DefinitionDetails definitionDetails = DefinitionDetails.builder().version(1)
        .templateDetails(templateDetails)
        .recordType(RecordType.INVOICE).build();
    ProcessDetails processDetails = ProcessDetails.builder().recordId("record1")
        .definitionDetails(definitionDetails).ownerId(1l).build();
    Mockito.when(processDetailRepo.findById(Mockito.anyString()))
        .thenReturn(Optional.of(processDetails));

    Map<String, Object> runtimeDefAttributes = new HashMap<>();
    runtimeDefAttributes.put("journalNo", "${journalNo}");
    runtimeDefAttributes.put("assigneeId", "${expertId}");
    runtimeDefAttributes.put("customerId", "${userId}");

    Map<String, String> modelDefAttributes = new HashMap<>();
    modelDefAttributes.put("visibility", "true");
    modelDefAttributes.put("estimate", "3");
    modelDefAttributes.put("taskType", "QB_INVOICE_APPROVAL");
    modelDefAttributes.put(WorkFlowVariables.EVENTS.getName(), "[\"created\",\"completed\"]");

    WorkflowActivityAttributes activityAttributes = WorkflowActivityAttributes.builder()
        .modelAttributes(modelDefAttributes).runtimeAttributes(runtimeDefAttributes).build();

    ActivityDetail activityDetails = ActivityDetail.builder().id(10).activityId("actId1")
        .activityName("actName1").attributes(ObjectConverter.toJson(activityAttributes))
        .type(TaskType.HUMAN_TASK)
        .templateDetails(templateDetails).build();
    Mockito.when(
        activityDetailRepo.findByTemplateDetailsAndActivityId(Mockito.any(TemplateDetails.class),
            Mockito.anyString())).thenReturn(Optional.of(activityDetails));

    Map<String, Object> runtimeAttributes = new HashMap<>(
        workerActionRequest.getVariableMap());

    WorkflowTaskRequest.WorkflowTaskRequestBuilder taskRequestBuilder = WorkflowTaskRequest
        .builder()
        .processInstanceId(workerActionRequest.getProcessInstanceId())
        .id(workerActionRequest.getTaskId())
        .activityId(activityDetails.getActivityId())
        .activityName(activityDetails.getActivityName())
        .taskType(TaskType.HUMAN_TASK)
        .taskAttributes(TaskAttributes.builder()
        		.modelAttributes(activityAttributes.getModelAttributes())
        		.runtimeAttributes(activityAttributes.getRuntimeAttributes())
        		.variables(runtimeAttributes).build());

    WorkflowTaskRequest createRequest = taskRequestBuilder.skipCallback(false)
        .publishExternalTaskEvent(false).publishWorkflowStateTransitionEvent(true)
        .command(TaskCommand.CREATE).status(TASK_STATUS_CREATED)
        .workerId(workerActionRequest.getWorkerId())
        .recordId(processDetails.getRecordId()).build();

    WorkflowTaskRequest completeRequest = taskRequestBuilder.skipCallback(true)
        .publishExternalTaskEvent(false).publishWorkflowStateTransitionEvent(false)
        .command(TaskCommand.COMPLETE).status(TASK_STATUS_COMPLETE)
        .workerId(workerActionRequest.getWorkerId())
        .recordId(processDetails.getRecordId()).build();

    Map<String, Object> responseMap = new HashMap<>();
    responseMap.put("txnId", "txn1");

    Mockito.when(taskManager.execute(Mockito.eq(createRequest)))
        .thenReturn(WorkflowTaskResponse.builder().txnId("txn1").responseMap(responseMap).build());

    Mockito.when(taskManager.execute(Mockito.eq(completeRequest)))
        .thenReturn(WorkflowTaskResponse.builder().txnId("txn1").build());

    Map<String, Object> response = workflowTaskModelHandler.executeAction(workerActionRequest);

    Assert.assertEquals(response.get("txnId"), (Object) "txn1");
    Mockito.verify(taskManager, Mockito.times(1)).execute(Mockito.eq(createRequest));
    Mockito.verify(taskManager, Mockito.times(1)).execute(Mockito.eq(completeRequest));
  }


  /**
   * Successful execution of Async HumanTask with tid.
   */
  @Test
  public void successExecution_HumanTask_async_tidPresent() {
    Map<String, String> extensionProperties = new HashMap<>();
    extensionProperties.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE, "HUMAN_TASK");
    extensionProperties.put("estimate", "1");
    extensionProperties.put("priority", "1");
    extensionProperties.put(ActivityConstants.EXTENSION_PROPERTY_TRANSACTION_MODE, "ASYNC");

    Map<String, String> variableMap = new HashMap<>();
    variableMap.put("assigneeRoles", "[Role1, Role2]");
    variableMap.put("assigneeId", "assignee1");
    variableMap.put(WorkflowConstants.ID_KEY, "record1");
    variableMap.put("txnId", "txn1");
    VariableMap variableMap1 = new VariableMapImpl();
    variableMap1.putAll(variableMap);

    WorkerActionRequest workerActionRequest = WorkerActionRequest.builder().activityId("actId1")
        .extensionProperties(extensionProperties).processInstanceId("processInstance1")
        .taskId("taskId1")
        .ownerId(1l).workerId("worker1").variableMap(variableMap1).build();

    TemplateDetails templateDetails = TemplateDetails.builder().offeringId("offering1").build();
    DefinitionDetails definitionDetails = DefinitionDetails.builder().version(1)
        .templateDetails(templateDetails)
        .recordType(RecordType.INVOICE).build();
    ProcessDetails processDetails = ProcessDetails.builder().recordId("record1")
        .definitionDetails(definitionDetails).ownerId(1l).build();
    Mockito.when(processDetailRepo.findById(Mockito.anyString()))
        .thenReturn(Optional.of(processDetails));

    Map<String, Object> runtimeDefAttributes = new HashMap<>();
    runtimeDefAttributes.put("journalNo", "${journalNo}");
    runtimeDefAttributes.put("assigneeId", "${expertId}");
    runtimeDefAttributes.put("customerId", "${userId}");

    Map<String, String> modelDefAttributes = new HashMap<>();
    modelDefAttributes.put("visibility", "true");
    modelDefAttributes.put("estimate", "3");
    modelDefAttributes.put("taskType", "QB_INVOICE_APPROVAL");

    WorkflowActivityAttributes activityAttributes = WorkflowActivityAttributes.builder()
        .modelAttributes(modelDefAttributes).runtimeAttributes(runtimeDefAttributes).build();

    ActivityDetail activityDetails = ActivityDetail.builder().id(10).activityId("actId1")
        .activityName("actName1").attributes(ObjectConverter.toJson(activityAttributes))
        .type(TaskType.HUMAN_TASK)
        .templateDetails(templateDetails).build();
    Mockito.when(
        activityDetailRepo.findByTemplateDetailsAndActivityId(Mockito.any(TemplateDetails.class),
            Mockito.anyString())).thenReturn(Optional.of(activityDetails));

    Map<String, Object> runtimeAttributes = new HashMap<>(
        workerActionRequest.getVariableMap());

    WorkflowTaskRequest.WorkflowTaskRequestBuilder taskRequestBuilder = WorkflowTaskRequest
        .builder()
        .processInstanceId(workerActionRequest.getProcessInstanceId())
        .id(workerActionRequest.getTaskId())
        .activityId(activityDetails.getActivityId())
        .txnId("txn1").activityName(activityDetails.getActivityName())
        .taskType(TaskType.HUMAN_TASK)
        .taskAttributes(TaskAttributes.builder()
        		.modelAttributes(activityAttributes.getModelAttributes())
        		.runtimeAttributes(activityAttributes.getRuntimeAttributes())
        		.variables(runtimeAttributes).build());

    WorkflowTaskRequest createRequest = taskRequestBuilder.skipCallback(true)
        .publishExternalTaskEvent(false).publishWorkflowStateTransitionEvent(false)
        .command(TaskCommand.CREATE).status(TASK_STATUS_CREATED)
        .workerId(workerActionRequest.getWorkerId())
        .recordId(processDetails.getRecordId()).build();

    WorkflowTaskRequest completeRequest = taskRequestBuilder.skipCallback(false)
        .publishExternalTaskEvent(false).publishWorkflowStateTransitionEvent(false)
        .command(TaskCommand.COMPLETE).status(TASK_STATUS_COMPLETE)
        .workerId(workerActionRequest.getWorkerId())
        .recordId(processDetails.getRecordId()).build();

    Mockito.when(taskManager.execute(Mockito.eq(createRequest)))
        .thenReturn(WorkflowTaskResponse.builder().txnId("txn1").build());

    Mockito.when(taskManager.execute(Mockito.eq(completeRequest)))
        .thenReturn(WorkflowTaskResponse.builder().txnId("txn1").build());

    Map<String, Object> responseMap = workflowTaskModelHandler.executeAction(workerActionRequest);
    Assert.assertNotNull(responseMap.get("actId1_response"));

    Mockito.verify(taskManager, Mockito.times(1)).execute(Mockito.eq(createRequest));
    Mockito.verify(taskManager, Mockito.times(1)).execute(Mockito.eq(completeRequest));
  }


  /**
   * Successful execution of Async HumanTask with tid.
   */
  @Test
  public void successExecution_HumanTask_async_tidPresent_txnVariablePresent() {
    Map<String, String> extensionProperties = new HashMap<>();
    extensionProperties.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE, "HUMAN_TASK");
    extensionProperties.put("estimate", "1");
    extensionProperties.put("priority", "1");
    extensionProperties.put(ActivityConstants.EXTENSION_PROPERTY_TRANSACTION_VARIABLE, "trxnId");
    extensionProperties.put(ActivityConstants.EXTENSION_PROPERTY_TRANSACTION_MODE, "ASYNC");

    Map<String, String> variableMap = new HashMap<>();
    variableMap.put("assigneeRoles", "[Role1, Role2]");
    variableMap.put("assigneeId", "assignee1");
    variableMap.put(WorkflowConstants.ID_KEY, "record1");
    variableMap.put("trxnId", "txn1");
    VariableMap variableMap1 = new VariableMapImpl();
    variableMap1.putAll(variableMap);

    WorkerActionRequest workerActionRequest = WorkerActionRequest.builder().activityId("act1")
        .extensionProperties(extensionProperties).processInstanceId("processInstance1")
        .taskId("taskId1")
        .ownerId(1l).workerId("worker1").variableMap(variableMap1).build();

    TemplateDetails templateDetails = TemplateDetails.builder().offeringId("offering1").build();
    DefinitionDetails definitionDetails = DefinitionDetails.builder().version(1)
        .templateDetails(templateDetails)
        .recordType(RecordType.INVOICE).build();
    ProcessDetails processDetails = ProcessDetails.builder().recordId("record1")
        .definitionDetails(definitionDetails).ownerId(1l).build();
    Mockito.when(processDetailRepo.findById(Mockito.anyString()))
        .thenReturn(Optional.of(processDetails));

    Map<String, Object> runtimeDefAttributes = new HashMap<>();
    runtimeDefAttributes.put("journalNo", "${journalNo}");
    runtimeDefAttributes.put("assigneeId", "${expertId}");
    runtimeDefAttributes.put("customerId", "${userId}");

    Map<String, String> modelDefAttributes = new HashMap<>();
    modelDefAttributes.put("visibility", "true");
    modelDefAttributes.put("estimate", "3");
    modelDefAttributes.put("taskType", "QB_INVOICE_APPROVAL");

    WorkflowActivityAttributes activityAttributes = WorkflowActivityAttributes.builder()
        .modelAttributes(modelDefAttributes).runtimeAttributes(runtimeDefAttributes).build();

    ActivityDetail activityDetails = ActivityDetail.builder().id(10).activityId("actId1")
        .activityName("actName1").type(TaskType.HUMAN_TASK)
        .templateDetails(templateDetails)
        .attributes(ObjectConverter.toJson(activityAttributes)).build();
    Mockito.when(
        activityDetailRepo.findByTemplateDetailsAndActivityId(Mockito.any(TemplateDetails.class),
            Mockito.anyString())).thenReturn(Optional.of(activityDetails));

    Map<String, Object> runtimeAttributes = new HashMap<>(
        workerActionRequest.getVariableMap());

    WorkflowTaskRequest.WorkflowTaskRequestBuilder taskRequestBuilder = WorkflowTaskRequest
        .builder()
        .processInstanceId(workerActionRequest.getProcessInstanceId())
        .id(workerActionRequest.getTaskId())
        .activityId(activityDetails.getActivityId())
        .taskType(TaskType.HUMAN_TASK)
        .txnId("txn1").activityName(activityDetails.getActivityName())
        .taskAttributes(TaskAttributes.builder()
        		.modelAttributes(activityAttributes.getModelAttributes())
        		.runtimeAttributes(activityAttributes.getRuntimeAttributes())
        		.variables(runtimeAttributes).build());

    WorkflowTaskRequest createRequest = taskRequestBuilder.skipCallback(true)
        .publishExternalTaskEvent(false).publishWorkflowStateTransitionEvent(false)
        .command(TaskCommand.CREATE).status(TASK_STATUS_CREATED)
        .workerId(workerActionRequest.getWorkerId())
        .recordId(processDetails.getRecordId()).build();

    WorkflowTaskRequest completeRequest = taskRequestBuilder.skipCallback(false)
        .publishExternalTaskEvent(false).publishWorkflowStateTransitionEvent(false)
        .command(TaskCommand.COMPLETE).status(TASK_STATUS_COMPLETE)
        .workerId(workerActionRequest.getWorkerId())
        .recordId(processDetails.getRecordId()).build();

    Map<String, Object> responseMap = new HashMap<>();
    responseMap.put("trxnId", "txn1");

    Mockito.when(taskManager.execute(Mockito.eq(createRequest)))
        .thenReturn(WorkflowTaskResponse.builder().responseMap(responseMap).build());

    Mockito.when(taskManager.execute(Mockito.eq(completeRequest)))
        .thenReturn(WorkflowTaskResponse.builder().build());

    Map<String, Object> response = workflowTaskModelHandler.executeAction(workerActionRequest);

    Assert.assertNotNull(response);
    Assert.assertNotNull(response.get("trxnId"));
    Assert.assertEquals(response.get("trxnId"), (Object) "txn1");
    Mockito.verify(taskManager, Mockito.times(1)).execute(Mockito.eq(createRequest));
    Mockito.verify(taskManager, Mockito.times(1)).execute(Mockito.eq(completeRequest));
  }


  @Test
  public void testLogErrorMetric() {
    Map<String, String> extensionProperties = new HashMap<>();
    extensionProperties.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE, "SYSTEM_TASK");
    extensionProperties.put(WorkflowConstants.HANDLER_ID, "intuit/test1");

    Map<String, String> variableMap = new HashMap<>();
    VariableMap variableMap1 = new VariableMapImpl();
    variableMap1.putAll(variableMap);

    WorkerActionRequest workerActionRequest = WorkerActionRequest.builder().activityId("act1")
        .extensionProperties(extensionProperties).processInstanceId("processInstance1")
        .taskId("taskId1")
        .ownerId(1l).workerId("worker1").variableMap(variableMap1).build();

    TemplateDetails templateDetails = TemplateDetails.builder().offeringId("offering1").build();
    DefinitionDetails definitionDetails = DefinitionDetails.builder().version(1)
        .templateDetails(templateDetails)
        .recordType(RecordType.INVOICE).build();
    ProcessDetails processDetails = ProcessDetails.builder().recordId("record1")
        .definitionDetails(definitionDetails).ownerId(1l).build();
    Mockito.when(processDetailRepo.findById(Mockito.anyString()))
        .thenReturn(Optional.of(processDetails));

    Map<String, Object> runtimeDefAttributes = new HashMap<>();

    Map<String, String> modelDefAttributes = new HashMap<>();


    WorkflowActivityAttributes activityAttributes = WorkflowActivityAttributes.builder()
        .modelAttributes(modelDefAttributes).runtimeAttributes(runtimeDefAttributes).build();

    ActivityDetail activityDetails = ActivityDetail.builder().id(10).activityId("actId1")
        .activityName("actName1").attributes(ObjectConverter.toJson(activityAttributes))
        .templateDetails(templateDetails).type(TaskType.SYSTEM_TASK).build();
    Mockito.when(
        activityDetailRepo.findByTemplateDetailsAndActivityId(Mockito.any(TemplateDetails.class),
            Mockito.anyString())).thenReturn(Optional.of(activityDetails));

    Mockito.when(workflowSystemTaskHandler.execute(Mockito.any(), Mockito.any()))
        .thenThrow(WorkflowGeneralException.class);
    try {
      workflowTaskModelHandler.execute(workerActionRequest);
      Assert.fail("Exception should be thrown");
    } catch (WorkflowGeneralException workflowGeneralException) {
      Mockito.verify(metricLogger, Mockito.times(1)).logErrorMetric(any(), any(), any());
    }
  }

  @Test
  public void testGetName() {
    Assert.assertEquals(
        TaskHandlerName.WAS_WORKFLOW_CUSTOM_TASK_HANDLER, workflowTaskModelHandler.getName());
  }
}
