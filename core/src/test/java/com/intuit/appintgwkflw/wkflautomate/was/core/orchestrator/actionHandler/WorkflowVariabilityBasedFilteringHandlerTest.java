package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;

import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureManager;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.AuthDetailsService;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.DefinitionServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.AuthDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.TaskHandlerName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.InternalStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.Status;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import com.intuit.appintgwkflw.wkflautomate.was.workflowvariability.DefinitionPendingDeletion;
import com.intuit.appintgwkflw.wkflautomate.was.workflowvariability.WorkflowVariabilityConfigHelper;
import com.intuit.appintgwkflw.wkflautomate.was.workflowvariability.WorkflowVariabilityRecord;
import com.intuit.appintgwkflw.wkflautomate.was.workflowvariability.WorkflowVariabilityRecordConfig;
import com.intuit.v4.Authorization;
import com.intuit.v4.workflows.Definition;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.camunda.bpm.engine.variable.VariableMap;
import org.camunda.bpm.engine.variable.impl.VariableMapImpl;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

public class WorkflowVariabilityBasedFilteringHandlerTest {

  @InjectMocks WorkflowVariabilityBasedFilteringHandler workflowVariabilityBasedFilteringHandler;
  @Mock private MetricLogger metricLogger;
  @Mock private DefinitionServiceHelper definitionServiceHelper;
  @Mock private WorkflowVariabilityConfigHelper workflowsFilterConfigMapper;
  @Mock private TemplateDetails bpmnTemplateDetail;
  @Mock private AuthDetailsService authDetailsService;
  @Mock private FeatureManager featureManager;
  private Authorization authorization = TestHelper.mockAuthorization("123");

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
    ReflectionTestUtils.setField(
        workflowVariabilityBasedFilteringHandler, "metricLogger", metricLogger);

    WorkflowVariabilityRecord wvr1 = new WorkflowVariabilityRecord();
    wvr1.setEntityType("bill");
    wvr1.setWorkflowTypes(Arrays.asList("customApproval"));
    WorkflowVariabilityRecord wvr2 = new WorkflowVariabilityRecord();
    wvr2.setEntityType("report");
    wvr2.setWorkflowTypes(Arrays.asList("customScheduledActions"));

    Mockito.when(
            workflowsFilterConfigMapper.getOfferingWorkflowVariabilityRecords("BILLPAY_ELITE"))
        .thenReturn(WorkflowVariabilityRecordConfig.builder().inclusion(Arrays.asList(wvr1)).exclusion(Collections.emptyList()).build());
    Mockito.when(workflowsFilterConfigMapper.getOfferingWorkflowVariabilityRecords("QBO_PLUS"))
        .thenReturn(WorkflowVariabilityRecordConfig.builder().inclusion(Arrays.asList(wvr2)).exclusion(Collections.emptyList()).build());
    Mockito.when(
            workflowsFilterConfigMapper.getOfferingWorkflowVariabilityRecords("BILL_PAY_BASIC"))
        .thenReturn(null);
    Mockito.when(authDetailsService.getAuthDetailsFromRealmIdSafe(any()))
        .thenReturn(AuthDetails.builder().subscriptionId("subId").build());
  }

  @Test
  public void testGetName() {
    Assert.assertEquals(
        TaskHandlerName.WORKFLOW_VARIABILITY_BASED_FILTER_HANDLER,
        workflowVariabilityBasedFilteringHandler.getName());
  }

  @Test
  public void testMetricLogger() {
    Map<String, String> testSchema = new HashMap<>();
    testSchema.put(WorkflowConstants.INTUIT_REALMID, "1234");
    Mockito.when(definitionServiceHelper.getAllDefinitionListForDeletion(1234L))
        .thenThrow(WorkflowGeneralException.class);
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId(null)
            .processInstanceId("iId")
            .inputVariables(testSchema)
            .handlerId("hId")
            .ownerId(1234L)
            .build();

    try {
      workflowVariabilityBasedFilteringHandler.execute(workerActionRequest);
      Assert.fail();
    } catch (Exception e) {
      Mockito.verify(metricLogger, Mockito.times(1)).logErrorMetric(any(), any(), any());
    }
  }

  @Test
  public void testWorkflowBasedFiltering(){
    VariableMap variableMap = new VariableMapImpl();
    Map<String, String> testSchema = new HashMap<>();
    testSchema.put(WorkflowConstants.INTUIT_REALMID, "1234");
    List<Map<String, String>> workflowFilters= List.of(Map.of("recordType","invoice","workflowType","approval"));
    variableMap.put(WorkflowConstants.WORKFLOW_FILTER,workflowFilters);
    variableMap.put(WorkflowConstants.INTUIT_REALMID, "1234");
    Mockito.when(definitionServiceHelper.getAllDefinitionListForDeletion(1234L))
        .thenThrow(WorkflowGeneralException.class);
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId(null)
            .processInstanceId("iId")
            .inputVariables(testSchema)
            .variableMap(variableMap)
            .handlerId("hId")
            .ownerId(1234L)
            .build();
    try {
      workflowVariabilityBasedFilteringHandler.execute(workerActionRequest);
      Assert.fail();
    } catch (Exception e) {
      Mockito.verify(metricLogger, Mockito.times(1)).logErrorMetric(any(), any(), any());
    }
  }
  @Test
  public void testExecution_Success_NoAuthDetails() {
    Map<String, String> testSchema = new HashMap<>();
    testSchema.put(WorkflowConstants.INTUIT_REALMID, "1234");
    testSchema.put(
        WorkflowConstants.SUBSCRIPTIONS,
        "[{\"offeringId\": \"Intuit.sbe.salsa.default\", \"offeringType\": \"QBO_PLUS\", \"status\": \"ACTIVE\"}]");
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId(null)
            .processInstanceId("iId")
            .inputVariables(testSchema)
            .handlerId("hId")
            .ownerId(1234L)
            .build();

    Mockito.when(authDetailsService.getAuthDetailsFromRealmIdSafe(any()))
        .thenReturn(new AuthDetails());

    Map<String, Object> response =
        workflowVariabilityBasedFilteringHandler.execute(workerActionRequest);
    Assert.assertEquals(response.get("aId_response"), Boolean.TRUE.toString());

    Assert.assertEquals(response.get(WorkflowConstants.AUTH_DETAILS_AVAILABLE), Boolean.FALSE);

    Assert.assertNull(response.get(WorkflowConstants.DEFINITION_DETAILS_PENDING_DELETION));
  }

  @Test
  public void testExecution_Success_singleSubscription() {
    Map<String, String> testSchema = new HashMap<>();
    testSchema.put(WorkflowConstants.INTUIT_REALMID, "1234");
    VariableMap variableMap = new VariableMapImpl();
    testSchema.put(
        WorkflowConstants.SUBSCRIPTIONS,
        "[{\"offeringId\": \"Intuit.sbe.salsa.default\", \"offeringType\": \"QBO_PLUS\", \"status\": \"ACTIVE\"}]");
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId(null)
            .processInstanceId("iId")
            .inputVariables(testSchema)
            .variableMap(variableMap)
            .handlerId("hId")
            .ownerId(1234L)
            .build();

    DefinitionDetails definitionDetail1 =
        TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail, authorization, "defId1");
    definitionDetail1.setOwnerId(1234L);
    definitionDetail1.setStatus(Status.ENABLED);
    definitionDetail1.setRecordType(RecordType.BILL);
    definitionDetail1.setTemplateDetails(
        new TemplateDetails().toBuilder().templateName("customApproval").build());

    DefinitionDetails definitionDetail2 =
        TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail, authorization, "defId2");
    definitionDetail2.setOwnerId(1234L);
    definitionDetail2.setStatus(Status.ENABLED);
    definitionDetail2.setInternalStatus(InternalStatus.STALE_DEFINITION);
    definitionDetail2.setRecordType(RecordType.BILL);
    definitionDetail2.setTemplateDetails(
        new TemplateDetails().toBuilder().templateName("customApproval").build());

    DefinitionDetails definitionDetail3 =
        TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail, authorization, "defId3");
    definitionDetail3.setOwnerId(1234L);
    definitionDetail3.setStatus(Status.DISABLED);
    definitionDetail3.setRecordType(RecordType.REPORT);
    definitionDetail3.setTemplateDetails(
        new TemplateDetails().toBuilder().templateName("customScheduledActions").build());

    DefinitionDetails definitionDetail4 =
        TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail, authorization, "defId4");
    definitionDetail4.setOwnerId(1234L);
    definitionDetail4.setStatus(Status.ENABLED);
    definitionDetail4.setRecordType(RecordType.INVOICE);
    definitionDetail4.setTemplateDetails(
        new TemplateDetails().toBuilder().templateName("customReminder").build());

    List<DefinitionDetails> definitionDetailsList =
        Arrays.asList(definitionDetail1, definitionDetail2, definitionDetail3, definitionDetail4);

    Mockito.when(definitionServiceHelper.getAllDefinitionListForDeletion(1234L))
        .thenReturn(definitionDetailsList);

    Mockito.when(
        featureManager.getBoolean(
            eq(WorkflowConstants.WORKFLOW_VARIABILITY_DOWNGRADE_FF), anyString()
        )).thenReturn(true);

    Map<String, Object> response =
        workflowVariabilityBasedFilteringHandler.execute(workerActionRequest);
    Assert.assertEquals(response.get("aId_response"), Boolean.TRUE.toString());

    List<DefinitionPendingDeletion> definitionPendingDeletions =
        getDefinitionDetailsPendingDeletion(
            Arrays.asList(definitionDetail1, definitionDetail2, definitionDetail4));

    Assert.assertEquals(
        response.get(WorkflowConstants.DEFINITION_DETAILS_PENDING_DELETION),
        definitionPendingDeletions
    );
  }

  @Test
  public void testExecution_Success_singleSubscription_VariabilityFFDisabled() {
    Map<String, String> testSchema = new HashMap<>();
    testSchema.put(WorkflowConstants.INTUIT_REALMID, "1234");
    VariableMap variableMap = new VariableMapImpl();
    testSchema.put(
        WorkflowConstants.SUBSCRIPTIONS,
        "[{\"offeringId\": \"Intuit.sbe.salsa.default\", \"offeringType\": \"QBO_PLUS\", \"status\": \"ACTIVE\"}]");
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId(null)
            .processInstanceId("iId")
            .inputVariables(testSchema)
            .variableMap(variableMap)
            .handlerId("hId")
            .ownerId(1234L)
            .build();

    DefinitionDetails definitionDetail1 =
        TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail, authorization, "defId1");
    definitionDetail1.setOwnerId(1234L);
    definitionDetail1.setStatus(Status.ENABLED);
    definitionDetail1.setRecordType(RecordType.BILL);
    definitionDetail1.setTemplateDetails(
        new TemplateDetails().toBuilder().templateName("customApproval").build());

    DefinitionDetails definitionDetail2 =
        TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail, authorization, "defId2");
    definitionDetail2.setOwnerId(1234L);
    definitionDetail2.setStatus(Status.ENABLED);
    definitionDetail2.setInternalStatus(InternalStatus.STALE_DEFINITION);
    definitionDetail2.setRecordType(RecordType.BILL);
    definitionDetail2.setTemplateDetails(
        new TemplateDetails().toBuilder().templateName("customApproval").build());

    DefinitionDetails definitionDetail3 =
        TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail, authorization, "defId3");
    definitionDetail3.setOwnerId(1234L);
    definitionDetail3.setStatus(Status.DISABLED);
    definitionDetail3.setRecordType(RecordType.REPORT);
    definitionDetail3.setTemplateDetails(
        new TemplateDetails().toBuilder().templateName("customScheduledActions").build());

    DefinitionDetails definitionDetail4 =
        TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail, authorization, "defId4");
    definitionDetail4.setOwnerId(1234L);
    definitionDetail4.setStatus(Status.ENABLED);
    definitionDetail4.setRecordType(RecordType.INVOICE);
    definitionDetail4.setTemplateDetails(
        new TemplateDetails().toBuilder().templateName("customReminder").build());

    List<DefinitionDetails> definitionDetailsList =
        Arrays.asList(definitionDetail1, definitionDetail2, definitionDetail3, definitionDetail4);

    Mockito.when(definitionServiceHelper.getAllDefinitionListForDeletion(1234L))
        .thenReturn(definitionDetailsList);

    Mockito.when(
        featureManager.getBoolean(
            eq(WorkflowConstants.WORKFLOW_VARIABILITY_DOWNGRADE_FF), anyString()
        )).thenReturn(false);

    Map<String, Object> response =
        workflowVariabilityBasedFilteringHandler.execute(workerActionRequest);
    Assert.assertEquals(response.get("aId_response"), Boolean.TRUE.toString());

    List<DefinitionPendingDeletion> definitionPendingDeletions =
        getDefinitionDetailsPendingDeletion(
            Arrays.asList(definitionDetail1, definitionDetail2, definitionDetail3, definitionDetail4));

    Assert.assertEquals(
        response.get(WorkflowConstants.DEFINITION_DETAILS_PENDING_DELETION),
        definitionPendingDeletions
    );
  }

  @Test
  public void testExecution_Success_multipleSubscriptionsActiveInactive() {
    Map<String, String> testSchema = new HashMap<>();
    testSchema.put(WorkflowConstants.INTUIT_REALMID, "1234");
    VariableMap variableMap = new VariableMapImpl();
    testSchema.put(
        WorkflowConstants.SUBSCRIPTIONS,
        "[{\"offeringId\": \"Intuit.sbe.salsa.default\", \"offeringType\": \"QBO_PLUS\", \"status\": \"ACTIVE\"}, {\"offeringId\": \"Intuit.core.billpay\", \"offeringType\": \"BILLPAY_ELITE\", \"status\": \"INACTIVE\"}]");
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId(null)
            .processInstanceId("iId")
            .inputVariables(testSchema)
            .variableMap(variableMap)
            .handlerId("hId")
            .ownerId(1234L)
            .build();

    DefinitionDetails definitionDetail1 =
        TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail, authorization, "defId1");
    definitionDetail1.setOwnerId(1234L);
    definitionDetail1.setStatus(Status.ENABLED);
    definitionDetail1.setRecordType(RecordType.BILL);
    definitionDetail1.setTemplateDetails(
        new TemplateDetails().toBuilder().templateName("customApproval").build());

    DefinitionDetails definitionDetail2 =
        TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail, authorization, "defId2");
    definitionDetail2.setOwnerId(1234L);
    definitionDetail2.setStatus(Status.ENABLED);
    definitionDetail2.setInternalStatus(InternalStatus.STALE_DEFINITION);
    definitionDetail2.setRecordType(RecordType.BILL);
    definitionDetail2.setTemplateDetails(
        new TemplateDetails().toBuilder().templateName("customApproval").build());

    DefinitionDetails definitionDetail3 =
        TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail, authorization, "defId3");
    definitionDetail3.setOwnerId(1234L);
    definitionDetail3.setStatus(Status.DISABLED);
    definitionDetail3.setRecordType(RecordType.REPORT);
    definitionDetail3.setTemplateDetails(
        new TemplateDetails().toBuilder().templateName("customScheduledActions").build());

    DefinitionDetails definitionDetail4 =
        TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail, authorization, "defId4");
    definitionDetail4.setOwnerId(1234L);
    definitionDetail4.setStatus(Status.ENABLED);
    definitionDetail4.setRecordType(RecordType.INVOICE);
    definitionDetail4.setTemplateDetails(
        new TemplateDetails().toBuilder().templateName("customReminder").build());

    List<DefinitionDetails> definitionDetailsList =
        Arrays.asList(definitionDetail1, definitionDetail2, definitionDetail3, definitionDetail4);

    Mockito.when(definitionServiceHelper.getAllDefinitionListForDeletion(1234L))
        .thenReturn(definitionDetailsList);

    Mockito.when(
        featureManager.getBoolean(
            eq(WorkflowConstants.WORKFLOW_VARIABILITY_DOWNGRADE_FF), anyString()
        )).thenReturn(true);

    Map<String, Object> response =
        workflowVariabilityBasedFilteringHandler.execute(workerActionRequest);
    Assert.assertEquals(response.get("aId_response"), Boolean.TRUE.toString());

    List<DefinitionPendingDeletion> definitionPendingDeletions =
        getDefinitionDetailsPendingDeletion(
            Arrays.asList(definitionDetail1, definitionDetail2, definitionDetail4));

    Assert.assertEquals(
        response.get(WorkflowConstants.DEFINITION_DETAILS_PENDING_DELETION),
        definitionPendingDeletions
    );
  }

  @Test
  public void testExecution_Success_multipleSubscriptionsActiveInactive_VariabilityFFDisabled() {
    Map<String, String> testSchema = new HashMap<>();
    VariableMap variableMap = new VariableMapImpl();
    testSchema.put(WorkflowConstants.INTUIT_REALMID, "1234");
    testSchema.put(
        WorkflowConstants.SUBSCRIPTIONS,
        "[{\"offeringId\": \"Intuit.sbe.salsa.default\", \"offeringType\": \"QBO_PLUS\", \"status\": \"ACTIVE\"}, {\"offeringId\": \"Intuit.core.billpay\", \"offeringType\": \"BILLPAY_ELITE\", \"status\": \"INACTIVE\"}]");
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId(null)
            .processInstanceId("iId")
            .inputVariables(testSchema)
            .variableMap(variableMap)
            .handlerId("hId")
            .ownerId(1234L)
            .build();

    DefinitionDetails definitionDetail1 =
        TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail, authorization, "defId1");
    definitionDetail1.setOwnerId(1234L);
    definitionDetail1.setStatus(Status.ENABLED);
    definitionDetail1.setRecordType(RecordType.BILL);
    definitionDetail1.setTemplateDetails(
        new TemplateDetails().toBuilder().templateName("customApproval").build());

    DefinitionDetails definitionDetail2 =
        TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail, authorization, "defId2");
    definitionDetail2.setOwnerId(1234L);
    definitionDetail2.setStatus(Status.ENABLED);
    definitionDetail2.setInternalStatus(InternalStatus.STALE_DEFINITION);
    definitionDetail2.setRecordType(RecordType.BILL);
    definitionDetail2.setTemplateDetails(
        new TemplateDetails().toBuilder().templateName("customApproval").build());

    DefinitionDetails definitionDetail3 =
        TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail, authorization, "defId3");
    definitionDetail3.setOwnerId(1234L);
    definitionDetail3.setStatus(Status.DISABLED);
    definitionDetail3.setRecordType(RecordType.REPORT);
    definitionDetail3.setTemplateDetails(
        new TemplateDetails().toBuilder().templateName("customScheduledActions").build());

    DefinitionDetails definitionDetail4 =
        TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail, authorization, "defId4");
    definitionDetail4.setOwnerId(1234L);
    definitionDetail4.setStatus(Status.ENABLED);
    definitionDetail4.setRecordType(RecordType.INVOICE);
    definitionDetail4.setTemplateDetails(
        new TemplateDetails().toBuilder().templateName("customReminder").build());

    List<DefinitionDetails> definitionDetailsList =
        Arrays.asList(definitionDetail1, definitionDetail2, definitionDetail3, definitionDetail4);

    Mockito.when(definitionServiceHelper.getAllDefinitionListForDeletion(1234L))
        .thenReturn(definitionDetailsList);

    Mockito.when(
        featureManager.getBoolean(
            eq(WorkflowConstants.WORKFLOW_VARIABILITY_DOWNGRADE_FF), anyString()
        )).thenReturn(false);

    Map<String, Object> response =
        workflowVariabilityBasedFilteringHandler.execute(workerActionRequest);
    Assert.assertEquals(response.get("aId_response"), Boolean.TRUE.toString());

    List<DefinitionPendingDeletion> definitionPendingDeletions =
        getDefinitionDetailsPendingDeletion(
            Arrays.asList(definitionDetail1, definitionDetail2, definitionDetail3, definitionDetail4));

    Assert.assertEquals(
        response.get(WorkflowConstants.DEFINITION_DETAILS_PENDING_DELETION),
        definitionPendingDeletions
    );
  }

  @Test
  public void testExecution_Success_multipleActiveSubscriptions() {
    Map<String, String> testSchema = new HashMap<>();
    testSchema.put(WorkflowConstants.INTUIT_REALMID, "1234");
    VariableMap variableMap = new VariableMapImpl();
    testSchema.put(
        WorkflowConstants.SUBSCRIPTIONS,
        "[{\"offeringId\": \"Intuit.sbe.salsa.default\", \"offeringType\": \"QBO_PLUS\", \"status\": \"ACTIVE\"}, {\"offeringId\": \"Intuit.core.billpay\", \"offeringType\": \"BILLPAY_ELITE\", \"status\": \"ACTIVE\"}]");
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId(null)
            .processInstanceId("iId")
            .inputVariables(testSchema)
            .variableMap(variableMap)
            .handlerId("hId")
            .ownerId(1234L)
            .build();

    DefinitionDetails definitionDetail1 =
        TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail, authorization, "defId1");
    definitionDetail1.setOwnerId(1234L);
    definitionDetail1.setStatus(Status.ENABLED);
    definitionDetail1.setRecordType(RecordType.BILL);
    definitionDetail1.setTemplateDetails(
        new TemplateDetails().toBuilder().templateName("customApproval").build());

    DefinitionDetails definitionDetail2 =
        TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail, authorization, "defId2");
    definitionDetail2.setOwnerId(1234L);
    definitionDetail2.setStatus(Status.ENABLED);
    definitionDetail2.setInternalStatus(InternalStatus.STALE_DEFINITION);
    definitionDetail2.setRecordType(RecordType.BILL);
    definitionDetail2.setTemplateDetails(
        new TemplateDetails().toBuilder().templateName("customApproval").build());

    DefinitionDetails definitionDetail3 =
        TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail, authorization, "defId3");
    definitionDetail3.setOwnerId(1234L);
    definitionDetail3.setStatus(Status.DISABLED);
    definitionDetail3.setRecordType(RecordType.REPORT);
    definitionDetail3.setTemplateDetails(
        new TemplateDetails().toBuilder().templateName("customScheduledActions").build());

    DefinitionDetails definitionDetail4 =
        TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail, authorization, "defId4");
    definitionDetail4.setOwnerId(1234L);
    definitionDetail4.setStatus(Status.ENABLED);
    definitionDetail4.setRecordType(RecordType.INVOICE);
    definitionDetail4.setTemplateDetails(
        new TemplateDetails().toBuilder().templateName("customReminder").build());

    List<DefinitionDetails> definitionDetailsList =
        Arrays.asList(definitionDetail1, definitionDetail2, definitionDetail3, definitionDetail4);
    Mockito.when(definitionServiceHelper.getAllDefinitionListForDeletion(1234L))
        .thenReturn(definitionDetailsList);

    Mockito.when(
        featureManager.getBoolean(
            eq(WorkflowConstants.WORKFLOW_VARIABILITY_DOWNGRADE_FF), anyString()
        )).thenReturn(true);

    Map<String, Object> response =
        workflowVariabilityBasedFilteringHandler.execute(workerActionRequest);
    Assert.assertEquals(response.get("aId_response"), Boolean.TRUE.toString());

    List<DefinitionPendingDeletion> definitionPendingDeletions =
        getDefinitionDetailsPendingDeletion(Arrays.asList(definitionDetail4));

    Assert.assertEquals(
        response.get(WorkflowConstants.DEFINITION_DETAILS_PENDING_DELETION),
        definitionPendingDeletions
    );
  }

  @Test
  public void testExecution_Success_multipleActiveSubscriptions_VariabilityFFDisabled() {
    Map<String, String> testSchema = new HashMap<>();
    testSchema.put(WorkflowConstants.INTUIT_REALMID, "1234");
    VariableMap variableMap = new VariableMapImpl();
    testSchema.put(
        WorkflowConstants.SUBSCRIPTIONS,
        "[{\"offeringId\": \"Intuit.sbe.salsa.default\", \"offeringType\": \"QBO_PLUS\", \"status\": \"ACTIVE\"}, {\"offeringId\": \"Intuit.core.billpay\", \"offeringType\": \"BILLPAY_ELITE\", \"status\": \"ACTIVE\"}]");
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId(null)
            .processInstanceId("iId")
            .inputVariables(testSchema)
            .variableMap(variableMap)
            .handlerId("hId")
            .ownerId(1234L)
            .build();

    DefinitionDetails definitionDetail1 =
        TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail, authorization, "defId1");
    definitionDetail1.setOwnerId(1234L);
    definitionDetail1.setStatus(Status.ENABLED);
    definitionDetail1.setRecordType(RecordType.BILL);
    definitionDetail1.setTemplateDetails(
        new TemplateDetails().toBuilder().templateName("customApproval").build());

    DefinitionDetails definitionDetail2 =
        TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail, authorization, "defId2");
    definitionDetail2.setOwnerId(1234L);
    definitionDetail2.setStatus(Status.ENABLED);
    definitionDetail2.setInternalStatus(InternalStatus.STALE_DEFINITION);
    definitionDetail2.setRecordType(RecordType.BILL);
    definitionDetail2.setTemplateDetails(
        new TemplateDetails().toBuilder().templateName("customApproval").build());

    DefinitionDetails definitionDetail3 =
        TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail, authorization, "defId3");
    definitionDetail3.setOwnerId(1234L);
    definitionDetail3.setStatus(Status.DISABLED);
    definitionDetail3.setRecordType(RecordType.REPORT);
    definitionDetail3.setTemplateDetails(
        new TemplateDetails().toBuilder().templateName("customScheduledActions").build());

    DefinitionDetails definitionDetail4 =
        TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail, authorization, "defId4");
    definitionDetail4.setOwnerId(1234L);
    definitionDetail4.setStatus(Status.ENABLED);
    definitionDetail4.setRecordType(RecordType.INVOICE);
    definitionDetail4.setTemplateDetails(
        new TemplateDetails().toBuilder().templateName("customReminder").build());

    List<DefinitionDetails> definitionDetailsList =
        Arrays.asList(definitionDetail1, definitionDetail2, definitionDetail3, definitionDetail4);
    Mockito.when(definitionServiceHelper.getAllDefinitionListForDeletion(1234L))
        .thenReturn(definitionDetailsList);

    Mockito.when(
        featureManager.getBoolean(
            eq(WorkflowConstants.WORKFLOW_VARIABILITY_DOWNGRADE_FF), anyString()
        )).thenReturn(false);

    Map<String, Object> response =
        workflowVariabilityBasedFilteringHandler.execute(workerActionRequest);

    Assert.assertEquals(response.get("aId_response"), Boolean.TRUE.toString());

    List<DefinitionPendingDeletion> definitionPendingDeletions =
        getDefinitionDetailsPendingDeletion(
            Arrays.asList(definitionDetail1, definitionDetail2, definitionDetail3, definitionDetail4)
        );

    Assert.assertEquals(
        response.get(WorkflowConstants.DEFINITION_DETAILS_PENDING_DELETION),
        definitionPendingDeletions
    );
  }

  @Test
  public void testExecution_Success_noActiveSubscription() {
    Map<String, String> testSchema = new HashMap<>();
    testSchema.put(WorkflowConstants.INTUIT_REALMID, "1234");
    testSchema.put(WorkflowConstants.SUBSCRIPTIONS, null);
    VariableMap variableMap = new VariableMapImpl();

    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId(null)
            .processInstanceId("iId")
            .inputVariables(testSchema)
            .variableMap(variableMap)
            .handlerId("hId")
            .ownerId(1234L)
            .build();

    DefinitionDetails definitionDetail1 =
        TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail, authorization, "defId1");
    definitionDetail1.setOwnerId(1234L);
    definitionDetail1.setStatus(Status.ENABLED);
    definitionDetail1.setRecordType(RecordType.BILL);
    definitionDetail1.setTemplateDetails(
        new TemplateDetails().toBuilder().templateName("customApproval").build());

    DefinitionDetails definitionDetail2 =
        TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail, authorization, "defId2");
    definitionDetail2.setOwnerId(1234L);
    definitionDetail2.setStatus(Status.ENABLED);
    definitionDetail2.setInternalStatus(InternalStatus.STALE_DEFINITION);
    definitionDetail2.setRecordType(RecordType.BILL);
    definitionDetail2.setTemplateDetails(
        new TemplateDetails().toBuilder().templateName("customApproval").build());

    DefinitionDetails definitionDetail3 =
        TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail, authorization, "defId3");
    definitionDetail3.setOwnerId(1234L);
    definitionDetail3.setStatus(Status.DISABLED);
    definitionDetail3.setRecordType(RecordType.REPORT);
    definitionDetail3.setTemplateDetails(
        new TemplateDetails().toBuilder().templateName("customScheduledActions").build());

    DefinitionDetails definitionDetail4 =
        TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail, authorization, "defId4");
    definitionDetail4.setOwnerId(1234L);
    definitionDetail4.setStatus(Status.ENABLED);
    definitionDetail4.setRecordType(RecordType.INVOICE);
    definitionDetail4.setTemplateDetails(
        new TemplateDetails().toBuilder().templateName("customReminder").build());

    List<DefinitionDetails> definitionDetailsList =
        Arrays.asList(definitionDetail1, definitionDetail2, definitionDetail3, definitionDetail4);
    Mockito.when(definitionServiceHelper.getAllDefinitionListForDeletion(1234L))
        .thenReturn(definitionDetailsList);

    Mockito.when(
        featureManager.getBoolean(
            eq(WorkflowConstants.WORKFLOW_VARIABILITY_DOWNGRADE_FF), anyString()
        )).thenReturn(true);

    Map<String, Object> response =
        workflowVariabilityBasedFilteringHandler.execute(workerActionRequest);
    Assert.assertEquals(response.get("aId_response"), Boolean.TRUE.toString());

    List<DefinitionPendingDeletion> definitionPendingDeletions =
        getDefinitionDetailsPendingDeletion(
            Arrays.asList(
                definitionDetail1, definitionDetail2, definitionDetail3, definitionDetail4));

    Assert.assertEquals(
        response.get(WorkflowConstants.DEFINITION_DETAILS_PENDING_DELETION),
        definitionPendingDeletions
    );
  }

  @Test
  public void testExecution_Success_emptyActiveSubscription() {
    Map<String, String> testSchema = new HashMap<>();
    testSchema.put(WorkflowConstants.INTUIT_REALMID, "1234");
    VariableMap variableMap = new VariableMapImpl();
    testSchema.put(WorkflowConstants.SUBSCRIPTIONS, "[]");
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId(null)
            .processInstanceId("iId")
            .inputVariables(testSchema)
            .variableMap(variableMap)
            .handlerId("hId")
            .ownerId(1234L)
            .build();

    DefinitionDetails definitionDetail1 =
        TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail, authorization, "defId1");
    definitionDetail1.setOwnerId(1234L);
    definitionDetail1.setStatus(Status.ENABLED);
    definitionDetail1.setRecordType(RecordType.BILL);
    definitionDetail1.setTemplateDetails(
        new TemplateDetails().toBuilder().templateName("customApproval").build());

    DefinitionDetails definitionDetail2 =
        TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail, authorization, "defId2");
    definitionDetail2.setOwnerId(1234L);
    definitionDetail2.setStatus(Status.ENABLED);
    definitionDetail2.setInternalStatus(InternalStatus.STALE_DEFINITION);
    definitionDetail2.setRecordType(RecordType.BILL);
    definitionDetail2.setTemplateDetails(
        new TemplateDetails().toBuilder().templateName("customApproval").build());

    DefinitionDetails definitionDetail3 =
        TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail, authorization, "defId3");
    definitionDetail3.setOwnerId(1234L);
    definitionDetail3.setStatus(Status.DISABLED);
    definitionDetail3.setRecordType(RecordType.REPORT);
    definitionDetail3.setTemplateDetails(
        new TemplateDetails().toBuilder().templateName("customScheduledActions").build());

    DefinitionDetails definitionDetail4 =
        TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail, authorization, "defId4");
    definitionDetail4.setOwnerId(1234L);
    definitionDetail4.setStatus(Status.ENABLED);
    definitionDetail4.setRecordType(RecordType.INVOICE);
    definitionDetail4.setTemplateDetails(
        new TemplateDetails().toBuilder().templateName("customReminder").build());

    List<DefinitionDetails> definitionDetailsList =
        Arrays.asList(definitionDetail1, definitionDetail2, definitionDetail3, definitionDetail4);
    Mockito.when(definitionServiceHelper.getAllDefinitionListForDeletion(1234L))
        .thenReturn(definitionDetailsList);

    Mockito.when(
        featureManager.getBoolean(
            eq(WorkflowConstants.WORKFLOW_VARIABILITY_DOWNGRADE_FF), anyString()
        )).thenReturn(true);

    Map<String, Object> response =
        workflowVariabilityBasedFilteringHandler.execute(workerActionRequest);
    Assert.assertEquals(response.get("aId_response"), Boolean.TRUE.toString());

    List<DefinitionPendingDeletion> definitionPendingDeletions =
        getDefinitionDetailsPendingDeletion(
            Arrays.asList(
                definitionDetail1, definitionDetail2, definitionDetail3, definitionDetail4));

    Assert.assertEquals(
        response.get(WorkflowConstants.DEFINITION_DETAILS_PENDING_DELETION),
        definitionPendingDeletions
    );
  }

  @Test
  public void testExecution_Success_noDefinitionsForOwnerId() {
    Map<String, String> testSchema = new HashMap<>();
    testSchema.put(WorkflowConstants.INTUIT_REALMID, "1234");
    VariableMap variableMap = new VariableMapImpl();
    testSchema.put(
        WorkflowConstants.SUBSCRIPTIONS,
        "[{\"offeringId\": \"Intuit.sbe.salsa.default\", \"offeringType\": \"QBO_PLUS\"}, {\"offeringId\": \"Intuit.core.billpay\", \"offeringType\": \"BILLPAY_ELITE\"}]");
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId(null)
            .processInstanceId("iId")
            .inputVariables(testSchema)
            .variableMap(variableMap)
            .handlerId("hId")
            .ownerId(1234L)
            .build();
    Mockito.when(definitionServiceHelper.getAllDefinitionListForDeletion(1234L))
        .thenReturn(Collections.emptyList());

    Mockito.when(
        featureManager.getBoolean(
            eq(WorkflowConstants.WORKFLOW_VARIABILITY_DOWNGRADE_FF), anyString()
        )).thenReturn(true);

    Map<String, Object> response =
        workflowVariabilityBasedFilteringHandler.execute(workerActionRequest);
    Assert.assertEquals(response.get("aId_response"), Boolean.TRUE.toString());
    Assert.assertEquals(response.get(WorkflowConstants.DEFINITION_DETAILS_PENDING_DELETION), new ArrayList<>());
  }

  @Test
  public void testExecution_Success_noWorkflowVariabilityRecordMapping() {
    Map<String, String> testSchema = new HashMap<>();
    testSchema.put(WorkflowConstants.INTUIT_REALMID, "1234");
    VariableMap variableMap = new VariableMapImpl();
    testSchema.put(
        WorkflowConstants.SUBSCRIPTIONS,
        "[{\"offeringId\": \"Intuit.sbe.salsa.default\", \"offeringType\": \"QBO_SIMPLESTART\"}]");
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId(null)
            .processInstanceId("iId")
            .inputVariables(testSchema)
            .variableMap(variableMap)
            .handlerId("hId")
            .ownerId(1234L)
            .build();

    DefinitionDetails definitionDetail1 =
        TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail, authorization, "defId1");
    definitionDetail1.setOwnerId(1234L);
    definitionDetail1.setStatus(Status.ENABLED);
    definitionDetail1.setRecordType(RecordType.BILL);
    definitionDetail1.setTemplateDetails(
        new TemplateDetails().toBuilder().templateName("customApproval").build());

    DefinitionDetails definitionDetail2 =
        TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail, authorization, "defId2");
    definitionDetail2.setOwnerId(1234L);
    definitionDetail2.setStatus(Status.ENABLED);
    definitionDetail2.setInternalStatus(InternalStatus.STALE_DEFINITION);
    definitionDetail2.setRecordType(RecordType.BILL);
    definitionDetail2.setTemplateDetails(
        new TemplateDetails().toBuilder().templateName("customApproval").build());

    DefinitionDetails definitionDetail3 =
        TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail, authorization, "defId3");
    definitionDetail3.setOwnerId(1234L);
    definitionDetail3.setStatus(Status.DISABLED);
    definitionDetail3.setRecordType(RecordType.REPORT);
    definitionDetail3.setTemplateDetails(
        new TemplateDetails().toBuilder().templateName("customScheduledActions").build());

    DefinitionDetails definitionDetail4 =
        TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail, authorization, "defId4");
    definitionDetail4.setOwnerId(1234L);
    definitionDetail4.setStatus(Status.ENABLED);
    definitionDetail4.setRecordType(RecordType.INVOICE);
    definitionDetail4.setTemplateDetails(
        new TemplateDetails().toBuilder().templateName("customReminder").build());

    List<DefinitionDetails> definitionDetailsList =
        Arrays.asList(definitionDetail1, definitionDetail2, definitionDetail3, definitionDetail4);
    Mockito.when(definitionServiceHelper.getAllDefinitionListForDeletion(1234L))
        .thenReturn(definitionDetailsList);

    Mockito.when(
        featureManager.getBoolean(
            eq(WorkflowConstants.WORKFLOW_VARIABILITY_DOWNGRADE_FF), anyString()
        )).thenReturn(true);

    Map<String, Object> response =
        workflowVariabilityBasedFilteringHandler.execute(workerActionRequest);
    Assert.assertEquals(response.get("aId_response"), Boolean.TRUE.toString());
    List<DefinitionPendingDeletion> definitionPendingDeletions =
        getDefinitionDetailsPendingDeletion(
            Arrays.asList(
                definitionDetail1, definitionDetail2, definitionDetail3, definitionDetail4));

    Assert.assertEquals(
        response.get(WorkflowConstants.DEFINITION_DETAILS_PENDING_DELETION),
        definitionPendingDeletions
    );
  }

  private List<DefinitionPendingDeletion> getDefinitionDetailsPendingDeletion(
      List<DefinitionDetails> definitionDetailsList) {
    return definitionDetailsList.stream()
        .map(DefinitionPendingDeletion::buildFromDefinitionDetails)
        .collect(Collectors.toList());
  }
}
