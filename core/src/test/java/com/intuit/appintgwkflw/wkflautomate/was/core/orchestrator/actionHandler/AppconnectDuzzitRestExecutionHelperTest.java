package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.EXTERNAL_TASK_ACCESS_FAILURE;
import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.EXTERNAL_TASK_HANDLER_ERROR;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.INTUIT_REALMID;
import static org.mockito.ArgumentMatchers.any;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.AppConnectConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.AppConnectDuzzitException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.httpClient.AppConnectWASClient;
import com.intuit.appintgwkflw.wkflautomate.was.core.camunda.template.schema.SchemaDecoder;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.AuthDetailsService;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.WorkerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.AuthDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.request.WorkflowTaskHandlerInput;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.response.WorkflowTaskHandlerResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails.ParameterDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpStatus;

/**
 * <AUTHOR>
 */
public class AppconnectDuzzitRestExecutionHelperTest {

  @Mock private WorkerUtil workerUtil;
  @Mock AuthDetailsService authDetailsService;
  @Mock private AppConnectWASClient wasHttpClient;
  @Mock private AppConnectWorkflowTaskHandlerHelper taskHandlerHelper;
  @Mock private AppConnectConfig appConnectConfig;

  @InjectMocks
  private AppconnectDuzzitRestExecutionHelper appconnectDuzzitRestExecutionHelper;
  private static Map<String, String> schema = new HashMap<>();

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
  }

  private static final String parametersSchema =
      TestHelper.readResourceAsString("schema/testData/parameters.json");

  static {
    schema.put(WorkFlowVariables.PARAMETERS_KEY.getName(), parametersSchema);
    schema.put(INTUIT_REALMID, "1234");
  }

  @SuppressWarnings("unchecked")
  @Test
  public void testExecuteAppActionSuccess() {
    String handlerId = "hid";
    String dependency = "";
    String responseError = "";
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId(handlerId)
            .taskId("externalTaskId")
            .ownerId(1234L)
            .build();

    WorkflowTaskHandlerResponse handlerResponse = new WorkflowTaskHandlerResponse();
    handlerResponse.setSuccess("true");
    Map<String, Object> data = new HashMap<>();
    data.put("dummy",new HashMap<>().put("key","value"));
    handlerResponse.setData(data);

    WASHttpResponse<Object> response =
        WASHttpResponse.<Object>builder()
            .status(HttpStatus.OK)
            .response(handlerResponse)
            .isSuccess2xx(true)
            .build();
    List<WorkflowTaskHandlerInput> mockTaskHandlerInputs = new ArrayList<>();
    WorkflowTaskHandlerInput testHandlerInput = new WorkflowTaskHandlerInput();
    testHandlerInput.setName("testName");
    testHandlerInput.setValue("testValue");
    mockTaskHandlerInputs.add(testHandlerInput);

    AuthDetails authDetails = new AuthDetails();
    authDetails.setSubscriptionId("123");

    Map<String, Object> res = new HashMap<>();
    res.put(workerActionRequest.getActivityId()
        + WorkflowConstants.UNDERSCORE
        + WorkFlowVariables.RESPONSE.getName(), "true");

    Mockito.when(taskHandlerHelper.prepareTaskHandlerInputs(any(), any()))
        .thenReturn(mockTaskHandlerInputs);
    Mockito.when(taskHandlerHelper.getAppConnectDuzzitException(any(), any()))
        .thenReturn(
            new AppConnectDuzzitException(
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorCode(),
                    dependency.toUpperCase(),
                    formatHandlerId(handlerId)),
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorMessage(),
                    dependency,
                    handlerId,
                    responseError)));

    Mockito.when(taskHandlerHelper.prepareTaskHandlerResponse(any(), any())).thenReturn(res);

    Mockito.when(authDetailsService.getAuthDetailsFromRealmId("1234")).thenReturn(authDetails);
    Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);
    Mockito.when(wasHttpClient.httpResponse(ArgumentMatchers.any())).thenReturn(response);
    Mockito.when(appConnectConfig.getConnectorEndpoint()).thenReturn("https://e2e.api.intuit.com/appconnect/connector");
    Mockito.when(appConnectConfig.getProviderAppId()).thenReturn("AP13702");

    Map<String, Object> resp = appconnectDuzzitRestExecutionHelper.executeWorkflowActionRequest(workerActionRequest, getParameterDetailsMap(workerActionRequest.getInputVariables()));

    Assert.assertTrue(
        resp.get(
                workerActionRequest.getActivityId()
                    + WorkflowConstants.UNDERSCORE
                    + WorkFlowVariables.RESPONSE.getName())
            .equals("true"));
  }

  @SuppressWarnings("unchecked")
  @Test
  public void testExecuteAppResponseDataFailure() {
    String handlerId = "hid";
    String dependency = "";
    String responseError = "";
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId(handlerId)
            .taskId("externalTaskId")
            .ownerId(1234L)
            .build();

    WorkflowTaskHandlerResponse handlerResponse = new WorkflowTaskHandlerResponse();
    handlerResponse.setSuccess("true");

    WASHttpResponse<Object> response =
        WASHttpResponse.<Object>builder()
            .status(HttpStatus.OK)
            .response(handlerResponse)
            .isSuccess2xx(true)
            .build();
    List<WorkflowTaskHandlerInput> mockTaskHandlerInputs = new ArrayList<>();
    WorkflowTaskHandlerInput testHandlerInput = new WorkflowTaskHandlerInput();
    testHandlerInput.setName("testName");
    testHandlerInput.setValue("testValue");
    mockTaskHandlerInputs.add(testHandlerInput);

    AuthDetails authDetails = new AuthDetails();
    authDetails.setSubscriptionId("123");

    Map<String, Object> res = new HashMap<>();
    res.put(workerActionRequest.getActivityId()
        + WorkflowConstants.UNDERSCORE
        + WorkFlowVariables.RESPONSE.getName(), "true");

    Mockito.when(taskHandlerHelper.prepareTaskHandlerInputs(any(), any()))
        .thenReturn(mockTaskHandlerInputs);
    Mockito.when(taskHandlerHelper.getAppConnectDuzzitException(any(), any()))
        .thenReturn(
            new AppConnectDuzzitException(
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorCode(),
                    dependency.toUpperCase(),
                    formatHandlerId(handlerId)),
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorMessage(),
                    dependency,
                    handlerId,
                    responseError)));
    Mockito.when(taskHandlerHelper.prepareTaskHandlerResponse(any(), any())).thenReturn(res);

    Mockito.when(authDetailsService.getAuthDetailsFromRealmId("1234")).thenReturn(authDetails);
    Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);
    Mockito.when(wasHttpClient.httpResponse(ArgumentMatchers.any())).thenReturn(response);
    Mockito.when(appConnectConfig.getConnectorEndpoint()).thenReturn("https://e2e.api.intuit.com/appconnect/connector");
    Mockito.when(appConnectConfig.getProviderAppId()).thenReturn("AP13702");

    Map<String, Object> resp = appconnectDuzzitRestExecutionHelper.executeWorkflowActionRequest(workerActionRequest, getParameterDetailsMap(workerActionRequest.getInputVariables()));

    Assert.assertTrue(
        resp.get(
                workerActionRequest.getActivityId()
                    + WorkflowConstants.UNDERSCORE
                    + WorkFlowVariables.RESPONSE.getName())
            .equals("true"));
  }

  @SuppressWarnings("unchecked")
  @Test
  public void testExecuteAppActionError() {
    String handlerId = "hid";
    String dependency = "";
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId(handlerId)
            .taskId("externalTaskId")
            .ownerId(1234L)
            .build();

    WorkflowTaskHandlerResponse handlerResponse = new WorkflowTaskHandlerResponse();
    handlerResponse.setSuccess("false");
    handlerResponse.setError("error");
    WASHttpResponse<Object> response =
        WASHttpResponse.builder()
            .status(HttpStatus.OK)
            .response(handlerResponse)
            .isSuccess2xx(false)
            .build();
    List<WorkflowTaskHandlerInput> mockTaskHandlerInputs = new ArrayList<>();
    WorkflowTaskHandlerInput testHandlerInput = new WorkflowTaskHandlerInput();
    testHandlerInput.setName("testName");
    testHandlerInput.setValue("testValue");
    mockTaskHandlerInputs.add(testHandlerInput);

    AuthDetails authDetails = new AuthDetails();
    authDetails.setSubscriptionId("123");

    Map<String, Object> res = new HashMap<>();
    res.put(
        workerActionRequest.getActivityId()
            + WorkflowConstants.UNDERSCORE
            + WorkFlowVariables.RESPONSE.getName(),
        "true");

    Mockito.when(taskHandlerHelper.prepareTaskHandlerInputs(any(), any()))
        .thenReturn(mockTaskHandlerInputs);
    Mockito.when(taskHandlerHelper.getAppConnectDuzzitException(any(), any()))
        .thenReturn(
            new AppConnectDuzzitException(
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorCode(),
                    dependency.toUpperCase(),
                    formatHandlerId(handlerId)),
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorMessage(),
                    dependency,
                    handlerId,
                    handlerResponse.getError())));
    Mockito.when(taskHandlerHelper.prepareTaskHandlerResponse(any(), any())).thenReturn(res);

    Mockito.when(authDetailsService.getAuthDetailsFromRealmId("1234")).thenReturn(authDetails);
    Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);
    Mockito.when(wasHttpClient.httpResponse(ArgumentMatchers.any())).thenReturn(response);
    Mockito.when(appConnectConfig.getConnectorEndpoint())
        .thenReturn("https://e2e.api.intuit.com/appconnect/connector");
    Mockito.when(appConnectConfig.getProviderAppId()).thenReturn("AP13702");

    try {
      appconnectDuzzitRestExecutionHelper.executeWorkflowActionRequest(workerActionRequest, getParameterDetailsMap(workerActionRequest.getInputVariables()));
      Assert.fail("The above method will throw exception.");
    } catch (Exception e) {
      Assert.assertTrue(e.getMessage().contains("External task failure"));
    }
  }

  @Test
  public void testExecuteAppActionException() {
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId(null)
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId("hId")
            .ownerId(1234L)
            .build();

    AuthDetails authDetails = new AuthDetails();
    authDetails.setSubscriptionId("123");
    Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);
    Mockito.when(authDetailsService.getAuthDetailsFromRealmId("1234")).thenThrow(
        WorkflowGeneralException.class);

    try {
      appconnectDuzzitRestExecutionHelper.executeWorkflowActionRequest(workerActionRequest, getParameterDetailsMap(workerActionRequest.getInputVariables()));
      Assert.fail();
    } catch (Exception e) {

    }
  }

  @Test
  public void testExecuteAppAction_wasCreatetask() {
    String handlerId = "intuit-workflows/was-create-task";
    String dependency = WorkflowConstants.PROJECT_SERVICE;
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId(handlerId)
            .ownerId(1234L)
            .taskId("externalTaskId")
            .build();

    WorkflowTaskHandlerResponse handlerResponse = new WorkflowTaskHandlerResponse();
    handlerResponse.setSuccess("false");
    handlerResponse.setError("error");
    WASHttpResponse<Object> response =
        WASHttpResponse.builder()
            .status(HttpStatus.OK)
            .response(handlerResponse)
            .isSuccess2xx(false)
            .build();
    List<WorkflowTaskHandlerInput> mockTaskHandlerInputs = new ArrayList<>();
    WorkflowTaskHandlerInput testHandlerInput = new WorkflowTaskHandlerInput();
    testHandlerInput.setName("testName");
    testHandlerInput.setValue("testValue");
    mockTaskHandlerInputs.add(testHandlerInput);

    AuthDetails authDetails = new AuthDetails();
    authDetails.setSubscriptionId("123");

    Map<String, Object> res = new HashMap<>();
    res.put(
        workerActionRequest.getActivityId()
            + WorkflowConstants.UNDERSCORE
            + WorkFlowVariables.RESPONSE.getName(),
        "true");

    Mockito.when(taskHandlerHelper.prepareTaskHandlerInputs(any(), any()))
        .thenReturn(mockTaskHandlerInputs);
    Mockito.when(taskHandlerHelper.getAppConnectDuzzitException(any(), any()))
        .thenReturn(
            new AppConnectDuzzitException(
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorCode(),
                    dependency.toUpperCase(),
                    formatHandlerId(handlerId)),
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorMessage(),
                    dependency,
                    handlerId,
                    handlerResponse.getError())));
    Mockito.when(taskHandlerHelper.prepareTaskHandlerResponse(any(), any())).thenReturn(res);

    Mockito.when(authDetailsService.getAuthDetailsFromRealmId("1234")).thenReturn(authDetails);
    Mockito.when(wasHttpClient.httpResponse(ArgumentMatchers.any())).thenReturn(response);
    Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);
    Mockito.when(appConnectConfig.getConnectorEndpoint())
        .thenReturn("https://e2e.api.intuit.com/appconnect/connector");
    Mockito.when(appConnectConfig.getProviderAppId()).thenReturn("AP13702");

    try {
      appconnectDuzzitRestExecutionHelper.executeWorkflowActionRequest(workerActionRequest, getParameterDetailsMap(workerActionRequest.getInputVariables()));
      Assert.fail("The above method should throw exception");
    } catch (AppConnectDuzzitException e) {
      Assert.assertTrue(
          e.getErrorCode().contains("WAS_CREATE_TASK")
              && e.getMessage().contains("Duzzit=intuit-workflows/was-create-task"));
    }
  }

  @Test
  public void testExecuteAppAction_UpdateInvoiceStatusFailure() {
    String handlerId = "intuit-workflows/was-update-invoice-status";
    String dependency = WorkflowConstants.QBO;
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId(handlerId)
            .ownerId(1234L)
            .taskId("externalTaskId")
            .build();

    WorkflowTaskHandlerResponse handlerResponse = new WorkflowTaskHandlerResponse();
    handlerResponse.setSuccess("false");
    handlerResponse.setError("error");
    WASHttpResponse<Object> response =
        WASHttpResponse.builder()
            .status(HttpStatus.OK)
            .response(handlerResponse)
            .isSuccess2xx(false)
            .build();
    List<WorkflowTaskHandlerInput> mockTaskHandlerInputs = new ArrayList<>();
    WorkflowTaskHandlerInput testHandlerInput = new WorkflowTaskHandlerInput();
    testHandlerInput.setName("testName");
    testHandlerInput.setValue("testValue");
    mockTaskHandlerInputs.add(testHandlerInput);

    AuthDetails authDetails = new AuthDetails();
    authDetails.setSubscriptionId("123");

    Map<String, Object> res = new HashMap<>();
    res.put(
        workerActionRequest.getActivityId()
            + WorkflowConstants.UNDERSCORE
            + WorkFlowVariables.RESPONSE.getName(),
        "true");

    Mockito.when(taskHandlerHelper.prepareTaskHandlerInputs(any(), any()))
        .thenReturn(mockTaskHandlerInputs);
    Mockito.when(taskHandlerHelper.getAppConnectDuzzitException(any(), any()))
        .thenReturn(
            new AppConnectDuzzitException(
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorCode(),
                    dependency.toUpperCase(),
                    formatHandlerId(handlerId)),
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorMessage(),
                    dependency,
                    handlerId,
                    handlerResponse.getError())));
    Mockito.when(taskHandlerHelper.prepareTaskHandlerResponse(any(), any())).thenReturn(res);

    Mockito.when(authDetailsService.getAuthDetailsFromRealmId("1234")).thenReturn(authDetails);
    Mockito.when(wasHttpClient.httpResponse(ArgumentMatchers.any())).thenReturn(response);
    Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);
    Mockito.when(appConnectConfig.getConnectorEndpoint())
        .thenReturn("https://e2e.api.intuit.com/appconnect/connector");
    Mockito.when(appConnectConfig.getProviderAppId()).thenReturn("AP13702");

    try {
      appconnectDuzzitRestExecutionHelper.executeWorkflowActionRequest(workerActionRequest, getParameterDetailsMap(workerActionRequest.getInputVariables()));
      Assert.fail("The above method should throw exception");
    } catch (AppConnectDuzzitException e) {
      Assert.assertTrue(
          e.getErrorCode().contains("WAS_UPDATE_INVOICE_STATUS")
              && e.getMessage().contains("Duzzit=intuit-workflows/was-update-invoice-status"));
    }
  }

  @Test
  public void testExecuteAppAction_ApprovalFailure() {
    String handlerId = "intuit-workflows/was-send-invoice-approval-notification";
    String dependency = WorkflowConstants.OINP;
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId(handlerId)
            .ownerId(1234L)
            .taskId("externalTaskId")
            .build();

    WorkflowTaskHandlerResponse handlerResponse = new WorkflowTaskHandlerResponse();
    handlerResponse.setSuccess("false");
    handlerResponse.setError("error");
    WASHttpResponse<Object> response =
        WASHttpResponse.builder()
            .status(HttpStatus.OK)
            .response(handlerResponse)
            .isSuccess2xx(false)
            .build();
    List<WorkflowTaskHandlerInput> mockTaskHandlerInputs = new ArrayList<>();
    WorkflowTaskHandlerInput testHandlerInput = new WorkflowTaskHandlerInput();
    testHandlerInput.setName("testName");
    testHandlerInput.setValue("testValue");
    mockTaskHandlerInputs.add(testHandlerInput);

    AuthDetails authDetails = new AuthDetails();
    authDetails.setSubscriptionId("123");

    Map<String, Object> res = new HashMap<>();
    res.put(
        workerActionRequest.getActivityId()
            + WorkflowConstants.UNDERSCORE
            + WorkFlowVariables.RESPONSE.getName(),
        "true");

    Mockito.when(taskHandlerHelper.prepareTaskHandlerInputs(any(), any()))
        .thenReturn(mockTaskHandlerInputs);
    Mockito.when(taskHandlerHelper.getAppConnectDuzzitException(any(), any()))
        .thenReturn(
            new AppConnectDuzzitException(
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorCode(),
                    dependency.toUpperCase(),
                    formatHandlerId(handlerId)),
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorMessage(),
                    dependency,
                    handlerId,
                    handlerResponse.getError())));
    Mockito.when(taskHandlerHelper.prepareTaskHandlerResponse(any(), any())).thenReturn(res);

    Mockito.when(authDetailsService.getAuthDetailsFromRealmId("1234")).thenReturn(authDetails);
    Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);
    Mockito.when(wasHttpClient.httpResponse(ArgumentMatchers.any())).thenReturn(response);
    Mockito.when(appConnectConfig.getConnectorEndpoint())
        .thenReturn("https://e2e.api.intuit.com/appconnect/connector");
    Mockito.when(appConnectConfig.getProviderAppId()).thenReturn("AP13702");

    try {
      appconnectDuzzitRestExecutionHelper.executeWorkflowActionRequest(workerActionRequest, getParameterDetailsMap(workerActionRequest.getInputVariables()));
      Assert.fail("The above method should throw exception");
    } catch (AppConnectDuzzitException e) {
      Assert.assertTrue(
          e.getErrorCode().contains("WAS_SEND_INVOICE_APPROVAL_NOTIFICATION")
              && e.getMessage()
              .contains("Duzzit=intuit-workflows/was-send-invoice-approval-notification"));
    }
  }

  @Test
  public void SendInvoiceNotificationToCreatorFailure() {
    String handlerId = "intuit-workflows/was-send-invoice-notification-to-creator";
    String dependency = WorkflowConstants.OINP;
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId(handlerId)
            .ownerId(1234L)
            .taskId("externalTaskId")
            .build();

    WorkflowTaskHandlerResponse handlerResponse = new WorkflowTaskHandlerResponse();
    handlerResponse.setSuccess("false");
    handlerResponse.setError("error");
    WASHttpResponse<Object> response =
        WASHttpResponse.builder()
            .status(HttpStatus.OK)
            .response(handlerResponse)
            .isSuccess2xx(false)
            .build();
    List<WorkflowTaskHandlerInput> mockTaskHandlerInputs = new ArrayList<>();
    WorkflowTaskHandlerInput testHandlerInput = new WorkflowTaskHandlerInput();
    testHandlerInput.setName("testName");
    testHandlerInput.setValue("testValue");
    mockTaskHandlerInputs.add(testHandlerInput);

    AuthDetails authDetails = new AuthDetails();
    authDetails.setSubscriptionId("123");

    Map<String, Object> res = new HashMap<>();
    res.put(
        workerActionRequest.getActivityId()
            + WorkflowConstants.UNDERSCORE
            + WorkFlowVariables.RESPONSE.getName(),
        "true");

    Mockito.when(taskHandlerHelper.prepareTaskHandlerInputs(any(), any()))
        .thenReturn(mockTaskHandlerInputs);
    Mockito.when(taskHandlerHelper.getAppConnectDuzzitException(any(), any()))
        .thenReturn(
            new AppConnectDuzzitException(
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorCode(),
                    dependency.toUpperCase(),
                    formatHandlerId(handlerId)),
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorMessage(),
                    dependency,
                    handlerId,
                    handlerResponse.getError())));
    Mockito.when(taskHandlerHelper.prepareTaskHandlerResponse(any(), any())).thenReturn(res);

    Mockito.when(authDetailsService.getAuthDetailsFromRealmId("1234")).thenReturn(authDetails);
    Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);
    Mockito.when(wasHttpClient.httpResponse(ArgumentMatchers.any())).thenReturn(response);
    Mockito.when(appConnectConfig.getConnectorEndpoint())
        .thenReturn("https://e2e.api.intuit.com/appconnect/connector");
    Mockito.when(appConnectConfig.getProviderAppId()).thenReturn("AP13702");

    try {
      appconnectDuzzitRestExecutionHelper.executeWorkflowActionRequest(workerActionRequest, getParameterDetailsMap(workerActionRequest.getInputVariables()));
      Assert.fail("The above method should throw exception");
    } catch (AppConnectDuzzitException e) {
      Assert.assertTrue(
          e.getErrorCode().contains("WAS_SEND_INVOICE_NOTIFICATION_TO_CREATOR")
              && e.getMessage()
              .contains("Duzzit=intuit-workflows/was-send-invoice-notification-to-creator"));
    }
  }

  @Test
  public void testExecuteAppAction_ReminderFailure() {
    String handlerId = "intuit-workflows/was-reminder-send-notification";
    String dependency = WorkflowConstants.OINP;
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId(handlerId)
            .ownerId(1234L)
            .taskId("externalTaskId")
            .build();

    WorkflowTaskHandlerResponse handlerResponse = new WorkflowTaskHandlerResponse();
    handlerResponse.setSuccess("false");
    handlerResponse.setError("error");
    WASHttpResponse<Object> response =
        WASHttpResponse.builder()
            .status(HttpStatus.OK)
            .response(handlerResponse)
            .isSuccess2xx(false)
            .build();
    List<WorkflowTaskHandlerInput> mockTaskHandlerInputs = new ArrayList<>();
    WorkflowTaskHandlerInput testHandlerInput = new WorkflowTaskHandlerInput();
    testHandlerInput.setName("testName");
    testHandlerInput.setValue("testValue");
    mockTaskHandlerInputs.add(testHandlerInput);

    AuthDetails authDetails = new AuthDetails();
    authDetails.setSubscriptionId("123");

    Map<String, Object> res = new HashMap<>();
    res.put(
        workerActionRequest.getActivityId()
            + WorkflowConstants.UNDERSCORE
            + WorkFlowVariables.RESPONSE.getName(),
        "true");

    Mockito.when(taskHandlerHelper.prepareTaskHandlerInputs(any(), any()))
        .thenReturn(mockTaskHandlerInputs);
    Mockito.when(taskHandlerHelper.getAppConnectDuzzitException(any(), any()))
        .thenReturn(
            new AppConnectDuzzitException(
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorCode(),
                    dependency.toUpperCase(),
                    formatHandlerId(handlerId)),
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorMessage(),
                    dependency,
                    handlerId,
                    handlerResponse.getError())));
    Mockito.when(taskHandlerHelper.prepareTaskHandlerResponse(any(), any())).thenReturn(res);

    Mockito.when(authDetailsService.getAuthDetailsFromRealmId("1234")).thenReturn(authDetails);
    Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);
    Mockito.when(wasHttpClient.httpResponse(ArgumentMatchers.any())).thenReturn(response);
    Mockito.when(appConnectConfig.getConnectorEndpoint())
        .thenReturn("https://e2e.api.intuit.com/appconnect/connector");
    Mockito.when(appConnectConfig.getProviderAppId()).thenReturn("AP13702");

    try {
      appconnectDuzzitRestExecutionHelper.executeWorkflowActionRequest(workerActionRequest, getParameterDetailsMap(workerActionRequest.getInputVariables()));
      Assert.fail("The above method should throw exception");
    } catch (AppConnectDuzzitException e) {
      Assert.assertTrue(
          e.getErrorCode().contains("WAS_REMINDER_SEND_NOTIFICATION")
              && e.getMessage().contains("Duzzit=intuit-workflows/was-reminder-send-notification"));
    }
  }

  @Test
  public void testExecuteAppAction_UnDepositedFundsWaitFailure() {
    String handlerId = "intuit-workflows/undeposited-funds-wait";
    String dependency = WorkflowConstants.QBO;
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId(handlerId)
            .ownerId(1234L)
            .taskId("externalTaskId")
            .build();

    WorkflowTaskHandlerResponse handlerResponse = new WorkflowTaskHandlerResponse();
    handlerResponse.setSuccess("false");
    handlerResponse.setError("error");
    WASHttpResponse<Object> response =
        WASHttpResponse.builder()
            .status(HttpStatus.OK)
            .response(handlerResponse)
            .isSuccess2xx(false)
            .build();
    List<WorkflowTaskHandlerInput> mockTaskHandlerInputs = new ArrayList<>();
    WorkflowTaskHandlerInput testHandlerInput = new WorkflowTaskHandlerInput();
    testHandlerInput.setName("testName");
    testHandlerInput.setValue("testValue");
    mockTaskHandlerInputs.add(testHandlerInput);

    AuthDetails authDetails = new AuthDetails();
    authDetails.setSubscriptionId("123");

    Map<String, Object> res = new HashMap<>();
    res.put(
        workerActionRequest.getActivityId()
            + WorkflowConstants.UNDERSCORE
            + WorkFlowVariables.RESPONSE.getName(),
        "true");

    Mockito.when(taskHandlerHelper.prepareTaskHandlerInputs(any(), any()))
        .thenReturn(mockTaskHandlerInputs);
    Mockito.when(taskHandlerHelper.getAppConnectDuzzitException(any(), any()))
        .thenReturn(
            new AppConnectDuzzitException(
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorCode(),
                    dependency.toUpperCase(),
                    formatHandlerId(handlerId)),
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorMessage(),
                    dependency,
                    handlerId,
                    handlerResponse.getError())));
    Mockito.when(taskHandlerHelper.prepareTaskHandlerResponse(any(), any())).thenReturn(res);

    Mockito.when(authDetailsService.getAuthDetailsFromRealmId("1234")).thenReturn(authDetails);
    Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);
    Mockito.when(wasHttpClient.httpResponse(ArgumentMatchers.any())).thenReturn(response);
    Mockito.when(appConnectConfig.getConnectorEndpoint())
        .thenReturn("https://e2e.api.intuit.com/appconnect/connector");
    Mockito.when(appConnectConfig.getProviderAppId()).thenReturn("AP13702");

    try {
      appconnectDuzzitRestExecutionHelper.executeWorkflowActionRequest(workerActionRequest, getParameterDetailsMap(workerActionRequest.getInputVariables()));
      Assert.fail("The above method should throw exception");
    } catch (AppConnectDuzzitException e) {
      Assert.assertTrue(
          e.getErrorCode().contains("UNDEPOSITED_FUNDS_WAIT")
              && e.getMessage().contains("Duzzit=intuit-workflows/undeposited-funds-wait"));
    }
  }

  @Test
  public void testExecuteAppAction_BillDueWaitFailure() {
    String handlerId = "intuit-workflows/bill-due-wait";
    String dependency = WorkflowConstants.QBO;
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId(handlerId)
            .ownerId(1234L)
            .taskId("externalTaskId")
            .build();

    WorkflowTaskHandlerResponse handlerResponse = new WorkflowTaskHandlerResponse();
    handlerResponse.setSuccess("false");
    handlerResponse.setError("error");
    WASHttpResponse<Object> response =
        WASHttpResponse.builder()
            .status(HttpStatus.OK)
            .response(handlerResponse)
            .isSuccess2xx(false)
            .build();
    List<WorkflowTaskHandlerInput> mockTaskHandlerInputs = new ArrayList<>();
    WorkflowTaskHandlerInput testHandlerInput = new WorkflowTaskHandlerInput();
    testHandlerInput.setName("testName");
    testHandlerInput.setValue("testValue");
    mockTaskHandlerInputs.add(testHandlerInput);

    AuthDetails authDetails = new AuthDetails();
    authDetails.setSubscriptionId("123");

    Map<String, Object> res = new HashMap<>();
    res.put(
        workerActionRequest.getActivityId()
            + WorkflowConstants.UNDERSCORE
            + WorkFlowVariables.RESPONSE.getName(),
        "true");

    Mockito.when(taskHandlerHelper.prepareTaskHandlerInputs(any(), any()))
        .thenReturn(mockTaskHandlerInputs);
    Mockito.when(taskHandlerHelper.getAppConnectDuzzitException(any(), any()))
        .thenReturn(
            new AppConnectDuzzitException(
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorCode(),
                    dependency.toUpperCase(),
                    formatHandlerId(handlerId)),
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorMessage(),
                    dependency,
                    handlerId,
                    handlerResponse.getError())));
    Mockito.when(taskHandlerHelper.prepareTaskHandlerResponse(any(), any())).thenReturn(res);

    Mockito.when(authDetailsService.getAuthDetailsFromRealmId("1234")).thenReturn(authDetails);
    Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);
    Mockito.when(wasHttpClient.httpResponse(ArgumentMatchers.any())).thenReturn(response);
    Mockito.when(appConnectConfig.getConnectorEndpoint())
        .thenReturn("https://e2e.api.intuit.com/appconnect/connector");
    Mockito.when(appConnectConfig.getProviderAppId()).thenReturn("AP13702");

    try {
      appconnectDuzzitRestExecutionHelper.executeWorkflowActionRequest(workerActionRequest, getParameterDetailsMap(workerActionRequest.getInputVariables()));
      Assert.fail("The above method should throw exception");
    } catch (AppConnectDuzzitException e) {
      Assert.assertTrue(
          e.getErrorCode().contains("BILL_DUE_WAIT")
              && e.getMessage().contains("Duzzit=intuit-workflows/bill-due-wait"));
    }
  }

  @Test
  public void testExecuteAppAction_ReceiptNotificationFailure() {
    String handlerId = "intuit-workflows/receipt-notification";
    String dependency = WorkflowConstants.OINP;
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId(handlerId)
            .ownerId(1234L)
            .taskId("externalTaskId")
            .build();

    WorkflowTaskHandlerResponse handlerResponse = new WorkflowTaskHandlerResponse();
    handlerResponse.setSuccess("false");
    handlerResponse.setError("error");
    WASHttpResponse<Object> response =
        WASHttpResponse.builder()
            .status(HttpStatus.OK)
            .response(handlerResponse)
            .isSuccess2xx(false)
            .build();
    List<WorkflowTaskHandlerInput> mockTaskHandlerInputs = new ArrayList<>();
    WorkflowTaskHandlerInput testHandlerInput = new WorkflowTaskHandlerInput();
    testHandlerInput.setName("testName");
    testHandlerInput.setValue("testValue");
    mockTaskHandlerInputs.add(testHandlerInput);

    AuthDetails authDetails = new AuthDetails();
    authDetails.setSubscriptionId("123");

    Map<String, Object> res = new HashMap<>();
    res.put(
        workerActionRequest.getActivityId()
            + WorkflowConstants.UNDERSCORE
            + WorkFlowVariables.RESPONSE.getName(),
        "true");

    Mockito.when(taskHandlerHelper.prepareTaskHandlerInputs(any(), any()))
        .thenReturn(mockTaskHandlerInputs);
    Mockito.when(taskHandlerHelper.getAppConnectDuzzitException(any(), any()))
        .thenReturn(
            new AppConnectDuzzitException(
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorCode(),
                    dependency.toUpperCase(),
                    formatHandlerId(handlerId)),
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorMessage(),
                    dependency,
                    handlerId,
                    handlerResponse.getError())));
    Mockito.when(taskHandlerHelper.prepareTaskHandlerResponse(any(), any())).thenReturn(res);

    Mockito.when(authDetailsService.getAuthDetailsFromRealmId("1234")).thenReturn(authDetails);
    Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);
    Mockito.when(wasHttpClient.httpResponse(ArgumentMatchers.any())).thenReturn(response);
    Mockito.when(appConnectConfig.getConnectorEndpoint())
        .thenReturn("https://e2e.api.intuit.com/appconnect/connector");
    Mockito.when(appConnectConfig.getProviderAppId()).thenReturn("AP13702");

    try {
      appconnectDuzzitRestExecutionHelper.executeWorkflowActionRequest(workerActionRequest, getParameterDetailsMap(workerActionRequest.getInputVariables()));
      Assert.fail("The above method should throw exception");
    } catch (AppConnectDuzzitException e) {
      Assert.assertTrue(
          e.getErrorCode().contains("RECEIPT_NOTIFICATION")
              && e.getMessage().contains("Duzzit=intuit-workflows/receipt-notification"));
    }
  }

  @Test
  public void testExecuteAppAction_InvoiceOverdueWaitFailure() {
    String handlerId = "intuit-workflows/overdue-invoice-wait";
    String dependency = WorkflowConstants.QBO;
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId(handlerId)
            .ownerId(1234L)
            .taskId("externalTaskId")
            .build();

    WorkflowTaskHandlerResponse handlerResponse = new WorkflowTaskHandlerResponse();
    handlerResponse.setSuccess("false");
    handlerResponse.setError("error");
    WASHttpResponse<Object> response =
        WASHttpResponse.builder()
            .status(HttpStatus.OK)
            .response(handlerResponse)
            .isSuccess2xx(false)
            .build();
    List<WorkflowTaskHandlerInput> mockTaskHandlerInputs = new ArrayList<>();
    WorkflowTaskHandlerInput testHandlerInput = new WorkflowTaskHandlerInput();
    testHandlerInput.setName("testName");
    testHandlerInput.setValue("testValue");
    mockTaskHandlerInputs.add(testHandlerInput);

    AuthDetails authDetails = new AuthDetails();
    authDetails.setSubscriptionId("123");

    Map<String, Object> res = new HashMap<>();
    res.put(
        workerActionRequest.getActivityId()
            + WorkflowConstants.UNDERSCORE
            + WorkFlowVariables.RESPONSE.getName(),
        "true");

    Mockito.when(taskHandlerHelper.prepareTaskHandlerInputs(any(), any()))
        .thenReturn(mockTaskHandlerInputs);
    Mockito.when(taskHandlerHelper.getAppConnectDuzzitException(any(), any()))
        .thenReturn(
            new AppConnectDuzzitException(
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorCode(),
                    dependency.toUpperCase(),
                    formatHandlerId(handlerId)),
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorMessage(),
                    dependency,
                    handlerId,
                    handlerResponse.getError())));
    Mockito.when(taskHandlerHelper.prepareTaskHandlerResponse(any(), any())).thenReturn(res);

    Mockito.when(authDetailsService.getAuthDetailsFromRealmId("1234")).thenReturn(authDetails);
    Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);
    Mockito.when(wasHttpClient.httpResponse(ArgumentMatchers.any())).thenReturn(response);
    Mockito.when(appConnectConfig.getConnectorEndpoint())
        .thenReturn("https://e2e.api.intuit.com/appconnect/connector");
    Mockito.when(appConnectConfig.getProviderAppId()).thenReturn("AP13702");

    try {
      appconnectDuzzitRestExecutionHelper.executeWorkflowActionRequest(workerActionRequest, getParameterDetailsMap(workerActionRequest.getInputVariables()));
      Assert.fail("The above method should throw exception");
    } catch (AppConnectDuzzitException e) {
      Assert.assertTrue(
          e.getErrorCode().contains("OVERDUE_INVOICE_WAIT")
              && e.getMessage().contains("Duzzit=intuit-workflows/overdue-invoice-wait"));
    }
  }

  @Test
  public void testExecuteAppAction_DeleteQBOWebhookFailure() {
    String handlerId = "intuit-workflows/Delete-QBO-Webhook";
    String dependency = WorkflowConstants.QBO;
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId(handlerId)
            .taskId("externalTaskId")
            .ownerId(1234L)
            .build();

    WorkflowTaskHandlerResponse handlerResponse = new WorkflowTaskHandlerResponse();
    handlerResponse.setSuccess("false");
    handlerResponse.setError("error");
    WASHttpResponse<Object> response =
        WASHttpResponse.builder()
            .status(HttpStatus.OK)
            .response(handlerResponse)
            .isSuccess2xx(false)
            .build();
    List<WorkflowTaskHandlerInput> mockTaskHandlerInputs = new ArrayList<>();
    WorkflowTaskHandlerInput testHandlerInput = new WorkflowTaskHandlerInput();
    testHandlerInput.setName("testName");
    testHandlerInput.setValue("testValue");
    mockTaskHandlerInputs.add(testHandlerInput);

    AuthDetails authDetails = new AuthDetails();
    authDetails.setSubscriptionId("123");

    Map<String, Object> res = new HashMap<>();
    res.put(
        workerActionRequest.getActivityId()
            + WorkflowConstants.UNDERSCORE
            + WorkFlowVariables.RESPONSE.getName(),
        "true");

    Mockito.when(taskHandlerHelper.prepareTaskHandlerInputs(any(), any()))
        .thenReturn(mockTaskHandlerInputs);
    Mockito.when(taskHandlerHelper.getAppConnectDuzzitException(any(), any()))
        .thenReturn(
            new AppConnectDuzzitException(
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorCode(),
                    dependency.toUpperCase(),
                    formatHandlerId(handlerId)),
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorMessage(),
                    dependency,
                    handlerId,
                    handlerResponse.getError())));
    Mockito.when(taskHandlerHelper.prepareTaskHandlerResponse(any(), any())).thenReturn(res);

    Mockito.when(authDetailsService.getAuthDetailsFromRealmId("1234")).thenReturn(authDetails);
    Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);
    Mockito.when(wasHttpClient.httpResponse(ArgumentMatchers.any())).thenReturn(response);
    Mockito.when(appConnectConfig.getConnectorEndpoint())
        .thenReturn("https://e2e.api.intuit.com/appconnect/connector");
    Mockito.when(appConnectConfig.getProviderAppId()).thenReturn("AP13702");

    try {
      appconnectDuzzitRestExecutionHelper.executeWorkflowActionRequest(workerActionRequest, getParameterDetailsMap(workerActionRequest.getInputVariables()));
      Assert.fail("The above method should throw exception");
    } catch (AppConnectDuzzitException e) {
      Assert.assertTrue(
          e.getErrorCode().contains("DELETE_QBO_WEBHOOK")
              && e.getMessage().contains("Duzzit=intuit-workflows/Delete-QBO-Webhook"));
    }
  }

  @Test
  public void testExecuteAppAction_GetStageConnectionFailure() {
    String handlerId = "intuit-workflows/get-stage-connection-entities";
    String dependency = WorkflowConstants.STAGE;
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId(handlerId)
            .ownerId(1234L)
            .taskId("externalTaskId")
            .build();

    WorkflowTaskHandlerResponse handlerResponse = new WorkflowTaskHandlerResponse();
    handlerResponse.setSuccess("false");
    handlerResponse.setError("error");
    WASHttpResponse<Object> response =
        WASHttpResponse.builder()
            .status(HttpStatus.OK)
            .response(handlerResponse)
            .isSuccess2xx(false)
            .build();
    List<WorkflowTaskHandlerInput> mockTaskHandlerInputs = new ArrayList<>();
    WorkflowTaskHandlerInput testHandlerInput = new WorkflowTaskHandlerInput();
    testHandlerInput.setName("testName");
    testHandlerInput.setValue("testValue");
    mockTaskHandlerInputs.add(testHandlerInput);

    AuthDetails authDetails = new AuthDetails();
    authDetails.setSubscriptionId("123");

    Map<String, Object> res = new HashMap<>();
    res.put(
        workerActionRequest.getActivityId()
            + WorkflowConstants.UNDERSCORE
            + WorkFlowVariables.RESPONSE.getName(),
        "true");

    Mockito.when(taskHandlerHelper.prepareTaskHandlerInputs(any(), any()))
        .thenReturn(mockTaskHandlerInputs);
    Mockito.when(taskHandlerHelper.getAppConnectDuzzitException(any(), any()))
        .thenReturn(
            new AppConnectDuzzitException(
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorCode(),
                    dependency.toUpperCase(),
                    formatHandlerId(handlerId)),
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorMessage(),
                    dependency,
                    handlerId,
                    handlerResponse.getError())));
    Mockito.when(taskHandlerHelper.prepareTaskHandlerResponse(any(), any())).thenReturn(res);

    Mockito.when(authDetailsService.getAuthDetailsFromRealmId("1234")).thenReturn(authDetails);
    Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);
    Mockito.when(wasHttpClient.httpResponse(ArgumentMatchers.any())).thenReturn(response);
    Mockito.when(appConnectConfig.getConnectorEndpoint())
        .thenReturn("https://e2e.api.intuit.com/appconnect/connector");
    Mockito.when(appConnectConfig.getProviderAppId()).thenReturn("AP13702");

    try {
      appconnectDuzzitRestExecutionHelper.executeWorkflowActionRequest(workerActionRequest, getParameterDetailsMap(workerActionRequest.getInputVariables()));
      Assert.fail("The above method should throw exception");
    } catch (AppConnectDuzzitException e) {
      Assert.assertTrue(
          e.getErrorCode().contains("GET_STAGE_CONNECTION_ENTITIES")
              && e.getMessage().contains("Duzzit=intuit-workflows/get-stage-connection-entities"));
    }
  }

  @Test
  public void testExecuteAppAction_StageDisconnectFailure() {
    String handlerId = "intuit-workflows/stage-disconnect";
    String dependency = WorkflowConstants.STAGE;
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId(handlerId)
            .ownerId(1234L)
            .taskId("externalTaskId")
            .build();

    WorkflowTaskHandlerResponse handlerResponse = new WorkflowTaskHandlerResponse();
    handlerResponse.setSuccess("false");
    handlerResponse.setError("error");
    WASHttpResponse<Object> response =
        WASHttpResponse.builder()
            .status(HttpStatus.OK)
            .response(handlerResponse)
            .isSuccess2xx(false)
            .build();
    List<WorkflowTaskHandlerInput> mockTaskHandlerInputs = new ArrayList<>();
    WorkflowTaskHandlerInput testHandlerInput = new WorkflowTaskHandlerInput();
    testHandlerInput.setName("testName");
    testHandlerInput.setValue("testValue");
    mockTaskHandlerInputs.add(testHandlerInput);

    AuthDetails authDetails = new AuthDetails();
    authDetails.setSubscriptionId("123");

    Map<String, Object> res = new HashMap<>();
    res.put(
        workerActionRequest.getActivityId()
            + WorkflowConstants.UNDERSCORE
            + WorkFlowVariables.RESPONSE.getName(),
        "true");

    Mockito.when(taskHandlerHelper.prepareTaskHandlerInputs(any(), any()))
        .thenReturn(mockTaskHandlerInputs);
    Mockito.when(taskHandlerHelper.getAppConnectDuzzitException(any(), any()))
        .thenReturn(
            new AppConnectDuzzitException(
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorCode(),
                    dependency.toUpperCase(),
                    formatHandlerId(handlerId)),
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorMessage(),
                    dependency,
                    handlerId,
                    handlerResponse.getError())));
    Mockito.when(taskHandlerHelper.prepareTaskHandlerResponse(any(), any())).thenReturn(res);

    Mockito.when(authDetailsService.getAuthDetailsFromRealmId("1234")).thenReturn(authDetails);
    Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);
    Mockito.when(wasHttpClient.httpResponse(ArgumentMatchers.any())).thenReturn(response);
    Mockito.when(appConnectConfig.getConnectorEndpoint())
        .thenReturn("https://e2e.api.intuit.com/appconnect/connector");
    Mockito.when(appConnectConfig.getProviderAppId()).thenReturn("AP13702");

    try {
      appconnectDuzzitRestExecutionHelper.executeWorkflowActionRequest(workerActionRequest, getParameterDetailsMap(workerActionRequest.getInputVariables()));
      Assert.fail("The above method should throw exception");
    } catch (AppConnectDuzzitException e) {
      Assert.assertTrue(
          e.getErrorCode().contains("STAGE_DISCONNECT")
              && e.getMessage().contains("Duzzit=intuit-workflows/stage-disconnect"));
    }
  }

  @Test
  public void testExecuteAppAction_StageEntityFailure() {
    String handlerId = "intuit-workflows/exclude-stage-entity";
    String dependency = WorkflowConstants.STAGE;
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId(handlerId)
            .taskId("externalTaskId")
            .ownerId(1234L)
            .build();

    WorkflowTaskHandlerResponse handlerResponse = new WorkflowTaskHandlerResponse();
    handlerResponse.setSuccess("false");
    handlerResponse.setError("error");
    WASHttpResponse<Object> response =
        WASHttpResponse.builder()
            .status(HttpStatus.OK)
            .response(handlerResponse)
            .isSuccess2xx(false)
            .build();
    List<WorkflowTaskHandlerInput> mockTaskHandlerInputs = new ArrayList<>();
    WorkflowTaskHandlerInput testHandlerInput = new WorkflowTaskHandlerInput();
    testHandlerInput.setName("testName");
    testHandlerInput.setValue("testValue");
    mockTaskHandlerInputs.add(testHandlerInput);

    AuthDetails authDetails = new AuthDetails();
    authDetails.setSubscriptionId("123");

    Map<String, Object> res = new HashMap<>();
    res.put(
        workerActionRequest.getActivityId()
            + WorkflowConstants.UNDERSCORE
            + WorkFlowVariables.RESPONSE.getName(),
        "true");

    Mockito.when(taskHandlerHelper.prepareTaskHandlerInputs(any(), any()))
        .thenReturn(mockTaskHandlerInputs);
    Mockito.when(taskHandlerHelper.getAppConnectDuzzitException(any(), any()))
        .thenReturn(
            new AppConnectDuzzitException(
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorCode(),
                    dependency.toUpperCase(),
                    formatHandlerId(handlerId)),
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorMessage(),
                    dependency,
                    handlerId,
                    handlerResponse.getError())));
    Mockito.when(taskHandlerHelper.prepareTaskHandlerResponse(any(), any())).thenReturn(res);

    Mockito.when(authDetailsService.getAuthDetailsFromRealmId("1234")).thenReturn(authDetails);
    Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);
    Mockito.when(wasHttpClient.httpResponse(ArgumentMatchers.any())).thenReturn(response);
    Mockito.when(appConnectConfig.getConnectorEndpoint())
        .thenReturn("https://e2e.api.intuit.com/appconnect/connector");
    Mockito.when(appConnectConfig.getProviderAppId()).thenReturn("AP13702");

    try {
      appconnectDuzzitRestExecutionHelper.executeWorkflowActionRequest(workerActionRequest, getParameterDetailsMap(workerActionRequest.getInputVariables()));
      Assert.fail("The above method should throw exception");
    } catch (AppConnectDuzzitException e) {
      Assert.assertTrue(
          e.getErrorCode().contains("EXCLUDE_STAGE_ENTITY")
              && e.getMessage().contains("Duzzit=intuit-workflows/exclude-stage-entity"));
    }
  }

  @Test
  public void testExecuteAppAction_ProjectTaskUpdateFailure() {
    String handlerId = "intuit-workflows/project-task-update";
    String dependency = WorkflowConstants.PROJECT_SERVICE;
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId(handlerId)
            .ownerId(1234L)
            .taskId("externalTaskId")
            .build();

    WorkflowTaskHandlerResponse handlerResponse = new WorkflowTaskHandlerResponse();
    handlerResponse.setSuccess("false");
    handlerResponse.setError("error");
    WASHttpResponse<Object> response =
        WASHttpResponse.builder()
            .status(HttpStatus.OK)
            .response(handlerResponse)
            .isSuccess2xx(false)
            .build();
    List<WorkflowTaskHandlerInput> mockTaskHandlerInputs = new ArrayList<>();
    WorkflowTaskHandlerInput testHandlerInput = new WorkflowTaskHandlerInput();
    testHandlerInput.setName("testName");
    testHandlerInput.setValue("testValue");
    mockTaskHandlerInputs.add(testHandlerInput);

    AuthDetails authDetails = new AuthDetails();
    authDetails.setSubscriptionId("123");

    Map<String, Object> res = new HashMap<>();
    res.put(
        workerActionRequest.getActivityId()
            + WorkflowConstants.UNDERSCORE
            + WorkFlowVariables.RESPONSE.getName(),
        "true");

    Mockito.when(taskHandlerHelper.prepareTaskHandlerInputs(any(), any()))
        .thenReturn(mockTaskHandlerInputs);
    Mockito.when(taskHandlerHelper.getAppConnectDuzzitException(any(), any()))
        .thenReturn(
            new AppConnectDuzzitException(
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorCode(),
                    dependency.toUpperCase(),
                    formatHandlerId(handlerId)),
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorMessage(),
                    dependency,
                    handlerId,
                    handlerResponse.getError())));
    Mockito.when(taskHandlerHelper.prepareTaskHandlerResponse(any(), any())).thenReturn(res);

    Mockito.when(authDetailsService.getAuthDetailsFromRealmId("1234")).thenReturn(authDetails);
    Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);
    Mockito.when(wasHttpClient.httpResponse(ArgumentMatchers.any())).thenReturn(response);
    Mockito.when(appConnectConfig.getConnectorEndpoint())
        .thenReturn("https://e2e.api.intuit.com/appconnect/connector");
    Mockito.when(appConnectConfig.getProviderAppId()).thenReturn("AP13702");

    try {
      appconnectDuzzitRestExecutionHelper.executeWorkflowActionRequest(workerActionRequest, getParameterDetailsMap(workerActionRequest.getInputVariables()));
      Assert.fail("The above method should throw exception");
    } catch (AppConnectDuzzitException e) {
      Assert.assertTrue(
          e.getErrorCode().contains("PROJECT_TASK_UPDATE")
              && e.getMessage().contains("Duzzit=intuit-workflows/project-task-update"));
    }
  }

  @Test
  public void testExecuteAppAction_UnsentInvoiceReminderWaitFailure() {
    String handlerId = "intuit-workflows/unsent-invoice-reminder-wait";
    String dependency = WorkflowConstants.OINP;
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId(handlerId)
            .ownerId(1234L)
            .taskId("externalTaskId")
            .build();

    WorkflowTaskHandlerResponse handlerResponse = new WorkflowTaskHandlerResponse();
    handlerResponse.setSuccess("false");
    handlerResponse.setError("error");
    WASHttpResponse<Object> response =
        WASHttpResponse.builder()
            .status(HttpStatus.OK)
            .response(handlerResponse)
            .isSuccess2xx(false)
            .build();
    List<WorkflowTaskHandlerInput> mockTaskHandlerInputs = new ArrayList<>();
    WorkflowTaskHandlerInput testHandlerInput = new WorkflowTaskHandlerInput();
    testHandlerInput.setName("testName");
    testHandlerInput.setValue("testValue");
    mockTaskHandlerInputs.add(testHandlerInput);

    AuthDetails authDetails = new AuthDetails();
    authDetails.setSubscriptionId("123");

    Map<String, Object> res = new HashMap<>();
    res.put(
        workerActionRequest.getActivityId()
            + WorkflowConstants.UNDERSCORE
            + WorkFlowVariables.RESPONSE.getName(),
        "true");

    Mockito.when(taskHandlerHelper.prepareTaskHandlerInputs(any(), any()))
        .thenReturn(mockTaskHandlerInputs);
    Mockito.when(taskHandlerHelper.getAppConnectDuzzitException(any(), any()))
        .thenReturn(
            new AppConnectDuzzitException(
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorCode(),
                    dependency.toUpperCase(),
                    formatHandlerId(handlerId)),
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorMessage(),
                    dependency,
                    handlerId,
                    handlerResponse.getError())));
    Mockito.when(taskHandlerHelper.prepareTaskHandlerResponse(any(), any())).thenReturn(res);

    Mockito.when(authDetailsService.getAuthDetailsFromRealmId("1234")).thenReturn(authDetails);
    Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);
    Mockito.when(wasHttpClient.httpResponse(ArgumentMatchers.any())).thenReturn(response);
    Mockito.when(appConnectConfig.getConnectorEndpoint())
        .thenReturn("https://e2e.api.intuit.com/appconnect/connector");
    Mockito.when(appConnectConfig.getProviderAppId()).thenReturn("AP13702");

    try {
      appconnectDuzzitRestExecutionHelper.executeWorkflowActionRequest(workerActionRequest, getParameterDetailsMap(workerActionRequest.getInputVariables()));
      Assert.fail("The above method should throw exception");
    } catch (AppConnectDuzzitException e) {
      Assert.assertTrue(
          e.getErrorCode().contains("UNSENT_INVOICE_REMINDER_WAIT")
              && e.getMessage().contains("Duzzit=intuit-workflows/unsent-invoice-reminder-wait"));
    }
  }

  @Test
  public void testExecuteAppAction_WASUpdateTaskFailure() {
    String handlerId = "intuit-workflows/was-update-task";
    String dependency = WorkflowConstants.PROJECT_SERVICE;
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId(handlerId)
            .ownerId(1234L)
            .taskId("externalTaskId")
            .build();

    WorkflowTaskHandlerResponse handlerResponse = new WorkflowTaskHandlerResponse();
    handlerResponse.setSuccess("false");
    handlerResponse.setError("error");
    WASHttpResponse<Object> response =
        WASHttpResponse.builder()
            .status(HttpStatus.OK)
            .response(handlerResponse)
            .isSuccess2xx(false)
            .build();
    List<WorkflowTaskHandlerInput> mockTaskHandlerInputs = new ArrayList<>();
    WorkflowTaskHandlerInput testHandlerInput = new WorkflowTaskHandlerInput();
    testHandlerInput.setName("testName");
    testHandlerInput.setValue("testValue");
    mockTaskHandlerInputs.add(testHandlerInput);

    AuthDetails authDetails = new AuthDetails();
    authDetails.setSubscriptionId("123");

    Map<String, Object> res = new HashMap<>();
    res.put(
        workerActionRequest.getActivityId()
            + WorkflowConstants.UNDERSCORE
            + WorkFlowVariables.RESPONSE.getName(),
        "true");

    Mockito.when(taskHandlerHelper.prepareTaskHandlerInputs(any(), any()))
        .thenReturn(mockTaskHandlerInputs);
    Mockito.when(taskHandlerHelper.getAppConnectDuzzitException(any(), any()))
        .thenReturn(
            new AppConnectDuzzitException(
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorCode(),
                    dependency.toUpperCase(),
                    formatHandlerId(handlerId)),
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorMessage(),
                    dependency,
                    handlerId,
                    handlerResponse.getError())));
    Mockito.when(taskHandlerHelper.prepareTaskHandlerResponse(any(), any())).thenReturn(res);

    Mockito.when(authDetailsService.getAuthDetailsFromRealmId("1234")).thenReturn(authDetails);
    Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);
    Mockito.when(wasHttpClient.httpResponse(ArgumentMatchers.any())).thenReturn(response);
    Mockito.when(appConnectConfig.getConnectorEndpoint())
        .thenReturn("https://e2e.api.intuit.com/appconnect/connector");
    Mockito.when(appConnectConfig.getProviderAppId()).thenReturn("AP13702");

    try {
      appconnectDuzzitRestExecutionHelper.executeWorkflowActionRequest(workerActionRequest, getParameterDetailsMap(workerActionRequest.getInputVariables()));
      Assert.fail("The above method should throw exception");
    } catch (AppConnectDuzzitException e) {
      Assert.assertTrue(
          e.getErrorCode().contains("WAS_UPDATE_TASK")
              && e.getMessage().contains("Duzzit=intuit-workflows/was-update-task"));
    }
  }

  @Test
  public void testExecuteAppAction_AccessFailure() {
    String handlerId = "hId";
    String dependency = "";
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId(handlerId)
            .taskId("externalTaskId")
            .ownerId(1234L)
            .build();

    WorkflowTaskHandlerResponse handlerResponse = new WorkflowTaskHandlerResponse();
    handlerResponse.setSuccess("false");
    handlerResponse.setError("error");
    WASHttpResponse<Object> response =
        WASHttpResponse.builder()
            .status(HttpStatus.BAD_REQUEST)
            .response(handlerResponse)
            .error("IAC-001002")
            .build();
    List<WorkflowTaskHandlerInput> mockTaskHandlerInputs = new ArrayList<>();
    WorkflowTaskHandlerInput testHandlerInput = new WorkflowTaskHandlerInput();
    testHandlerInput.setName("testName");
    testHandlerInput.setValue("testValue");
    mockTaskHandlerInputs.add(testHandlerInput);

    AuthDetails authDetails = new AuthDetails();
    authDetails.setSubscriptionId("123");

    Map<String, Object> res = new HashMap<>();
    res.put(
        workerActionRequest.getActivityId()
            + WorkflowConstants.UNDERSCORE
            + WorkFlowVariables.RESPONSE.getName(),
        "true");

    Mockito.when(taskHandlerHelper.prepareTaskHandlerInputs(any(), any()))
        .thenReturn(mockTaskHandlerInputs);
    Mockito.when(taskHandlerHelper.getAppConnectDuzzitException(any(), any()))
        .thenReturn(
            new AppConnectDuzzitException(
                EXTERNAL_TASK_ACCESS_FAILURE.name(),
                String.format(
                    EXTERNAL_TASK_ACCESS_FAILURE.getErrorMessage(),
                    dependency,
                    handlerId,
                    handlerResponse.getError())));
    Mockito.when(taskHandlerHelper.prepareTaskHandlerResponse(any(), any())).thenReturn(res);

    Mockito.when(authDetailsService.getAuthDetailsFromRealmId("1234")).thenReturn(authDetails);
    Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);
    Mockito.when(wasHttpClient.httpResponse(ArgumentMatchers.any())).thenReturn(response);
    Mockito.when(appConnectConfig.getConnectorEndpoint())
        .thenReturn("https://e2e.api.intuit.com/appconnect/connector");
    Mockito.when(appConnectConfig.getProviderAppId()).thenReturn("AP13702");

    try {
      appconnectDuzzitRestExecutionHelper.executeWorkflowActionRequest(workerActionRequest, getParameterDetailsMap(workerActionRequest.getInputVariables()));
      Assert.fail("The above method should throw exception");
    } catch (AppConnectDuzzitException e) {
      Assert.assertTrue(e.getErrorCode().contains("EXTERNAL_TASK_ACCESS_FAILURE"));
    }
  }

  @Test
  public void testExecuteWorkflowActionRequest() {
    String handlerId = "hid";
    String dependency = "";

    Map<String, Object> outputFromAC = new HashMap<>();
    outputFromAC.put("success", "true");
    WorkerActionRequest workerActionRequest =
            WorkerActionRequest.builder()
                    .activityId("aId")
                    .processDefinitionId("dId")
                    .processInstanceId("iId")
                    .inputVariables(schema)
                    .handlerId("intuit-workflows/test")
                    .taskId("externalTaskId")
                    .ownerId(1234L)
                    .build();

    WorkflowTaskHandlerResponse handlerResponse = new WorkflowTaskHandlerResponse();
    handlerResponse.setSuccess("true");
    WASHttpResponse<Object> response =
            WASHttpResponse.builder()
                    .status(HttpStatus.OK)
                    .response(handlerResponse)
                    .isSuccess2xx(true)
                    .build();
    List<WorkflowTaskHandlerInput> mockTaskHandlerInputs = new ArrayList<>();
    WorkflowTaskHandlerInput testHandlerInput = new WorkflowTaskHandlerInput();
    testHandlerInput.setName("testName");
    testHandlerInput.setValue("testValue");

    mockTaskHandlerInputs.add(testHandlerInput);

    AuthDetails authDetails = new AuthDetails();
    authDetails.setSubscriptionId("123");

    Map<String, Object> res = new HashMap<>();
    res.put(
            workerActionRequest.getActivityId()
                    + WorkflowConstants.UNDERSCORE
                    + WorkFlowVariables.RESPONSE.getName(),
            "true");

    Mockito.when(taskHandlerHelper.prepareTaskHandlerInputs(any(), any()))
            .thenReturn(mockTaskHandlerInputs);
    Mockito.when(taskHandlerHelper.prepareTaskHandlerResponse(ArgumentMatchers.any(), ArgumentMatchers.any())).thenReturn(outputFromAC);
    Mockito.when(authDetailsService.getAuthDetailsFromRealmId("1234")).thenReturn(authDetails);
    Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);
    Mockito.when(wasHttpClient.httpResponse(ArgumentMatchers.any())).thenReturn(response);
    Mockito.when(appConnectConfig.getConnectorEndpoint())
            .thenReturn("https://e2e.api.intuit.com/appconnect/connector");
    Mockito.when(appConnectConfig.getProviderAppId()).thenReturn("AP13702");
    Mockito.when(taskHandlerHelper.getAppConnectDuzzitException(any(), any()))
            .thenReturn(
                    new AppConnectDuzzitException(
                            EXTERNAL_TASK_ACCESS_FAILURE.name(),
                            String.format(
                                    EXTERNAL_TASK_ACCESS_FAILURE.getErrorMessage(),
                                    dependency,
                                    handlerId,
                                    handlerResponse.getError())));
    List<WorkflowTaskHandlerInput> additionalTaskHandlerInputs = new ArrayList<>();
    WorkflowTaskHandlerInput workflowTaskHandlerInput = new WorkflowTaskHandlerInput();
    workflowTaskHandlerInput.setName("testName");
    workflowTaskHandlerInput.setValue("testValue");
    additionalTaskHandlerInputs.add(workflowTaskHandlerInput);

    Map<String, Object> responseFromAC = appconnectDuzzitRestExecutionHelper
            .executeWorkflowActionRequest(workerActionRequest,
            getParameterDetailsMap(workerActionRequest.getInputVariables()), additionalTaskHandlerInputs);

    Assert.assertEquals(responseFromAC.get("success"), "true");
  }

  @Test
  public void testExecuteWorkflowActionRequest_withNonNullAdditionalInputs() {
    String handlerId = "hid";
    String dependency = "";

    Map<String, Object> outputFromAC = new HashMap<>();
    outputFromAC.put("success", "true");
    WorkerActionRequest workerActionRequest =
            WorkerActionRequest.builder()
                    .activityId("aId")
                    .processDefinitionId("dId")
                    .processInstanceId("iId")
                    .inputVariables(schema)
                    .handlerId("intuit-workflows/test")
                    .taskId("externalTaskId")
                    .ownerId(1234L)
                    .build();

    WorkflowTaskHandlerResponse handlerResponse = new WorkflowTaskHandlerResponse();
    handlerResponse.setSuccess("true");
    WASHttpResponse<Object> response =
            WASHttpResponse.builder()
                    .status(HttpStatus.OK)
                    .response(handlerResponse)
                    .isSuccess2xx(true)
                    .build();
    List<WorkflowTaskHandlerInput> mockTaskHandlerInputs = new ArrayList<>();
    WorkflowTaskHandlerInput testHandlerInput = new WorkflowTaskHandlerInput();
    testHandlerInput.setName("testName");
    testHandlerInput.setValue("testValue");
    mockTaskHandlerInputs.add(testHandlerInput);

    AuthDetails authDetails = new AuthDetails();
    authDetails.setSubscriptionId("123");

    Map<String, Object> expectedRes = new HashMap<>();
    expectedRes.put(workerActionRequest.getActivityId() + WorkflowConstants.UNDERSCORE + WorkFlowVariables.RESPONSE.getName(), "true");

    Mockito.when(taskHandlerHelper.prepareTaskHandlerInputs(any(), any())).thenReturn(mockTaskHandlerInputs);
    Mockito.when(taskHandlerHelper.prepareTaskHandlerResponse(any(), any())).thenReturn(outputFromAC);
    Mockito.when(authDetailsService.getAuthDetailsFromRealmId("1234")).thenReturn(authDetails);
    Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);
    Mockito.when(wasHttpClient.httpResponse(any())).thenReturn(response);
    Mockito.when(appConnectConfig.getConnectorEndpoint()).thenReturn("https://e2e.api.intuit.com/appconnect/connector");
    Mockito.when(appConnectConfig.getProviderAppId()).thenReturn("AP13702");
    Mockito.when(taskHandlerHelper.getAppConnectDuzzitException(any(), any())).thenReturn(
            new AppConnectDuzzitException(
                    EXTERNAL_TASK_ACCESS_FAILURE.name(),
                    String.format(EXTERNAL_TASK_ACCESS_FAILURE.getErrorMessage(), dependency, handlerId, handlerResponse.getError())));

    // Create a non-null additionalTaskHandlerInputs list.
    List<WorkflowTaskHandlerInput> additionalTaskHandlerInputs = new ArrayList<>();
    WorkflowTaskHandlerInput additionalInput = new WorkflowTaskHandlerInput();
    additionalInput.setName("extraInput");
    additionalInput.setValue("extraValue");
    additionalTaskHandlerInputs.add(additionalInput);

    // Execute and verify that no exception is thrown and the output is as expected.
    Map<String, Object> responseFromAC = appconnectDuzzitRestExecutionHelper
            .executeWorkflowActionRequest(workerActionRequest,
                    getParameterDetailsMap(workerActionRequest.getInputVariables()), additionalTaskHandlerInputs);

    Assert.assertEquals("true", responseFromAC.get("success"));
  }


  private Optional<Map<String, ParameterDetails>> getParameterDetailsMap(Map<String, String> schema) {
    return SchemaDecoder.getParametersForHandler(schema);
  }

  public String formatHandlerId(String handlerId) {
    int splitCount = handlerId.split(WorkflowConstants.BACKSLASH).length - 1;
    if (splitCount != 0) {
      handlerId =
          handlerId
              .split(WorkflowConstants.BACKSLASH)[splitCount]
              .replace(WorkflowConstants.HYPHEN, WorkflowConstants.UNDERSCORE)
              .toUpperCase();
    } else {
      handlerId = WorkflowError.EXTERNAL_TASK_HANDLER_ERROR.name();
    }
    return handlerId;
  }
}
