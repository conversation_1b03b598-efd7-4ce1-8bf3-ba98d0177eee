package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.SchedulingSvcConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.mappers.ActionModelToScheduleRequestMapper;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.SchedulingServiceUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.SchedulingSvcAdapter;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.SchedulerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.SchedulerDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.WorkflowNameEnum;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.enums.Status;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.model.EventScheduleWorkflowActionModel;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.model.SchedulingMetaData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.request.SchedulingSvcRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.respone.SchedulingSvcResponse;
import com.intuit.async.execution.request.State;
import com.intuit.v4.common.RecurrenceRule;
import com.intuit.v4.common.TimeDuration;
import com.intuit.v4.workflows.Definition;
import org.joda.time.LocalDate;
import org.json.JSONObject;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.*;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.junit.Assert.*;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/** <AUTHOR> */
public class SchedulingServiceImplTest {
    @Mock private ActionModelToScheduleRequestMapper actionModelToScheduleRequestMapper;
    @Mock private SchedulingSvcAdapter schedulingSvcAdapter;
    @Mock private FeatureFlagManager featureFlagManager;
    @Mock
    private SchedulerDetailsRepository schedulerDetailsRepository;
    @Mock
    SchedulingSvcConfig schedulingSvcConfig;
    @Mock
    private DefinitionDetails definitionDetails;

    @InjectMocks
    private SchedulingServiceImpl schedulingService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        when(actionModelToScheduleRequestMapper.convertToScheduleRequest(any(), any())).thenReturn(new SchedulingSvcRequest());
    }

    @Test
    public void test_createSchedules_success() {
        EventScheduleWorkflowActionModel eventScheduleWorkflowActionModel =
                new EventScheduleWorkflowActionModel("customReminder_customStart", new LocalDate("2099-1-2"), new RecurrenceRule());
        String realmId = "testRealm";
        when(schedulingSvcAdapter.invokeScheduleServiceForCreateUpdate(any(), any(), any())).thenReturn(new ArrayList<>());
        List<SchedulingSvcResponse> result = schedulingService.createSchedules(SchedulingServiceUtil.getSchedulingSvcRequestsPayload(List.of(eventScheduleWorkflowActionModel), actionModelToScheduleRequestMapper, SchedulingMetaData.builder().workflowName("customReminder").definitionKey("definitionKey").status(Status.ACTIVE).build(), new State()),
                realmId);
        assertNotNull(result);
    }

    @Test
    public void test_createSchedules_emptyList() {
        List<SchedulingSvcRequest> eventSchedules = Collections.emptyList();
        String realmId = "testRealm";

        try {
            schedulingService.createSchedules(eventSchedules, realmId);
        } catch (WorkflowGeneralException workflowGeneralException) {
            Assert.assertEquals(
                    WorkflowError.INPUT_INVALID, workflowGeneralException.getWorkflowError());
        }
    }

    @Test
    public void test_updateSchedules_success() {
        EventScheduleWorkflowActionModel eventScheduleWorkflowActionModel =
                new EventScheduleWorkflowActionModel("customReminder_customStart", new LocalDate("2099-1-2"), new RecurrenceRule());
        String realmId = "testRealm";
        when(schedulingSvcAdapter.invokeScheduleServiceForCreateUpdate(any(), any(), any())).thenReturn(new ArrayList<>());
        List<SchedulingSvcResponse> result = schedulingService.updateSchedules(SchedulingServiceUtil.getSchedulingSvcRequestsPayload(List.of(eventScheduleWorkflowActionModel), actionModelToScheduleRequestMapper, SchedulingMetaData.builder().definitionKey("definitionKey").workflowName("customReminder").status(Status.ACTIVE).build(), new State()), realmId);

        assertNotNull(result);
    }

    @Test
    public void test_updateSchedules_emptyList() {
        List<SchedulingSvcRequest> eventSchedules = Collections.emptyList();
        String realmId = "testRealm";
        try {
            schedulingService.updateSchedules(eventSchedules, realmId);
        } catch (WorkflowGeneralException workflowGeneralException) {
            Assert.assertEquals(
                    WorkflowError.INPUT_INVALID, workflowGeneralException.getWorkflowError());
        }
    }

    @Test
    public void test_getSchedules_success() {
        List<String> scheduleIds = new ArrayList<>();
        scheduleIds.add("scheduleId1");
        String realmId = "testRealm";
        when(schedulingSvcAdapter.invokeScheduleServiceForGet(any(), any())).thenReturn(new ArrayList<>());
        List<SchedulingSvcResponse> result = schedulingService.getSchedules(scheduleIds, realmId);
        assertNotNull(result);
    }

    @Test
    public void test_getSchedules_emptyList() {
        List<String> scheduleIds = Collections.emptyList();
        String realmId = "testRealm";
        try {
            schedulingService.getSchedules(scheduleIds, realmId);
        } catch (WorkflowGeneralException workflowGeneralException) {
            Assert.assertEquals(
                    WorkflowError.INPUT_INVALID, workflowGeneralException.getWorkflowError());
        }
    }

    @Test
    public void test_deleteSchedules_success() {
        List<String> scheduleIds = new ArrayList<>();
        scheduleIds.add("scheduleId1");
        String realmId = "testRealm";
        when(schedulingSvcAdapter.invokeScheduleServiceForDelete(any(), any())).thenReturn(new ArrayList<>());
        List<SchedulingSvcResponse> result = schedulingService.deleteSchedules(scheduleIds, realmId);

        assertNotNull(result);
    }

    @Test
    public void test_deleteSchedules_emptyList() {
        List<String> scheduleIds = Collections.emptyList();
        String realmId = "testRealm";
        try {
            schedulingService.deleteSchedules(scheduleIds, realmId);
        } catch (WorkflowGeneralException workflowGeneralException) {
            Assert.assertEquals(
                    WorkflowError.INPUT_INVALID, workflowGeneralException.getWorkflowError());
        }
    }

    @Test
    public void testIsEnabled_WorkflowNameMatches_FFEnabled() {
        when(featureFlagManager.getBoolean(any(), any())).thenReturn(true);
        DefinitionDetails definitionDetails = Mockito.mock(DefinitionDetails.class);
        TemplateDetails templateDetails = new TemplateDetails();
        templateDetails.setTemplateName(WorkflowNameEnum.CUSTOM_SCHEDULED_ACTIONS.getName());
        when(definitionDetails.getTemplateDetails()).thenReturn(templateDetails);
        assertTrue(schedulingService.isEnabled(definitionDetails,"realmId"));
    }

    @Test
    public void testIsEnabled_WorkflowNameMatches_FFDisabled() {
        when(featureFlagManager.getBoolean(any(), any())).thenReturn(false);
        DefinitionDetails definitionDetails = Mockito.mock(DefinitionDetails.class);
        TemplateDetails templateDetails = new TemplateDetails();
        templateDetails.setTemplateName(WorkflowNameEnum.CUSTOM_SCHEDULED_ACTIONS.getName());
        when(definitionDetails.getTemplateDetails()).thenReturn(templateDetails);
        assertFalse(schedulingService.isEnabled(definitionDetails, "realmId"));
    }

    @Test
    public void testIsEnabled_WorkflowNameDoesNotMatch() {
        DefinitionDetails definitionDetails = Mockito.mock(DefinitionDetails.class);
        TemplateDetails templateDetails = new TemplateDetails();
        templateDetails.setTemplateName("someotherworkflow");
        when(definitionDetails.getTemplateDetails()).thenReturn(templateDetails);
        assertFalse(schedulingService.isEnabled(definitionDetails, "realmId"));
    }

    @Test
    public void testIsEnabled_RecurrenceWithTimeZone() {
        DefinitionDetails definitionDetails = Mockito.mock(DefinitionDetails.class);
        TemplateDetails templateDetails = new TemplateDetails();
        templateDetails.setTemplateName("someotherworkflow");
        when(definitionDetails.getTemplateDetails()).thenReturn(templateDetails);
        when(definitionDetails.getDefinitionData()).thenReturn(new byte[0]);
        RecurrenceRule recurrenceRule = new RecurrenceRule();
        recurrenceRule.setTimeZone("UTC");
        assertFalse(schedulingService.isEnabled(definitionDetails, "realmId"));
    }

    @Test
    public void testIsEnabled_RecurrenceWithPlaceholderValuesNull() {
        DefinitionDetails definitionDetails = Mockito.mock(DefinitionDetails.class);
        TemplateDetails templateDetails = new TemplateDetails();
        templateDetails.setTemplateName("someotherworkflow");
        when(definitionDetails.getTemplateDetails()).thenReturn(templateDetails);
        assertFalse(schedulingService.isEnabled(definitionDetails, "realmId"));
    }

    @Test
    public void testIsEnabled_RecurrenceWithPlaceholderValuesUserVariablesNull() {
        DefinitionDetails definitionDetails = Mockito.mock(DefinitionDetails.class);
        TemplateDetails templateDetails = new TemplateDetails();
        templateDetails.setTemplateName("someotherworkflow");
        when(definitionDetails.getPlaceholderValue()).thenReturn("{\"" + WorkflowConstants.USER_VARIABLES + "\": null}");
        when(definitionDetails.getTemplateDetails()).thenReturn(templateDetails);
        assertFalse(schedulingService.isEnabled(definitionDetails, "realmId"));
    }

    @Test
    public void testIsEnabled_RecurrenceWithPlaceholderValuesEmptyUserVariables() {
        DefinitionDetails definitionDetails = Mockito.mock(DefinitionDetails.class);
        TemplateDetails templateDetails = new TemplateDetails();
        templateDetails.setTemplateName("someotherworkflow");
        when(definitionDetails.getPlaceholderValue()).thenReturn("{\"" + WorkflowConstants.USER_VARIABLES + "\": {}}");
        when(definitionDetails.getTemplateDetails()).thenReturn(templateDetails);
        assertFalse(schedulingService.isEnabled(definitionDetails, "realmId"));
    }

    @Test
    public void testIsEnabled_RecurrenceWithPlaceholderValuesRecurrence() {
        DefinitionDetails definitionDetails = Mockito.mock(DefinitionDetails.class);
        TemplateDetails templateDetails = new TemplateDetails();
        templateDetails.setTemplateName("someotherworkflow");
        JSONObject userVariables = new JSONObject();
        RecurrenceRule recurrenceRule = new RecurrenceRule();
        recurrenceRule.setTimeZone("UTC");
        TimeDuration timeDuration = new TimeDuration();
        timeDuration.setHours(5);
        timeDuration.setMinutes(45);
        recurrenceRule.setRecurrenceTime(timeDuration);
        userVariables.put(WorkFlowVariables.RECURRENCE_RULE_KEY.getName(), ObjectConverter.toJson(recurrenceRule));

        String placeholderValues = new JSONObject()
                .put(WorkflowConstants.USER_VARIABLES, userVariables)
                .toString();
        when(definitionDetails.getPlaceholderValue()).thenReturn(placeholderValues);
        when(definitionDetails.getTemplateDetails()).thenReturn(templateDetails);
        assertTrue(schedulingService.isEnabled(definitionDetails, "realmId"));
    }

    @Test
    public void testIsEnabled_RecurrenceDisabled() {
        DefinitionDetails definitionDetails = Mockito.mock(DefinitionDetails.class);
        TemplateDetails templateDetails = new TemplateDetails();
        templateDetails.setTemplateName("someotherworkflow");
        when(definitionDetails.getTemplateDetails()).thenReturn(templateDetails);
        RecurrenceRule recurrenceRule = new RecurrenceRule();
        assertFalse(schedulingService.isEnabled(definitionDetails, "realmId"));
    }

    @Test
    public void testIsEnabled_WorkflowNameMatches_FFEnabled_Definition() {
        when(featureFlagManager.getBoolean(any(), any())).thenReturn(true);
        Definition definition = Mockito.mock(Definition.class);
        when(definition.getName()).thenReturn(WorkflowNameEnum.CUSTOM_SCHEDULED_ACTIONS.getName());
        assertTrue(schedulingService.isEnabled(definition,"realmId"));
    }

    @Test
    public void testIsEnabled_WorkflowNameMatches_FFDisabled_Definition() {
        when(featureFlagManager.getBoolean(any(), any())).thenReturn(false);
        Definition definition = Mockito.mock(Definition.class);
        when(definition.getName()).thenReturn(WorkflowNameEnum.CUSTOM_SCHEDULED_ACTIONS.getName());
        assertFalse(schedulingService.isEnabled(definition, "realmId"));
    }

    @Test
    public void testIsEnabled_WorkflowNameDoesNotMatch_Definition() {
        Definition definition = Mockito.mock(Definition.class);
        when(definition.getName()).thenReturn("someotherworkflow");
        assertFalse(schedulingService.isEnabled(definition, "realmId"));
    }

    @Test
    public void testIsEnabled_RecurrenceWithTimeZone_Definition() {
        Definition definition = Mockito.mock(Definition.class);
        when(definition.getName()).thenReturn("someotherworkflow");
        RecurrenceRule recurrenceRule = new RecurrenceRule();
        recurrenceRule.setTimeZone("UTC");
        when(definition.getRecurrence()).thenReturn(recurrenceRule);
        assertFalse(schedulingService.isEnabled(definition, "realmId"));
    }

    @Test
    public void testIsEnabled_RecurrenceWithDefinitionData_Definition() {
        Definition definition = Mockito.mock(Definition.class);
        when(definition.getName()).thenReturn("someotherworkflow");
        RecurrenceRule recurrenceRule = new RecurrenceRule();
        recurrenceRule.setTimeZone("UTC");
        TimeDuration timeDuration = new TimeDuration();
        timeDuration.setHours(5);
        timeDuration.setMinutes(45);
        recurrenceRule.setRecurrenceTime(timeDuration);
        when(definition.getRecurrence()).thenReturn(recurrenceRule);
        assertTrue(schedulingService.isEnabled(definition, "realmId"));
    }

    @Test
    public void testIsMigratedToSchedulingSvc_AllMigrated() {
        SchedulerDetails schedulerDetails1 = new SchedulerDetails();
        schedulerDetails1.setIsMigrated(true);
        SchedulerDetails schedulerDetails2 = new SchedulerDetails();
        schedulerDetails2.setIsMigrated(true);

        when(schedulerDetailsRepository.findByDefinitionDetails(definitionDetails))
                .thenReturn(Optional.of(List.of(schedulerDetails1, schedulerDetails2)));
        when(schedulingSvcConfig.isMigrationEnabled()).thenReturn(true);
        assertTrue(schedulingService.isMigrated(definitionDetails));
    }

    @Test
    public void testIsMigratedToSchedulingSvc_NotAllMigrated() {
        SchedulerDetails schedulerDetails1 = new SchedulerDetails();
        schedulerDetails1.setIsMigrated(true);
        SchedulerDetails schedulerDetails2 = new SchedulerDetails();
        schedulerDetails2.setIsMigrated(false);

        when(schedulerDetailsRepository.findByDefinitionDetails(definitionDetails))
                .thenReturn(Optional.of(List.of(schedulerDetails1, schedulerDetails2)));
        when(schedulingSvcConfig.isMigrationEnabled()).thenReturn(true);
        assertFalse(schedulingService.isMigrated(definitionDetails));
    }

    @Test
    public void testIsMigrated_EmptyList() {
        when(schedulerDetailsRepository.findByDefinitionDetails(definitionDetails))
                .thenReturn(Optional.of(List.of()));
        when(schedulingSvcConfig.isMigrationEnabled()).thenReturn(true);
        assertFalse(schedulingService.isMigrated(definitionDetails));
    }

    @Test
    public void testIsMigrated_NoDetails() {
        when(schedulerDetailsRepository.findByDefinitionDetails(definitionDetails))
                .thenReturn(Optional.empty());
        when(schedulingSvcConfig.isMigrationEnabled()).thenReturn(true);
        assertFalse(schedulingService.isMigrated(definitionDetails));
    }

    @Test
    public void testIsMigrated_ConfigDisabled() {
        when(schedulingSvcConfig.isMigrationEnabled()).thenReturn(false);
        assertFalse(schedulingService.isMigrated(definitionDetails));
    }
}

