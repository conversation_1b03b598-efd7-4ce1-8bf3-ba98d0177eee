package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.capability;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;

import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.ReadCustomDefinitionHandlerTest;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler.TransactionEntityFactory;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler.TriggerHandlerTestData;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.AuthHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ActivityDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.BpmnComponentType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CustomWorkflowType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DefinitionType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.InternalStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ModelType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.TemplateCategory;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter.TransactionEntity;
import com.intuit.v4.workflows.Definition;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import lombok.SneakyThrows;
import org.apache.commons.io.IOUtils;
import org.javatuples.Pair;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class CustomWorkflowTemplateQueryCapabilityTest {

  private static final String ACTIVITY_ATTRIBUTES_WITHOUT_STARTABLE_EVENTS = "{\"modelAttributes\": {\"stepDetails\": \"{\\\"startEvent\\\":[\\\"startEvent\\\",\\\"Account-Manager\\\"] }\", \"taskDetails\": \"{ \\\"required\\\": true }\", \"activityName\": \"startEvent\", \"handlerDetails\": \"{\\\"taskHandler\\\":\\\"was\\\",\\\"recordType\\\":\\\"test\\\"}\", \"currentStepDetails\": \"{ \\\"required\\\": true }\", \"processVariablesDetails\": \"[{\\\"variableName\\\":\\\"userId\\\",\\\"variableType\\\":\\\"String\\\"},{\\\"variableName\\\":\\\"assigneeId\\\",\\\"variableType\\\":\\\"String\\\"}]\", \"ignoreProcessVariablesDetails\": \"true\"}}";

  private final TemplateDetails bpmnTemplateDetail = TriggerHandlerTestData.getBPMNTemplateDetails();

  private static final String CUSTOM_REMINDER_BPMN = "bpmn/customWorkflowDefinition.bpmn";

  @Mock
  private ActivityDetailsRepository activityDetailsRepository;

  @Mock
  private AuthHelper authHelper;

  @Mock
  private DefinitionDetailsRepository definitionDetailsRepository;

  @Mock
  private WASContextHandler contextHandler;

  @InjectMocks
  private CustomWorkflowQueryCapability customWorkflowQueryCapability;

  private TransactionEntity transactionEntityUpdated;

  private byte[] readBPMNAsBytes() throws IOException {
    try (InputStream fisBpmn =
        ReadCustomDefinitionHandlerTest.class
            .getClassLoader()
            .getResourceAsStream(CUSTOM_REMINDER_BPMN)) {
      return IOUtils.toByteArray(fisBpmn);
    }
  }

  @SneakyThrows
  @Before
  public void prepareMockData() {
    transactionEntityUpdated =
    		TransactionEntityFactory.getInstanceOf(TriggerHandlerTestData.prepareV3TriggerMessage("created", "bill").getTriggerMessage(),
    	            contextHandler);
    Mockito.when(authHelper.getOwnerId()).thenReturn("12345");
    TemplateDetails templateDetails = new TemplateDetails();
    DefinitionDetails definitionDetails = new DefinitionDetails();
    definitionDetails.setTemplateDetails(templateDetails);
    definitionDetails.setDefinitionData(readBPMNAsBytes());
    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();
    definitionDetailsList.add(definitionDetails);
    templateDetails.setDefinitionType(DefinitionType.USER);
    Mockito.when(
            definitionDetailsRepository
                .findAllEnabledDefinitionsForOwnerIdRecordTypeAndTemplateName(
                    Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
        .thenReturn(Optional.ofNullable(definitionDetailsList));
    Mockito.when(
        definitionDetailsRepository
            .findLatestEnabledDefinitionsForOwnerIdRecordTypeAndTemplateName(
                Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyBoolean() ))
        .thenReturn(Optional.ofNullable(definitionDetailsList));
    CustomWorkflowConfig customWorkflowConfig = TestHelper.loadCustomConfig();
    customWorkflowQueryCapability =
        new CustomWorkflowQueryCapability(activityDetailsRepository,
            authHelper, definitionDetailsRepository, customWorkflowConfig);
  }

  @Test
  public void getTemplateDetailsTestSuccess() {
    transactionEntityUpdated =
    		TransactionEntityFactory.getInstanceOf(TriggerHandlerTestData.prepareV3TriggerMessage("created", "bill").getTriggerMessage(),
            contextHandler);

    List<TemplateDetails> templateDetailsList =
        customWorkflowQueryCapability.getTemplateDetails(transactionEntityUpdated);

    assertEquals(1, templateDetailsList.size());
  }

  @Test
  public void getTemplateDataTestSuccess() {
    transactionEntityUpdated =
    		TransactionEntityFactory.getInstanceOf(TriggerHandlerTestData.prepareV3TriggerMessage("created", "bill").getTriggerMessage(),
            contextHandler);

    byte[] templateData = customWorkflowQueryCapability.getTemplateData(transactionEntityUpdated);

    assertTrue(templateData.length > 0);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void getTemplateDataTestFailure() {
    DefinitionDetails definitionDetails = new DefinitionDetails();
    definitionDetails.setInternalStatus(InternalStatus.MARKED_FOR_DELETE);

    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();
    definitionDetailsList.add(definitionDetails);

    Mockito
        .when(definitionDetailsRepository
            .findLatestEnabledDefinitionsForOwnerIdRecordTypeAndTemplateName(Mockito.any(),
                Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyBoolean()))
            .thenReturn(
            Optional.of(definitionDetailsList));

    transactionEntityUpdated = TransactionEntityFactory.getInstanceOf(TriggerHandlerTestData.prepareV3TriggerMessage("created", "bill").getTriggerMessage(),
            contextHandler);

    byte[] templateData = customWorkflowQueryCapability.getTemplateData(transactionEntityUpdated);

    assertTrue(templateData.length > 0);
  }

  @Test
  public void getTemplateDataFromDefinitionData() throws IOException {
    transactionEntityUpdated =
        TransactionEntityFactory.getInstanceOf(TriggerHandlerTestData.prepareV3TriggerMessage("created", "bill").getTriggerMessage(),
            contextHandler);

    TemplateDetails templateDetails = new TemplateDetails();
    templateDetails.setTemplateData(readBPMNAsBytes());

    DefinitionDetails definitionDetails = mock(DefinitionDetails.class);
    Mockito.when(definitionDetails.getTemplateDetails()).thenReturn(templateDetails);
    Mockito.when(definitionDetails.getDefinitionData()).thenReturn(null);

    Mockito.when(
            definitionDetailsRepository
                .findLatestEnabledDefinitionsForOwnerIdRecordTypeAndTemplateName(
                    Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyBoolean() ))
        .thenReturn(Optional.of(List.of(definitionDetails)));

    byte[] templateData = customWorkflowQueryCapability.getTemplateData(transactionEntityUpdated);

    Mockito.verify(definitionDetails, Mockito.times(1)).getTemplateDetails();
    assertTrue(templateData.length > 0);
  }

  @Test
  public void getDmnTemplateDetailsSuccess() {
    transactionEntityUpdated =
    		TransactionEntityFactory.getInstanceOf(TriggerHandlerTestData.prepareV3TriggerMessage("created").getTriggerMessage(),
            contextHandler);
    bpmnTemplateDetail.setTemplateName("customApproval");
    DefinitionDetails definitionDetails =
        TriggerHandlerTestData.getDefinitionDetails(bpmnTemplateDetail);
    definitionDetails.setDefinitionData(definitionDetails.getTemplateDetails().getTemplateData());

    Mockito.when(definitionDetailsRepository.findByParentId(Mockito.any()))
        .thenReturn(Optional.of(Collections.singletonList(definitionDetails)));
    Pair<String, byte[]> templateDetails =
        customWorkflowQueryCapability.getDmnTemplateDetails(
            transactionEntityUpdated.getEventHeaders().getProviderWorkflowId(),
            Collections.singletonList(definitionDetails));

    assertEquals("customApproval", templateDetails.getValue0());
    assertTrue(templateDetails.getValue1().length > 0);
  }

  @Test
  public void getEnabledDefinitionsSuccess() {
    transactionEntityUpdated =
    		TransactionEntityFactory.getInstanceOf(TriggerHandlerTestData.prepareV3TriggerMessage("created").getTriggerMessage(),
            contextHandler);

    List<DefinitionDetails> defList =
        customWorkflowQueryCapability.getEnabledDefinitions(transactionEntityUpdated, true);

    assertEquals(1, defList.size());
  }

  @Test
  public void isCustomWorkflowTest() {
    Assert.assertTrue(customWorkflowQueryCapability.isCustomWorkflow("approval", "invoice"));
    Assert.assertTrue(customWorkflowQueryCapability.isCustomWorkflow("approval", "bill"));
    Assert.assertTrue(customWorkflowQueryCapability.isCustomWorkflow("reminder", "bill"));
  }

  @Test
  public void isCustomWorkflowTemplateTest() {
    Assert.assertTrue(customWorkflowQueryCapability.isCustomWorkflow("customApproval", RecordType.INVOICE));
    Assert.assertTrue(customWorkflowQueryCapability.isCustomWorkflow("customApproval", RecordType.INVOICE));
    Assert.assertTrue(customWorkflowQueryCapability.isCustomWorkflow("customReminder", RecordType.INVOICE));
  }

  @Test
  public void getCustomWorkflowTemplateNameTest() {
    Assert.assertEquals(
        "customApproval", CustomWorkflowType.getTemplateName("approval"));
    Assert.assertEquals(
        "customReminder", CustomWorkflowType.getTemplateName("reminder"));
    Assert.assertNull(CustomWorkflowType.getTemplateName("approval1"));
  }

  @Test
  public void isCustomDefinitionPresentForWorkflowTest() {
    Mockito.when(definitionDetailsRepository.findAllEnabledDefinitionsForOwnerIdAndTemplateName(12345L, ModelType.BPMN, "invoiceapproval", false))
        .thenReturn(Optional.of(Collections.singletonList(new DefinitionDetails())));
    Mockito.when(definitionDetailsRepository.findAllEnabledDefinitionsForOwnerIdAndTemplateName(12345L, ModelType.BPMN, "billapproval", false))
        .thenReturn(Optional.empty());
    Assert.assertTrue(customWorkflowQueryCapability.isPrecannedDefinitionPresentForWorkflow("approval", "invoice"));
    Assert.assertFalse(customWorkflowQueryCapability.isPrecannedDefinitionPresentForWorkflow("approval", "bill"));
  }

  @Test
  public void isLatestCustomDefinitionForWorkflowTest() {
    Mockito.when(definitionDetailsRepository.findLatestEnabledDefinitionsForOwnerIdRecordTypeAndTemplateName(12345L, ModelType.BPMN, RecordType.INVOICE, "customApproval", false))
        .thenReturn(Optional.of(Collections.singletonList(new DefinitionDetails())));
    Assert.assertTrue(customWorkflowQueryCapability.isLatestCustomPresentForWorkflow("approval", "invoice"));
  }

  @Test
  public void isNotLatestCustomDefinitionForWorkflowTest() {
    Mockito.when(definitionDetailsRepository.findLatestEnabledDefinitionsForOwnerIdRecordTypeAndTemplateName(12345L, ModelType.BPMN, RecordType.INVOICE, "customApproval", false))
        .thenReturn(Optional.empty());
    Assert.assertFalse(customWorkflowQueryCapability.isLatestCustomPresentForWorkflow("approval", "invoice"));
  }

  @Test
  public void getEnabledDefinition_withNoEnabledDefinition_thenThrowException() {
    transactionEntityUpdated =
    		TransactionEntityFactory.getInstanceOf(TriggerHandlerTestData.prepareV3TriggerMessage("created").getTriggerMessage(),
            contextHandler);
    Optional<List<DefinitionDetails>> optionalDefinitionDetailsList = Optional.empty();
    doReturn(optionalDefinitionDetailsList)
        .when(definitionDetailsRepository)
        .findAllEnabledDefinitionsForOwnerIdRecordTypeAndTemplateName(12345L, ModelType.BPMN, RecordType.INVOICE, "customApproval");
    try {
      customWorkflowQueryCapability.getEnabledDefinitions(
          transactionEntityUpdated, false);
      Assert.fail("WorkflowGeneralException expected");
    } catch (WorkflowGeneralException e) {
      Assert.assertEquals(
          e.getWorkflowError(), WorkflowError.ENABLED_DEFINITION_NOT_FOUND);
    }
  }

  @Test
  public void getEnabledDefinition_withEmptyEnabledDefinition_thenThrowException() {
    transactionEntityUpdated =
    		TransactionEntityFactory.getInstanceOf(TriggerHandlerTestData.prepareV3TriggerMessage("created").getTriggerMessage(),
    	            contextHandler);
    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();
    Optional<List<DefinitionDetails>> optionalDefinitionDetailsList = Optional.of(definitionDetailsList);
    doReturn(optionalDefinitionDetailsList)
        .when(definitionDetailsRepository)
        .findAllEnabledDefinitionsForOwnerIdRecordTypeAndTemplateName(12345L, ModelType.BPMN, RecordType.INVOICE, "customApproval");
    try {
      customWorkflowQueryCapability.getEnabledDefinitions(
          transactionEntityUpdated, false);
      Assert.fail("WorkflowGeneralException expected");
    } catch (WorkflowGeneralException e) {
      Assert.assertEquals(
              WorkflowError.ENABLED_DEFINITION_NOT_FOUND, e.getWorkflowError());
    }
  }

  @Test
  public void isPrecannedDefinitionPresentDuringMigrationTest() {
    String definitionId = "111";

    Definition definition = TestHelper.mockDefinitionEntity();
    Assert.assertFalse(
        customWorkflowQueryCapability.isPrecannedDefinitionPresentDuringMigration(definition));

    definition.setId(TestHelper.getGlobalId("1234"));
    definition.getId().setLocalId(definitionId);
    Assert.assertFalse(
        customWorkflowQueryCapability.isPrecannedDefinitionPresentDuringMigration(definition));

    bpmnTemplateDetail.setTemplateName("invoiceapproval");
    DefinitionDetails definitionDetails =
        TriggerHandlerTestData.getDefinitionDetails(bpmnTemplateDetail);
    definitionDetails.setDefinitionId(definitionId);
    definitionDetails.setDefinitionData(definitionDetails.getTemplateDetails().getTemplateData());
    definitionDetails.getTemplateDetails().setTemplateCategory(TemplateCategory.HUB.toString());
    Optional<DefinitionDetails> optionalDefinitionDetails = Optional.of(definitionDetails);
    Mockito.when(definitionDetailsRepository.findByDefinitionId(Mockito.any()))
        .thenReturn(optionalDefinitionDetails);
    Assert.assertTrue(customWorkflowQueryCapability.isPrecannedDefinitionPresentDuringMigration(
        definition));

  }

  @Test
  public void testGetExtensionPropertiesForStartEvents_WithNoStartableEvents() {
    ActivityDetail activityDetail = new ActivityDetail();
    activityDetail.setId(1L);
    activityDetail.setActivityId("startEvent");
    activityDetail.setActivityType(BpmnComponentType.START_EVENT.getName());
    activityDetail.setAttributes(ACTIVITY_ATTRIBUTES_WITHOUT_STARTABLE_EVENTS);

    ActivityDetail result = customWorkflowQueryCapability
            .fetchInitialStartEventActivityDetail(transactionEntityUpdated);

    Assert.assertNull(result.getAttributes());
  }

  @Test
  public void testGetExtensionPropertiesForStartEvents_NoEnabledDefinition() {
    Mockito.when(definitionDetailsRepository.findLatestEnabledDefinitionsForOwnerIdRecordTypeAndTemplateName(
            ArgumentMatchers.anyLong(), ArgumentMatchers.any(ModelType.class), ArgumentMatchers.any(RecordType.class),
                    ArgumentMatchers.anyString(), ArgumentMatchers.eq(false)))
            .thenReturn(Optional.empty());

    Assert.assertThrows(WorkflowGeneralException.class,
            () -> customWorkflowQueryCapability.fetchInitialStartEventActivityDetail(transactionEntityUpdated));
  }

}
