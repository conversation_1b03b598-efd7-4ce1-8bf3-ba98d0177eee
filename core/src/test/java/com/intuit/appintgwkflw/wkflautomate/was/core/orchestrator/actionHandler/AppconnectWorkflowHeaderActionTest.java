package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import com.intuit.appintgwkflw.wkflautomate.was.common.util.httpClient.AppConnectWASClient;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.AuthDetailsService;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.AuthDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.request.AppConnectTaskHandlerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.request.WorkflowTaskHandlerAction;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.response.WorkflowTaskHandlerResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpRequest;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;

/**
 * <AUTHOR>
 */
public class AppconnectWorkflowHeaderActionTest {

  @Mock
  private AppConnectWASClient wasHttpClient;

  @Mock
  private AuthDetailsService authDetailsService;

  @InjectMocks
  private AppconnectWorkflowHeaderAction taskHandlerAction;

  @Before
  public void init() {

    MockitoAnnotations.initMocks(this);
  }

  @Test
  public void testExecute() {

    final WorkflowTaskHandlerAction workflowTaskHandlerAction =
        WorkflowTaskHandlerAction.builder().bpmnTaskId("bpmnID").build();
    final AppConnectTaskHandlerRequest actionRequest =
        AppConnectTaskHandlerRequest.builder()
            .workflowId("TEST")
            .instanceId("iID")
            .externalTaskId("EXTERNAL_ID")
            .action(workflowTaskHandlerAction)
            .build();

    final WASHttpRequest<AppConnectTaskHandlerRequest, WorkflowTaskHandlerResponse> wasHttpRequest =
        WASHttpRequest.<AppConnectTaskHandlerRequest, WorkflowTaskHandlerResponse>builder()
            .url(actionRequest.getEndpoint())
            .request(actionRequest)
            .httpMethod(HttpMethod.POST)
            .build();

    try {
      taskHandlerAction.execute(wasHttpRequest);
    } catch (final Exception e) {
      Assert.fail();
    }
  }

  @Test
  public void testExecuteWithHeaders() {

    final WorkflowTaskHandlerAction workflowTaskHandlerAction =
        WorkflowTaskHandlerAction.builder().bpmnTaskId("bpmnID").build();
    final AppConnectTaskHandlerRequest actionRequest =
        AppConnectTaskHandlerRequest.builder()
            .workflowId("TEST")
            .instanceId("iID")
            .externalTaskId("EXTERNAL_ID")
            .action(workflowTaskHandlerAction)
            .build();

    final HttpHeaders requestHeaders = new HttpHeaders();
    requestHeaders.setContentType(MediaType.APPLICATION_JSON);
    requestHeaders.set("TEST", "TEST");

    final WASHttpRequest<AppConnectTaskHandlerRequest, WorkflowTaskHandlerResponse> wasHttpRequest =
        WASHttpRequest.<AppConnectTaskHandlerRequest, WorkflowTaskHandlerResponse>builder()
            .url(actionRequest.getEndpoint())
            .request(actionRequest)
            .httpMethod(HttpMethod.POST)
            .requestHeaders(requestHeaders)
            .build();

    final WorkflowTaskHandlerResponse handlerResponse = new WorkflowTaskHandlerResponse();
    handlerResponse.setSuccess("true");

    taskHandlerAction.execute(wasHttpRequest);
    Assert.assertTrue(wasHttpRequest.getRequestHeaders().getFirst("TEST").equals("TEST"));
  }
}
