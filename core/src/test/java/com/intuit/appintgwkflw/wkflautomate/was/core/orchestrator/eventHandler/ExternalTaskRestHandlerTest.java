package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.eventHandler;

import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.EventHeaderConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.externaltask.ExternalTaskCompleted;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.externaltask.ExternalTaskStatus;
import com.intuit.v4.workflows.tasks.Task;
import java.util.HashMap;
import java.util.Map;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ExternalTaskRestHandlerTest {

  @InjectMocks
  ExternalTaskRestHandler handler;

  @Mock
    ExternalTaskEventHandlerHelper externalTaskEventHandlerHelper;


  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
  }

  @Test
  public void testeventEntityName() throws Exception {
    EventEntityType eventEntityType = handler.getName();
    Assert.assertEquals(EventEntityType.EXTERNALTASKREST, eventEntityType);
  }

  @Test
    public void testExecute() throws Exception {
    ExternalTaskCompleted event = ExternalTaskCompleted.builder().status(ExternalTaskStatus.SUCCESS.getStatus()).build();
    Map<String, String> headers = new HashMap<>();
    handler.execute(event, headers);
    Mockito.verify(externalTaskEventHandlerHelper, Mockito.times(1)).handleOtherStatus(event, headers, EventEntityType.EXTERNALTASKREST);
  }

  @Test
    public void testExecuteUpdate() throws Exception {
    ExternalTaskCompleted event = ExternalTaskCompleted.builder().status(ExternalTaskStatus.SUCCESS.getStatus()).build();
    Map<String, String> headers = new HashMap<>();
    Task task = new Task();
    task.setActivityInstanceId("activityInstanceId");
    ArgumentCaptor<Map<String, String>> argumentCaptor = ArgumentCaptor.forClass(Map.class);
    Mockito.doNothing().when(externalTaskEventHandlerHelper).handleOtherStatus(Mockito.any(), argumentCaptor.capture(), Mockito.eq(EventEntityType.EXTERNALTASKREST));

    handler.executeUpdate(task, event, headers);

    Assert.assertTrue(argumentCaptor.getValue().containsKey(EventHeaderConstants.ENTITY_ID));
    Assert.assertEquals("activityInstanceId: ", argumentCaptor.getValue().get(EventHeaderConstants.ENTITY_ID));
    Mockito.verify(externalTaskEventHandlerHelper, Mockito.times(1)).handleOtherStatus(Mockito.any(), Mockito.anyMap(), Mockito.eq(EventEntityType.EXTERNALTASKREST));
  }
}
