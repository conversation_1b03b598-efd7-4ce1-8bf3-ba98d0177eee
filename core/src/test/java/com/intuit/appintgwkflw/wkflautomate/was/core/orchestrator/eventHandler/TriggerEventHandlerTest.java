package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.eventHandler;

import static org.mockito.Mockito.never;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.RunTimeService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.EventHeaderConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.TriggerTargetAPI;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.trigger.EntityChangeIdentifier;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.trigger.MetaData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.trigger.Trigger;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.trigger.Variables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.metaDataTags.MetaDataTags;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.ResponseStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.TriggerStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowGenericResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowTriggerResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowTriggersResponse;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter.EventHeaders;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class TriggerEventHandlerTest {

  @Mock private RunTimeService runTimeService;
  @Mock private WASContextHandler handler;
  @Mock private MetricLogger metricsLogger;
  @InjectMocks private TriggerEventHandler triggerHandler;
  private static final String TRIGGER_ID = "1";
  private static final String TRIGGER_WORKFLOW = "approval";
  private static final String TRIGGER_TYPE = "invoice";
  private static final String TRIGGER_MESSAGE = "created";
  private static final String OWNER_ID = "1234";

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
  }

  @Test
  public void testTransformEvent() throws Exception {
    Trigger workflowTriggerEvent = getTriggerPayload("1","invoice","approval","created");
    Trigger event = triggerHandler.transform(ObjectConverter.toJson(workflowTriggerEvent));

    Assert.assertNotNull(event);
    Assert.assertNotNull(event.getMetaData());
    Assert.assertEquals("1", event.getMetaData().getEntityId());
    Assert.assertEquals("invoice", event.getMetaData().getEntityType());
    Assert.assertEquals("approval", event.getMetaData().getWorkflow());
    Assert.assertEquals(
        "created", event.getMetaData().getEntityChangeIdentifier().getMessageName());

    Assert.assertEquals(Collections.EMPTY_MAP, event.getEntity());
    Assert.assertEquals(Collections.EMPTY_MAP, event.getVariables().getLocal());
    Assert.assertEquals(Collections.EMPTY_MAP, event.getVariables().getGlobal());
  }


  @Test(expected = WorkflowGeneralException.class)
  public void testTransformNullEvent() throws Exception {
    triggerHandler.transform(null);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testTransformIncorrectEvent() throws Exception {
    triggerHandler.transform("hello");
  }

  @Test
  public void testExecuteMissingEntityId() throws Exception {
    Trigger event = getTriggerPayload(null, "invoice", "approval", "created");
    triggerHandler.execute(event, getHeadersWithOwnerId());
    Mockito.verify(metricsLogger)
        .logErrorMetric(
            Mockito.eq(MetricName.EVENT_TRIGGER), Mockito.eq(Type.EVENT_METRIC), Mockito.any());
  }

  @Test
  public void testExecuteMissingRecordType() throws Exception {
    Trigger event = getTriggerPayload("1", null, "approval", "created");
    triggerHandler.execute(event, getHeadersWithOwnerId());
    Mockito.verify(metricsLogger)
        .logErrorMetric(
            Mockito.eq(MetricName.EVENT_TRIGGER), Mockito.eq(Type.EVENT_METRIC), Mockito.any());
  }

  @Test
  public void testExecuteMissingWorkflow() throws Exception {
    Trigger event = getTriggerPayload("1", "invoice", null, "created");
    triggerHandler.execute(event, getHeadersWithOwnerId());
    Mockito.verify(metricsLogger)
        .logErrorMetric(
            Mockito.eq(MetricName.EVENT_TRIGGER), Mockito.eq(Type.EVENT_METRIC), Mockito.any());
  }

  @Test
  public void testExecuteMissingEntityChangeType() throws Exception {
    Trigger event = getTriggerPayload("1", "invoice", "approval", null);
    triggerHandler.execute(event, getHeadersWithOwnerId());
    Mockito.verify(metricsLogger)
        .logErrorMetric(
            Mockito.eq(MetricName.EVENT_TRIGGER), Mockito.eq(Type.EVENT_METRIC), Mockito.any());
  }

  @Test
  public void testExecuteIncorrectRecordType() throws Exception {
    Trigger event = getTriggerPayload("1", "invoices", "approval", "created");
    triggerHandler.execute(event, getHeadersWithOwnerId());
    Mockito.verify(metricsLogger)
        .logErrorMetric(
            Mockito.eq(MetricName.EVENT_TRIGGER), Mockito.eq(Type.EVENT_METRIC), Mockito.any());
  }

  @Test
  public void testExecuteMissingMetaData() throws Exception {
    Trigger event = new Trigger(null, null, Collections.EMPTY_MAP);
    triggerHandler.execute(event, getHeadersWithOwnerId());
    Mockito.verify(metricsLogger)
        .logErrorMetric(
            Mockito.eq(MetricName.EVENT_TRIGGER), Mockito.eq(Type.EVENT_METRIC), Mockito.any());
  }

  @Test
  public void testExecuteMissingOwner() throws Exception {
    Trigger event = getTriggerPayload("1", "invoice", "approval", "created");
    triggerHandler.execute(event, new HashMap<String, String>());
    Mockito.verify(metricsLogger)
        .logErrorMetric(
            Mockito.eq(MetricName.EVENT_TRIGGER), Mockito.eq(Type.EVENT_METRIC), Mockito.any());
  }

  @Test
  public void testExecute() throws Exception {
    Trigger event = getTriggerPayload("1", "invoice", "approval", "created");
    Map<String, String> headers = new HashMap<String, String>();
    headers.put(EventHeaderConstants.OWNER_ID, "1234");

    Mockito.when(runTimeService.processTriggerMessageV2(Mockito.any()))
        .thenReturn(WorkflowGenericResponse.builder().build());

    triggerHandler.execute(event, headers);
    Mockito.verify(runTimeService).processTriggerMessageV2(Mockito.any());
  }

  @Test
  public void testExecuteV1Trigger() throws Exception {
    Trigger event = getTriggerPayloadForV1Trigger("1", "invoice", "approval", "created");
    Map<String, String> headers = new HashMap<String, String>();
    headers.put(EventHeaderConstants.OWNER_ID, "1234");

    Mockito.when(runTimeService.processTriggerMessage(Mockito.any()))
            .thenReturn(WorkflowGenericResponse.builder().build());

    triggerHandler.execute(event, headers);
    Mockito.verify(runTimeService).processTriggerMessage(Mockito.any());
  }
  
  @Test
  public void testExecuteV1Trigger_tag1() throws Exception {
    Trigger event = getTriggerPayloadForV1Trigger("1", "invoice", "approval", "created");
    MetaDataTags tags = new MetaDataTags();
    tags.setVersion("1.0.0");
    event.getMetaData().setTags(tags);;
    Map<String, String> headers = new HashMap<String, String>();
    headers.put(EventHeaderConstants.OWNER_ID, "1234");

    Mockito.when(runTimeService.processTriggerMessage(Mockito.any()))
            .thenReturn(WorkflowGenericResponse.builder().build());

    triggerHandler.execute(event, headers);
    Mockito.verify(runTimeService).processTriggerMessage(Mockito.any());
  }
  @Test
  public void testExecuteV1Trigger_tag1_1() throws Exception {
    Trigger event = getTriggerPayloadForV1Trigger("1", "invoice", "approval", "created");
    MetaDataTags tags = new MetaDataTags();
    tags.setVersion("1.0.1");
    event.getMetaData().setTags(tags);;
    Map<String, String> headers = new HashMap<String, String>();
    headers.put(EventHeaderConstants.OWNER_ID, "1234");

    Mockito.when(runTimeService.processTriggerMessage(Mockito.any()))
            .thenReturn(WorkflowGenericResponse.builder().build());

    triggerHandler.execute(event, headers);
    Mockito.verify(runTimeService).processTriggerMessage(Mockito.any());
  }

  @Test
  public void testExecuteEvaluateAndTrigger() throws Exception {
    Trigger event = getTriggerPayloadForEvaluateAndTrigger("1", "invoice", "approval", "created");
    Map<String, String> headers = new HashMap<String, String>();
    headers.put(EventHeaderConstants.OWNER_ID, "1234");

    Mockito.when(runTimeService.processEvaluateAndTriggerMessage(Mockito.any()))
            .thenReturn(WorkflowGenericResponse.builder().build());

    triggerHandler.execute(event, headers);
    ArgumentCaptor<Map> mapArgumentCaptor = ArgumentCaptor.forClass(HashMap.class);
    Mockito.verify(runTimeService).processEvaluateAndTriggerMessage(mapArgumentCaptor.capture());
    EventHeaders eventHeaders = (EventHeaders)mapArgumentCaptor.getValue().get("eventHeaders");
    Assert.assertEquals("defKey", eventHeaders.getDefinitionKey());
  }

  @Test(expected = WorkflowRetriableException.class)
  public void testExecuteRetriableException() throws Exception {
    Trigger event = getTriggerPayload("1", "invoice", "approval", "created");
    Map<String, String> headers = new HashMap<String, String>();
    headers.put(EventHeaderConstants.OWNER_ID, "1234");

    Mockito.when(runTimeService.processTriggerMessageV2(Mockito.any()))
        .thenThrow(new WorkflowRetriableException(new NullPointerException()));

    triggerHandler.execute(event, headers);
    Mockito.verify(runTimeService).processTriggerMessageV2(Mockito.any());
    Mockito.verify(metricsLogger, never())
        .logErrorMetric(Mockito.any(), Mockito.any(), Mockito.any());
  }

  @Test
  public void testExecuteException() throws Exception {
    Trigger event = getTriggerPayload("1", "invoice", "approval", "created");
    Map<String, String> headers = new HashMap<String, String>();
    headers.put(EventHeaderConstants.OWNER_ID, "1234");

    Mockito.when(runTimeService.processTriggerMessageV2(Mockito.any()))
        .thenThrow(new WorkflowGeneralException("test"));

    triggerHandler.execute(event, headers);
    Mockito.verify(runTimeService).processTriggerMessageV2(Mockito.any());
    Mockito.verify(
        metricsLogger).logErrorMetric(Mockito.eq(MetricName.EVENT_TRIGGER), Mockito.any(),Mockito.any());
  }

  @Test
  public void testExecuteHandleResponseNoAction() throws Exception {
    Trigger event = getTriggerPayload("1", "invoice", "approval", "created");
    Map<String, String> headers = new HashMap<String, String>();
    headers.put(EventHeaderConstants.OWNER_ID, "1234");

    WorkflowResponse reponse =
        WorkflowTriggerResponse.builder().status(TriggerStatus.NO_ACTION).build();
    Mockito.when(runTimeService.processTriggerMessageV2(Mockito.any()))
        .thenReturn(
            WorkflowGenericResponse.builder()
                .status(ResponseStatus.SUCCESS)
                .response(reponse)
                .build());

    triggerHandler.execute(event, headers);
    Mockito.verify(runTimeService).processTriggerMessageV2(Mockito.any());
    Mockito.verify(metricsLogger)
        .logErrorMetric(
            Mockito.eq(MetricName.EVENT_TRIGGER_NO_ACTION), Mockito.any(), Mockito.any());
  }

  @Test
  public void testExecuteHandleResponsesNoAction() throws Exception {
    Trigger event = getTriggerPayload("1", "invoice", "approval", "created");
    Map<String, String> headers = new HashMap<String, String>();
    headers.put(EventHeaderConstants.OWNER_ID, "1234");

    WorkflowTriggerResponse responseSuccess =
        WorkflowTriggerResponse.builder().status(TriggerStatus.PROCESS_SIGNALLED).build();
    WorkflowTriggerResponse responseNoAct =
        WorkflowTriggerResponse.builder().status(TriggerStatus.NO_ACTION).build();

    List<WorkflowTriggerResponse> list =
        Arrays.asList(new WorkflowTriggerResponse[] {responseNoAct, responseSuccess});
    WorkflowTriggersResponse responses = new WorkflowTriggersResponse(list);

    Mockito.when(runTimeService.processTriggerMessageV2(Mockito.any()))
        .thenReturn(
            WorkflowGenericResponse.builder()
                .status(ResponseStatus.SUCCESS)
                .response(responses)
                .build());

    triggerHandler.execute(event, headers);
    Mockito.verify(runTimeService).processTriggerMessageV2(Mockito.any());
    Mockito.verify(metricsLogger)
        .logErrorMetric(
            Mockito.eq(MetricName.EVENT_TRIGGER_NO_ACTION), Mockito.any(), Mockito.any());
  }

  @Test
  public void testExecuteHandleResponseFailure() throws Exception {
    Trigger event = getTriggerPayload("1", "invoice", "approval", "created");
    Map<String, String> headers = new HashMap<String, String>();
    headers.put(EventHeaderConstants.OWNER_ID, "1234");

    WorkflowResponse reponse =
        WorkflowTriggerResponse.builder().status(TriggerStatus.ERROR_SIGNALING_PROCESS).build();
    Mockito.when(runTimeService.processTriggerMessageV2(Mockito.any()))
        .thenReturn(
            WorkflowGenericResponse.builder()
                .status(ResponseStatus.SUCCESS)
                .response(reponse)
                .build());

    triggerHandler.execute(event, headers);
    Mockito.verify(runTimeService).processTriggerMessageV2(Mockito.any());
    Mockito.verify(metricsLogger)
        .logErrorMetric(
            Mockito.eq(MetricName.EVENT_TRIGGER_SIGNAL_FAILED), Mockito.any(), Mockito.any());
  }

  @Test
  public void testExecuteHandleResponsesFailure() throws Exception {
    Trigger event = getTriggerPayload("1", "invoice", "approval", "created");
    Map<String, String> headers = new HashMap<String, String>();
    headers.put(EventHeaderConstants.OWNER_ID, "1234");

    WorkflowTriggerResponse responseSuccess =
        WorkflowTriggerResponse.builder().status(TriggerStatus.PROCESS_SIGNALLED).build();
    WorkflowTriggerResponse responseNoAct =
        WorkflowTriggerResponse.builder().status(TriggerStatus.ERROR_STARTING_PROCESS).build();

    List<WorkflowTriggerResponse> list =
        Arrays.asList(new WorkflowTriggerResponse[] {responseNoAct, responseSuccess});
    WorkflowTriggersResponse responses = new WorkflowTriggersResponse(list);

    Mockito.when(runTimeService.processTriggerMessageV2(Mockito.any()))
        .thenReturn(
            WorkflowGenericResponse.builder()
                .status(ResponseStatus.SUCCESS)
                .response(responses)
                .build());

    triggerHandler.execute(event, headers);
    Mockito.verify(runTimeService).processTriggerMessageV2(Mockito.any());
    Mockito.verify(metricsLogger)
        .logErrorMetric(
            Mockito.eq(MetricName.EVENT_TRIGGER_START_FAILED), Mockito.any(), Mockito.any());
  }

  @Test
  public void testExecuteHandleResponseError() throws Exception {
    Trigger event = getTriggerPayload("1", "invoice", "approval", "created");
    Map<String, String> headers = new HashMap<String, String>();
    headers.put(EventHeaderConstants.OWNER_ID, "1234");

    Mockito.when(runTimeService.processTriggerMessageV2(Mockito.any()))
        .thenReturn(WorkflowGenericResponse.builder().build());

    triggerHandler.execute(event, headers);
    Mockito.verify(runTimeService).processTriggerMessageV2(Mockito.any());
  }

  @Test
  public void testExecuteHandleResponsesError() throws Exception {
    Trigger event = getTriggerPayload("1", "invoice", "approval", "created");
    Map<String, String> headers = new HashMap<String, String>();
    headers.put(EventHeaderConstants.OWNER_ID, "1234");

    Mockito.when(runTimeService.processTriggerMessageV2(Mockito.any()))
        .thenReturn(WorkflowGenericResponse.builder().build());

    triggerHandler.execute(event, headers);
    Mockito.verify(runTimeService).processTriggerMessageV2(Mockito.any());
  }

  @Test
  public void testExecuteEntityChangeIdentifierNull() throws Exception {
    MetaData metaData = MetaData.builder()
            .workflow(TRIGGER_WORKFLOW)
            .entityType(TRIGGER_TYPE)
            .entityChangeIdentifier(null)
            .entityId(TRIGGER_ID)
            .build();

    Variables variables = new Variables(Collections.EMPTY_MAP, Collections.EMPTY_MAP);
    Trigger workflowTriggerEvent = new Trigger(metaData, variables, Collections.EMPTY_MAP);
    triggerHandler.execute(workflowTriggerEvent, getHeadersWithOwnerId());
    Mockito.verify(metricsLogger)
            .logErrorMetric(
                    Mockito.eq(MetricName.EVENT_TRIGGER), Mockito.eq(Type.EVENT_METRIC), Mockito.any());
  }

  @Test
  public void testExecuteResponseStatusFailWithWfTriggerResponse() throws Exception {
    Trigger event = getTriggerPayload(TRIGGER_ID, TRIGGER_TYPE, TRIGGER_WORKFLOW, TRIGGER_MESSAGE);
    Map<String, String> headers = new HashMap<String, String>();
    headers.put(EventHeaderConstants.OWNER_ID, OWNER_ID);
    WorkflowTriggerResponse responses = WorkflowTriggerResponse.builder().status(TriggerStatus.PROCESS_SIGNALLED).build();
    WorkflowGenericResponse response = WorkflowGenericResponse.builder().status(
            ResponseStatus.FAILURE).response(responses).build();

    Mockito.when(runTimeService.processTriggerMessageV2(Mockito.any()))
            .thenReturn(response);

    triggerHandler.execute(event, headers);
    Mockito.verify(runTimeService).processTriggerMessageV2(Mockito.any());
  }

  @Test
  public void testExecuteResponseStatusFailWithOutWfTriggerResponse() throws Exception {
    Trigger event = getTriggerPayload(TRIGGER_ID, TRIGGER_TYPE, TRIGGER_WORKFLOW, TRIGGER_MESSAGE);
    Map<String, String> headers = new HashMap<String, String>();
    headers.put(EventHeaderConstants.OWNER_ID, OWNER_ID);
    WorkflowGenericResponse response = WorkflowGenericResponse.builder().status(
            ResponseStatus.FAILURE).build();

    Mockito.when(runTimeService.processTriggerMessageV2(Mockito.any()))
            .thenReturn(response);

    triggerHandler.execute(event, headers);
    Mockito.verify(runTimeService).processTriggerMessageV2(Mockito.any());
  }

  @Test
  public void testNoActionTriggerNullStatus() throws Exception {
    Trigger event = getTriggerPayload(TRIGGER_ID, TRIGGER_TYPE, TRIGGER_WORKFLOW, TRIGGER_MESSAGE);
    Map<String, String> headers = new HashMap<String, String>();
    headers.put(EventHeaderConstants.OWNER_ID, OWNER_ID);
    WorkflowTriggerResponse responses = WorkflowTriggerResponse.builder().status(null).build();
    WorkflowGenericResponse response = WorkflowGenericResponse.builder().status(
            ResponseStatus.SUCCESS).response(responses).build();

    Mockito.when(runTimeService.processTriggerMessageV2(Mockito.any()))
            .thenReturn(response);

    triggerHandler.execute(event, headers);
    Mockito.verify(runTimeService).processTriggerMessageV2(Mockito.any());
  }

  @Test
  public void testNoActionTriggerDefaultStatus() throws Exception {
    Trigger event = getTriggerPayload(TRIGGER_ID, TRIGGER_TYPE, TRIGGER_WORKFLOW, TRIGGER_MESSAGE);
    Map<String, String> headers = new HashMap<String, String>();
    headers.put(EventHeaderConstants.OWNER_ID, OWNER_ID);
    WorkflowTriggerResponse responseSuccess =
            WorkflowTriggerResponse.builder().status(TriggerStatus.PROCESS_SIGNALLED).build();
    WorkflowTriggerResponse responseNoAct =
            WorkflowTriggerResponse.builder().status(TriggerStatus.ERROR_STARTING_PROCESS).build();

    List<WorkflowTriggerResponse> list =
            Arrays.asList(new WorkflowTriggerResponse[] {responseSuccess, responseNoAct});
    WorkflowTriggersResponse responses = new WorkflowTriggersResponse(list);
    Mockito.when(runTimeService.processTriggerMessageV2(Mockito.any()))
            .thenReturn(
                    WorkflowGenericResponse.builder()
                            .status(ResponseStatus.SUCCESS)
                            .response(responses)
                            .build());

    triggerHandler.execute(event, headers);
    Mockito.verify(runTimeService).processTriggerMessageV2(Mockito.any());
  }

  @Test
  public void testNoActionTriggerEmpty() throws Exception {
    Trigger event = getTriggerPayload(TRIGGER_ID, TRIGGER_TYPE, TRIGGER_WORKFLOW, TRIGGER_MESSAGE);
    Map<String, String> headers = new HashMap<String, String>();
    headers.put(EventHeaderConstants.OWNER_ID, OWNER_ID);
    WorkflowTriggersResponse responses = new WorkflowTriggersResponse(null);
    Mockito.when(runTimeService.processTriggerMessageV2(Mockito.any()))
            .thenReturn(
                    WorkflowGenericResponse.builder()
                            .status(ResponseStatus.SUCCESS)
                            .response(responses)
                            .build());

    triggerHandler.execute(event, headers);
    Mockito.verify(runTimeService).processTriggerMessageV2(Mockito.any());
  }

  private Trigger getTriggerPayload(String id, String type, String workflow, String message) {
    MetaData metaData = getMetaData(id, type, workflow, message);
    Variables variables = getVariables();

    Trigger workflowTriggerEvent = new Trigger(metaData, variables, Collections.EMPTY_MAP);
    return workflowTriggerEvent;
  }

  private Trigger getTriggerPayloadForV1Trigger(String id, String type, String workflow, String message) {
    MetaData metaData = getMetaDataForV1Trigger(id, type, workflow, message);
    Variables variables = getVariables();

    Trigger workflowTriggerEvent = new Trigger(metaData, variables, Collections.EMPTY_MAP);
    return workflowTriggerEvent;
  }

  private Trigger getTriggerPayloadForEvaluateAndTrigger(String id, String type, String workflow, String message) {
    MetaData metaData = getMetaDataForEvaluateAndTrigger(id, type, workflow, message);
    Variables variables = getVariables();

    Trigger workflowTriggerEvent = new Trigger(metaData, variables, Collections.EMPTY_MAP);
    return workflowTriggerEvent;
  }

  private Variables getVariables() {
    Variables variables = new Variables(Collections.EMPTY_MAP, Collections.EMPTY_MAP);
    return variables;
  }

  private MetaData getMetaData(String id,String type, String workflow,String message) {
    EntityChangeIdentifier entityChangeIdentifier =
       new EntityChangeIdentifier(message);

    return MetaData.builder()
            .workflow(workflow)
            .entityType(type)
            .entityChangeIdentifier(entityChangeIdentifier)
            .entityId(id)
            .build();
  }

  private MetaData getMetaDataForV1Trigger(String id,String type, String workflow,String message) {
    EntityChangeIdentifier entityChangeIdentifier =
            new EntityChangeIdentifier(message);

    return MetaData.builder()
            .workflow(workflow)
            .entityType(type)
            .entityChangeIdentifier(entityChangeIdentifier)
            .entityId(id)
            .targetApi(TriggerTargetAPI.TRIGGER_V1)
            .build();
  }

  private MetaData getMetaDataForEvaluateAndTrigger(String id,String type, String workflow,String message) {
    EntityChangeIdentifier entityChangeIdentifier =
            new EntityChangeIdentifier(message);

    return MetaData.builder()
            .workflow(workflow)
            .entityType(type)
            .entityChangeIdentifier(entityChangeIdentifier)
            .entityId(id)
            .targetApi(TriggerTargetAPI.EVALUATE_AND_TRIGGER_V2)
            .definitionKey("defKey")
            .build();
  }

  private Map<String, String> getHeadersWithOwnerId() {
    Map<String, String> headers = new HashMap<String, String>();
    headers.put(EventHeaderConstants.OWNER_ID, "1234");

    return headers;
  }
}
