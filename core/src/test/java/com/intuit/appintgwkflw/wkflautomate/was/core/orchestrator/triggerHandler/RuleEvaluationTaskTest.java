package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.bpmnengineadapter.BPMNEngineRunTimeServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.SingleDefinitionDmnEvaluator;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DefinitionType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.EvaluateRuleRequest;
import com.intuit.async.execution.request.State;
import com.intuit.v4.Authorization;
import com.intuit.v4.workflows.Definition;
import java.util.HashMap;
import org.camunda.bpm.dmn.engine.DmnDecisionTableResult;
import org.camunda.commons.utils.IoUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class RuleEvaluationTaskTest {

  private String requestKey = "requestKey";
  private String responseKey = "responseKey";
  private Definition definition;
  private DefinitionDetails definitionDetails;
  private Authorization authorization;
  private byte[] dmn;

  @Mock
  private TemplateDetails templateDetails;

  @Mock
  BPMNEngineRunTimeServiceRest bpmnEngineDefinitionServiceRest;

  @Mock
  SingleDefinitionDmnEvaluator singleDefinitionDmnEvaluator;

  @Before
  public void setup() {
    definition  = TestHelper.mockDefinitionEntity();
    authorization = new Authorization();
    authorization.putRealm("12345");
    definition.setId(TestHelper.getGlobalId(DefinitionTestConstants.DEF_ID));
    definitionDetails = TestHelper.mockDefinitionDetails(definition, templateDetails,
        authorization);
    dmn =
        IoUtil.inputStreamAsByteArray(
            RuleEvaluationTaskTest.class
                .getClassLoader()
                .getResourceAsStream("dmn/decision_invoiceapproval.dmn"));
    definitionDetails.setDefinitionData(dmn);
    MockitoAnnotations.initMocks(this);
  }

  @Test
  public void testRuleEvaluationTaskWithDefinitionTypeUser() {
    State inputRequest = new State();
    Mockito.when(definitionDetails.getTemplateDetails().getDefinitionType()).thenReturn(
        DefinitionType.USER
    );
    RuleEvaluationTask ruleEvaluationTask =
        new RuleEvaluationTask(requestKey, responseKey, bpmnEngineDefinitionServiceRest,
            definitionDetails, singleDefinitionDmnEvaluator);
    ruleEvaluationTask.execute(inputRequest);
    Mockito.verify(bpmnEngineDefinitionServiceRest)
        .evaluateDecision(inputRequest.getValue(requestKey));
  }

  @Test
  public void testRuleEvaluationTaskWithDefinitionTypeSingle() {
    State inputRequest = new State();
    inputRequest.addValue(requestKey, new EvaluateRuleRequest("def-id", new HashMap<>()));
    DmnDecisionTableResult dmnDecisionRuleResults = Mockito.mock(DmnDecisionTableResult.class);
    Mockito.when(definitionDetails.getTemplateDetails().getDefinitionType()).thenReturn(
        DefinitionType.SINGLE
    );
    RuleEvaluationTask ruleEvaluationTask =
        new RuleEvaluationTask(requestKey, responseKey, bpmnEngineDefinitionServiceRest,
            definitionDetails, singleDefinitionDmnEvaluator);
    State state = ruleEvaluationTask.execute(inputRequest);
    Mockito.verifyNoInteractions(bpmnEngineDefinitionServiceRest);
    Assert.assertNotNull(state.getValue(responseKey));
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testDmnFetchTaskFailureNew() {
    State inputRequest = new State();
    RuleEvaluationTask ruleEvaluationTask = new RuleEvaluationTask(requestKey, responseKey,
        null, definitionDetails, singleDefinitionDmnEvaluator);
    ruleEvaluationTask.execute(inputRequest);
    Mockito.verify(bpmnEngineDefinitionServiceRest)
        .evaluateDecision(inputRequest.getValue(responseKey));
  }
}
