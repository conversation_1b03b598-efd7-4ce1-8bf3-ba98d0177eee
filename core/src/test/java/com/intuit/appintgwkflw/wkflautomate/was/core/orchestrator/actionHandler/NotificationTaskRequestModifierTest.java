package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.AuthHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.impl.ProcessDetailsRepoService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.*;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qbo.oinp.constants.OinpBridgeConstants;
import org.camunda.bpm.engine.variable.VariableMap;
import org.camunda.bpm.engine.variable.impl.VariableMapImpl;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants.TASK_STATUS_CREATED;

/**
 * Modifies the WorkflowTask request of NOTIFICATION_TASK
 *
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class NotificationTaskRequestModifierTest {
  @Mock private ProcessDetailsRepoService processDetailsRepoService;

  @Mock private ParameterDetailsExtractorHelper parameterDetailsExtractorHelper;

  @InjectMocks private NotificationTaskRequestModifier notificationTaskRequestModifier;

  /** Notification data is being populated with the placeholder values */
  @Test
  public void modifyTaskRequest_placeholderValues() {
    Map<String, String> extensionProperties = new HashMap<>();
    extensionProperties.put(
        ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE,
        String.valueOf(TaskType.NOTIFICATION_TASK));
    extensionProperties.put("serviceName", "test");
    extensionProperties.put("notificationName", "test");
    extensionProperties.put("notificationDataType", "test");
    extensionProperties.put("taskDetails", "{\"fatal\": false}");

    Map<String, String> variableMap = new HashMap<>();
    variableMap.put("entityType", "Invoice");
    variableMap.put("TxnDate", "2023-05-30");
    variableMap.put("intuit_userid", "-13246382639");
    VariableMap variableMap1 = new VariableMapImpl();
    variableMap1.putAll(variableMap);

    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("actId1")
            .extensionProperties(extensionProperties)
            .processInstanceId("processInstance1")
            .taskId("taskId1")
            .ownerId(1l)
            .workerId("worker1")
            .variableMap(variableMap1)
            .inputVariables(variableMap)
            .build();

    Map<String, Object> runtimeDefAttributes = new HashMap<>();
    runtimeDefAttributes.put("notificationData", "");
    runtimeDefAttributes.put("notificationMetaData", "{\"authId\": -1})");

    Map<String, String> modelDefAttributes = new HashMap<>();
    modelDefAttributes.put("activityName", "Send tray notification");
    modelDefAttributes.putAll(extensionProperties);

    Map<String, String> variables = new HashMap<>();
    variables.put("notificationData", "");
    variables.put("entityType", "Invoice");
    variables.put("TxnDate", "2023-05-30");
    VariableMap variableMap2 = new VariableMapImpl();
    variableMap2.putAll(variables);

    WorkflowTaskRequest.WorkflowTaskRequestBuilder taskRequestBuilder =
        WorkflowTaskRequest.builder()
            .processInstanceId(workerActionRequest.getProcessInstanceId())
            .id(workerActionRequest.getTaskId())
            .activityId(workerActionRequest.getActivityId())
            .activityName("actName1")
            .taskType(TaskType.NOTIFICATION_TASK)
            .taskAttributes(
                TaskAttributes.builder()
                    .modelAttributes(modelDefAttributes)
                    .runtimeAttributes(runtimeDefAttributes)
                    .variables(variableMap2)
                    .build());

    TemplateDetails templateDetails = TemplateDetails.builder().offeringId("offering1").build();
    DefinitionDetails definitionDetails =
        DefinitionDetails.builder()
            .version(1)
            .templateDetails(templateDetails)
            .recordType(RecordType.INVOICE)
            .build();

    ProcessDetails processDetails =
        ProcessDetails.builder()
            .recordId("record1")
            .definitionDetails(definitionDetails)
            .ownerId(1l)
            .build();

    WorkflowTaskRequest createTaskRequest =
        taskRequestBuilder
            .skipCallback(false)
            .publishExternalTaskEvent(false)
            .publishWorkflowStateTransitionEvent(false)
            .command(TaskCommand.CREATE)
            .status(TASK_STATUS_CREATED)
            .workerId(workerActionRequest.getWorkerId())
            .recordId(processDetails.getRecordId())
            .build();

    Mockito.when(
            processDetailsRepoService.findByProcessIdWithoutDefinitionData(
                Mockito.eq(workerActionRequest.getProcessInstanceId())))
        .thenReturn(Optional.of(definitionDetails));

    Map<String, HandlerDetails.ParameterDetails> parameterDetailsMap = new HashMap<>();
    HandlerDetails.ParameterDetails parameterDetailsMessage = new HandlerDetails.ParameterDetails();
    parameterDetailsMessage.getFieldValue().add("SampleValue1");
    HandlerDetails.ParameterDetails parameterDetailsSubject = new HandlerDetails.ParameterDetails();
    parameterDetailsSubject.getFieldValue().add("SampleValue2");
    HandlerDetails.ParameterDetails parameterDetailsNotificationName =
        new HandlerDetails.ParameterDetails();
    parameterDetailsNotificationName.getFieldValue().add("customWorkflow");
    HandlerDetails.ParameterDetails parameterDetailsNotificationDataType =
        new HandlerDetails.ParameterDetails();
    parameterDetailsNotificationDataType.getFieldValue().add("customWorkflow");
    HandlerDetails.ParameterDetails parameterDetailsNotificationData =
        new HandlerDetails.ParameterDetails();
    parameterDetailsNotificationData
        .getFieldValue()
        .add("{\"Message\": \"[[Message]]\", \"Subject\": \"[[Subject]]\"}");
    HandlerDetails.ParameterDetails parameterDetailsServiceName =
        new HandlerDetails.ParameterDetails();
    parameterDetailsServiceName.getFieldValue().add("Workflow");
    parameterDetailsMap.put("Message", parameterDetailsMessage);
    parameterDetailsMap.put("Subject", parameterDetailsSubject);
    parameterDetailsMap.put("notificationName", parameterDetailsNotificationName);
    parameterDetailsMap.put("notificationDataType", parameterDetailsNotificationDataType);
    parameterDetailsMap.put("notificationData", parameterDetailsNotificationData);
    parameterDetailsMap.put("serviceName", parameterDetailsServiceName);
    Mockito.when(
            parameterDetailsExtractorHelper.getParameterDetails(
                Mockito.eq(workerActionRequest), Mockito.eq(definitionDetails)))
        .thenReturn(Optional.of(parameterDetailsMap));

    notificationTaskRequestModifier.getTaskRequest(createTaskRequest, workerActionRequest);
    Assert.assertEquals(
        "{\"Message\": \"SampleValue1\", \"Subject\": \"SampleValue2\"}",
        createTaskRequest
            .getTaskAttributes()
            .getRuntimeAttributes()
            .get(OinpBridgeConstants.NOTIFICATION_DATA));
    Assert.assertEquals(
        "{\"Message\": \"SampleValue1\", \"Subject\": \"SampleValue2\"}",
        createTaskRequest
            .getTaskAttributes()
            .getVariables()
            .get(OinpBridgeConstants.NOTIFICATION_DATA));
    Assert.assertEquals(
        "customWorkflow",
        createTaskRequest
            .getTaskAttributes()
            .getModelAttributes()
            .get(OinpBridgeConstants.NOTIFICATION_NAME));
    Assert.assertEquals(
        "customWorkflow",
        createTaskRequest
            .getTaskAttributes()
            .getModelAttributes()
            .get(OinpBridgeConstants.NOTIFICATION_DATA_TYPE));
    Assert.assertEquals(
        "Workflow",
        createTaskRequest
            .getTaskAttributes()
            .getModelAttributes()
            .get(OinpBridgeConstants.SERVICE_NAME));
  }

  /** Notification data is being populated with the process variables */
  @Test
  public void modifyTaskRequest_processVariables() {
    Map<String, String> extensionProperties = new HashMap<>();
    extensionProperties.put(
        ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE,
        String.valueOf(TaskType.NOTIFICATION_TASK));
    extensionProperties.put("serviceName", "Workflow");
    extensionProperties.put("notificationName", "test");
    extensionProperties.put("notificationDataType", "test");
    extensionProperties.put("taskDetails", "{\"fatal\": false}");

    Map<String, String> variableMap = new HashMap<>();
    variableMap.put("entityType", "Invoice");
    variableMap.put("TxnDate", "2023-05-30");
    variableMap.put("intuit_userid", "-13246382639");
    VariableMap variableMap1 = new VariableMapImpl();
    variableMap1.putAll(variableMap);

    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("actId1")
            .extensionProperties(extensionProperties)
            .processInstanceId("processInstance1")
            .taskId("taskId1")
            .ownerId(1l)
            .workerId("worker1")
            .variableMap(variableMap1)
            .inputVariables(variableMap)
            .build();

    Map<String, Object> runtimeDefAttributes = new HashMap<>();
    runtimeDefAttributes.put("notificationData", "");
    runtimeDefAttributes.put("notificationMetaData", "{\"authId\": -1})");

    Map<String, String> modelDefAttributes = new HashMap<>();
    modelDefAttributes.put("activityName", "Send tray notification");
    modelDefAttributes.putAll(extensionProperties);

    Map<String, String> variables = new HashMap<>();
    variables.put("notificationData", "");
    variables.put("entityType", "Invoice");
    variables.put("TxnDate", "2023-05-30");
    VariableMap variableMap2 = new VariableMapImpl();
    variableMap2.putAll(variables);

    WorkflowTaskRequest.WorkflowTaskRequestBuilder taskRequestBuilder =
        WorkflowTaskRequest.builder()
            .processInstanceId(workerActionRequest.getProcessInstanceId())
            .id(workerActionRequest.getTaskId())
            .activityId(workerActionRequest.getActivityId())
            .activityName("actName1")
            .taskType(TaskType.NOTIFICATION_TASK)
            .taskAttributes(
                TaskAttributes.builder()
                    .modelAttributes(modelDefAttributes)
                    .runtimeAttributes(runtimeDefAttributes)
                    .variables(variableMap2)
                    .build());

    TemplateDetails templateDetails = TemplateDetails.builder().offeringId("offering1").build();
    DefinitionDetails definitionDetails =
        DefinitionDetails.builder()
            .version(1)
            .templateDetails(templateDetails)
            .recordType(RecordType.INVOICE)
            .build();

    ProcessDetails processDetails =
        ProcessDetails.builder()
            .recordId("record1")
            .definitionDetails(definitionDetails)
            .ownerId(1l)
            .build();

    WorkflowTaskRequest createTaskRequest =
        taskRequestBuilder
            .skipCallback(false)
            .publishExternalTaskEvent(false)
            .publishWorkflowStateTransitionEvent(false)
            .command(TaskCommand.CREATE)
            .status(TASK_STATUS_CREATED)
            .workerId(workerActionRequest.getWorkerId())
            .recordId(processDetails.getRecordId())
            .build();

    Mockito.when(
            processDetailsRepoService.findByProcessIdWithoutDefinitionData(
                Mockito.eq(workerActionRequest.getProcessInstanceId())))
        .thenReturn(Optional.of(definitionDetails));

    Map<String, HandlerDetails.ParameterDetails> parameterDetailsMap = new HashMap<>();
    HandlerDetails.ParameterDetails parameterDetailsMessage = new HandlerDetails.ParameterDetails();
    parameterDetailsMessage.getFieldValue().add("SampleValue1");
    HandlerDetails.ParameterDetails parameterDetailsSubject = new HandlerDetails.ParameterDetails();
    parameterDetailsSubject.getFieldValue().add("SampleValue2");
    HandlerDetails.ParameterDetails parameterDetailsNotificationName =
        new HandlerDetails.ParameterDetails();
    parameterDetailsNotificationName.getFieldValue().add("customWorkflow");
    HandlerDetails.ParameterDetails parameterDetailsNotificationDataType =
        new HandlerDetails.ParameterDetails();
    parameterDetailsNotificationDataType.getFieldValue().add("customWorkflow");
    HandlerDetails.ParameterDetails parameterDetailsNotificationData =
        new HandlerDetails.ParameterDetails();
    parameterDetailsNotificationData
        .getFieldValue()
        .add("{\"EntityType\": \"[[entityType]]\", \"TxnDate\": \"[[TxnDate]]\"}");
    HandlerDetails.ParameterDetails parameterDetailsServiceName =
        new HandlerDetails.ParameterDetails();
    parameterDetailsServiceName.getFieldValue().add("Workflow");
    parameterDetailsMap.put("Message", parameterDetailsMessage);
    parameterDetailsMap.put("Subject", parameterDetailsSubject);
    parameterDetailsMap.put("notificationName", parameterDetailsNotificationName);
    parameterDetailsMap.put("notificationDataType", parameterDetailsNotificationDataType);
    parameterDetailsMap.put("notificationData", parameterDetailsNotificationData);
    parameterDetailsMap.put("serviceName", parameterDetailsServiceName);
    Mockito.when(
            parameterDetailsExtractorHelper.getParameterDetails(
                Mockito.eq(workerActionRequest), Mockito.eq(definitionDetails)))
        .thenReturn(Optional.of(parameterDetailsMap));

    notificationTaskRequestModifier.getTaskRequest(createTaskRequest, workerActionRequest);

    Assert.assertEquals(
        "{\"EntityType\": \"Invoice\", \"TxnDate\": \"2023-05-30\"}",
        createTaskRequest
            .getTaskAttributes()
            .getRuntimeAttributes()
            .get(OinpBridgeConstants.NOTIFICATION_DATA));
    Assert.assertEquals(
        "{\"EntityType\": \"Invoice\", \"TxnDate\": \"2023-05-30\"}",
        createTaskRequest
            .getTaskAttributes()
            .getVariables()
            .get(OinpBridgeConstants.NOTIFICATION_DATA));
    Assert.assertEquals(
        "customWorkflow",
        createTaskRequest
            .getTaskAttributes()
            .getModelAttributes()
            .get(OinpBridgeConstants.NOTIFICATION_NAME));
    Assert.assertEquals(
        "customWorkflow",
        createTaskRequest
            .getTaskAttributes()
            .getModelAttributes()
            .get(OinpBridgeConstants.NOTIFICATION_DATA_TYPE));
    Assert.assertEquals(
        "Workflow",
        createTaskRequest
            .getTaskAttributes()
            .getModelAttributes()
            .get(OinpBridgeConstants.SERVICE_NAME));
  }

  /**
   * All the notification parameters are default and nothing needs to modified form the placeholder
   * or process values
   */
  @Test
  public void modifyTaskRequest_defaultDetails() {
    Map<String, String> extensionProperties = new HashMap<>();
    extensionProperties.put(
        ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE,
        String.valueOf(TaskType.NOTIFICATION_TASK));
    extensionProperties.put("serviceName", "Workflow");
    extensionProperties.put("notificationName", "customWorkflow");
    extensionProperties.put("notificationDataType", "customWorkflow");
    extensionProperties.put("taskDetails", "{\"fatal\": false}");

    Map<String, String> variableMap = new HashMap<>();
    variableMap.put("entityType", "Invoice");
    variableMap.put("TxnDate", "2023-05-30");
    variableMap.put("intuit_userid", "-13246382639");
    VariableMap variableMap1 = new VariableMapImpl();
    variableMap1.putAll(variableMap);

    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("actId1")
            .extensionProperties(extensionProperties)
            .processInstanceId("processInstance1")
            .taskId("taskId1")
            .ownerId(1l)
            .workerId("worker1")
            .variableMap(variableMap1)
            .inputVariables(variableMap)
            .build();

    Map<String, Object> runtimeDefAttributes = new HashMap<>();
    runtimeDefAttributes.put(
        "notificationData",
        "{\"Message\": \"review your tasks\", \"Subject\": \"Review your tasks\"}");
    runtimeDefAttributes.put("notificationMetaData", "{\"authId\": -1})");

    Map<String, String> modelDefAttributes = new HashMap<>();
    modelDefAttributes.put("activityName", "Send tray notification");
    modelDefAttributes.putAll(extensionProperties);

    Map<String, String> variables = new HashMap<>();
    variables.put("entityType", "Invoice");
    variables.put("TxnDate", "2023-05-30");
    VariableMap variableMap2 = new VariableMapImpl();
    variableMap2.putAll(variables);

    WorkflowTaskRequest.WorkflowTaskRequestBuilder taskRequestBuilder =
        WorkflowTaskRequest.builder()
            .processInstanceId(workerActionRequest.getProcessInstanceId())
            .id(workerActionRequest.getTaskId())
            .activityId(workerActionRequest.getActivityId())
            .activityName("actName1")
            .taskType(TaskType.NOTIFICATION_TASK)
            .taskAttributes(
                TaskAttributes.builder()
                    .modelAttributes(modelDefAttributes)
                    .runtimeAttributes(runtimeDefAttributes)
                    .variables(variableMap2)
                    .build());

    TemplateDetails templateDetails = TemplateDetails.builder().offeringId("offering1").build();
    DefinitionDetails definitionDetails =
        DefinitionDetails.builder()
            .version(1)
            .templateDetails(templateDetails)
            .recordType(RecordType.INVOICE)
            .build();

    ProcessDetails processDetails =
        ProcessDetails.builder()
            .recordId("record1")
            .definitionDetails(definitionDetails)
            .ownerId(1l)
            .build();

    WorkflowTaskRequest createTaskRequest =
        taskRequestBuilder
            .skipCallback(false)
            .publishExternalTaskEvent(false)
            .publishWorkflowStateTransitionEvent(false)
            .command(TaskCommand.CREATE)
            .status(TASK_STATUS_CREATED)
            .workerId(workerActionRequest.getWorkerId())
            .recordId(processDetails.getRecordId())
            .build();

    Mockito.when(
            processDetailsRepoService.findByProcessIdWithoutDefinitionData(
                Mockito.eq(workerActionRequest.getProcessInstanceId())))
        .thenReturn(Optional.of(definitionDetails));

    Map<String, HandlerDetails.ParameterDetails> parameterDetailsMap = new HashMap<>();
    HandlerDetails.ParameterDetails parameterDetailsRealmId = new HandlerDetails.ParameterDetails();
    parameterDetailsMap.put("Message", parameterDetailsRealmId);
    Mockito.when(
            parameterDetailsExtractorHelper.getParameterDetails(
                Mockito.eq(workerActionRequest), Mockito.eq(definitionDetails)))
        .thenReturn(Optional.of(parameterDetailsMap));

    notificationTaskRequestModifier.getTaskRequest(createTaskRequest, workerActionRequest);

    Assert.assertEquals(
        "{\"Message\": \"review your tasks\", \"Subject\": \"Review your tasks\"}",
        createTaskRequest
            .getTaskAttributes()
            .getRuntimeAttributes()
            .get(OinpBridgeConstants.NOTIFICATION_DATA));
    Assert.assertEquals(
        "customWorkflow",
        createTaskRequest
            .getTaskAttributes()
            .getModelAttributes()
            .get(OinpBridgeConstants.NOTIFICATION_NAME));
    Assert.assertEquals(
        "customWorkflow",
        createTaskRequest
            .getTaskAttributes()
            .getModelAttributes()
            .get(OinpBridgeConstants.NOTIFICATION_DATA_TYPE));
    Assert.assertEquals(
        "Workflow",
        createTaskRequest
            .getTaskAttributes()
            .getModelAttributes()
            .get(OinpBridgeConstants.SERVICE_NAME));
  }

  /** placeholder value which is to be filled in the notification data is not found */
  @Test
  public void modifyTaskRequest_dataNotFound() {
    Map<String, String> extensionProperties = new HashMap<>();
    extensionProperties.put(
        ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE,
        String.valueOf(TaskType.NOTIFICATION_TASK));
    extensionProperties.put("serviceName", "Workflow");
    extensionProperties.put("notificationName", "test");
    extensionProperties.put("notificationDataType", "test");
    extensionProperties.put("taskDetails", "{\"fatal\": false}");

    Map<String, String> variableMap = new HashMap<>();
    variableMap.put("entityType", "Invoice");
    variableMap.put("TxnDate", "2023-05-30");
    variableMap.put("intuit_userid", "-13246382639");
    VariableMap variableMap1 = new VariableMapImpl();
    variableMap1.putAll(variableMap);

    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("actId1")
            .extensionProperties(extensionProperties)
            .processInstanceId("processInstance1")
            .taskId("taskId1")
            .ownerId(1l)
            .workerId("worker1")
            .variableMap(variableMap1)
            .inputVariables(variableMap)
            .build();

    Map<String, Object> runtimeDefAttributes = new HashMap<>();
    runtimeDefAttributes.put("notificationData", "");
    runtimeDefAttributes.put("notificationMetaData", "{\"authId\": -1})");

    Map<String, String> modelDefAttributes = new HashMap<>();
    modelDefAttributes.put("activityName", "Send tray notification");
    modelDefAttributes.putAll(extensionProperties);

    Map<String, String> variables = new HashMap<>();
    variables.put("notificationData", "");
    variables.put("entityType", "Invoice");
    variables.put("TxnDate", "2023-05-30");
    VariableMap variableMap2 = new VariableMapImpl();
    variableMap2.putAll(variables);

    WorkflowTaskRequest.WorkflowTaskRequestBuilder taskRequestBuilder =
        WorkflowTaskRequest.builder()
            .processInstanceId(workerActionRequest.getProcessInstanceId())
            .id(workerActionRequest.getTaskId())
            .activityId(workerActionRequest.getActivityId())
            .activityName("actName1")
            .taskType(TaskType.NOTIFICATION_TASK)
            .taskAttributes(
                TaskAttributes.builder()
                    .modelAttributes(modelDefAttributes)
                    .runtimeAttributes(runtimeDefAttributes)
                    .variables(variableMap2)
                    .build());

    TemplateDetails templateDetails = TemplateDetails.builder().offeringId("offering1").build();
    DefinitionDetails definitionDetails =
        DefinitionDetails.builder()
            .version(1)
            .templateDetails(templateDetails)
            .recordType(RecordType.INVOICE)
            .build();

    ProcessDetails processDetails =
        ProcessDetails.builder()
            .recordId("record1")
            .definitionDetails(definitionDetails)
            .ownerId(1l)
            .build();

    WorkflowTaskRequest createTaskRequest =
        taskRequestBuilder
            .skipCallback(false)
            .publishExternalTaskEvent(false)
            .publishWorkflowStateTransitionEvent(false)
            .command(TaskCommand.CREATE)
            .status(TASK_STATUS_CREATED)
            .workerId(workerActionRequest.getWorkerId())
            .recordId(processDetails.getRecordId())
            .build();

    Mockito.when(
            processDetailsRepoService.findByProcessIdWithoutDefinitionData(
                Mockito.eq(workerActionRequest.getProcessInstanceId())))
        .thenReturn(Optional.of(definitionDetails));

    Map<String, HandlerDetails.ParameterDetails> parameterDetailsMap = new HashMap<>();
    HandlerDetails.ParameterDetails parameterDetailsMessage = new HandlerDetails.ParameterDetails();
    parameterDetailsMessage.getFieldValue().add("SampleValue1");
    HandlerDetails.ParameterDetails parameterDetailsNotificationName =
        new HandlerDetails.ParameterDetails();
    parameterDetailsNotificationName.getFieldValue().add("customWorkflow");
    HandlerDetails.ParameterDetails parameterDetailsNotificationDataType =
        new HandlerDetails.ParameterDetails();
    parameterDetailsNotificationDataType.getFieldValue().add("customWorkflow");
    HandlerDetails.ParameterDetails parameterDetailsNotificationData =
        new HandlerDetails.ParameterDetails();
    parameterDetailsNotificationData
        .getFieldValue()
        .add("{\"Message\": \"[[Message]]\", \"Subject\": \"[[Subject]]\"}");
    HandlerDetails.ParameterDetails parameterDetailsServiceName =
        new HandlerDetails.ParameterDetails();
    parameterDetailsServiceName.getFieldValue().add("Workflow");
    parameterDetailsMap.put("Message", parameterDetailsMessage);
    parameterDetailsMap.put("notificationName", parameterDetailsNotificationName);
    parameterDetailsMap.put("notificationDataType", parameterDetailsNotificationDataType);
    parameterDetailsMap.put("notificationData", parameterDetailsNotificationData);
    parameterDetailsMap.put("serviceName", parameterDetailsServiceName);
    Mockito.when(
            parameterDetailsExtractorHelper.getParameterDetails(
                Mockito.eq(workerActionRequest), Mockito.eq(definitionDetails)))
        .thenReturn(Optional.of(parameterDetailsMap));

    notificationTaskRequestModifier.getTaskRequest(createTaskRequest, workerActionRequest);
    Assert.assertEquals(
        "{\"Message\": \"SampleValue1\", \"Subject\": \"[[Subject]]\"}",
        createTaskRequest
            .getTaskAttributes()
            .getRuntimeAttributes()
            .get(OinpBridgeConstants.NOTIFICATION_DATA));
    Assert.assertEquals(
        "{\"Message\": \"SampleValue1\", \"Subject\": \"[[Subject]]\"}",
        createTaskRequest
            .getTaskAttributes()
            .getVariables()
            .get(OinpBridgeConstants.NOTIFICATION_DATA));
    Assert.assertEquals(
        "customWorkflow",
        createTaskRequest
            .getTaskAttributes()
            .getModelAttributes()
            .get(OinpBridgeConstants.NOTIFICATION_NAME));
    Assert.assertEquals(
        "customWorkflow",
        createTaskRequest
            .getTaskAttributes()
            .getModelAttributes()
            .get(OinpBridgeConstants.NOTIFICATION_DATA_TYPE));
    Assert.assertEquals(
        "Workflow",
        createTaskRequest
            .getTaskAttributes()
            .getModelAttributes()
            .get(OinpBridgeConstants.SERVICE_NAME));
  }

  /** When the parameterDetails is empty, the flow is not effected */
  @Test
  public void successfulExecution_emptyParameterDetailsMap() {
    Map<String, String> extensionProperties = new HashMap<>();
    extensionProperties.put(
        ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE,
        String.valueOf(TaskType.NOTIFICATION_TASK));
    extensionProperties.put("serviceName", "Workflow");
    extensionProperties.put("notificationName", "customWorkflow");
    extensionProperties.put(
        "notificationData", "{\"To\": \"<EMAIL>\", \"Message\": \"test\"}");

    Map<String, String> variableMap = new HashMap<>();
    variableMap.put("entityType", "Invoice");
    variableMap.put("intuit_userid", "-13246382639");
    VariableMap variableMap1 = new VariableMapImpl();
    variableMap1.putAll(variableMap);

    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("actId1")
            .extensionProperties(extensionProperties)
            .processInstanceId("processInstance1")
            .taskId("taskId1")
            .ownerId(1l)
            .workerId("worker1")
            .variableMap(variableMap1)
            .inputVariables(variableMap)
            .build();

    TemplateDetails templateDetails = TemplateDetails.builder().offeringId("offering1").build();
    DefinitionDetails definitionDetails =
        DefinitionDetails.builder()
            .version(1)
            .templateDetails(templateDetails)
            .recordType(RecordType.INVOICE)
            .build();

    ProcessDetails processDetails =
        ProcessDetails.builder()
            .recordId("record1")
            .definitionDetails(definitionDetails)
            .ownerId(1l)
            .build();
    Mockito.when(
            processDetailsRepoService.findByProcessIdWithoutDefinitionData(
                Mockito.eq(workerActionRequest.getProcessInstanceId())))
        .thenReturn(Optional.of(definitionDetails));

    Map<String, Object> runtimeDefAttributes = new HashMap<>();
    runtimeDefAttributes.put("notificationData", "");
    runtimeDefAttributes.put("notificationMetaData", "{\"authId\": -1})");

    Map<String, String> modelDefAttributes = new HashMap<>();
    modelDefAttributes.put("activityName", "Send tray notification");
    modelDefAttributes.putAll(extensionProperties);

    Map<String, String> variables = new HashMap<>();
    variables.put("notificationData", "");
    VariableMap variableMap2 = new VariableMapImpl();
    variableMap2.putAll(variables);

    WorkflowTaskRequest.WorkflowTaskRequestBuilder taskRequestBuilder =
        WorkflowTaskRequest.builder()
            .processInstanceId(workerActionRequest.getProcessInstanceId())
            .id(workerActionRequest.getTaskId())
            .activityId(workerActionRequest.getActivityId())
            .activityName("actName1")
            .taskType(TaskType.NOTIFICATION_TASK)
            .taskAttributes(
                TaskAttributes.builder()
                    .modelAttributes(modelDefAttributes)
                    .runtimeAttributes(runtimeDefAttributes)
                    .variables(variableMap2)
                    .build());
    WorkflowActivityAttributes activityAttributes =
        WorkflowActivityAttributes.builder()
            .modelAttributes(modelDefAttributes)
            .runtimeAttributes(runtimeDefAttributes)
            .build();

    ActivityDetail activityDetails =
        ActivityDetail.builder()
            .id(10)
            .activityId("actId1")
            .activityName("actName1")
            .attributes(ObjectConverter.toJson(activityAttributes))
            .templateDetails(templateDetails)
            .type(TaskType.NOTIFICATION_TASK)
            .parentId(0)
            .build();
    Mockito.when(
            parameterDetailsExtractorHelper.getParameterDetails(
                Mockito.eq(workerActionRequest), Mockito.eq(definitionDetails)))
        .thenReturn(Optional.empty());

    WorkflowTaskRequest createRequest =
        taskRequestBuilder
            .skipCallback(false)
            .publishExternalTaskEvent(false)
            .publishWorkflowStateTransitionEvent(false)
            .command(TaskCommand.CREATE)
            .status(TASK_STATUS_CREATED)
            .workerId(workerActionRequest.getWorkerId())
            .recordId(processDetails.getRecordId())
            .build();
    createRequest =
        notificationTaskRequestModifier.getTaskRequest(createRequest, workerActionRequest);
    Assert.assertEquals(
        "{\"To\": \"<EMAIL>\", \"Message\": \"test\"}",
        createRequest.getTaskAttributes().getModelAttributes().get("notificationData"));
  }

  @Test
  public void modifyTaskRequest_checkSetLocale() {
    Map<String, String> extensionProperties = new HashMap<>();
    extensionProperties.put(
        ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE,
        String.valueOf(TaskType.NOTIFICATION_TASK));
    extensionProperties.put("serviceName", "test");
    extensionProperties.put("notificationName", "test");
    extensionProperties.put("notificationDataType", "test");
    extensionProperties.put("taskDetails", "{\"fatal\": false}");

    Map<String, String> variableMap = new HashMap<>();
    variableMap.put("entityType", "Invoice");
    variableMap.put("TxnDate", "2023-05-30");
    variableMap.put("intuit_userid", "-13246382639");
    variableMap.put("notificationMetaData", "{\"authId\": -1, \"locale\": \"[[locale]]\"}");
    VariableMap variableMap1 = new VariableMapImpl();
    variableMap1.putAll(variableMap);

    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("actId1")
            .extensionProperties(extensionProperties)
            .processInstanceId("processInstance1")
            .taskId("taskId1")
            .ownerId(1l)
            .workerId("worker1")
            .variableMap(variableMap1)
            .inputVariables(variableMap)
            .build();

    Map<String, Object> runtimeDefAttributes = new HashMap<>();
    runtimeDefAttributes.put("notificationData", "");
    runtimeDefAttributes.put(
        "notificationMetaData", "");

    Map<String, String> modelDefAttributes = new HashMap<>();
    modelDefAttributes.put("activityName", "Send tray notification");
    modelDefAttributes.putAll(extensionProperties);

    Map<String, String> variables = new HashMap<>();
    variables.put("notificationData", "");
    variables.put("entityType", "Invoice");
    variables.put("TxnDate", "2023-05-30");
    VariableMap variableMap2 = new VariableMapImpl();
    variableMap2.putAll(variables);

    WorkflowTaskRequest.WorkflowTaskRequestBuilder taskRequestBuilder =
        WorkflowTaskRequest.builder()
            .processInstanceId(workerActionRequest.getProcessInstanceId())
            .id(workerActionRequest.getTaskId())
            .activityId(workerActionRequest.getActivityId())
            .activityName("actName1")
            .taskType(TaskType.NOTIFICATION_TASK)
            .taskAttributes(
                TaskAttributes.builder()
                    .modelAttributes(modelDefAttributes)
                    .runtimeAttributes(runtimeDefAttributes)
                    .variables(variableMap2)
                    .build());

    TemplateDetails templateDetails = TemplateDetails.builder().offeringId("offering1").build();
    DefinitionDetails definitionDetails =
        DefinitionDetails.builder()
            .version(1)
            .templateDetails(templateDetails)
            .recordType(RecordType.INVOICE)
            .placeholderValue("{\"user_meta_data\": {\"intuit_was_locale\": \"fr_CA\"}}")
            .build();

    ProcessDetails processDetails =
        ProcessDetails.builder()
            .recordId("record1")
            .definitionDetails(definitionDetails)
            .ownerId(1l)
            .build();

    WorkflowTaskRequest createTaskRequest =
        taskRequestBuilder
            .skipCallback(false)
            .publishExternalTaskEvent(false)
            .publishWorkflowStateTransitionEvent(false)
            .command(TaskCommand.CREATE)
            .status(TASK_STATUS_CREATED)
            .workerId(workerActionRequest.getWorkerId())
            .recordId(processDetails.getRecordId())
            .build();

    Mockito.when(
            processDetailsRepoService.findByProcessIdWithoutDefinitionData(
                Mockito.eq(workerActionRequest.getProcessInstanceId())))
        .thenReturn(Optional.of(definitionDetails));

    Map<String, HandlerDetails.ParameterDetails> parameterDetailsMap = new HashMap<>();
    HandlerDetails.ParameterDetails parameterDetailsNotificationData =
        new HandlerDetails.ParameterDetails();
    parameterDetailsNotificationData
        .getFieldValue()
        .add("{\"Message\": \"[[Message]]\", \"Subject\": \"[[Subject]]\"}");
    parameterDetailsMap.put("notificationData", parameterDetailsNotificationData);
    Mockito.when(
            parameterDetailsExtractorHelper.getParameterDetails(
                Mockito.eq(workerActionRequest), Mockito.eq(definitionDetails)))
        .thenReturn(Optional.of(parameterDetailsMap));

    notificationTaskRequestModifier.getTaskRequest(createTaskRequest, workerActionRequest);
    Assert.assertEquals(
        "{\"authId\": -1, \"locale\": \"fr_CA\"}",
        createTaskRequest
            .getTaskAttributes()
            .getRuntimeAttributes()
            .get(OinpBridgeConstants.NOTIFICATION_METADATA));

    Assert.assertEquals(
        "{\"authId\": -1, \"locale\": \"fr_CA\"}",
        createTaskRequest
            .getTaskAttributes()
            .getVariables()
            .get(OinpBridgeConstants.NOTIFICATION_METADATA));
  }

  @Test
  public void modifyTaskRequest_checkSetLocaleNull() {
    Map<String, String> extensionProperties = new HashMap<>();
    extensionProperties.put(
        ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE,
        String.valueOf(TaskType.NOTIFICATION_TASK));
    extensionProperties.put("serviceName", "test");
    extensionProperties.put("notificationName", "test");
    extensionProperties.put("notificationDataType", "test");
    extensionProperties.put("taskDetails", "{\"fatal\": false}");

    Map<String, String> variableMap = new HashMap<>();
    variableMap.put("entityType", "Invoice");
    variableMap.put("TxnDate", "2023-05-30");
    variableMap.put("intuit_userid", "-13246382639");
    variableMap.put("notificationMetaData", "{\"authId\": -1, \"locale\": \"[[locale]]\"}");
    VariableMap variableMap1 = new VariableMapImpl();
    variableMap1.putAll(variableMap);

    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("actId1")
            .extensionProperties(extensionProperties)
            .processInstanceId("processInstance1")
            .taskId("taskId1")
            .ownerId(1l)
            .workerId("worker1")
            .variableMap(variableMap1)
            .inputVariables(variableMap)
            .build();

    Map<String, Object> runtimeDefAttributes = new HashMap<>();
    runtimeDefAttributes.put("notificationData", "");
    runtimeDefAttributes.put("notificationMetaData", "");

    Map<String, String> modelDefAttributes = new HashMap<>();
    modelDefAttributes.put("activityName", "Send tray notification");
    modelDefAttributes.putAll(extensionProperties);

    Map<String, String> variables = new HashMap<>();
    variables.put("notificationData", "");
    variables.put("entityType", "Invoice");
    variables.put("TxnDate", "2023-05-30");
    VariableMap variableMap2 = new VariableMapImpl();
    variableMap2.putAll(variables);

    WorkflowTaskRequest.WorkflowTaskRequestBuilder taskRequestBuilder =
        WorkflowTaskRequest.builder()
            .processInstanceId(workerActionRequest.getProcessInstanceId())
            .id(workerActionRequest.getTaskId())
            .activityId(workerActionRequest.getActivityId())
            .activityName("actName1")
            .taskType(TaskType.NOTIFICATION_TASK)
            .taskAttributes(
                TaskAttributes.builder()
                    .modelAttributes(modelDefAttributes)
                    .runtimeAttributes(runtimeDefAttributes)
                    .variables(variableMap2)
                    .build());

    TemplateDetails templateDetails = TemplateDetails.builder().offeringId("offering1").build();
    DefinitionDetails definitionDetails =
        DefinitionDetails.builder()
            .version(1)
            .templateDetails(templateDetails)
            .recordType(RecordType.INVOICE)
            .placeholderValue("{\"user_meta_data\": {\"intuit_was_locale\": null}}")
            .build();

    ProcessDetails processDetails =
        ProcessDetails.builder()
            .recordId("record1")
            .definitionDetails(definitionDetails)
            .ownerId(1l)
            .build();

    WorkflowTaskRequest createTaskRequest =
        taskRequestBuilder
            .skipCallback(false)
            .publishExternalTaskEvent(false)
            .publishWorkflowStateTransitionEvent(false)
            .command(TaskCommand.CREATE)
            .status(TASK_STATUS_CREATED)
            .workerId(workerActionRequest.getWorkerId())
            .recordId(processDetails.getRecordId())
            .build();

    Mockito.when(
            processDetailsRepoService.findByProcessIdWithoutDefinitionData(
                Mockito.eq(workerActionRequest.getProcessInstanceId())))
        .thenReturn(Optional.of(definitionDetails));

    Map<String, HandlerDetails.ParameterDetails> parameterDetailsMap = new HashMap<>();
    HandlerDetails.ParameterDetails parameterDetailsNotificationData =
        new HandlerDetails.ParameterDetails();
    parameterDetailsNotificationData
        .getFieldValue()
        .add("{\"Message\": \"[[Message]]\", \"Subject\": \"[[Subject]]\"}");
    parameterDetailsMap.put("notificationData", parameterDetailsNotificationData);
    Mockito.when(
            parameterDetailsExtractorHelper.getParameterDetails(
                Mockito.eq(workerActionRequest), Mockito.eq(definitionDetails)))
        .thenReturn(Optional.of(parameterDetailsMap));

    notificationTaskRequestModifier.getTaskRequest(createTaskRequest, workerActionRequest);
    Assert.assertEquals(
        "{\"authId\": -1, \"locale\": \"\"}",
        createTaskRequest
            .getTaskAttributes()
            .getRuntimeAttributes()
            .get(OinpBridgeConstants.NOTIFICATION_METADATA));

    Assert.assertEquals(
        "{\"authId\": -1, \"locale\": \"\"}",
        createTaskRequest
            .getTaskAttributes()
            .getVariables()
            .get(OinpBridgeConstants.NOTIFICATION_METADATA));
  }

  @Test
  public void modifyTaskRequest_checkNotificationMetaDataNotSet() {
    Map<String, String> extensionProperties = new HashMap<>();
    extensionProperties.put(
        ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE,
        String.valueOf(TaskType.NOTIFICATION_TASK));
    extensionProperties.put("serviceName", "test");
    extensionProperties.put("notificationName", "test");
    extensionProperties.put("notificationDataType", "test");
    extensionProperties.put("taskDetails", "{\"fatal\": false}");

    Map<String, String> variableMap = new HashMap<>();
    variableMap.put("entityType", "Invoice");
    variableMap.put("TxnDate", "2023-05-30");
    variableMap.put("intuit_userid", "-13246382639");
    VariableMap variableMap1 = new VariableMapImpl();
    variableMap1.putAll(variableMap);

    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("actId1")
            .extensionProperties(extensionProperties)
            .processInstanceId("processInstance1")
            .taskId("taskId1")
            .ownerId(1l)
            .workerId("worker1")
            .variableMap(variableMap1)
            .inputVariables(variableMap)
            .build();

    Map<String, Object> runtimeDefAttributes = new HashMap<>();
    runtimeDefAttributes.put("notificationData", "");
    runtimeDefAttributes.put("notificationMetaData", "");

    Map<String, String> modelDefAttributes = new HashMap<>();
    modelDefAttributes.put("activityName", "Send tray notification");
    modelDefAttributes.putAll(extensionProperties);

    Map<String, String> variables = new HashMap<>();
    variables.put("notificationData", "");
    variables.put("notificationMetaData", "");
    variables.put("entityType", "Invoice");
    variables.put("TxnDate", "2023-05-30");
    VariableMap variableMap2 = new VariableMapImpl();
    variableMap2.putAll(variables);

    WorkflowTaskRequest.WorkflowTaskRequestBuilder taskRequestBuilder =
        WorkflowTaskRequest.builder()
            .processInstanceId(workerActionRequest.getProcessInstanceId())
            .id(workerActionRequest.getTaskId())
            .activityId(workerActionRequest.getActivityId())
            .activityName("actName1")
            .taskType(TaskType.NOTIFICATION_TASK)
            .taskAttributes(
                TaskAttributes.builder()
                    .modelAttributes(modelDefAttributes)
                    .runtimeAttributes(runtimeDefAttributes)
                    .variables(variableMap2)
                    .build());

    TemplateDetails templateDetails = TemplateDetails.builder().offeringId("offering1").build();
    DefinitionDetails definitionDetails =
        DefinitionDetails.builder()
            .version(1)
            .templateDetails(templateDetails)
            .recordType(RecordType.INVOICE)
            .placeholderValue("{\"user_meta_data\": {\"intuit_was_locale\": null}}")
            .build();

    ProcessDetails processDetails =
        ProcessDetails.builder()
            .recordId("record1")
            .definitionDetails(definitionDetails)
            .ownerId(1l)
            .build();

    WorkflowTaskRequest createTaskRequest =
        taskRequestBuilder
            .skipCallback(false)
            .publishExternalTaskEvent(false)
            .publishWorkflowStateTransitionEvent(false)
            .command(TaskCommand.CREATE)
            .status(TASK_STATUS_CREATED)
            .workerId(workerActionRequest.getWorkerId())
            .recordId(processDetails.getRecordId())
            .build();

    Mockito.when(
            processDetailsRepoService.findByProcessIdWithoutDefinitionData(
                Mockito.eq(workerActionRequest.getProcessInstanceId())))
        .thenReturn(Optional.of(definitionDetails));

    Map<String, HandlerDetails.ParameterDetails> parameterDetailsMap = new HashMap<>();
    HandlerDetails.ParameterDetails parameterDetailsNotificationData =
        new HandlerDetails.ParameterDetails();
    parameterDetailsNotificationData
        .getFieldValue()
        .add("{\"Message\": \"[[Message]]\", \"Subject\": \"[[Subject]]\"}");
    parameterDetailsMap.put("notificationData", parameterDetailsNotificationData);
    Mockito.when(
            parameterDetailsExtractorHelper.getParameterDetails(
                Mockito.eq(workerActionRequest), Mockito.eq(definitionDetails)))
        .thenReturn(Optional.of(parameterDetailsMap));

    notificationTaskRequestModifier.getTaskRequest(createTaskRequest, workerActionRequest);
    Assert.assertEquals(
        "",
        createTaskRequest
            .getTaskAttributes()
            .getRuntimeAttributes()
            .get(OinpBridgeConstants.NOTIFICATION_METADATA));
    Assert.assertEquals(
        "",
        createTaskRequest
            .getTaskAttributes()
            .getVariables()
            .get(OinpBridgeConstants.NOTIFICATION_METADATA));
  }
  
  
  /**
   * All the notification parameters are default and nothing needs to modified form the placeholder
   * or process values
   */
  @Test
  public void modifyTaskRequest_parentDetails() {
    Map<String, String> extensionProperties = new HashMap<>();
    extensionProperties.put(
        ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE,
        String.valueOf(TaskType.NOTIFICATION_TASK));
    extensionProperties.put("serviceName", "Workflow");
    extensionProperties.put("notificationName", "customWorkflow");
    extensionProperties.put("notificationDataType", "customWorkflow");
    extensionProperties.put("taskDetails", "{\"fatal\": false}");

    Map<String, String> variableMap = new HashMap<>();
    variableMap.put("entityType", "Invoice");
    variableMap.put("TxnDate", "2023-05-30");
    variableMap.put("intuit_userid", "-13246382639");
    VariableMap variableMap1 = new VariableMapImpl();
    variableMap1.putAll(variableMap);

    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("actId1")
            .extensionProperties(extensionProperties)
            .processInstanceId("processInstance1")
            .taskId("taskId1")
            .ownerId(1l)
            .workerId("worker1")
            .variableMap(variableMap1)
            .inputVariables(variableMap)
            .rootProcessInstanceId("rootProcessInstance1")
            .build();

    Map<String, Object> runtimeDefAttributes = new HashMap<>();
    runtimeDefAttributes.put(
        "notificationData",
        "{\"Message\": \"review your tasks\", \"Subject\": \"Review your tasks\"}");
    runtimeDefAttributes.put("notificationMetaData", "{\"authId\": -1})");

    Map<String, String> modelDefAttributes = new HashMap<>();
    modelDefAttributes.put("activityName", "Send tray notification");
    modelDefAttributes.putAll(extensionProperties);

    Map<String, String> variables = new HashMap<>();
    variables.put("entityType", "Invoice");
    variables.put("TxnDate", "2023-05-30");
    VariableMap variableMap2 = new VariableMapImpl();
    variableMap2.putAll(variables);

    WorkflowTaskRequest.WorkflowTaskRequestBuilder taskRequestBuilder =
        WorkflowTaskRequest.builder()
            .processInstanceId(workerActionRequest.getProcessInstanceId())
            .id(workerActionRequest.getTaskId())
            .activityId(workerActionRequest.getActivityId())
            .activityName("actName1")
            .taskType(TaskType.NOTIFICATION_TASK)
            .taskAttributes(
                TaskAttributes.builder()
                    .modelAttributes(modelDefAttributes)
                    .runtimeAttributes(runtimeDefAttributes)
                    .variables(variableMap2)
                    .build());

    TemplateDetails templateDetails = TemplateDetails.builder().offeringId("offering1").build();
    DefinitionDetails definitionDetails =
        DefinitionDetails.builder()
            .version(1)
            .templateDetails(templateDetails)
            .recordType(RecordType.INVOICE)
            .build();

    ProcessDetails processDetails =
        ProcessDetails.builder()
            .recordId("record1")
            .definitionDetails(definitionDetails)
            .ownerId(1l)
            .build();

    WorkflowTaskRequest createTaskRequest =
        taskRequestBuilder
            .skipCallback(false)
            .publishExternalTaskEvent(false)
            .publishWorkflowStateTransitionEvent(false)
            .command(TaskCommand.CREATE)
            .status(TASK_STATUS_CREATED)
            .workerId(workerActionRequest.getWorkerId())
            .recordId(processDetails.getRecordId())
            .build();

    Mockito.when(
            processDetailsRepoService.findByProcessIdWithoutDefinitionData(
                Mockito.eq(workerActionRequest.getRootProcessInstanceId())))
        .thenReturn(Optional.of(definitionDetails));

    Map<String, HandlerDetails.ParameterDetails> parameterDetailsMap = new HashMap<>();
    HandlerDetails.ParameterDetails parameterDetailsRealmId = new HandlerDetails.ParameterDetails();
    parameterDetailsMap.put("Message", parameterDetailsRealmId);
    Mockito.when(
            parameterDetailsExtractorHelper.getParameterDetails(
                Mockito.eq(workerActionRequest), Mockito.eq(definitionDetails)))
        .thenReturn(Optional.of(parameterDetailsMap));

    notificationTaskRequestModifier.getTaskRequest(createTaskRequest, workerActionRequest);

    Assert.assertEquals(
        "{\"Message\": \"review your tasks\", \"Subject\": \"Review your tasks\"}",
        createTaskRequest
            .getTaskAttributes()
            .getRuntimeAttributes()
            .get(OinpBridgeConstants.NOTIFICATION_DATA));
    Assert.assertEquals(
        "customWorkflow",
        createTaskRequest
            .getTaskAttributes()
            .getModelAttributes()
            .get(OinpBridgeConstants.NOTIFICATION_NAME));
    Assert.assertEquals(
        "customWorkflow",
        createTaskRequest
            .getTaskAttributes()
            .getModelAttributes()
            .get(OinpBridgeConstants.NOTIFICATION_DATA_TYPE));
    Assert.assertEquals(
        "Workflow",
        createTaskRequest
            .getTaskAttributes()
            .getModelAttributes()
            .get(OinpBridgeConstants.SERVICE_NAME));
  }

  @Test
  public void testGetName(){
    Assert.assertEquals(TaskType.NOTIFICATION_TASK, notificationTaskRequestModifier.getName());
  }
}
