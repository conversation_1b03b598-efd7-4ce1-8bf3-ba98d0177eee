package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.eventHandler;


import static org.mockito.Mockito.never;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.taskcompletionhandler.ExternalTaskCompletionHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.taskcompletionhandler.TaskCompletionHandlers;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.common.WorkflowCustomTaskHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.common.WorkflowExternalTaskManager;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.WorkflowTaskConfig;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityProgressDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TransactionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ActivityProgressDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.EventHeaderConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.externaltask.ExternalTaskCompleted;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.externaltask.ExternalTaskStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskAttributes;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskCommand;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowActivityAttributes;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskResponse;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(MockitoJUnitRunner.class)
public class ExternalTaskEventHandlerHelperTest {

    @InjectMocks
    private ExternalTaskEventHandlerHelper externalTaskEventHandlerHelper;

    @Mock
    ExternalTaskCompletionHandler taskCompletionHandlerMock;

    @Mock
    private ActivityProgressDetailsRepository progressDetailRepo;

    @Mock
    private WorkflowExternalTaskManager workflowTaskManager;

    @Mock
    private WorkflowTaskConfig workflowTaskConfig;

    @Mock
    private WASContextHandler wasContextHandler;

    @Mock
    private WorkflowCustomTaskHelper customTaskHelper;

    @Before
    public void init() {
        MockitoAnnotations.initMocks(this);
        TaskCompletionHandlers.addHandler(EventEntityType.EXTERNALTASK, taskCompletionHandlerMock);
        customTaskHelper = new WorkflowCustomTaskHelper(null, null, progressDetailRepo,
                workflowTaskManager, workflowTaskConfig, wasContextHandler);
        ReflectionTestUtils.setField(externalTaskEventHandlerHelper, "customTaskHelper", customTaskHelper);
    }


    @Test
    public void testExecuteSuccess() throws Exception {
        ExternalTaskCompleted task =
                ExternalTaskCompleted.builder()
                        .status(ExternalTaskStatus.SUCCESS.getStatus())
                        .variables(Collections.EMPTY_MAP)
                        .localVariables(Collections.EMPTY_MAP)
                        .build();
        Mockito.when(workflowTaskConfig.isEnable()).thenReturn(false);
        externalTaskEventHandlerHelper.handleOtherStatus(task, getHeaders(), EventEntityType.EXTERNALTASK);
        Mockito.verify(taskCompletionHandlerMock).completeTask(task, getHeaders());
    }

    @Test
    public void testExecuteFailure() throws Exception {
        ExternalTaskCompleted task =
                ExternalTaskCompleted.builder()
                        .status(ExternalTaskStatus.FAILED.getStatus())
                        .errorMessage("errorMessage")
                        .build();

        Mockito.when(workflowTaskConfig.isEnable()).thenReturn(false);

        externalTaskEventHandlerHelper.handleOtherStatus(task, getHeaders(), EventEntityType.EXTERNALTASK);
        Mockito.verify(taskCompletionHandlerMock).invokeFailure(task, getHeaders());
    }

    @Test(expected = WorkflowGeneralException.class)
    public void testExecuteMissingEntityId() throws Exception {
        ExternalTaskCompleted task =
                ExternalTaskCompleted.builder()
                        .status(ExternalTaskStatus.FAILED.getStatus())
                        .variables(Collections.EMPTY_MAP)
                        .build();

        Map<String, String> headers = new HashMap<>();
        externalTaskEventHandlerHelper.handleOtherStatus(task, headers, EventEntityType.EXTERNALTASK);
    }

    @Test
    public void testExecuteCamundaExceptionOnComplete() throws Exception {
        ExternalTaskCompleted task =
                ExternalTaskCompleted.builder()
                        .status(ExternalTaskStatus.SUCCESS.getStatus())
                        .variables(Collections.EMPTY_MAP)
                        .build();

        Mockito.doThrow(new WorkflowGeneralException(WorkflowError.CAMUNDA_COMPLETE_TASK_FAILED))
                .when(taskCompletionHandlerMock)
                .completeTask(Mockito.any(), Mockito.any());

        Mockito.when(workflowTaskConfig.isEnable()).thenReturn(false);

        externalTaskEventHandlerHelper.handleOtherStatus(task, getHeaders(), EventEntityType.EXTERNALTASK);

        Mockito.verify(taskCompletionHandlerMock).handleFailure(Mockito.any(Map.class), Mockito.any());
    }

    @Test
    public void testExecuteNPEOnComplete() throws Exception {
        ExternalTaskCompleted task =
                ExternalTaskCompleted.builder()
                        .status(ExternalTaskStatus.SUCCESS.getStatus())
                        .variables(Collections.EMPTY_MAP)
                        .build();
        Mockito.when(workflowTaskConfig.isEnable()).thenReturn(false);
        Mockito.doThrow(new NullPointerException())
                .when(taskCompletionHandlerMock)
                .completeTask(Mockito.any(), Mockito.any());
        externalTaskEventHandlerHelper.handleOtherStatus(task, getHeaders(), EventEntityType.EXTERNALTASK);

        Mockito.verify(taskCompletionHandlerMock).handleFailure(Mockito.any(Map.class), Mockito.any());
    }

    @Test(expected = WorkflowGeneralException.class)
    public void testExecuteCamundaExceptionOn404() throws Exception {
        ExternalTaskCompleted task =
                ExternalTaskCompleted.builder()
                        .status(ExternalTaskStatus.SUCCESS.getStatus())
                        .variables(Collections.EMPTY_MAP)
                        .build();

        Mockito.doThrow(new WorkflowGeneralException(WorkflowError.CAMUNDA_TASK_NOT_FOUND))
                .when(taskCompletionHandlerMock)
                .completeTask(Mockito.any(), Mockito.any());
        externalTaskEventHandlerHelper.handleOtherStatus(task, getHeaders(), EventEntityType.EXTERNALTASK);

        Mockito.verify(taskCompletionHandlerMock, never()).handleFailure(Mockito.any(), Mockito.any());
    }

    @Test(expected = WorkflowRetriableException.class)
    public void testExecuteCamundaExceptionRetryable() throws Exception {
        ExternalTaskCompleted task =
                ExternalTaskCompleted.builder()
                        .status(ExternalTaskStatus.SUCCESS.getStatus())
                        .variables(Collections.EMPTY_MAP)
                        .build();

        Mockito.doThrow(new WorkflowRetriableException(WorkflowError.EXTERNAL_TASK_HANDLER_ERROR))
                .when(taskCompletionHandlerMock)
                .completeTask(Mockito.any(), Mockito.any());

        externalTaskEventHandlerHelper.handleOtherStatus(task, getHeaders(), EventEntityType.EXTERNALTASK);

        Mockito.verify(taskCompletionHandlerMock, never()).handleFailure(Mockito.any(), Mockito.any());
    }

    @Test(expected = WorkflowGeneralException.class)
    public void testExecuteCamundaExceptionOnFailure() throws Exception {
        ExternalTaskCompleted task =
                ExternalTaskCompleted.builder()
                        .status(ExternalTaskStatus.FAILED.getStatus())
                        .errorMessage("errorMessage")
                        .build();

        Mockito.doThrow(new WorkflowGeneralException(WorkflowError.CAMUNDA_COMPLETE_TASK_FAILED))
                .when(taskCompletionHandlerMock)
                .invokeFailure(Mockito.any(), Mockito.any());

        externalTaskEventHandlerHelper.handleOtherStatus(task, getHeaders(), EventEntityType.EXTERNALTASK);
        Mockito.verify(taskCompletionHandlerMock).invokeFailure(Mockito.any(), Mockito.any());
    }

    @Test(expected = WorkflowGeneralException.class)
    public void testHandleFailure_failure() throws Exception {
        ExternalTaskCompleted task =
                ExternalTaskCompleted.builder()
                        .status(ExternalTaskStatus.SUCCESS.getStatus())
                        .errorMessage("errorMessage")
                        .build();

        Mockito.doThrow(new WorkflowGeneralException(WorkflowError.CAMUNDA_FAILURE_TASK_FAILED))
                .when(taskCompletionHandlerMock)
                .completeTask(Mockito.any(), Mockito.any());
        Mockito.doThrow(new WorkflowGeneralException(WorkflowError.CAMUNDA_FAILURE_TASK_FAILED))
                .when(taskCompletionHandlerMock)
                .handleFailure(Mockito.any(), Mockito.any());
        externalTaskEventHandlerHelper.handleOtherStatus(task, getHeaders(), EventEntityType.EXTERNALTASK);
    }


    private Map<String, String> getHeaders() {
        Map<String, String> headers = new HashMap<>();
        headers.put(EventHeaderConstants.ENTITY_ID, "taskId:workerId");
        headers.put(EventHeaderConstants.DOMAIN_EVENT, EventEntityType.EXTERNALTASK.getEntityType());

        return headers;
    }

    @Test
    public void testExecuteUpdateStatus() throws Exception {
        ExternalTaskCompleted task = ExternalTaskCompleted.builder()
                .status(ExternalTaskStatus.BLOCKED.getStatus())
                .variables(Collections.EMPTY_MAP).localVariables(Collections.EMPTY_MAP).build();

        Mockito.when(workflowTaskConfig.isEnable()).thenReturn(false);

        externalTaskEventHandlerHelper.handleOtherStatus(task, getHeaders(), EventEntityType.EXTERNALTASK);
        Mockito.verify(taskCompletionHandlerMock).updateStatus(task, getHeaders());
        Mockito.verify(wasContextHandler, Mockito.times(1))
                .addKey(WASContextEnums.EVENT_TYPE, ActivityConstants.TASK_EVENT_TYPE_UPDATE);
    }


    @Test(expected = WorkflowGeneralException.class)
    public void testExecuteUpdateStatus_failure_throwException() throws Exception {
        ExternalTaskCompleted task = ExternalTaskCompleted.builder()
                .status(ExternalTaskStatus.BLOCKED.getStatus())
                .variables(Collections.EMPTY_MAP).localVariables(Collections.EMPTY_MAP).build();

        WorkflowGeneralException ex = new WorkflowGeneralException(
                WorkflowError.EXTERNAL_TASK_DETAILS_NOT_FOUND_ERROR);

        Mockito.doThrow(ex).when(taskCompletionHandlerMock)
                .updateStatus(Mockito.any(ExternalTaskCompleted.class),
                        Mockito.anyMap());
        externalTaskEventHandlerHelper.handleOtherStatus(task, getHeaders(), EventEntityType.EXTERNALTASK);
    }

    @Test
    public void testExecuteUpdateStatus_failure_metricLogging() throws Exception {
        ExternalTaskCompleted task = ExternalTaskCompleted.builder()
                .status(ExternalTaskStatus.BLOCKED.getStatus())
                .variables(Collections.EMPTY_MAP).localVariables(Collections.EMPTY_MAP).build();

        WorkflowGeneralException ex = new WorkflowGeneralException(
                WorkflowError.UPDATE_EXECUTION_VARIABLE_FAILED);
        Mockito.doThrow(ex).when(taskCompletionHandlerMock)
                .updateStatus(Mockito.any(ExternalTaskCompleted.class),
                        Mockito.anyMap());

        Mockito.when(workflowTaskConfig.isEnable()).thenReturn(false);
        externalTaskEventHandlerHelper.handleOtherStatus(task, getHeaders(), EventEntityType.EXTERNALTASK);
        Mockito.verify(taskCompletionHandlerMock, Mockito.times(1))
                .updateStatus(Mockito.any(ExternalTaskCompleted.class), Mockito.anyMap());
        Mockito.verify(taskCompletionHandlerMock, Mockito.times(1)).handleFailure(Mockito.anyMap(),
                Mockito.any(Exception.class));
    }


    @Test
    public void test_WorflowTaskManager_update() {
        ExternalTaskCompleted task = ExternalTaskCompleted.builder()
                .status(ExternalTaskStatus.BLOCKED.getStatus())
                .variables(Collections.EMPTY_MAP).localVariables(Collections.EMPTY_MAP).build();

        Map<String, Object> runtimeDefAttributes = new HashMap<>();
        runtimeDefAttributes.put("journalNo", "${journalNo}");
        runtimeDefAttributes.put("assigneeId", "${expertId}");
        runtimeDefAttributes.put("customerId", "${userId}");

        Map<String, String> modelDefAttributes = new HashMap<>();
        modelDefAttributes.put("visibility", "true");
        modelDefAttributes.put("estimate", "3");
        modelDefAttributes.put("taskType", "QB_INVOICE_APPROVAL");

        WorkflowActivityAttributes activityAttributes = WorkflowActivityAttributes.builder()
                .modelAttributes(modelDefAttributes).runtimeAttributes(runtimeDefAttributes).build();

        ProcessDetails processDetails = ProcessDetails.builder().processId("pid1")
                .definitionDetails(DefinitionDetails.builder()
                        .templateDetails(TemplateDetails.builder().templateName("template").build())
                        .build()).build();
        ActivityDetail activityDetail = ActivityDetail.builder()
                .activityId("actId1").activityName("actName1").type(TaskType.HUMAN_TASK)
                .attributes(ObjectConverter.toJson(activityAttributes)).build();
        TransactionDetails txnDetails = TransactionDetails.builder().id(1l).txnId("txn1").build();
        ActivityProgressDetails progressDetails = ActivityProgressDetails.builder()
                .processDetails(processDetails).activityDefinitionDetail(activityDetail)
                .txnDetails(txnDetails).build();

        Mockito.when(progressDetailRepo.findById(Mockito.anyString()))
                .thenReturn(Optional.of(progressDetails));

        WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder()
                .id("taskId").processInstanceId(processDetails.getProcessId())
                .txnId(progressDetails.getTxnDetails().getTxnId())
                .command(TaskCommand.UPDATE).status(ExternalTaskStatus.BLOCKED.getStatus())
                .publishExternalTaskEvent(false).publishWorkflowStateTransitionEvent(false)
                .taskType(TaskType.HUMAN_TASK).activityId("actId1").activityName("actName1")
                .workerId("workerId")
                .taskAttributes(TaskAttributes.builder()
                        .modelAttributes(activityAttributes.getModelAttributes())
                        .runtimeAttributes(activityAttributes.getRuntimeAttributes())
                        .variables(runtimeDefAttributes).build())
                .build();

        Mockito.when(workflowTaskManager.execute(Mockito.eq(taskRequest)))
                .thenReturn(WorkflowTaskResponse.builder().build());

        Mockito.when(workflowTaskConfig.isEnable()).thenReturn(true);

        externalTaskEventHandlerHelper.handleOtherStatus(task, getHeaders(), EventEntityType.EXTERNALTASK);

        Mockito.verify(workflowTaskManager, Mockito.times(1))
                .execute(Mockito.eq(taskRequest));
    }

    @Test
    public void test_WorflowTaskManager_complete() {

        Map<String, Object> variableMap = new HashMap<>();
        variableMap.put("abc", new HashMap<>());
        ((Map<String, Object>) variableMap.get("abc")).put("type", "string");
        ((Map<String, Object>) variableMap.get("abc")).put("value", "def");
        ExternalTaskCompleted task = ExternalTaskCompleted.builder()
                .status(ExternalTaskStatus.SUCCESS.getStatus())
                .variables(variableMap).localVariables(Collections.EMPTY_MAP).build();

        Map<String, Object> runtimeDefAttributes = new HashMap<>();
        runtimeDefAttributes.put("journalNo", "${journalNo}");
        runtimeDefAttributes.put("assigneeId", "${expertId}");
        runtimeDefAttributes.put("customerId", "${userId}");

        Map<String, String> modelDefAttributes = new HashMap<>();
        modelDefAttributes.put("visibility", "true");
        modelDefAttributes.put("estimate", "3");
        modelDefAttributes.put("taskType", "QB_INVOICE_APPROVAL");

        WorkflowActivityAttributes activityAttributes = WorkflowActivityAttributes.builder()
                .modelAttributes(modelDefAttributes).runtimeAttributes(runtimeDefAttributes).build();

        ProcessDetails processDetails = ProcessDetails.builder().processId("pid1")
                .definitionDetails(DefinitionDetails.builder()
                        .templateDetails(TemplateDetails.builder().templateName("template").build())
                        .build()).build();
        ActivityDetail activityDetail = ActivityDetail.builder()
                .activityId("actId1").activityName("actName1").type(TaskType.HUMAN_TASK)
                .attributes(ObjectConverter.toJson(activityAttributes)).build();
        TransactionDetails txnDetails = TransactionDetails.builder().id(1l).txnId("txn1").build();

        Map<String, String> runtimeAttributes = new HashMap<>();
        runtimeAttributes.put("a1", "a1");
        ActivityProgressDetails progressDetails = ActivityProgressDetails.builder()
                .processDetails(processDetails).activityDefinitionDetail(activityDetail)
                .txnDetails(txnDetails).attributes(ObjectConverter.toJson(runtimeAttributes)).build();

        Mockito.when(progressDetailRepo.findById(Mockito.anyString()))
                .thenReturn(Optional.of(progressDetails));

        WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder()
                .id("taskId").processInstanceId(processDetails.getProcessId())
                .txnId(progressDetails.getTxnDetails().getTxnId())
                .taskType(TaskType.HUMAN_TASK).activityId("actId1").activityName("actName1")
                .command(TaskCommand.COMPLETE).status(ActivityConstants.TASK_STATUS_COMPLETE)
                .publishExternalTaskEvent(false).publishWorkflowStateTransitionEvent(false)
                .workerId("workerId")
                .taskAttributes(TaskAttributes.builder().variables(variableMap).build())
                .build();

        Mockito.when(workflowTaskManager.execute(Mockito.eq(taskRequest)))
                .thenReturn(WorkflowTaskResponse.builder().build());

        Mockito.when(workflowTaskConfig.isEnable()).thenReturn(true);

        externalTaskEventHandlerHelper.handleOtherStatus(task, getHeaders(), EventEntityType.EXTERNALTASK);

        Mockito.verify(workflowTaskManager, Mockito.times(1))
                .execute(Mockito.eq(taskRequest));
    }


    @Test
    public void test_WorflowTaskManager_failed() {
        ExternalTaskCompleted task = ExternalTaskCompleted.builder()
                .status(ExternalTaskStatus.FAILED.getStatus())
                .variables(Collections.EMPTY_MAP).localVariables(Collections.EMPTY_MAP).build();

        Map<String, Object> runtimeDefAttributes = new HashMap<>();
        runtimeDefAttributes.put("journalNo", "${journalNo}");
        runtimeDefAttributes.put("assigneeId", "${expertId}");
        runtimeDefAttributes.put("customerId", "${userId}");

        Map<String, String> modelDefAttributes = new HashMap<>();
        modelDefAttributes.put("visibility", "true");
        modelDefAttributes.put("estimate", "3");
        modelDefAttributes.put("taskType", "QB_INVOICE_APPROVAL");

        WorkflowActivityAttributes activityAttributes = WorkflowActivityAttributes.builder()
                .modelAttributes(modelDefAttributes).runtimeAttributes(runtimeDefAttributes).build();

        ProcessDetails processDetails = ProcessDetails.builder().processId("pid1")
                .definitionDetails(DefinitionDetails.builder()
                        .templateDetails(TemplateDetails.builder().templateName("template").build())
                        .build()).build();
        ActivityDetail activityDetail = ActivityDetail.builder()
                .activityId("actId1").activityName("actName1").type(TaskType.HUMAN_TASK)
                .attributes(ObjectConverter.toJson(activityAttributes))
                .build();

        TransactionDetails txnDetails = TransactionDetails.builder().id(1l).txnId("txn1").build();
        ActivityProgressDetails progressDetails = ActivityProgressDetails.builder()
                .processDetails(processDetails).activityDefinitionDetail(activityDetail)
                .txnDetails(txnDetails).build();

        Mockito.when(progressDetailRepo.findById(Mockito.anyString()))
                .thenReturn(Optional.of(progressDetails));

        WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder()
                .id("taskId").processInstanceId(processDetails.getProcessId())
                .txnId(progressDetails.getTxnDetails().getTxnId())
                .command(TaskCommand.FAILED).status(ExternalTaskStatus.FAILED.getStatus())
                .publishExternalTaskEvent(false).publishWorkflowStateTransitionEvent(false)
                .taskType(TaskType.HUMAN_TASK).activityId("actId1").activityName("actName1")
                .workerId("workerId")
                .taskAttributes(TaskAttributes.builder()
                        .modelAttributes(activityAttributes.getModelAttributes())
                        .runtimeAttributes(activityAttributes.getRuntimeAttributes())
                        .variables(runtimeDefAttributes).build())
                .build();

        Mockito.when(workflowTaskManager
                        .execute(Mockito.any(WorkflowTaskRequest.class)))
                .thenReturn(WorkflowTaskResponse.builder().build());

        Mockito.when(workflowTaskConfig.isEnable()).thenReturn(true);

        externalTaskEventHandlerHelper.handleOtherStatus(task, getHeaders(), EventEntityType.EXTERNALTASK);

        Mockito.verify(workflowTaskManager, Mockito.times(1))
                .execute(Mockito.eq(taskRequest));
        Mockito.verify(wasContextHandler, Mockito.times(1))
                .addKey(WASContextEnums.EVENT_TYPE, ActivityConstants.TASK_STATUS_FAILED);
    }

    @Test
    public void test_WorflowTaskManager_NoProgressDetails() {
        ExternalTaskCompleted task = ExternalTaskCompleted.builder()
                .status(ExternalTaskStatus.SUCCESS.getStatus())
                .variables(Collections.EMPTY_MAP).localVariables(Collections.EMPTY_MAP).build();

        Mockito.when(workflowTaskConfig.isEnable()).thenReturn(true);

        Mockito.when(progressDetailRepo.findById(Mockito.anyString()))
                .thenReturn(Optional.ofNullable(null));

        externalTaskEventHandlerHelper.handleOtherStatus(task, getHeaders(), EventEntityType.EXTERNALTASK);

        Mockito.verify(workflowTaskManager, Mockito.never())
                .execute(Mockito.any(WorkflowTaskRequest.class));
    }


    @SuppressWarnings("unchecked")
    @Test(expected = WorkflowGeneralException.class)
    public void test_WorflowTaskManager_complete_CamundaCompleteFailed() {

        Map<String, Object> variableMap = new HashMap<>();
        variableMap.put("abc", new HashMap<>());
        ((Map<String, Object>) variableMap.get("abc")).put("value", "def");
        ExternalTaskCompleted task = ExternalTaskCompleted.builder()
                .status(ExternalTaskStatus.SUCCESS.getStatus())
                .variables(variableMap).localVariables(Collections.EMPTY_MAP).build();

        Mockito.when(workflowTaskConfig.isEnable()).thenReturn(true);

        Mockito.doThrow(new WorkflowRetriableException(WorkflowError.CAMUNDA_COMPLETE_TASK_FAILED))
                .when(taskCompletionHandlerMock)
                .completeTask(Mockito.any(), Mockito.any());

        externalTaskEventHandlerHelper.handleOtherStatus(task, getHeaders(), EventEntityType.EXTERNALTASKREST);
    }


    @Test(expected = RuntimeException.class)
    public void testExecuteUpdateStatus_WorflowTaskManagerExecution_Failed() throws Exception {
        ExternalTaskCompleted task = ExternalTaskCompleted.builder()
                .status(ExternalTaskStatus.BLOCKED.getStatus())
                .variables(Collections.EMPTY_MAP).localVariables(Collections.EMPTY_MAP).build();

        Mockito.when(workflowTaskConfig.isEnable()).thenThrow(new RuntimeException(""));

        externalTaskEventHandlerHelper.handleOtherStatus(task, getHeaders(), EventEntityType.EXTERNALTASK);
    }

    @Test
    public void test_ExtendStatus() {
        ExternalTaskCompleted task = ExternalTaskCompleted.builder()
                .status(ExternalTaskStatus.EXTEND_LOCK.getStatus())
                .build();
        externalTaskEventHandlerHelper.handleExtendLockStatus(task, getHeaders());
        Mockito.verify(taskCompletionHandlerMock, Mockito.times(1)).extendLock(Mockito.any(), Mockito.anyMap());
    }

    @Test(expected = WorkflowGeneralException.class)
    public void test_ExtendStatus_Failed() {
        ExternalTaskCompleted task = ExternalTaskCompleted.builder()
                .status(ExternalTaskStatus.EXTEND_LOCK.getStatus())
                .build();
        Mockito.doThrow(WorkflowGeneralException.class).when(taskCompletionHandlerMock).extendLock(Mockito.any(), Mockito.anyMap());
        externalTaskEventHandlerHelper.handleExtendLockStatus(task, getHeaders());
    }

    @Test
    public void handleFailureWithRetryStatus_Success() {
        ExternalTaskCompleted task = ExternalTaskCompleted.builder()
                .status(ExternalTaskStatus.FAIL_WITH_RETRY.getStatus())
                .retries(1)
                .extendDuration(1000L)
                .build();
        externalTaskEventHandlerHelper.handleFailureWithRetryStatus(task, getHeaders(), EventEntityType.EXTERNALTASK);
        Mockito.verify(taskCompletionHandlerMock, Mockito.times(1)).invokeFailureWithRetry(Mockito.any(), Mockito.anyMap());
    }

    @Test
    public void handleFailureWithRetryStatus_ValidationFailure_RetriesNegative() {
        ExternalTaskCompleted task = ExternalTaskCompleted.builder()
                .status(ExternalTaskStatus.FAIL_WITH_RETRY.getStatus())
                .retries(-1)
                .extendDuration(1000L)
                .build();
        Throwable e = Assertions.assertThrows(WorkflowGeneralException.class, () -> {
            externalTaskEventHandlerHelper.handleFailureWithRetryStatus(task, getHeaders(), EventEntityType.EXTERNALTASK);
        });
        Assert.assertEquals("Validation failed. error=retries < 0", e.getMessage());

    }

    @Test
    public void handleFailureWithRetryStatus_ValidationFailure_RetriesNull() {
        ExternalTaskCompleted task = ExternalTaskCompleted.builder()
                .status(ExternalTaskStatus.FAIL_WITH_RETRY.getStatus())
                .retries(null)
                .extendDuration(1000L)
                .build();
        Throwable e = Assertions.assertThrows(WorkflowGeneralException.class, () -> {
            externalTaskEventHandlerHelper.handleFailureWithRetryStatus(task, getHeaders(), EventEntityType.EXTERNALTASK);
        });
        Assert.assertEquals("Validation failed. error=retries is null", e.getMessage());

    }

    @Test
    public void handleFailureWithRetryStatus_ValidationFailure_ExtendDuration_Null() {
        ExternalTaskCompleted task = ExternalTaskCompleted.builder()
                .status(ExternalTaskStatus.FAIL_WITH_RETRY.getStatus())
                .retries(10)
                .extendDuration(null)
                .build();
        Throwable e = Assertions.assertThrows(WorkflowGeneralException.class, () -> {
            externalTaskEventHandlerHelper.handleFailureWithRetryStatus(task, getHeaders(), EventEntityType.EXTERNALTASK);
        });
        Assert.assertEquals("Validation failed. error=extendDuration is null", e.getMessage());

    }

    @Test
    public void handleFailureWithRetryStatus_ValidationFailure_ExtendDuration_ZeroOrNegative() {
        ExternalTaskCompleted task = ExternalTaskCompleted.builder()
                .status(ExternalTaskStatus.FAIL_WITH_RETRY.getStatus())
                .retries(10)
                .extendDuration(0L)
                .build();
        Throwable e = Assertions.assertThrows(WorkflowGeneralException.class, () -> {
            externalTaskEventHandlerHelper.handleFailureWithRetryStatus(task, getHeaders(), EventEntityType.EXTERNALTASK);
        });
        Assert.assertEquals("Validation failed. error=extendDuration <= 0", e.getMessage());

    }

    @Test
    public void test_invokeFailureWithRetryFailed() {
        ExternalTaskCompleted task = ExternalTaskCompleted.builder()
                .status(ExternalTaskStatus.FAIL_WITH_RETRY.getStatus())
                .retries(1)
                .extendDuration(1000L)
                .build();
        Mockito.doThrow(new WorkflowGeneralException(WorkflowError.EXTERNAL_TASK_VALIDATION_ERROR)).when(taskCompletionHandlerMock).invokeFailureWithRetry(Mockito.any(), Mockito.anyMap());
        Assertions.assertThrows(WorkflowGeneralException.class, () -> {
            externalTaskEventHandlerHelper.handleFailureWithRetryStatus(task, getHeaders(), EventEntityType.EXTERNALTASK);
        });

    }

    @Test
    public void test_invokeFailureWithRetryFailed_WithRetryExhausted() {
        ExternalTaskCompleted task = ExternalTaskCompleted.builder()
                .status(ExternalTaskStatus.FAIL_WITH_RETRY.getStatus())
                .retries(1)
                .extendDuration(1000L)
                .build();
        Mockito.doThrow(new WorkflowGeneralException(WorkflowError.TASK_FAIL_WITH_RETRIES_EXHAUSTED)).when(taskCompletionHandlerMock).invokeFailureWithRetry(Mockito.any(), Mockito.anyMap());
        ArgumentCaptor<ExternalTaskCompleted> captor = ArgumentCaptor.forClass(ExternalTaskCompleted.class);
        Mockito.doNothing().when(taskCompletionHandlerMock).invokeFailure(captor.capture(), Mockito.anyMap());
        externalTaskEventHandlerHelper.handleFailureWithRetryStatus(task, getHeaders(), EventEntityType.EXTERNALTASK);
        Assert.assertEquals(ExternalTaskStatus.FAILED.getStatus(), captor.getValue().getStatus());
        Mockito.verify(taskCompletionHandlerMock, Mockito.times(1)).invokeFailure(Mockito.any(), Mockito.anyMap());
        Mockito.verify(workflowTaskConfig, Mockito.times(1)).isEnable();
    }
}
