package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import static org.mockito.ArgumentMatchers.any;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import org.camunda.bpm.engine.variable.VariableMap;
import org.camunda.bpm.engine.variable.impl.VariableMapImpl;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

/**
 * Helper for extracting the parameter details
 *
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class ParameterDetailsExtractorHelperTest {
  @Mock private AppConnectParameterDetailsExtractorHelper appConnectParameterDetailsExtractorHelper;

  @InjectMocks private ParameterDetailsExtractorHelper parameterDetailsExtractorHelper;

  @InjectMocks private DefaultParameterDetailsExtractor defaultParameterDetailsExtractor;

  private static final String parametersSchema =
      TestHelper.readResourceAsString("schema/testData/parameters.json");
  private static final Map<String, String> schema = new HashMap<>();

  static {
    schema.put(WorkFlowVariables.PARAMETERS_KEY.getName(), parametersSchema);
    schema.put(
        "handlerDetails",
        "{\"taskHandler\":\"appconnect\",\"actionName\":\"executeWorkflowAction\",\"handlerId\":\"intuit-workflows/was-send-notification\",\"recordType\":null,\"responseFields\":null,\"handlerScope\":null}");
    schema.put(WorkflowConstants.DEFINITION_KEY, "2121312213");
    schema.put(WorkflowConstants.INTUIT_REALMID, "362727827");
    schema.put(WorkFlowVariables.TASK_DETAILS_KEY.getName(), "{ \"required\": true}");
  }

  @Before
  public void init() {
    ReflectionTestUtils.setField(
        parameterDetailsExtractorHelper,
        "appConnectParameterDetailsExtractorHelper",
        appConnectParameterDetailsExtractorHelper);
    Mockito.when(
            appConnectParameterDetailsExtractorHelper.getAppConnectParameterDetailExtractor(
                any(), any(), any(), any()))
        .thenReturn(defaultParameterDetailsExtractor);
  }

  /** the parameter details are being extracted successfully */
  @Test
  public void getParameterDetailsSuccess() {
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId("hId")
            .build();
    TemplateDetails templateDetails = TemplateDetails.builder().offeringId("offering1").build();
    DefinitionDetails definitionDetails =
        DefinitionDetails.builder()
            .version(1)
            .templateDetails(templateDetails)
            .recordType(RecordType.INVOICE)
            .build();

    Optional<Map<String, HandlerDetails.ParameterDetails>> parameterDetailsMap =
        parameterDetailsExtractorHelper.getParameterDetails(workerActionRequest, definitionDetails);

    Assert.assertEquals(
        "blah blah link blah", parameterDetailsMap.get().get("Message").getFieldValue().get(0));
  }

  /**
   * the handler details not being populated in the activity details raises a
   * WorkflowGeneralException
   */
  @Test(expected = WorkflowGeneralException.class)
  public void getParameterDetailsFailed() {
    Map<String, String> extensionProperties = new HashMap<>();
    extensionProperties.put(
        ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE,
        String.valueOf(TaskType.NOTIFICATION_TASK));
    extensionProperties.put("serviceName", "Workflow");

    Map<String, String> variableMap = new HashMap<>();
    variableMap.put("entityType", "Invoice");
    variableMap.put("TxnDate", "2023-05-30");
    VariableMap variableMap1 = new VariableMapImpl();
    variableMap1.putAll(variableMap);

    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("actId1")
            .extensionProperties(extensionProperties)
            .processInstanceId("processInstance1")
            .taskId("taskId1")
            .ownerId(1l)
            .workerId("worker1")
            .variableMap(variableMap1)
            .inputVariables(variableMap)
            .build();
    TemplateDetails templateDetails = TemplateDetails.builder().offeringId("offering1").build();
    DefinitionDetails definitionDetails =
        DefinitionDetails.builder()
            .version(1)
            .templateDetails(templateDetails)
            .recordType(RecordType.INVOICE)
            .build();

    parameterDetailsExtractorHelper.getParameterDetails(workerActionRequest, definitionDetails);
  }
}
