package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables.RESPONSE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.UNDERSCORE;
import static org.mockito.ArgumentMatchers.any;

import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import java.util.HashMap;
import java.util.Map;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.impl.DataStoreDeleteTaskService;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import org.springframework.test.util.ReflectionTestUtils;

/** <AUTHOR> */
public class DataStoreDeleteTaskHandlerTest {

  @InjectMocks private DataStoreDeleteTaskHandler dataStoreDeleteTaskHandler;
  @Mock private DataStoreDeleteTaskService dataStoreDeleteTaskService;
  @Mock private MetricLogger metricLogger;

  private static Map<String, String> schema = new HashMap<>();
  private static final String parametersSchema =
      TestHelper.readResourceAsString("schema/testData/parameters.json");

  static {
    schema.put(WorkFlowVariables.PARAMETERS_KEY.getName(), parametersSchema);
    schema.put(WorkflowConstants.INTUIT_REALMID, "123");
  }

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
    ReflectionTestUtils.setField(dataStoreDeleteTaskHandler, "metricLogger", metricLogger);
  }

  @Test
  public void testExecuteAction_success() {
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .ownerId(123L)
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId("hId")
            .build();
    Mockito.when(dataStoreDeleteTaskService.execute(Mockito.any())).thenReturn( Map.of(
	        new StringBuilder(workerActionRequest.getActivityId())
	            .append(UNDERSCORE)
	            .append(RESPONSE.getName())
	            .toString(),
	        Boolean.TRUE.toString()));
    Map<String, Object> resp = dataStoreDeleteTaskHandler.executeAction(workerActionRequest);

    Mockito.verify(dataStoreDeleteTaskService, Mockito.times(1)).execute(Mockito.any());

    Assert.assertTrue(
        resp.get(
                workerActionRequest.getActivityId()
                    + WorkflowConstants.UNDERSCORE
                    + WorkFlowVariables.RESPONSE.getName())
            .equals("true"));
  }


  @Test(expected=WorkflowGeneralException.class)
  public void testExecuteAction_exception() {
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .ownerId(123L)
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId("hId")
            .build();
    Mockito.when(dataStoreDeleteTaskService.execute(Mockito.any()))
     .thenThrow(new WorkflowGeneralException(WorkflowError.ERROR_PROCESSING_TASK_DETAILS.name()));

    Map<String, Object> resp = dataStoreDeleteTaskHandler.executeAction(workerActionRequest);

    Mockito.verify(dataStoreDeleteTaskService, Mockito.times(1)).execute(Mockito.any());

    Assert.assertTrue(
        resp.get(
                workerActionRequest.getActivityId()
                    + WorkflowConstants.UNDERSCORE
                    + WorkFlowVariables.RESPONSE.getName())
            .equals("true"));
  }

  @Test
  public void testGetName() {
    Assert.assertEquals(
        "was_dataStoreDeletion", dataStoreDeleteTaskHandler.getName().getTaskHandlerName());
  }

  @Test
  public void test_logMetric() {
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .ownerId(123L)
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId("hId")
            .build();
    Mockito.when(dataStoreDeleteTaskService.execute(Mockito.any()))
        .thenThrow(
            new WorkflowGeneralException(WorkflowError.ERROR_PROCESSING_TASK_DETAILS.name()));
    try {
      dataStoreDeleteTaskHandler.execute(workerActionRequest);
      Assert.fail("Exception should be thrown");
    } catch (WorkflowGeneralException workflowGeneralException) {
      Assert.assertEquals(workflowGeneralException.getWorkflowError(), WorkflowError.INVALID_INPUT);
      Mockito.verify(metricLogger, Mockito.times(1)).logErrorMetric(any(), any(), any());
    }
  }
}
