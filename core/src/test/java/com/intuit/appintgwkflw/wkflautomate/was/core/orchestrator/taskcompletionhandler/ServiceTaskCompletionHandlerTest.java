package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.taskcompletionhandler;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.camunda.service.CamundaRunTimeServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.EventHeaderConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.externaltask.ExternalTaskCompleted;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.externaltask.ExternalTaskStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.request.ServiceTaskCompleteRequest;

@RunWith(MockitoJUnitRunner.class)
public class ServiceTaskCompletionHandlerTest {

  @Mock CamundaRunTimeServiceRest camundaRest;

  @InjectMocks
  ServiceTaskCompletionHandler handler;

  @Captor
  private ArgumentCaptor<ServiceTaskCompleteRequest> taskCompleteRequest;

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
  }

  @Test
  public void testCompleteTask_success() throws Exception {
    ExternalTaskCompleted task =
        ExternalTaskCompleted.builder()
            .status(ExternalTaskStatus.SUCCESS.getStatus())
            .variables(Collections.EMPTY_MAP)
            .localVariables(Collections.EMPTY_MAP)
            .build();

    Map<String, String> headers = new HashMap<>();
    headers.put(EventHeaderConstants.ENTITY_ID, "taskId");
    handler.completeTask(task, headers);
    Mockito.verify(camundaRest).messageServiceTask(taskCompleteRequest.capture());
    Assert.assertFalse(taskCompleteRequest.getValue().isFailed());
    Assert.assertEquals("taskId", taskCompleteRequest.getValue().getExecutionId());

  }

  @Test
  public void testExecuteFailure_success() throws Exception {
    ExternalTaskCompleted task =
        ExternalTaskCompleted.builder()
            .status(ExternalTaskStatus.FAILED.getStatus())
            .errorMessage("errorMessage")
            .build();

    Map<String, String> headers = new HashMap<>();
    headers.put(EventHeaderConstants.ENTITY_ID, "taskId");
    handler.invokeFailure(task, headers);
    Mockito.verify(camundaRest).messageServiceTask(taskCompleteRequest.capture());
    Assert.assertTrue(taskCompleteRequest.getValue().isFailed());
    Assert.assertEquals("taskId", taskCompleteRequest.getValue().getExecutionId());
    Assert.assertEquals("errorMessage", taskCompleteRequest.getValue().getFailureMessage());
  }

  @Test
  public void testHandleFailure() throws Exception {
    ExternalTaskCompleted task =
        ExternalTaskCompleted.builder()
            .status(ExternalTaskStatus.SUCCESS.getStatus())
            .variables(Collections.EMPTY_MAP)
            .build();

    Map<String, String> headers = new HashMap<>();
    headers.put(EventHeaderConstants.ENTITY_ID, "taskId");

    handler.handleFailure(headers, new Exception());

    Mockito.verify(camundaRest).messageServiceTask(taskCompleteRequest.capture());
    Assert.assertTrue(taskCompleteRequest.getValue().isFailed());
    Assert.assertEquals("taskId", taskCompleteRequest.getValue().getExecutionId());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testCompleteTask_failure() throws Exception {
    ExternalTaskCompleted task =
        ExternalTaskCompleted.builder()
            .status(ExternalTaskStatus.SUCCESS.getStatus())
            .variables(Collections.EMPTY_MAP)
            .build();

    Map<String, String> headers = new HashMap<>();
    headers.put(EventHeaderConstants.ENTITY_ID, "taskId");

    Mockito.doThrow(new WorkflowGeneralException(WorkflowError.CAMUNDA_TASK_NOT_FOUND))
        .when(camundaRest)
        .messageServiceTask(taskCompleteRequest.capture());
    handler.completeTask(task, headers);
  }

  @Test
  public void testUpdateStatus_success() throws Exception {
    ExternalTaskCompleted task =
        ExternalTaskCompleted.builder()
            .status(ExternalTaskStatus.BLOCKED.getStatus())
            .variables(Collections.EMPTY_MAP)
            .build();

    Map<String, String> headers = new HashMap<>();
    headers.put(EventHeaderConstants.ENTITY_ID, "taskId");
    handler.updateStatus(task, headers);
    // No Assertions as no handling currently
    Assert.assertEquals(1, headers.size());
  }

  @Test(expected = UnsupportedOperationException.class)
  public void testExtendStatus() throws Exception {
    ExternalTaskCompleted task =
        ExternalTaskCompleted.builder()
            .status(ExternalTaskStatus.EXTEND_LOCK.getStatus())
            .extendDuration(12345L)
            .build();

    Map<String, String> headers = new HashMap<>();
    headers.put(EventHeaderConstants.ENTITY_ID, "taskId");
    handler.extendLock(task, headers);
    // No Assertions as no handling currently
    Assert.assertEquals(1, headers.size());
  }
  
  @Test
  public void testgetName_success() throws Exception {
	  EventEntityType eventEntityType = handler.getName();
	  Assert.assertEquals(EventEntityType.SERVICE_TASK, eventEntityType);
  }

}
