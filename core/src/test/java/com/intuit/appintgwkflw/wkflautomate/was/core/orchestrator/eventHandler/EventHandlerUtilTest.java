package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.eventHandler;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;

/**
 * 
 * <AUTHOR>
 *
 */
@RunWith(MockitoJUnitRunner.class)
public class EventHandlerUtilTest {

	
	@Test
	  public void testTransform() throws Exception {

		WASContextHandler contextHandler = Mockito.mock(WASContextHandler.class);
		Mockito.doNothing().when(contextHandler).addKey(Mockito.eq(WASContextEnums.WORKFLOW), 
				Mockito.anyString());
		ProcessDetails processDetails = ProcessDetails.builder()
			.definitionDetails(DefinitionDetails.builder()
					.templateDetails(TemplateDetails.builder().templateName("tmp1").build()).build()).build();
		EventHandlerUtil.populateContextFromProcessDetails(contextHandler, processDetails);
		Mockito.verify(contextHandler, Mockito.times(1)).addKey(Mockito.eq(WASContextEnums.WORKFLOW), 
				Mockito.anyString());
	  }
	
}
