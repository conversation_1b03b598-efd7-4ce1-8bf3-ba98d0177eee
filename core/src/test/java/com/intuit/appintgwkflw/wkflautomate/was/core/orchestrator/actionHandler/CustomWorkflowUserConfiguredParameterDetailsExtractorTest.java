package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import static org.mockito.ArgumentMatchers.any;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.FilterParameterExtractorUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.impl.ProcessDetailsRepoService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

public class CustomWorkflowUserConfiguredParameterDetailsExtractorTest {

  private CustomWorkflowConfig customWorkflowConfig;
  private WorkerActionRequest workerActionRequest;
  private static final String parametersSchema =
          TestHelper.readResourceAsString("schema/testData/parameters.json");
  private static final Map<String, String> schema = new HashMap<>();
  public static String PLACEHOLDER_VALUES_PATH = "placeholder/appconnect_test_placeholder.json";

  static {
    schema.put(WorkFlowVariables.PARAMETERS_KEY.getName(), parametersSchema);
    schema.put(WorkFlowVariables.HANDLER_DETAILS_KEY.getName(), "{}");
    schema.put("entityType", "invoice");
    schema.put(WorkflowConstants.DEFINITION_KEY, "2121312213");
    schema.put(WorkflowConstants.INTUIT_REALMID, "362727827");
  }

  @Mock private ProcessDetailsRepoService processDetailsRepoService;
  @Mock private FilterParameterExtractorUtil filterParameterExtractorUtil;
  @Before
  public void setUp() throws Exception {
    MockitoAnnotations.openMocks(this);
    customWorkflowConfig = TestHelper.loadCustomConfig();
    TemplateDetails templateDetails =
            TemplateDetails.builder()
                    .id(DefinitionTestConstants.TEMPLATE_ID)
                    .templateName("customReminder")
                    .ownerId(Long.valueOf("9999"))
                    .allowMultipleDefinitions(true)
                    .build();
    DefinitionDetails mockedDefinitionDetails =
            DefinitionDetails.builder()
                    .templateDetails(templateDetails)
                    .placeholderValue(TestHelper.readResourceAsString(PLACEHOLDER_VALUES_PATH))
                    .build();
    Mockito.when(processDetailsRepoService.findByProcessIdWithoutDefinitionData(any()))
            .thenReturn(Optional.of(mockedDefinitionDetails));
  }

  @Test
  public void extractAppconnectParameterDetailsCustomWorkflow() {
    String handlerId = "hid";
    workerActionRequest =
            WorkerActionRequest.builder()
                    .activityId("createTask")
                    .processDefinitionId("dId")
                    .processInstanceId("iId")
                    .ownerId(Long.valueOf("9999"))
                    .inputVariables(schema)
                    .handlerId(handlerId)
                    .build();
    CustomWorkflowUserConfiguredParameterDetailsExtractor customWorkflowUserConfiguredParameterDetailsExtractor =
            new CustomWorkflowUserConfiguredParameterDetailsExtractor(
                    customWorkflowConfig, processDetailsRepoService, filterParameterExtractorUtil);
    Map<String, HandlerDetails.ParameterDetails> parameterDetails =
            customWorkflowUserConfiguredParameterDetailsExtractor.getParameterDetails(workerActionRequest).get();
    Assert.assertEquals(
            Arrays.asList(
                    "Review [[DocNumber]] for Amount [[TxnAmount]] and Balance [[TxnBalanceAmount]]-URGENT"),
            parameterDetails.get("TaskName").getFieldValue());
  }

  @Test
  public void extractAppconnectParameterDetailsCustomWorkflowFailed() {
    Mockito.when(processDetailsRepoService.findByProcessIdWithoutDefinitionData(any()))
            .thenReturn(Optional.ofNullable(null));
    String handlerId = "hid";
    workerActionRequest =
            WorkerActionRequest.builder()
                    .activityId("createTask")
                    .processDefinitionId("dId")
                    .processInstanceId("iId")
                    .ownerId(Long.valueOf("9999"))
                    .inputVariables(schema)
                    .handlerId(handlerId)
                    .build();
    CustomWorkflowUserConfiguredParameterDetailsExtractor customWorkflowUserConfiguredParameterDetailsExtractor =
            new CustomWorkflowUserConfiguredParameterDetailsExtractor(
                    customWorkflowConfig, processDetailsRepoService,filterParameterExtractorUtil);

    try {
      Map<String, HandlerDetails.ParameterDetails> parameterDetails =
              customWorkflowUserConfiguredParameterDetailsExtractor.getParameterDetails(workerActionRequest).get();
      Assert.fail("The above method should throw exception");
    } catch (WorkflowGeneralException e) {
      Assert.assertTrue(
              e.getMessage().contains(WorkflowError.DEFINITION_NOT_FOUND.getErrorMessage()));
    }
  }
}