package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.PROCESS_EXPIRY_TIME_DURATION;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.MultiStepConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.WorkflowTemplate;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionActivityDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.impl.ProcessDetailsRepoService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.async.execution.request.State;
import com.intuit.v4.Authorization;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.json.JSONObject;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

public class ActivitySequenceVariableExtractorTest {

  @Mock
  private ProcessDetailsRepoService processDetailsRepoService;
  @Mock
  private DefinitionActivityDetailsRepository definitionActivityDetailsRepository;
  @Mock
  private TemplateDetails bpmnTemplateDetail;
  @Mock
  private MultiStepConfig multiStepConfig;
  @InjectMocks
  private ActivitySequenceVariableExtractor activitySequenceVariableExtractor;

  private static State state = new State();
  private final static String OWNER_ID = "123";
  private final static String DEFINITION_ID = "dId";
  Authorization authorization = TestHelper.mockAuthorization(OWNER_ID);

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
  }

  static {
    state.addValue(WorkflowConstants.ACTIVITY_ID, "action-2");
    state.addValue(WorkflowConstants.ROOT_PROCESS_INSTANCE_ID, "rpi-1");
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testDefinitionNotFound() {
    Optional<DefinitionDetails> definitionDetails = Optional.empty();

    Mockito.when(processDetailsRepoService.findByProcessIdWithoutDefinitionData(
            Mockito.any()))
        .thenReturn(definitionDetails);

    activitySequenceVariableExtractor.execute(state);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testChildTaskDetailsForActivityNotFound() {
    DefinitionDetails definitionDetails = TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail,
        authorization, DEFINITION_ID);

    Mockito.when(processDetailsRepoService.findByProcessIdWithoutDefinitionData(
            Mockito.any()))
        .thenReturn(Optional.of(definitionDetails));

    Optional<DefinitionActivityDetail> optionalDefinitionActivityDetail = Optional.empty();
    Mockito.when(definitionActivityDetailsRepository.
            findDefinitionActivityDetailByActivityIdAndDefinitionDetails_DefinitionId(
                Mockito.any(), Mockito.any()))
        .thenReturn(optionalDefinitionActivityDetail);

    activitySequenceVariableExtractor.execute(state);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testPlaceholderNotFoundChildTaskDetails() {
    DefinitionDetails definitionDetails = TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail,
        authorization, DEFINITION_ID);

    Mockito.when(processDetailsRepoService.findByProcessIdWithoutDefinitionData(
            Mockito.any()))
        .thenReturn(Optional.of(definitionDetails));

    DefinitionActivityDetail parentDefinitionActivityDetail =
        TestHelper.mockDefinitionActivityDetails(
            "action-1",
            null
        );
    Mockito.when(definitionActivityDetailsRepository.
            findDefinitionActivityDetailByActivityIdAndDefinitionDetails_DefinitionId(
                Mockito.any(), Mockito.any()))
        .thenReturn(Optional.of(parentDefinitionActivityDetail));

    activitySequenceVariableExtractor.execute(state);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testUserAttributesNotFoundInPlaceHolderForChildTaskDetails() {
    DefinitionDetails definitionDetails = TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail,
        authorization, DEFINITION_ID);

    Mockito.when(processDetailsRepoService.findByProcessIdWithoutDefinitionData(
            Mockito.any()))
        .thenReturn(Optional.of(definitionDetails));

    DefinitionActivityDetail parentDefinitionActivityDetail =
        TestHelper.mockDefinitionActivityDetails(
            "action-1",
            null
        );
    ;
    DefinitionActivityDetail childDefinitionActivityDetail =
        TestHelper.mockDefinitionActivityDetails(
            "createTask",
            null
        );
    ;
    childDefinitionActivityDetail.setUserAttributes(new String("{}"));
    parentDefinitionActivityDetail.setChildActivityDetails(
        Collections.singletonList(childDefinitionActivityDetail));

    Mockito.when(definitionActivityDetailsRepository.
            findDefinitionActivityDetailByActivityIdAndDefinitionDetails_DefinitionId(
                Mockito.any(), Mockito.any()))
        .thenReturn(Optional.of(parentDefinitionActivityDetail));

    activitySequenceVariableExtractor.execute(state);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testSelectedNotBooleanInUserAttributes() {
    DefinitionDetails definitionDetails = TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail,
        authorization, DEFINITION_ID);

    Mockito.when(processDetailsRepoService.findByProcessIdWithoutDefinitionData(
            Mockito.any()))
        .thenReturn(Optional.of(definitionDetails));

    DefinitionActivityDetail parentDefinitionActivityDetail = TestHelper.mockDefinitionActivityDetails(
        "action-1",
        null
    );

    DefinitionActivityDetail childDefinitionActivityDetail = TestHelper.mockDefinitionActivityDetails(
        "action-1",
        TestHelper.getMockUserAttributes(true)
    );
    JSONObject placeholder = new JSONObject(childDefinitionActivityDetail.getUserAttributes());
    placeholder.put(WorkflowConstants.SELECTED, "random");
    childDefinitionActivityDetail.setUserAttributes(String.valueOf(placeholder));
    parentDefinitionActivityDetail.setChildActivityDetails(
        Collections.singletonList(childDefinitionActivityDetail));

    Mockito.when(definitionActivityDetailsRepository.
            findDefinitionActivityDetailByActivityIdAndDefinitionDetails_DefinitionId(
                Mockito.any(), Mockito.any()))
        .thenReturn(Optional.of(parentDefinitionActivityDetail));

    activitySequenceVariableExtractor.execute(state);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testSelectedNotPresentInUserAttributes() {
    DefinitionDetails definitionDetails = TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail,
        authorization, DEFINITION_ID);

    Mockito.when(processDetailsRepoService.findByProcessIdWithoutDefinitionData(
            Mockito.any()))
        .thenReturn(Optional.of(definitionDetails));

    DefinitionActivityDetail parentDefinitionActivityDetail = TestHelper.mockDefinitionActivityDetails(
        "action-1",
        null
    );

    DefinitionActivityDetail childDefinitionActivityDetail = TestHelper.mockDefinitionActivityDetails(
        "action-1",
        new JSONObject("{}")
    );
    parentDefinitionActivityDetail.setChildActivityDetails(
        Collections.singletonList(childDefinitionActivityDetail));

    Mockito.when(definitionActivityDetailsRepository.
            findDefinitionActivityDetailByActivityIdAndDefinitionDetails_DefinitionId(
                Mockito.any(), Mockito.any()))
        .thenReturn(Optional.of(parentDefinitionActivityDetail));

    activitySequenceVariableExtractor.execute(state);
  }

  @Test
  public void testExecuteSuccess() {
    DefinitionDetails definitionDetails = TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail,
        authorization, DEFINITION_ID);

    Mockito.when(processDetailsRepoService.findByProcessIdWithoutDefinitionData(
            Mockito.any()))
        .thenReturn(Optional.of(definitionDetails));

    DefinitionActivityDetail parentDefinitionActivityDetail = TestHelper.mockDefinitionActivityDetails(
        "action-1",
        null
    );

    List<DefinitionActivityDetail> childDefinitionActivityDetails = new ArrayList<>();

    childDefinitionActivityDetails.add(
        TestHelper.mockDefinitionActivityDetails(
            "createTask",
            TestHelper.getMockUserAttributes(true)
        )
    );
    childDefinitionActivityDetails.add(
        TestHelper.mockDefinitionActivityDetails(
            "sendCompanyEmail",
            TestHelper.getMockUserAttributes(false)
        )
    );
    childDefinitionActivityDetails.add(
        TestHelper.mockDefinitionActivityDetails(
            "sendPushNotification",
            TestHelper.getMockUserAttributes(true)
        )
    );
    parentDefinitionActivityDetail.setChildActivityDetails(childDefinitionActivityDetails);

    Mockito.when(definitionActivityDetailsRepository.
            findDefinitionActivityDetailByActivityIdAndDefinitionDetails_DefinitionId(
                Mockito.any(), Mockito.any()))
        .thenReturn(Optional.of(parentDefinitionActivityDetail));

    Map<String, Object> expectedSequenceMap = new HashMap<>();
    expectedSequenceMap.put("createTask", "true");
    expectedSequenceMap.put("sendCompanyEmail", "false");
    expectedSequenceMap.put("sendPushNotification", "true");
    activitySequenceVariableExtractor.execute(state);
    Map<String, Object> actualSequenceMap = state.getValue(
        WorkflowConstants.ACTIVITY_MAPPED_SEQUENCE_VARIABLE_MAP);
    Assert.assertEquals(expectedSequenceMap, actualSequenceMap);
  }

  @Test
  public void testSuccessForCustomReminderWithRecurringTask() {
    DefinitionDetails definitionDetails = TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail,
        authorization, DEFINITION_ID);

    Mockito.when(processDetailsRepoService.findByProcessIdWithoutDefinitionData(
        Mockito.any())).thenReturn(Optional.of(definitionDetails));

    DefinitionActivityDetail parentDefinitionActivityDetail = TestHelper.mockDefinitionActivityDetails(
        "action-1", null);

    List<DefinitionActivityDetail> childDefinitionActivityDetails = new ArrayList<>();

    childDefinitionActivityDetails.add(
        TestHelper.mockDefinitionActivityDetails(
            "createTask",
            TestHelper.getMockUserAttributes(true)
        )
    );
    parentDefinitionActivityDetail.setChildActivityDetails(childDefinitionActivityDetails);
    parentDefinitionActivityDetail.setUserAttributes(
        TestHelper.getMockUserAttributesForRecurringReminder(
            true, 2, 3).toString());

    Mockito.when(definitionActivityDetailsRepository.
            findDefinitionActivityDetailByActivityIdAndDefinitionDetails_DefinitionId(
                Mockito.any(), Mockito.any()))
        .thenReturn(Optional.of(parentDefinitionActivityDetail));

    Map<String, Object> expectedSequenceMap = new HashMap<>();
    expectedSequenceMap.put("createTask", "true");
    expectedSequenceMap.put(PROCESS_EXPIRY_TIME_DURATION, "P6D");
    expectedSequenceMap.put(WorkflowConstants.MAX_SCHEDULE_COUNT, 3);

    activitySequenceVariableExtractor.execute(state);
    Map<String, Object> actualSequenceMap = state.getValue(
        WorkflowConstants.ACTIVITY_MAPPED_SEQUENCE_VARIABLE_MAP);
    Assert.assertEquals(expectedSequenceMap, actualSequenceMap);
  }

  @Test
  public void testSuccessForCustomReminderWithNonRecurringTask() {
    TemplateDetails bpmnTemplateDetail = new TemplateDetails();
    bpmnTemplateDetail.setTemplateName("customReminder");
    DefinitionDetails definitionDetails = TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail,
        authorization, DEFINITION_ID);

    Mockito.when(processDetailsRepoService.findByProcessIdWithoutDefinitionData(
        Mockito.any())).thenReturn(Optional.of(definitionDetails));

    DefinitionActivityDetail parentDefinitionActivityDetail = TestHelper.mockDefinitionActivityDetails(
        "action-1", null);

    List<DefinitionActivityDetail> childDefinitionActivityDetails = new ArrayList<>();
    childDefinitionActivityDetails.add(
        TestHelper.mockDefinitionActivityDetails(
            "createTask", TestHelper.getMockUserAttributes(true)));

    parentDefinitionActivityDetail.setChildActivityDetails(childDefinitionActivityDetails);
    parentDefinitionActivityDetail.setUserAttributes(
        TestHelper.getMockUserAttributesForRecurringReminder(
            false, null, null).toString());

    Mockito.when(definitionActivityDetailsRepository.
            findDefinitionActivityDetailByActivityIdAndDefinitionDetails_DefinitionId(
                Mockito.any(), Mockito.any()))
        .thenReturn(Optional.of(parentDefinitionActivityDetail));

    Map<String, WorkflowTemplate> workflowTemplates = new HashMap<>();
    WorkflowTemplate customReminder = new WorkflowTemplate();
    customReminder.setProcessExpiryTimeDuration("P6D");
    workflowTemplates.put("customReminder", customReminder);
    Mockito.when(multiStepConfig.getWorkflowTemplates()).thenReturn(workflowTemplates);

    activitySequenceVariableExtractor.execute(state);

    Map<String, Object> expectedSequenceMap = new HashMap<>();
    expectedSequenceMap.put("createTask", "true");
    expectedSequenceMap.put(PROCESS_EXPIRY_TIME_DURATION, "P6D");

    Map<String, Object> actualSequenceMap = state.getValue(
        WorkflowConstants.ACTIVITY_MAPPED_SEQUENCE_VARIABLE_MAP);
    Assert.assertEquals(expectedSequenceMap, actualSequenceMap);
  }

  @Test
  public void testCustomReminderWithRecurringTaskWithoutValidParameters() {
    DefinitionDetails definitionDetails = TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail,
        authorization, DEFINITION_ID);

    Mockito.when(processDetailsRepoService.findByProcessIdWithoutDefinitionData(
        Mockito.any())).thenReturn(Optional.of(definitionDetails));

    DefinitionActivityDetail parentDefinitionActivityDetail = TestHelper.mockDefinitionActivityDetails(
        "action-1", null);

    List<DefinitionActivityDetail> childDefinitionActivityDetails = new ArrayList<>();

    childDefinitionActivityDetails.add(
        TestHelper.mockDefinitionActivityDetails(
            "createTask",
            TestHelper.getMockUserAttributes(true)
        )
    );
    parentDefinitionActivityDetail.setChildActivityDetails(childDefinitionActivityDetails);
    parentDefinitionActivityDetail.setUserAttributes(
        TestHelper.getMockUserAttributesForRecurringReminder(
            true, null, 3).toString());

    Mockito.when(definitionActivityDetailsRepository.
            findDefinitionActivityDetailByActivityIdAndDefinitionDetails_DefinitionId(
                Mockito.any(), Mockito.any()))
        .thenReturn(Optional.of(parentDefinitionActivityDetail));

    Map<String, Object> expectedSequenceMap = new HashMap<>();
    expectedSequenceMap.put("createTask", "true");

    activitySequenceVariableExtractor.execute(state);
    Map<String, Object> actualSequenceMap = state.getValue(
        WorkflowConstants.ACTIVITY_MAPPED_SEQUENCE_VARIABLE_MAP);
    Assert.assertEquals(expectedSequenceMap, actualSequenceMap);
  }

}