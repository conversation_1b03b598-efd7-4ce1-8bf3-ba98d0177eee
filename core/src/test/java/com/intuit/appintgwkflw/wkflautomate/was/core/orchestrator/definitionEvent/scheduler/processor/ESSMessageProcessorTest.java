package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.processor;

import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.SQSConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowEventException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.offlineticket.OfflineTicketClientException;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.executor.message.ScheduleMessageExecutor;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import java.util.Map;

import com.intuit.identity.authn.offline.sdk.exceptions.UnauthorizedProductImpersonationException;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.cloud.aws.messaging.listener.Acknowledgment;
import org.springframework.test.util.ReflectionTestUtils;
import org.junit.Before;
import org.mockito.MockitoAnnotations;

/** <AUTHOR> */
@RunWith(MockitoJUnitRunner.class)
public class ESSMessageProcessorTest {
  @InjectMocks private ESSMessageProcessor essMessageProcessor;
  @Mock private SQSConfig sqsConfig;
  @Mock private MetricLogger metricLogger;
  @Mock private ScheduleMessageExecutor scheduleMessageExecutor;
  public final String MESSAGE_PATH = "eventScheduler/message.json";
  public final String MESSAGE = TestHelper.readResourceAsString(MESSAGE_PATH);

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
    ReflectionTestUtils.setField(
        essMessageProcessor, "scheduleMessageExecutor", scheduleMessageExecutor);
    ReflectionTestUtils.setField(essMessageProcessor, "sqsConfig", sqsConfig);
    ReflectionTestUtils.setField(essMessageProcessor, "metricLogger", metricLogger);
  }

  @Test
  public void test_process() {
    Acknowledgment acknowledgment = Mockito.mock(Acknowledgment.class);
    essMessageProcessor.process(
        MESSAGE, acknowledgment, Map.of(WorkflowConstants.SQS_APPROXIMATE_RECEIVE_COUNT, "1"));
    Mockito.verify(scheduleMessageExecutor, Mockito.times(1)).transformAndExecute(MESSAGE);
    Mockito.verify(acknowledgment, Mockito.times(1)).acknowledge();
  }

  @Test
  public void test_process_with_retry_exhaust() {
    Acknowledgment acknowledgment = Mockito.mock(Acknowledgment.class);
    Mockito.doThrow(new WorkflowRetriableException("test"))
        .when(scheduleMessageExecutor)
        .transformAndExecute(MESSAGE);
    Mockito.when(sqsConfig.getRetryCount()).thenReturn(3);
    essMessageProcessor.process(
        MESSAGE, acknowledgment, Map.of(WorkflowConstants.SQS_APPROXIMATE_RECEIVE_COUNT, "4"));
    Mockito.verify(scheduleMessageExecutor, Mockito.times(1)).transformAndExecute(MESSAGE);
    Mockito.verify(acknowledgment, Mockito.times(1)).acknowledge();

  }

  @Test
  public void test_process_with_retry_not_exhaust() {
    Acknowledgment acknowledgment = Mockito.mock(Acknowledgment.class);
    Mockito.doThrow(new WorkflowRetriableException("test"))
        .when(scheduleMessageExecutor)
        .transformAndExecute(MESSAGE);
    Mockito.when(sqsConfig.getRetryCount()).thenReturn(3);
    try {
      essMessageProcessor.process(
          MESSAGE, acknowledgment, Map.of(WorkflowConstants.SQS_APPROXIMATE_RECEIVE_COUNT, "2"));
      Assert.fail();
    } catch (Exception exception) {
      Mockito.verify(scheduleMessageExecutor, Mockito.times(1)).transformAndExecute(MESSAGE);
      Mockito.verify(acknowledgment, Mockito.times(0)).acknowledge();
    }
  }

  @Test
  public void test_process_with_non_retry_exveption() {
    Acknowledgment acknowledgment = Mockito.mock(Acknowledgment.class);
    Mockito.doThrow(new RuntimeException("test"))
        .when(scheduleMessageExecutor)
        .transformAndExecute(MESSAGE);
    essMessageProcessor.process(
        MESSAGE, acknowledgment, Map.of(WorkflowConstants.SQS_APPROXIMATE_RECEIVE_COUNT, "4"));
    Mockito.verify(scheduleMessageExecutor, Mockito.times(1)).transformAndExecute(MESSAGE);
    Mockito.verify(acknowledgment, Mockito.times(1)).acknowledge();

  }

  @Test
  public void test_process_with_event_exception() {
    Acknowledgment acknowledgment = Mockito.mock(Acknowledgment.class);
    Mockito.doThrow(new WorkflowEventException("event tests"))
        .when(scheduleMessageExecutor)
        .transformAndExecute(MESSAGE);
    Mockito.when(sqsConfig.getRetryCount()).thenReturn(3);
    try {
      essMessageProcessor.process(
          MESSAGE, acknowledgment, Map.of(WorkflowConstants.SQS_APPROXIMATE_RECEIVE_COUNT, "2"));
      Assert.fail();
    } catch (Exception exception) {
      Mockito.verify(scheduleMessageExecutor, Mockito.times(1)).transformAndExecute(MESSAGE);
      Mockito.verify(acknowledgment, Mockito.times(0)).acknowledge();
    }
  }

  @Test
  public void test_process_with_offlineticket_exception() {
    Acknowledgment acknowledgment = Mockito.mock(Acknowledgment.class);
    Mockito.doThrow(new OfflineTicketClientException("offline tests"))
        .when(scheduleMessageExecutor)
        .transformAndExecute(MESSAGE);
    Mockito.when(sqsConfig.getRetryCount()).thenReturn(3);
    try {
      essMessageProcessor.process(
          MESSAGE, acknowledgment, Map.of(WorkflowConstants.SQS_APPROXIMATE_RECEIVE_COUNT, "2"));
      Assert.fail();
    } catch (Exception exception) {
      Mockito.verify(scheduleMessageExecutor, Mockito.times(1)).transformAndExecute(MESSAGE);
      Mockito.verify(acknowledgment, Mockito.times(0)).acknowledge();
    }
  }

  @Test
  public void test_process_with_UnauthorizedProductImpersonationException() {
    Acknowledgment acknowledgment = Mockito.mock(Acknowledgment.class);
    UnauthorizedProductImpersonationException unauthorizedException = new UnauthorizedProductImpersonationException("Unauthorized");
    OfflineTicketClientException offlineTicketClientException = new OfflineTicketClientException(unauthorizedException);
    Mockito.doThrow(offlineTicketClientException)
            .when(scheduleMessageExecutor)
            .transformAndExecute(MESSAGE);
    essMessageProcessor.process(
            MESSAGE, acknowledgment, Map.of(WorkflowConstants.SQS_APPROXIMATE_RECEIVE_COUNT, "2"));
    Mockito.verify(scheduleMessageExecutor, Mockito.times(1)).transformAndExecute(MESSAGE);
    Mockito.verify(acknowledgment, Mockito.times(1)).acknowledge();
  }
}
