package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.capability;


import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler.TransactionEntityFactory;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler.TriggerHandlerTestData;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter.EventHeaders;
import junit.framework.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;


@RunWith(MockitoJUnitRunner.class)
public class TemplateQueryCapabilityFactoryTest {

  private EventHeaders invoiceApprovalPayload;
  private EventHeaders engagementPayload;
  private EventHeaders payloadWithWorkflowId;
  private EventHeaders customWorkflowPayload;

  private EventHeaders onDemandApprovalPayload;
  private static final String UPDATED_EVENT = "updated";
  private static final String CREATED_EVENT = "created";
  private static final String OWNER_ID = "intuit_realm_id";

  @Mock
  DefaultTemplateQueryCapability defaultTemplateQueryCapability;
  @Mock
  WorkflowProviderTemplateQueryCapability workflowProviderTemplateQueryCapability;
  @Mock
  CustomWorkflowQueryCapability customWorkflowQueryCapability;
  @Mock
  PrecannedToCustomMigrationQueryCapability precannedToCustomMigrationQueryCapability;

  @Mock
  DefinitionKeyQueryCapability definitionKeyQueryCapability;

  @Mock private WASContextHandler contextHandler;
  @InjectMocks
  private TemplateQueryCapabilityFactory templateQueryCapabilityFactory;

  @Before
  public void prepareMockData() {
    invoiceApprovalPayload =
		TransactionEntityFactory.getInstanceOf(
			TriggerHandlerTestData.prepareV3TriggerMessage(UPDATED_EVENT).getTriggerMessage(),
        contextHandler).getEventHeaders();
    payloadWithWorkflowId =
		TransactionEntityFactory.getInstanceOf(
				TriggerHandlerTestData.prepareV3TriggerMessage(CREATED_EVENT).getTriggerMessage(),
	        contextHandler).getEventHeaders();
    payloadWithWorkflowId.setProviderWorkflowId("1234");
    customWorkflowPayload =
		TransactionEntityFactory.getInstanceOf(
				TriggerHandlerTestData.prepareV3TriggerMessage(CREATED_EVENT, "bill").getTriggerMessage(),
	        contextHandler).getEventHeaders();
    engagementPayload =
            TransactionEntityFactory.getInstanceOf(
                    TriggerHandlerTestData.prepareV3TriggerMessage(CREATED_EVENT, "engagement").getTriggerMessage(),
                    contextHandler).getEventHeaders();
    onDemandApprovalPayload = TransactionEntityFactory.getInstanceOf(
            TriggerHandlerTestData.prepareV3TriggerMessage(UPDATED_EVENT).getTriggerMessage(),
            contextHandler).getEventHeaders();
    onDemandApprovalPayload.setOnDemandApproval(true);

  }

  @Test
  public void getDefaultTemplateQueryCapabilityTest() {
    TemplateQueryCapabilityIf templateQueryCapability = templateQueryCapabilityFactory.getTemplateQueryCapability(engagementPayload);
    Assert.assertTrue(templateQueryCapability instanceof DefaultTemplateQueryCapability);
  }

  @Test
  public void getDefaultTemplateQueryCapabilityTestWithCustomConfig() {
    Mockito.when(customWorkflowQueryCapability.isCustomWorkflow(Mockito.anyString(), Mockito.anyString())).thenReturn(true);
    Mockito.when(customWorkflowQueryCapability.isPrecannedDefinitionPresentForWorkflow(Mockito.anyString(), Mockito.anyString())).thenReturn(true);
    TemplateQueryCapabilityIf templateQueryCapability = templateQueryCapabilityFactory.getTemplateQueryCapability(invoiceApprovalPayload);
    Assert.assertTrue(templateQueryCapability instanceof DefaultTemplateQueryCapability);
  }

  @Test
  public void getWorkflowProviderTemplateQueryCapabilityTest() {
    TemplateQueryCapabilityIf templateQueryCapability = templateQueryCapabilityFactory.getTemplateQueryCapability(payloadWithWorkflowId);
    Assert.assertTrue(templateQueryCapability instanceof WorkflowProviderTemplateQueryCapability);
  }

  @Test
  public void getCustomWorkflowQueryCapabilityTest() {
    Mockito.when(customWorkflowQueryCapability.isCustomWorkflow(Mockito.anyString(), Mockito.anyString())).thenReturn(true);
    Mockito.when(customWorkflowQueryCapability.isPrecannedDefinitionPresentForWorkflow(Mockito.anyString(), Mockito.anyString())).thenReturn(false);
    TemplateQueryCapabilityIf templateQueryCapability = templateQueryCapabilityFactory.getTemplateQueryCapability(customWorkflowPayload);
    Assert.assertTrue(templateQueryCapability instanceof CustomWorkflowQueryCapability);
  }

  @Test
  public void getPrecannedToCustomMigrationQueryCapabilityTest() {
    Mockito.when(customWorkflowQueryCapability.isCustomWorkflow(Mockito.anyString(), Mockito.anyString())).thenReturn(true);
    Mockito.when(customWorkflowQueryCapability.isPrecannedDefinitionPresentForWorkflow(Mockito.anyString(), Mockito.anyString())).thenReturn(true);
    Mockito.when(customWorkflowQueryCapability.isLatestCustomPresentForWorkflow(Mockito.anyString(), Mockito.anyString())).thenReturn(true);
    TemplateQueryCapabilityIf templateQueryCapability = templateQueryCapabilityFactory.getTemplateQueryCapability(customWorkflowPayload);
    Assert.assertTrue(templateQueryCapability instanceof PrecannedToCustomMigrationQueryCapability);
  }

  @Test
  public void getDefinitionKeyQueryCapabilityTest() {
    EventHeaders eventHeaders = new EventHeaders();
    eventHeaders.setDefinitionKey("1234");
    TemplateQueryCapabilityIf templateQueryCapabilityIf = templateQueryCapabilityFactory.getTemplateQueryCapability(eventHeaders);
    Assert.assertTrue(templateQueryCapabilityIf instanceof DefinitionKeyQueryCapability);
  }

  @Test
  public void getDefaultTemplateQUeryCapabilityForOnDemand(){
    TemplateQueryCapabilityIf templateQueryCapability = templateQueryCapabilityFactory.getTemplateQueryCapability(onDemandApprovalPayload);
    Assert.assertTrue(templateQueryCapability instanceof DefaultTemplateQueryCapability);
  }


}
