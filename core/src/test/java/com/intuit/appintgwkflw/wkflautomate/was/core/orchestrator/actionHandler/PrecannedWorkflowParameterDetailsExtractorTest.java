package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import static org.mockito.ArgumentMatchers.any;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.impl.ProcessDetailsRepoService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

public class PrecannedWorkflowParameterDetailsExtractorTest {

  private WorkerActionRequest workerActionRequest;

  private static final String parametersSchema =
      TestHelper.readResourceAsString("schema/testData/parameters.json");
  private static final Map<String, String> schema = new HashMap<>();
  public static String PLACEHOLDER_VALUES_PATH =
      "placeholder/appconnect_test_placeholder_precanned.json";

  static {
    schema.put(WorkFlowVariables.PARAMETERS_KEY.getName(), parametersSchema);
    schema.put("entityType", "invoice");
  }

  @Mock private ProcessDetailsRepoService processDetailsRepoService;

  @Before
  public void setUp() throws Exception {
    MockitoAnnotations.openMocks(this);
    TemplateDetails templateDetails =
        TemplateDetails.builder()
            .id(DefinitionTestConstants.TEMPLATE_ID)
            .templateName("depositbankreminder")
            .ownerId(Long.valueOf("9999"))
            .allowMultipleDefinitions(true)
            .build();
    Optional<DefinitionDetails> mockedDefinitionDetails =
        Optional.of(DefinitionDetails.builder()
            .templateDetails(templateDetails)
            .placeholderValue(TestHelper.readResourceAsString(PLACEHOLDER_VALUES_PATH))
            .build());
    Mockito.when(processDetailsRepoService.findByProcessIdWithoutDefinitionData(any()))
        .thenReturn(mockedDefinitionDetails);
  }

  @Test
  public void extractAppconnectParameterDetailsCustomWorkflow() {
    String handlerId = "hid";
    workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("createTask_bankdepositreminder")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .ownerId(Long.valueOf("9999"))
            .handlerId(handlerId)
            .build();
    PrecannedWorkflowParameterDetailsExtractor precannedAppconnectParameterDetailsExtractor =
        new PrecannedWorkflowParameterDetailsExtractor(processDetailsRepoService);
    Map<String, HandlerDetails.ParameterDetails> parameterDetails =
        precannedAppconnectParameterDetailsExtractor.getParameterDetails(workerActionRequest).get();
    Assert.assertEquals(
        Arrays.asList("blah blah link blah"),
        parameterDetails.get("Message").getFieldValue());
  }

  @Test
  public void extractAppconnectParameterDetailsPreCannedWorkflowFromExtension() {
    String handlerId = "hid";
    workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("createTask_bankdepositreminder")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(Map.of("entityType", "invoice"))
            .ownerId(Long.valueOf("9999"))
            .handlerId(handlerId)
            .extensionProperties(Map.of(WorkFlowVariables.PARAMETERS_KEY.getName(), parametersSchema))
            .build();
    PrecannedWorkflowParameterDetailsExtractor precannedAppconnectParameterDetailsExtractor =
        new PrecannedWorkflowParameterDetailsExtractor(processDetailsRepoService);
    Map<String, HandlerDetails.ParameterDetails> parameterDetails =
        precannedAppconnectParameterDetailsExtractor.getParameterDetails(workerActionRequest).get();
    Assert.assertEquals(
        Arrays.asList("blah blah link blah"),
        parameterDetails.get("Message").getFieldValue());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void extractAppconnectParameterDetailsPreCannedWorkflowNoDefinitionFound() {
    String handlerId = "hid";
    Mockito.when(processDetailsRepoService.findByProcessIdWithoutDefinitionData(any()))
        .thenReturn(Optional.empty());
    workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("createTask_bankdepositreminder")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(Map.of("entityType", "invoice"))
            .ownerId(Long.valueOf("9999"))
            .handlerId(handlerId)
            .extensionProperties(Map.of(WorkFlowVariables.PARAMETERS_KEY.getName(), parametersSchema))
            .build();
    PrecannedWorkflowParameterDetailsExtractor precannedAppconnectParameterDetailsExtractor =
        new PrecannedWorkflowParameterDetailsExtractor(processDetailsRepoService);
    precannedAppconnectParameterDetailsExtractor.getParameterDetails(workerActionRequest).get();
  }

  @Test
  public void extractAppconnectParameterDetailsPreCannedWorkflowFromExtensionN() {
    String handlerId = "hid";
    workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("createTask_bankdepositreminder")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(Map.of("entityType", "invoice"))
            .ownerId(Long.valueOf("9999"))
            .handlerId(handlerId)
            .extensionProperties(Map.of("notificationData", parametersSchema))
            .build();
    PrecannedWorkflowParameterDetailsExtractor precannedAppconnectParameterDetailsExtractor =
        new PrecannedWorkflowParameterDetailsExtractor(processDetailsRepoService);
    Optional<Map<String, HandlerDetails.ParameterDetails>> parameterDetails =
        precannedAppconnectParameterDetailsExtractor.getParameterDetails(workerActionRequest);
    Assert.assertEquals(
        Optional.empty(),
        parameterDetails);
  }
}
