package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.listener;

import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.processor.ESSMessageProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ThreadPoolExecutor;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.cloud.aws.messaging.listener.Acknowledgment;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(MockitoJUnitRunner.class)
public class ESSMessageListenerTest {

  @InjectMocks private ESSMessageListener essMessageListener;
  @Mock private ESSMessageProcessor essMessageProcessor;
  @Mock private ThreadPoolExecutor threadPoolQueue;
  @Mock private WASContextHandler wasContextHandler;
  public final String MESSAGE_PATH = "eventScheduler/message.json";
  public final String MESSAGE = TestHelper.readResourceAsString(MESSAGE_PATH);

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
    ReflectionTestUtils.setField(essMessageListener, "essMessageProcessor", essMessageProcessor);
    ReflectionTestUtils.setField(essMessageListener, "threadPoolQueue", threadPoolQueue);
    ReflectionTestUtils.setField(essMessageListener, "wasContextHandler", wasContextHandler);
  }

  @Test
  public void test_receivedMessage() {
    Acknowledgment acknowledgment = Mockito.mock(Acknowledgment.class);
    essMessageListener.processMessage(
        MESSAGE, acknowledgment, Map.of(WorkflowConstants.SQS_APPROXIMATE_RECEIVE_COUNT, "1"));
    Mockito.verify(threadPoolQueue, Mockito.times(1)).submit((Runnable) Mockito.any());
  }

  @Test
  public void test_processMessage() {
    Acknowledgment acknowledgment = Mockito.mock(Acknowledgment.class);
    Map<String, String> headersMap = new HashMap<>();
    headersMap.put(WorkflowConstants.SQS_APPROXIMATE_RECEIVE_COUNT, "1");
    essMessageListener.process(MESSAGE, acknowledgment, headersMap);
    Mockito.verify(essMessageProcessor, Mockito.times(1))
        .process(MESSAGE, acknowledgment, headersMap);
    Mockito.verify(wasContextHandler, Mockito.times(1)).clear();
  }
}
