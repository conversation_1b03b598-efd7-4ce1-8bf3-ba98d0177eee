package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.DOMAIN_EVENT_TRIGGER;

import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.DomainEventConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.DomainEventConsumerConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CustomWorkflowType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter.TransactionEntity;

@RunWith(MockitoJUnitRunner.class)
public class FilterTriggerUtilTest {

	@Mock
	private WASContextHandler contextHandler;

	@Mock
	private FeatureFlagManager featureFlagManager;

	@Mock
	private DomainEventConfig domainEventConfig;

	@InjectMocks
	private FilterTriggerUtil filterTriggerUtil;

	@Test
	public void testConfigNotEnabled() {
		TriggerProcessDetails triggerProcessDetails = getTriggerPayload(null,
				CustomWorkflowType.NOTIFICATION.getTemplateName(), null);

		TransactionEntity transactionEntity = TransactionEntityFactory
				.getInstanceOf(triggerProcessDetails.getTriggerMessage(), contextHandler);

		Mockito.when(domainEventConfig.getConsumer()).thenReturn(new DomainEventConsumerConfig());
		Assert.assertFalse(filterTriggerUtil.filterNotificationWorkflow(transactionEntity));

	}
	
	@Test
	public void testConfigNotPresent() {
		TriggerProcessDetails triggerProcessDetails = getTriggerPayload(null,
				CustomWorkflowType.NOTIFICATION.getTemplateName(), null);
		TransactionEntity transactionEntity = TransactionEntityFactory
				.getInstanceOf(triggerProcessDetails.getTriggerMessage(), contextHandler);
		Assert.assertFalse(filterTriggerUtil.filterNotificationWorkflow(transactionEntity));
	}

	@Test
	public void testConfigEnabled_Source_null() {
		TriggerProcessDetails triggerProcessDetails = getTriggerPayload(null,
				CustomWorkflowType.NOTIFICATION.getTemplateName(), null);

		TransactionEntity transactionEntity = TransactionEntityFactory
				.getInstanceOf(triggerProcessDetails.getTriggerMessage(), contextHandler);

		DomainEventConsumerConfig consumerConfig = new DomainEventConsumerConfig();
		consumerConfig.setProcessNotificationWorkflow(true);
		Mockito.when(domainEventConfig.getConsumer()).thenReturn(consumerConfig);
		Assert.assertFalse(filterTriggerUtil.filterNotificationWorkflow(transactionEntity));

	}

	@Test
	public void testConfigEnabled_NonNotificationWf() {
		TriggerProcessDetails triggerProcessDetails = getTriggerPayload("id",
				CustomWorkflowType.REMINDER.getTemplateName(), null);

		TransactionEntity transactionEntity = TransactionEntityFactory
				.getInstanceOf(triggerProcessDetails.getTriggerMessage(), contextHandler);

		DomainEventConsumerConfig consumerConfig = new DomainEventConsumerConfig();
		consumerConfig.setProcessNotificationWorkflow(true);
		Mockito.when(domainEventConfig.getConsumer()).thenReturn(consumerConfig);
		Assert.assertFalse(filterTriggerUtil.filterNotificationWorkflow(transactionEntity));

	}

  @Test
  public void testConfigEnabled_AppconnectTrigger_NotificationWf_EntityNotFullRolledOut() {
    TriggerProcessDetails triggerProcessDetails =
			getTriggerPayloadWithoutProviderId(CustomWorkflowType.NOTIFICATION.getTemplateName(), "DOMAIN_EVENT");

    TransactionEntity transactionEntity =
        TransactionEntityFactory.getInstanceOf(
            triggerProcessDetails.getTriggerMessage(), contextHandler);

    DomainEventConsumerConfig consumerConfig = new DomainEventConsumerConfig();
    consumerConfig.setProcessNotificationWorkflow(true);
    consumerConfig.setCompletedEntities(new HashSet<>(Collections.singleton("invoicecustomNotification")));

    Mockito.when(domainEventConfig.getConsumer()).thenReturn(consumerConfig);
    Assert.assertFalse(filterTriggerUtil.filterNotificationWorkflow(transactionEntity));
  }

	@Test
	public void testConfigEnabled_AppconnectTrigger_NotificationWf() {
		TriggerProcessDetails triggerProcessDetails = getTriggerPayload("id",
				CustomWorkflowType.NOTIFICATION.getTemplateName(), null);

		TransactionEntity transactionEntity = TransactionEntityFactory
				.getInstanceOf(triggerProcessDetails.getTriggerMessage(), contextHandler);

		DomainEventConsumerConfig consumerConfig = new DomainEventConsumerConfig();
		consumerConfig.setProcessNotificationWorkflow(true);

		Mockito.when(domainEventConfig.getConsumer()).thenReturn(consumerConfig);
		Mockito.when(contextHandler.get(Mockito.any())).thenReturn("123");
		Mockito.when(featureFlagManager.getBoolean(DOMAIN_EVENT_TRIGGER, false,
				(transactionEntity.getEntityType().getRecordType()
						+ transactionEntity.getEventHeaders().getWorkflow()).toLowerCase(),
				Long.valueOf("123"))).thenReturn(true);
		Assert.assertTrue(filterTriggerUtil.filterNotificationWorkflow(transactionEntity));

	}
	
	@Test
	public void testConfigEnabled_AppconnectTrigger_NotificationWf_FFDisabled() {
		TriggerProcessDetails triggerProcessDetails = getTriggerPayload("id",
				CustomWorkflowType.NOTIFICATION.getTemplateName(), null);

		TransactionEntity transactionEntity = TransactionEntityFactory
				.getInstanceOf(triggerProcessDetails.getTriggerMessage(), contextHandler);

		DomainEventConsumerConfig consumerConfig = new DomainEventConsumerConfig();
		consumerConfig.setProcessNotificationWorkflow(true);

		Mockito.when(domainEventConfig.getConsumer()).thenReturn(consumerConfig);
		Mockito.when(contextHandler.get(Mockito.any())).thenReturn("123");
		Mockito.when(featureFlagManager.getBoolean(DOMAIN_EVENT_TRIGGER, false,
				(transactionEntity.getEntityType().getRecordType()
						+ transactionEntity.getEventHeaders().getWorkflow()).toLowerCase(),
				Long.valueOf("123"))).thenReturn(false);
		Assert.assertFalse(filterTriggerUtil.filterNotificationWorkflow(transactionEntity));

	}
	
	@Test
	public void testConfigEnabled_DomainEvent_NotificationWf_FFDisabled() {
		TriggerProcessDetails triggerProcessDetails = getTriggerPayload(null,
				CustomWorkflowType.NOTIFICATION.getTemplateName(), null);

		TransactionEntity transactionEntity = TransactionEntityFactory
				.getInstanceOf(triggerProcessDetails.getTriggerMessage(), contextHandler);

		DomainEventConsumerConfig consumerConfig = new DomainEventConsumerConfig();
		consumerConfig.setProcessNotificationWorkflow(true);

		transactionEntity.getEventHeaders().setDefinitionKey("defKey");
		Mockito.when(domainEventConfig.getConsumer()).thenReturn(consumerConfig);
		Mockito.when(contextHandler.get(Mockito.any())).thenReturn("123");
		Mockito.when(featureFlagManager.getBoolean(DOMAIN_EVENT_TRIGGER, false,
				(transactionEntity.getEntityType().getRecordType()
						+ transactionEntity.getEventHeaders().getWorkflow()).toLowerCase(),
				Long.valueOf("123"))).thenReturn(false);
		Assert.assertTrue(filterTriggerUtil.filterNotificationWorkflow(transactionEntity));

	}
	
	@Test
	public void testConfigEnabled_DomainEvent_NotificationWf_FFEnabled() {
		TriggerProcessDetails triggerProcessDetails = getTriggerPayload(null,
				CustomWorkflowType.NOTIFICATION.getTemplateName(), null);

		TransactionEntity transactionEntity = TransactionEntityFactory
				.getInstanceOf(triggerProcessDetails.getTriggerMessage(), contextHandler);

		DomainEventConsumerConfig consumerConfig = new DomainEventConsumerConfig();
		consumerConfig.setProcessNotificationWorkflow(true);

		transactionEntity.getEventHeaders().setDefinitionKey("defKey");
		Mockito.when(domainEventConfig.getConsumer()).thenReturn(consumerConfig);
		Mockito.when(contextHandler.get(Mockito.any())).thenReturn("123");
		Mockito.when(featureFlagManager.getBoolean(DOMAIN_EVENT_TRIGGER, false,
				(transactionEntity.getEntityType().getRecordType()
						+ transactionEntity.getEventHeaders().getWorkflow()).toLowerCase(),
				Long.valueOf("123"))).thenReturn(true);
		Assert.assertFalse(filterTriggerUtil.filterNotificationWorkflow(transactionEntity));

	}
	
	@Test
	public void testConfigEnabled_DomainEvent_NotificationWfActionKey_FFEnabled() {
		TriggerProcessDetails triggerProcessDetails = getTriggerPayload(null,
				CustomWorkflowType.NOTIFICATION.getActionKey(), null);

		TransactionEntity transactionEntity = TransactionEntityFactory
				.getInstanceOf(triggerProcessDetails.getTriggerMessage(), contextHandler);

		DomainEventConsumerConfig consumerConfig = new DomainEventConsumerConfig();
		consumerConfig.setProcessNotificationWorkflow(true);

		transactionEntity.getEventHeaders().setDefinitionKey("defKey");
		Mockito.when(domainEventConfig.getConsumer()).thenReturn(consumerConfig);
		Mockito.when(contextHandler.get(Mockito.any())).thenReturn("123");
		Mockito.when(featureFlagManager.getBoolean(DOMAIN_EVENT_TRIGGER, false,
				(transactionEntity.getEntityType().getRecordType()
						+ CustomWorkflowType.NOTIFICATION.getTemplateName()).toLowerCase(),
				Long.valueOf("123"))).thenReturn(true);
		Assert.assertFalse(filterTriggerUtil.filterNotificationWorkflow(transactionEntity));

	}
	
	@SuppressWarnings("serial")
	private TriggerProcessDetails getTriggerPayload(String workflowId, String workflowName, String source) {

		Map<String, Object> trigger = new HashMap<>();
		Map<String, Object> eventHeaders = new HashMap<>();
		Map<String, Object> entityPayload = new HashMap<>();
		eventHeaders.put("workflow", workflowName);
		eventHeaders.put("entityType", RecordType.INVOICE);
		eventHeaders.put("entityChangeType", WorkflowConstants.NEW_CUSTOM_START);
		eventHeaders.put("entityId", "35");

		if (StringUtils.isNotBlank(workflowId)) {
			eventHeaders.put("providerWorkflowId", workflowId);
		}
		trigger.put("eventHeaders", eventHeaders);
		entityPayload.put("Invoice", new HashMap<String, String>() {
			{
				put("a", "b");
			}
		});
		trigger.put("entity", entityPayload);
		trigger.put(WorkflowConstants.SOURCE, source);
		return TriggerProcessDetails.builder().triggerMessage(trigger).build();
	}

	private TriggerProcessDetails getTriggerPayloadWithoutProviderId(String workflowName, String source) {

		Map<String, Object> trigger = new HashMap<>();
		Map<String, Object> eventHeaders = new HashMap<>();
		Map<String, Object> entityPayload = new HashMap<>();
		eventHeaders.put("workflow", workflowName);
		eventHeaders.put("entityType", RecordType.INVOICE);
		eventHeaders.put("entityChangeType", WorkflowConstants.NEW_CUSTOM_START);
		eventHeaders.put("entityId", "35");

		trigger.put("eventHeaders", eventHeaders);
		entityPayload.put("Invoice", new HashMap<String, String>() {
			{
				put("a", "b");
			}
		});
		trigger.put("entity", entityPayload);
		trigger.put(WorkflowConstants.SOURCE, source);
		return TriggerProcessDetails.builder().triggerMessage(trigger).build();
	}
}
