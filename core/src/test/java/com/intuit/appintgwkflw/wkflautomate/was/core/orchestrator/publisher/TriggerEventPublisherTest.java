package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.publisher;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.CUSTOM_REMINDER;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.ID_KEY;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.trigger.EntityChangeIdentifier;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.trigger.MetaData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.trigger.Trigger;
import com.intuit.appintgwkflw.wkflautomate.was.event.api.EventPublisherCapability;
import java.util.Map;
import java.util.UUID;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;
/** <AUTHOR> */
@RunWith(MockitoJUnitRunner.class)
public class TriggerEventPublisherTest {
  @InjectMocks private TriggerEventPublisher triggerEventPublisher;
  @Mock private EventPublisherCapability eventPublisherCapability;
  @Mock private WASContextHandler wasContextHandler;

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
    ReflectionTestUtils.setField(
        triggerEventPublisher, "eventPublisherCapability", eventPublisherCapability);
    ReflectionTestUtils.setField(triggerEventPublisher, "wasContextHandler", wasContextHandler);
    Mockito.when(wasContextHandler.get(WASContextEnums.INTUIT_TID))
        .thenReturn(UUID.randomUUID().toString());
    Mockito.when(wasContextHandler.get(WASContextEnums.OFFERING_ID))
        .thenReturn(UUID.randomUUID().toString());
    Mockito.when(wasContextHandler.get(WASContextEnums.ENTITY_ID))
        .thenReturn(UUID.randomUUID().toString());
    Mockito.when(wasContextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("123456");
  }

  @Test
  public void testPublish() {
    triggerEventPublisher.publishTriggerEvent(getTriggerPayload());
    verify(eventPublisherCapability, times(1)).publish(Mockito.any(), Mockito.any());
  }

  private Trigger getTriggerPayload() {
    MetaData metaData = MetaData.builder()
            .workflow(CUSTOM_REMINDER)
            .entityType(RecordType.INVOICE.getRecordType())
            .entityChangeIdentifier(new EntityChangeIdentifier(WorkflowConstants.NEW_CUSTOM_START))
            .entityId("123")
            .providerWorkflowId("123")
            .build();
    return new Trigger(
        metaData, null, Map.of(RecordType.INVOICE.getRecordType(), Map.of("Id", "123")));
  }
}
