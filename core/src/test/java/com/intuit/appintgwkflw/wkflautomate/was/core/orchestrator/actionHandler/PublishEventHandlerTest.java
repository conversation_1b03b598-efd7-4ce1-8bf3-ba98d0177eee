 package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

 import static org.mockito.ArgumentMatchers.any;

 import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.MDCContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
 import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
 import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowEventException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowNonRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.EventPublisherUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.OfferingConfig;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.TaskHandlerName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import com.intuit.appintgwkflw.wkflautomate.was.event.api.EventPublisherCapability;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import org.hibernate.JDBCException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
 import org.springframework.test.util.ReflectionTestUtils;

 public class PublishEventHandlerTest {

  private OfferingConfig offeringConfig;

  @Mock private ProcessDetailsRepository processDetailsRepository;
  @Mock private EventPublisherCapability eventPublisherCapability;
  @Mock private WASContextHandler contextHandler;
  @Mock private EventPublisherUtil eventPublisherUtil;
  @Mock private MetricLogger metricLogger;

  private PublishEventHandler publishEventHandler;

  private Optional<ProcessDetails> processDetails;
  private WorkerActionRequest workerActionRequest;
  private WorkerActionRequest workerActionRequestWithHandlerScope;

  public static Boolean isRetriableException(WorkflowEventException exception) {
    return exception.getWorkflowGeneralException() instanceof WorkflowRetriableException
        ? Boolean.TRUE : Boolean.FALSE;
  }

  public static Boolean isNonRetriableException(WorkflowEventException exception) {
    return exception.getWorkflowGeneralException() instanceof WorkflowNonRetriableException
        ? Boolean.TRUE : Boolean.FALSE;
  }

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);


    String processInstanceId = "iTd";

    workerActionRequest = WorkerActionRequest.builder()
        .activityId("aId")
        .ownerId(123L)
        .processDefinitionId("dId")
        .processInstanceId(processInstanceId)
        .handlerId("hId")
        .inputVariables(new HashMap<>())
        .build();

    workerActionRequestWithHandlerScope = WorkerActionRequest.builder()
        .activityId("aId")
        .ownerId(123L)
        .processDefinitionId("dId")
        .processInstanceId(processInstanceId)
        .handlerId("hId")
        .inputVariables(new HashMap<>())
        .handlerScope("test")
        .build();

    TemplateDetails templateDetails = TemplateDetails.builder()
        .templateName("templateName")
        .build();

    DefinitionDetails definitionDetails = DefinitionDetails.builder()
        .recordType(RecordType.INVOICE)
        .templateDetails(templateDetails)
        .build();

    processDetails = Optional.of(
        ProcessDetails.builder()
            .definitionDetails(definitionDetails)
            .processId(processInstanceId)
            .ownerId(123L)
            .recordId("rId")
            .build()
    );

    offeringConfig = new OfferingConfig();
    offeringConfig.setDefaultOffering("ttlive");

    contextHandler = new MDCContextHandler();
    contextHandler.addKey(WASContextEnums.INTUIT_TID, "tid");
    publishEventHandler =
        new PublishEventHandler(
            processDetailsRepository,
            eventPublisherCapability,
            contextHandler,
            eventPublisherUtil);
    ReflectionTestUtils.setField(publishEventHandler, "metricLogger", metricLogger);
  }

  @Test
  public void testPublishEventSuccess() {
	  String processInstanceId = "iTd";
	  Map<String, String> inputVariables = new HashMap<>(workerActionRequest.getInputVariables());
	  inputVariables.put(ActivityConstants.WORKFLOW_DEFAULT_TRANSACTION_VARIABLE, "txn1");

	  WorkerActionRequest workerActionRequest = WorkerActionRequest.builder()
		        .activityId("aId")
		        .ownerId(123L)
		        .processDefinitionId("dId")
		        .processInstanceId(processInstanceId)
		        .handlerId("hId")
		        .inputVariables(inputVariables)
		        .build();

    Mockito.when(processDetailsRepository.findByIdWithoutDefinitionData(workerActionRequest.getProcessInstanceId()))
        .thenReturn(processDetails);

    Map<String, Object> response = publishEventHandler.executeAction(workerActionRequest);
    // Response shouldn't be null
    Assert.assertNotNull(response);
    // Response will have one key
    Assert.assertEquals(1, response.size());
    // Response will have PENDING key
    Assert.assertTrue(response.containsKey(WorkFlowVariables.PENDING_EXTEND_LOCK.getName()));
    // Value of PENDING key should be True
    Assert.assertEquals(
        Boolean.TRUE,
        response.entrySet().stream()
            .filter(stringObjectEntry -> stringObjectEntry.getKey()
                .equalsIgnoreCase(WorkFlowVariables.PENDING_EXTEND_LOCK.getName()))
            .map(x -> x.getValue())
            .findFirst()
            .get()
    );
  }

  @Test
  public void testPublishEventWithHandlerScopeSuccess() {


 Mockito.when(processDetailsRepository.findByIdWithoutDefinitionData(workerActionRequestWithHandlerScope.getProcessInstanceId()))
        .thenReturn(processDetails);

    Map<String, Object> response =
 publishEventHandler.executeAction(workerActionRequestWithHandlerScope);
    // Response shouldn't be null
    Assert.assertNotNull(response);
    // Response will have one key
    Assert.assertEquals(1, response.size());
    // Response will have PENDING key
    Assert.assertTrue(response.containsKey(WorkFlowVariables.PENDING_EXTEND_LOCK.getName()));
    // Value of PENDING key should be True
    Assert.assertEquals(
        Boolean.TRUE,
        response.entrySet().stream()
            .filter(stringObjectEntry -> stringObjectEntry.getKey()
                .equalsIgnoreCase(WorkFlowVariables.PENDING_EXTEND_LOCK.getName()))
            .map(x -> x.getValue())
            .findFirst()
            .get()
    );
  }

  @Test
  public void testPublishEventName() {
    Assert.assertEquals(TaskHandlerName.WAS_PUBLISH_EVENT_HANDLER, publishEventHandler.getName());
  }

  @Test
  public void testHandlerIdNotPresent() {
    WorkerActionRequest workerActionRequest = WorkerActionRequest.builder().build();
    try {
      publishEventHandler.executeAction(workerActionRequest);
      Assert.fail();
    } catch (WorkflowEventException e) {
      Assert.assertTrue(isNonRetriableException(e));
    }
  }

  @Test
  public void testHandlerIdEmpty() {
    WorkerActionRequest workerActionRequest = WorkerActionRequest.builder().handlerId("").build();
    try {
      publishEventHandler.executeAction(workerActionRequest);
      Assert.fail();
    } catch (WorkflowEventException e) {
      Assert.assertTrue(isNonRetriableException(e));
    }
  }

  @Test
  public void testHandlerIdBlank() {
    WorkerActionRequest workerActionRequest = WorkerActionRequest.builder().handlerId("   ")
        .build();
    try {
      publishEventHandler.executeAction(workerActionRequest);
      Assert.fail();
    } catch (WorkflowEventException e) {
      Assert.assertTrue(isNonRetriableException(e));
    }
  }

  @Test(expected = WorkflowEventException.class)
  public void testNullTaskId() {
    WorkerActionRequest workerActionRequest = WorkerActionRequest.builder().handlerId("test")
        .build();
    publishEventHandler.executeAction(workerActionRequest);
  }

  @Test(expected = WorkflowEventException.class)
  public void testNullWorkerId() {
    WorkerActionRequest workerActionRequest = WorkerActionRequest.builder().handlerId("test")
        .taskId("abc")
        .build();
    publishEventHandler.executeAction(workerActionRequest);
  }

  @Test(expected = WorkflowEventException.class)
  public void validEntityId() {
    WorkerActionRequest workerActionRequest = WorkerActionRequest.builder().handlerId("test")
        .taskId("abc")
        .workerId("worker_1")
        .build();
    publishEventHandler.executeAction(workerActionRequest);
  }

  @Test
  public void testProcessDetailsNotPresent() {
    Mockito.when(processDetailsRepository.findByIdWithoutDefinitionData(workerActionRequest.getProcessInstanceId()))
        .thenReturn(Optional.empty());
    try {
      publishEventHandler.executeAction(workerActionRequest);
      Assert.fail();
    } catch (WorkflowEventException e) {
      Assert.assertTrue(isRetriableException(e));
    }
  }

  @Test
  public void testEventPublisherCapabilityFailure() {
    Mockito.when(processDetailsRepository.findByIdWithoutDefinitionData(workerActionRequest.getProcessInstanceId()))
        .thenReturn(processDetails);
    Mockito.doThrow(new WorkflowEventException(
        new WorkflowGeneralException(WorkflowError.INVALID_EVENT_CONFIG_ERROR)))
        .when(eventPublisherCapability).publish(Mockito.any(), Mockito.any());
    try {
      publishEventHandler.executeAction(workerActionRequest);
      Assert.fail();
    } catch (WorkflowEventException e) {
      Assert.assertEquals(WorkflowError.INVALID_EVENT_CONFIG_ERROR.name(),
          e.getWorkflowGeneralException().getError().getMessage());
    }
  }

  @Test
  public void testOfferingConfigNull() {
    offeringConfig = null;
    Mockito.when(processDetailsRepository.findByIdWithoutDefinitionData(workerActionRequest.getProcessInstanceId()))
        .thenReturn(processDetails);
    try {
      publishEventHandler.executeAction(workerActionRequest);
    } catch (Exception e) {
      Assert.fail();
    }
  }

  @Test
  public void testOfferingIdNull() {
    offeringConfig.setDefaultOffering(null);
    Mockito.when(processDetailsRepository.findByIdWithoutDefinitionData(workerActionRequest.getProcessInstanceId()))
        .thenReturn(processDetails);
    try {
      publishEventHandler.executeAction(workerActionRequest);
    } catch (Exception e) {
      Assert.fail();
    }
  }

  @Test
  public void testDBConnectionError() {
    Mockito.doThrow(new JDBCException("Exception", new SQLException()))
        .when(processDetailsRepository).findById(workerActionRequest.getProcessInstanceId());
    try {
      publishEventHandler.executeAction(workerActionRequest);
      Assert.fail();
    } catch (WorkflowEventException e) {
      Assert.assertTrue(isRetriableException(e));
    }
  }

  @Test
  public void testLogErrorMetric() {
    WorkerActionRequest workerActionRequest = WorkerActionRequest.builder().build();
    try {
      publishEventHandler.execute(workerActionRequest);
      Assert.fail("Exception should be thrown");
    } catch (WorkflowNonRetriableException e) {
      Assert.assertEquals(e.getWorkflowError(), WorkflowError.MISSING_HANDLER_ID);
      Mockito.verify(metricLogger, Mockito.times(0)).logErrorMetric(any(), any(), any());
    }
  }
}
