package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent;

import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.impl.CustomReminderFetchTransactionsProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.resilience.circuitbreaker.handler.CircuitBreakerActionHandlers;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DefinitionEventType;
import java.util.Optional;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;

import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

/** <AUTHOR> */
@RunWith(MockitoJUnitRunner.class)
public class DefinitionEventProcessorsTest {
  @Mock private CustomReminderFetchTransactionsProcessor customReminderFetchTransactionsProcessor;

  @Test
  public void testGetWithAndWithoutHandlers() {
    Assert.assertEquals(Optional.empty(), CircuitBreakerActionHandlers.getHandler(null));

    DefinitionEventProcessors.addProcessor(
        DefinitionEventType.CUSTOM_REMINDER_FETCH_TRANSACTIONS,
        customReminderFetchTransactionsProcessor);
    Assert.assertNotEquals(
        Optional.empty(),
        DefinitionEventProcessors.getProcessor(
            DefinitionEventType.CUSTOM_REMINDER_FETCH_TRANSACTIONS));
  }
}
