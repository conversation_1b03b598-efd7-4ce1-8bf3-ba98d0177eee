package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.executor.actions;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.MDCContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.SchedulingSvcConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.AuthDetailsService;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.DefinitionServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.RunTimeService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.SchedulingService;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.AuthDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.SchedulerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.SchedulerAction;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.TemplateCategory;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.TriggerStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowGenericResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowTriggerResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.listner.EventScheduleMessageData;
import com.intuit.foundation.workflow.scheduling.Execution;
import com.intuit.v4.Authorization;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.times;

/**
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class SchedulingScheduledActionsProcessorTest {
    @InjectMocks
    private SchedulingScheduledActionsProcessor schedulingScheduledActionsProcessor;

    @Mock
    private DefinitionServiceHelper definitionServiceHelper;

    @Mock private SchedulingService schedulingService;

    @Mock private SchedulingSvcConfig schedulingSvcConfig;

    @Mock private RunTimeService runTimeService;

    @Mock private AuthDetailsService authDetailsService;

    @Mock private MetricLogger metricLogger;
    private static final long ownerId = *********;

    private static final AuthDetails authDetails = new AuthDetails();

    private static final String CUSTOM_SCHEDULEDACTIONS_WORKFLOW_DEF =
            TestHelper.readResourceAsString("bpmn/customScheduledActionsESSCompatible.bpmn");

    @Before
    public void init() {
        MockitoAnnotations.initMocks(this);
        ReflectionTestUtils.setField(
                schedulingScheduledActionsProcessor, "wasContextHandler", new MDCContextHandler());
        ReflectionTestUtils.setField(schedulingScheduledActionsProcessor, "definitionServiceHelper", definitionServiceHelper);
        ReflectionTestUtils.setField(schedulingScheduledActionsProcessor, "runTimeService", runTimeService);
        ReflectionTestUtils.setField(schedulingScheduledActionsProcessor, "authDetailsService", authDetailsService);
        ReflectionTestUtils.setField(schedulingScheduledActionsProcessor, "schedulingSvcConfig", schedulingSvcConfig);
        ReflectionTestUtils.setField(schedulingScheduledActionsProcessor, "schedulingService", schedulingService);
        Mockito.when(definitionServiceHelper.findByParentId(anyString()))
                .thenReturn(Optional.of(getDMNDefinitionDetails()));
        Mockito.when(authDetailsService.getAuthDetailsFromRealmId(any())).thenReturn(authDetails);
        Mockito.when(runTimeService.processTriggerMessageV2(any()))
                .thenReturn(
                        WorkflowGenericResponse.builder()
                                .response(
                                        WorkflowTriggerResponse.builder()
                                                .processId("p")
                                                .status(TriggerStatus.PROCESS_STARTED)
                                                .build())
                                .build());
    }

    @Test
    public void testProcess_NoAuth() {
        Mockito.when(authDetailsService.renewOfflineTicketAndUpdateDB(authDetails)).thenReturn("abcde");
        schedulingScheduledActionsProcessor.process(
                getDefinitionDetails(CUSTOM_SCHEDULEDACTIONS_WORKFLOW_DEF), new Execution());
        Mockito.verify(definitionServiceHelper, times(1)).findByParentId(Mockito.any());
        Mockito.verify(runTimeService, times(1)).processTriggerMessageV2(Mockito.any());

        WASContextHandler wasContextHandler1 =
                (WASContextHandler)
                        ReflectionTestUtils.getField(schedulingScheduledActionsProcessor, "wasContextHandler");
        Authorization auth = new Authorization();
        auth.set(wasContextHandler1.get(WASContextEnums.AUTHORIZATION_HEADER));
        Assert.assertNull(auth.getAuthId());
        Assert.assertNull(auth.getRealm());
    }

    @Test
    public void testProcess_ProcessingDisabled() {
        DefinitionDetails definitionDetails = getDefinitionDetails(CUSTOM_SCHEDULEDACTIONS_WORKFLOW_DEF);
        Mockito.when(schedulingService.isMigrated(Mockito.any())).thenReturn(true);
        Mockito.when(schedulingSvcConfig.isNumaflowProcessingEnabled()).thenReturn(false);
        Map<String, String> result = schedulingScheduledActionsProcessor.process(
                definitionDetails,
                new Execution());
        Assert.assertNotNull(result);
        Assert.assertEquals(result, Collections.emptyMap());
    }

    @Test
    public void testProcess_CorrectAuth() {
        Mockito.when(authDetailsService.renewOfflineTicketAndUpdateDB(authDetails))
                .thenReturn(
                        "Intuit_IAM_Authentication intuit_token_type=IAM-Ticket,intuit_appid=Intuit.appintgwkflw.wkflautomate.wfas,intuit_app_secret=xyz,intuit_token=eyJhbGciOiJSUz,intuit_userid=1234,intuit_accountid=*********,intuit_realmid=*********");
        schedulingScheduledActionsProcessor.process(
                getDefinitionDetails(CUSTOM_SCHEDULEDACTIONS_WORKFLOW_DEF), new Execution());
        Mockito.verify(definitionServiceHelper, times(1)).findByParentId(Mockito.any());
        Mockito.verify(runTimeService, times(1)).processTriggerMessageV2(Mockito.any());

        WASContextHandler wasContextHandler1 =
                (WASContextHandler)
                        ReflectionTestUtils.getField(schedulingScheduledActionsProcessor, "wasContextHandler");
        Authorization auth = new Authorization();
        auth.set(wasContextHandler1.get(WASContextEnums.AUTHORIZATION_HEADER));
        Assert.assertNotNull(auth.getAuthId());
        Assert.assertEquals(ownerId, Long.parseLong(auth.getRealm()));
    }

    @Test(expected = WorkflowGeneralException.class)
    public void testProcess_Error() {
        Mockito.when(authDetailsService.renewOfflineTicketAndUpdateDB(authDetails)).thenReturn("abcde");
        Mockito.when(runTimeService.processTriggerMessageV2(any()))
                .thenThrow(WorkflowGeneralException.class);
        schedulingScheduledActionsProcessor.process(
                getDefinitionDetails(CUSTOM_SCHEDULEDACTIONS_WORKFLOW_DEF),
                new Execution());
        Mockito.verify(metricLogger, times(1)).logErrorMetric(any(), any(), any());
    }

    @Test
    public void testProcess_NoTrigger_NoDMN() {
        Mockito.when(definitionServiceHelper.findByParentId(any())).thenReturn(Optional.empty());
        schedulingScheduledActionsProcessor.process(
                getDefinitionDetails(CUSTOM_SCHEDULEDACTIONS_WORKFLOW_DEF),
                new Execution());
        Mockito.verify(runTimeService, times(0)).processTriggerMessageV2(Mockito.any());
    }

    private SchedulerDetails getSchedulerDetails(String data) {
        return SchedulerDetails.builder()
                .definitionDetails(getDefinitionDetails(data))
                .schedulerId("1234")
                .schedulerAction(SchedulerAction.CUSTOM_SCHEDULEDACTIONS_CUSTOM_START)
                .ownerId(ownerId)
                .build();
    }

    private EventScheduleMessageData getMessageData() {
        EventScheduleMessageData eventScheduleMessageData = new EventScheduleMessageData();
        eventScheduleMessageData.setMessageId("mes123");
        eventScheduleMessageData.setScheduleId("1234");
        return eventScheduleMessageData;
    }

    private DefinitionDetails getDefinitionDetails(String data) {
        DefinitionDetails definitionDetails = new DefinitionDetails();
        definitionDetails.setDefinitionId("def123");
        definitionDetails.setOwnerId(Long.valueOf(ownerId));
        definitionDetails.setRecordType(RecordType.STATEMENT);
        definitionDetails.setDefinitionData(data.getBytes());
        TemplateDetails templateDetails = new TemplateDetails();
        templateDetails.setOfferingId("off123");
        templateDetails.setTemplateCategory(TemplateCategory.CUSTOM.name());
        templateDetails.setTemplateName("customScheduledActions");
        definitionDetails.setTemplateDetails(templateDetails);
        definitionDetails.setWorkflowId("workflow123");
        definitionDetails.setModifiedByUserId(Long.valueOf("1234567"));
        return definitionDetails;
    }

    private List<DefinitionDetails> getDMNDefinitionDetails() {
        DefinitionDetails definitionDetails = new DefinitionDetails();
        definitionDetails.setDefinitionId("dmndef123");
        definitionDetails.setOwnerId(Long.valueOf(ownerId));
        definitionDetails.setRecordType(RecordType.STATEMENT);
        TemplateDetails templateDetails = new TemplateDetails();
        templateDetails.setOfferingId("off123");
        definitionDetails.setTemplateDetails(templateDetails);
        definitionDetails.setPlaceholderValue(
                "{\"rule_line_variables\": [{\"parameterName\": \"Customer\", \"parameterType\": \"LIST\", \"$sdk_validated\": true, \"conditionalExpression\": \"CONTAINS 2\"}, {\"parameterName\": \"StatementType\", \"parameterType\": \"LIST\", \"$sdk_validated\": true, \"conditionalExpression\": \"CONTAINS BALANCE_FORWARD\"}]}");
        return List.of(definitionDetails);
    }
}
