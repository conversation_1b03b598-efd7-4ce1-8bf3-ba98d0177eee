package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.TaskHandlerName;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

/** <AUTHOR> */
public class WorkflowTaskHandlersTest {

  @Mock private AppConnectWorkflowTaskHandler appConnectActionHandler;

  @SuppressWarnings("deprecation")
  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
    Mockito.when(appConnectActionHandler.getName())
        .thenReturn(TaskHandlerName.APP_CONNECT_WORKFLOW_TASK_ACTION_HANDLER);
    WorkflowTaskHandlers.addHandler(appConnectActionHandler.getName(), appConnectActionHandler);
  }

  @Test
  public void getTestNUll() {
    WorkflowTaskHandler handler = WorkflowTaskHandlers.getHandler(null);
    Assert.assertNull(handler);
  }

  @Test
  public void getTestAction() {
    WorkflowTaskHandler handler = WorkflowTaskHandlers.getHandler(TaskHandlerName.APP_CONNECT_WORKFLOW_TASK_ACTION_HANDLER);
    Assert.assertNotNull(handler);
  }
  @Test
  public void containsFalse() {
    Assert.assertFalse(WorkflowTaskHandlers.contains(null));
  }
  @Test
  public void containsTrue() {
    Assert.assertTrue(WorkflowTaskHandlers.contains(TaskHandlerName.APP_CONNECT_WORKFLOW_TASK_ACTION_HANDLER));
  }
}
