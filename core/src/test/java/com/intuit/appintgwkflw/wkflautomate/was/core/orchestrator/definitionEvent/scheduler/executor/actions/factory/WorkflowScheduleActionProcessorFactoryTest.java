package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.executor.actions.factory;

import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.executor.actions.CustomReminderScheduleActionProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.executor.actions.factory.WorkflowScheduleActionProcessorFactory;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.WorkflowNameEnum;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class WorkflowScheduleActionProcessorFactoryTest {

  @Mock private CustomReminderScheduleActionProcessor customReminderScheduleProcessor;

  @Test
  public void testGetWithAndWithoutProcessors() {
    Assert.assertNull(WorkflowScheduleActionProcessorFactory.getProcessor(WorkflowNameEnum.CUSTOM_REMINDER));
    WorkflowScheduleActionProcessorFactory.addProcessor(
        WorkflowNameEnum.CUSTOM_REMINDER, customReminderScheduleProcessor);
    Assert.assertNotNull(WorkflowScheduleActionProcessorFactory.getProcessor(WorkflowNameEnum.CUSTOM_REMINDER));
  }
}
