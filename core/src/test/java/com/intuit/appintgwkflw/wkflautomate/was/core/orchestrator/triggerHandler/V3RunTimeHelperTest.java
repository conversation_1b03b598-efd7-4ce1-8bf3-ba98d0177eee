package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ApprovalTaskConstants.APPROVER_ID;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.enums.SchedulerAction.CUSTOM_REMINDER_CUSTOM_RECUR;
import static java.util.Collections.singletonList;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyObject;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.IXPManager;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WASContext;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders.TemplateBuilderTestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.impl.ProcessDomainEventHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.capability.CustomWorkflowQueryCapability;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.capability.DefaultTemplateQueryCapability;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.capability.TemplateQueryCapabilityFactory;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.AuthHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.TemplateDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DefinitionType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ModelType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ProcessStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.TemplateCategory;
import com.intuit.appintgwkflw.wkflautomate.was.entity.request.DomainEntityRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.ResponseStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.TriggerStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowGenericResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowTriggerResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowTriggersResponse;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter.TransactionEntity;
import com.intuit.foundation.workflow.workflowautomation.Process;
import com.intuit.foundation.workflow.workflowautomation.types.WorkflowExternalEntity;
import com.intuit.v4.Authorization;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import lombok.SneakyThrows;
import org.apache.commons.lang.StringUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class V3RunTimeHelperTest {

  /** */
  private static final String UPDATED_EVENT = "updated";
  private static final String OBJECT_VARIABLE_TYPE_PAYLOAD = "objectVariableTypePayload";
  private static final String REALM_ID = "12345";
  private static final String WORKFLOW_ID = UUID.randomUUID().toString();
  private static final String TEMPLATE_NAME = "invoiceapproval";
  private final TemplateDetails bpmnTemplateDetail =
      TriggerHandlerTestData.getBPMNTemplateDetails();
  @Mock private DefinitionDetailsRepository definitionDetailsRepository;
  @Mock private ProcessDomainEventHandler processDomainEventHandler;

  @Mock private CustomWorkflowQueryCapability customWorkflowQueryCapability;

  @Mock
  private CustomWorkflowConfig customWorkflowConfig;

  private CustomWorkflowConfig customWorkflowConfig1;

  @Mock private DefaultTemplateQueryCapability defaultTemplateQueryCapability;

  @Mock private TemplateQueryCapabilityFactory templateQueryCapabilityFactory;
  @InjectMocks private V3RunTimeHelper v3RunTimeHelper;
  private TransactionEntity transactionEntityUpdated;

  @Mock private TemplateDetailsRepository templateDetailsRepository;
  @Mock private AuthHelper authHelper;
  @Mock private ProcessDetailsRepository processDetailsRepository;
  @Mock private FeatureFlagManager featureFlagManager;
  @Mock private WASContextHandler contextHandler;

  @Mock private IXPManager ixpManager;

  private static final String CREATED = "created";
  private static final String PROCESS_ID = "pId";
  private static final String ENTITY_ID = "entityId";
  public static String PLACEHOLDER_VALUES_PATH = "placeholder/placeholder_value.json";

  @Before
  @SneakyThrows
  public void prepareMockData() {

    customWorkflowConfig1= TemplateBuilderTestHelper.getConfig();
    transactionEntityUpdated =
    	TransactionEntityFactory.getInstanceOf(
    		TriggerHandlerTestData.prepareV3TriggerMessage(UPDATED_EVENT).getTriggerMessage(),
    		contextHandler);
    Authorization authorization = new Authorization();
    authorization.putRealm(Long.toString(1234l));
    WASContext.setAuthContext(authorization);
  }

  @Test
  public void testWorkflowGenericResponseBuilder() {

    final WorkflowGenericResponse.WorkflowGenericResponseBuilder expected =
        WorkflowGenericResponse.builder().status(ResponseStatus.FAILURE).response(null);
    final WorkflowGenericResponse.WorkflowGenericResponseBuilder actual =
        v3RunTimeHelper.getDefaultResponseBuilder(null);
    Assert.assertEquals(expected.toString(), actual.toString());
  }

  @Test
  public void testSaveProcessDetailsInstance() {

    try {
      ProcessDetails processDetails =
          ProcessDetails.builder()
              .processId("pId")
              .recordId("123")
              .ownerId(12345L)
              .processStatus(ProcessStatus.ACTIVE)
              .build();
      Process process = new Process();
      process.setId("pId");
      WorkflowExternalEntity workflowExternalEntity = new WorkflowExternalEntity();
      workflowExternalEntity.setEntityId("123");
      workflowExternalEntity.setEntityType("recordType");
      process.setTriggerEntity(workflowExternalEntity);
      process.setOwnerId("12345");
      Mockito.when(processDetailsRepository.save(any())).thenReturn(processDetails);
      ProcessDetails response =
          v3RunTimeHelper.saveProcessDetailsInstance(
              "123",
              12345L,
              "pId",
              ProcessStatus.ACTIVE,
              DefinitionDetails.builder().build(),
              null,
              Collections.emptyMap()
          );
      assertNotNull(response);
    } catch (final Exception e) {
      Assert.fail();
    }
  }

  @Test
  public void extractVariablesFromEntityStringPresent() {

    final Map<String, Object> payload = getPayload();
    final Map<String, Object> response =
        v3RunTimeHelper.extractVariablesFromEntity(payload, "DepartmentRef_value", "String", true);
    Assert.assertEquals("1", response.get("value"));
  }

  @Test
  public void extractVariablesFromEntityStringNotPresent() {

    final Map<String, Object> payload = getPayload();
    payload.put("DepartmentRef", null);
    final Map<String, Object> response =
        v3RunTimeHelper.extractVariablesFromEntity(payload, "DepartmentRef_value", "String", true);
    Assert.assertEquals("", response.get("value"));
  }

  @Test
  public void extractVariablesFromEntityStringNotPresent_testDefaultValue() {

    final Map<String, Object> payload = getPayload();
    final Map<String, Object> response =
        v3RunTimeHelper.extractVariablesFromEntity(payload, "Term", "String", true);
    Assert.assertEquals("", response.get("value"));
  }

  @Test
  public void extractVariablesFromEntityStringNotPresent_testDefaultValueTypestring() {

    final Map<String, Object> payload = getPayload();
    final Map<String, Object> response =
        v3RunTimeHelper.extractVariablesFromEntity(payload, "Term", "string", true);
    Assert.assertEquals("", response.get("value"));
  }

  @Test
  public void extractVariablesFromEntityStringNull_testDefaultValue() {

    final Map<String, Object> payload = getPayload();
    payload.put("Term", null);
    final Map<String, Object> response =
        v3RunTimeHelper.extractVariablesFromEntity(payload, "Term", "string", true);
    Assert.assertEquals("", response.get("value"));
  }

  @Test
  public void extractVariablesFromEntityObjectArrayListPresent() {

    final Map<String, Object> payload = getPayloadForBulk();
    final Map<String, Object> response =
        v3RunTimeHelper.extractVariablesFromEntity(payload, "processId", "Object", true);
    Assert.assertEquals("[\"1\",\"2\"]", response.get("value"));
  }

  @Test
  public void extractVariablesFromEntityObjectArrayListPresent_overrideFalse() {

    final Map<String, Object> payload = getPayloadForBulk();
    final Map<String, Object> response =
        v3RunTimeHelper.extractVariablesFromEntity(payload, "processId", "Object", false);
    Assert.assertEquals("[\"1\",\"2\"]", response.get("value"));
  }

  @Test
  public void extractVariablesFromEntityObjectMapPresent_overrideFalse() {

    final Map<String, Object> payload = getPayloadForBulk();
    final Map<String, Object> response =
        v3RunTimeHelper.extractVariablesFromEntity(payload, "myMapVar", "Object", false);
    Assert.assertEquals("{\"address\":\"address1\",\"name\":\"process1\"}", response.get("value"));
  }

  @Test(expected = WorkflowGeneralException.class)
  public void extractVariablesFromEntityObjectNonSerializablePresent() {

    final Map<String, Object> payload = getPayloadForBulk();
    payload.put("processId", new Object());
    v3RunTimeHelper.extractVariablesFromEntity(payload, "processId", "Object", true);
  }

  @Test
  public void extractVariablesFromEntityObjectNonJavaUtilsPresent() {
    final Map<String, Object> payload = getPayloadForBulk();
    char[] resReturn = new char[10];
    payload.put("processId", resReturn);
    v3RunTimeHelper.extractVariablesFromEntity(payload, "processId", "Object", true);
  }

  @Test
  public void extractVariablesFromEntityObjectNotPresent() {

    final Map<String, Object> payload = getPayload();
    payload.put("processId", null);
    final Map<String, Object> response =
        v3RunTimeHelper.extractVariablesFromEntity(payload, "processId", "Object", true);
    Assert.assertEquals(0, response.size());
    Assert.assertNull(response.get("value"));
  }

  @Test
  public void extractVariablesFromEntityStringValueNotPresent() {

    final Map<String, Object> payload = getPayload();
    payload.put("DepartmentRef", Collections.emptyMap());
    final Map<String, Object> response =
        v3RunTimeHelper.extractVariablesFromEntity(payload, "DepartmentRef_value", "String", true);
    Assert.assertEquals(1, response.size());
    Assert.assertEquals("", response.get("value"));
  }

  @Test
  public void extractVariablesFromEntityObjStringPresent() {

    final Map<String, Object> payload = getPayload();
    final Map<String, Object> response =
        v3RunTimeHelper.extractVariablesFromEntity(
            payload, "TxnTaxDetail_TxnTaxCodeRef_value", "String", true);
    Assert.assertEquals("4", response.get("value"));
  }

  @Test
  public void extractVariablesFromEntityObjStringNotPresent() {

    final Map<String, Object> payload = getPayload();
    payload.put("TxnTaxDetail", null);
    final Map<String, Object> response =
        v3RunTimeHelper.extractVariablesFromEntity(
            payload, "TxnTaxDetail_TxnTaxCodeRef_value", "String", true);
    Assert.assertEquals("", response.get("value"));
  }

  @Test
  public void extractVariablesFromEntityObjLevel2StringNotPresent() {

    final Map<String, Object> payload = getPayload();
    final Map<String, Object> level2Payload = new HashMap<>();
    level2Payload.put("TxnTaxCodeRef", null);
    payload.put("TxnTaxDetail", level2Payload);
    final Map<String, Object> response =
        v3RunTimeHelper.extractVariablesFromEntity(
            payload, "TxnTaxDetail_TxnTaxCodeRef_value", "String", true);
    Assert.assertEquals("", response.get("value"));
  }

  @Test
  public void extractVariablesFromEntityObjStringValueNotPresent() {

    final Map<String, Object> payload = getPayload();
    payload.put("TxnTaxDetail", Collections.emptyMap());
    final Map<String, Object> response =
        v3RunTimeHelper.extractVariablesFromEntity(
            payload, "TxnTaxDetail_TxnTaxCodeRef_value", "String", true);
    Assert.assertEquals("", response.get("value"));
  }

  @Test
  public void extractVariablesFromEntityObjLevel2StringValueNotPresent() {

    final Map<String, Object> payload = getPayload();
    final Map<String, Object> level2Payload = new HashMap<>();
    level2Payload.put("TxnTaxCodeRef", Collections.emptyMap());
    payload.put("TxnTaxDetail", level2Payload);
    final Map<String, Object> response =
        v3RunTimeHelper.extractVariablesFromEntity(
            payload, "TxnTaxDetail_TxnTaxCodeRef_value", "String", true);
    Assert.assertEquals("", response.get("value"));
  }

  @Test(expected = ClassCastException.class)
  public void extractVariablesFromEntityWhereInputLevelBeyondEntity() {

    v3RunTimeHelper.extractVariablesFromEntity(
        getPayload(), "TxnTaxDetail_TxnTaxCodeRef_value_extraAttr", "String", true);
  }

  @Test
  public void extractVariablesFromEntityWhereInputLevelBeyondLimit5() {

    final Map<String, Object> payload = getPayload();
    Map<String, Object> response = null;
    response =
        v3RunTimeHelper.extractVariablesFromEntity(
            payload,
            "testlevel1_testlevel2_testlevel3_testlevel4_testlevel5_testlevel6",
            "String",
            true);
    Assert.assertEquals("", response.get("value"));
  }

  @Test
  public void extractVariablesFromEntityDoublePresent() {

    final Map<String, Object> payload = getPayload();
    final Map<String, Object> response =
        v3RunTimeHelper.extractVariablesFromEntity(payload, "TotalAmt", "Double", true);
    Assert.assertEquals(1, response.size());
    Assert.assertEquals(128, response.get("value"));
  }

  @Test
  public void extractVariablesFromEntityDoubleValueNotPresent() {

    final Map<String, Object> payload = getPayload();
    payload.put("TotalAmt", null);
    final Map<String, Object> response =
        v3RunTimeHelper.extractVariablesFromEntity(payload, "TotalAmt", "Double", true);
    Assert.assertNull(response.get("value"));
  }

  @Test
  public void extractVariablesFromEntityOverideFalse() {

    final Map<String, Object> payload = getPayload();
    payload.put("name", null);
    final Map<String, Object> response =
        v3RunTimeHelper.extractVariablesFromEntity(payload, "name", "String", false);
    Assert.assertEquals(0, response.size());
  }

  @Test
  public void extractVariablesFromEntityObjStringValueNotPresentOverideFalse() {

    final Map<String, Object> payload = getPayload();
    payload.put("TxnTaxDetail", Collections.emptyMap());
    final Map<String, Object> response =
        v3RunTimeHelper.extractVariablesFromEntity(
            payload, "TxnTaxDetail_TxnTaxCodeRef_value", "String", false);
    Assert.assertEquals(0, response.size());
  }

  @Test
  public void extractVariablesFromEntityObjStringPresentOverideFalse() {

    final Map<String, Object> payload = getPayload();
    final Map<String, Object> response =
        v3RunTimeHelper.extractVariablesFromEntity(
            payload, "TxnTaxDetail_TxnTaxCodeRef_value", "String", false);
    Assert.assertEquals(1, response.size());
    Assert.assertEquals("4", response.get("value"));
  }

  @Test
  public void extractVariablesFromEntityFromAuth() {
    Mockito.when(authHelper.getAuthValueForKey(WorkflowConstants.INTUIT_USERID)).thenReturn("1234");
    Mockito.when(authHelper.canExtractVariablesFromAuth(WorkflowConstants.INTUIT_USERID))
        .thenReturn(true);
    Mockito.when(authHelper.getAuthValueForKey(WorkflowConstants.INTUIT_REALMID))
        .thenReturn("3456");
    Mockito.when(authHelper.canExtractVariablesFromAuth(WorkflowConstants.INTUIT_REALMID))
        .thenReturn(true);
    final Map<String, Object> payload = getPayload();
    Map<String, Object> response =
        v3RunTimeHelper.extractVariablesFromEntity(payload, "intuit_userid", "String", false);
    Assert.assertEquals(1, response.size());
    Assert.assertEquals("1234", response.get("value"));
    response =
        v3RunTimeHelper.extractVariablesFromEntity(payload, "intuit_realmid", "String", false);
    Assert.assertEquals(1, response.size());
    Assert.assertEquals("3456", response.get("value"));
  }

  @Test
  public void whenGetTriggerResponse_thenGetSuccess() {

    final WorkflowGenericResponse response =
        v3RunTimeHelper.getTriggerResponse(ResponseStatus.SUCCESS, TriggerStatus.NO_ACTION, null);

    assertEquals(ResponseStatus.SUCCESS, response.getStatus());
  }

  @Test
  public void getTriggerResponseV2Payload() {

    final WorkflowGenericResponse response =
        v3RunTimeHelper.getTriggerResponse(
            ResponseStatus.SUCCESS, TriggerStatus.NO_ACTION, "pId", "dId", "dName");

    assertEquals(ResponseStatus.SUCCESS, response.getStatus());
    assertEquals(1, ((WorkflowTriggersResponse) response.getResponse()).getTriggers().size());
  }

  @Test
  public void getWorkflowTriggerResponse() {

    final WorkflowTriggerResponse response =
        v3RunTimeHelper.getWorkflowTriggerResponse(
            TriggerStatus.NO_ACTION, "pId", "dId", "dName");

    assertEquals(TriggerStatus.NO_ACTION, response.getStatus());
    assertEquals("pId", response.getProcessId());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testgetEnabledSystemDefinitionNoActiveDefn() {

    TransactionEntity transactionEntity =
		TransactionEntityFactory.getInstanceOf(
	    		TriggerHandlerTestData.prepareV3TriggerMessage(UPDATED_EVENT).getTriggerMessage(),
	    		contextHandler);;
    TemplateDetails templateDetails = TriggerHandlerTestData.getBPMNTemplateDetails();

    v3RunTimeHelper.getEnabledSystemDefinition(transactionEntity, singletonList(templateDetails));
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testgetEnabledSystemDefinitionNoTemplates() {

    TransactionEntity transactionEntity =
		TransactionEntityFactory.getInstanceOf(
	    		TriggerHandlerTestData.prepareV3TriggerMessage(UPDATED_EVENT).getTriggerMessage(),
	    		contextHandler);;

    v3RunTimeHelper.getEnabledSystemDefinition(transactionEntity, null);
  }

  @Test
  public void testGetEnabledSystemDefinitionForOnDemandApproval() {

    TransactionEntity transactionEntity =
        TransactionEntityFactory.getInstanceOf(
            TriggerHandlerTestData.prepareV3TriggerMessage(UPDATED_EVENT).getTriggerMessage(),
            contextHandler);
    transactionEntity.getV3EntityPayload().put(WorkflowConstants.ON_DEMAND_APPROVAL, true);
    DefinitionDetails definitionDetails =
        TriggerHandlerTestData.getDefinitionDetails(bpmnTemplateDetail);

    Mockito.when(
            definitionDetailsRepository.findEnabledSystemDefinitionForOnDemandApproval(
                eq(ModelType.BPMN), anyList()))
        .thenReturn(Optional.of(singletonList(definitionDetails)));
    List<DefinitionDetails> defnDetails =  v3RunTimeHelper.getEnabledSystemDefinition(transactionEntity, Collections.singletonList(TriggerHandlerTestData.getBPMNTemplateDetails()));
    Assert.assertNotNull(defnDetails);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testgetEnabledSystemDefinitionEmptyTemplates() {

    TransactionEntity transactionEntity =
		TransactionEntityFactory.getInstanceOf(
	    		TriggerHandlerTestData.prepareV3TriggerMessage(UPDATED_EVENT).getTriggerMessage(),
	    		contextHandler);

    v3RunTimeHelper.getEnabledSystemDefinition(transactionEntity, new ArrayList<>());
  }

  @Test
  public void testgetEnabledSystemDefinition() {

    TransactionEntity transactionEntity =
		TransactionEntityFactory.getInstanceOf(
	    		TriggerHandlerTestData.prepareV3TriggerMessage(UPDATED_EVENT).getTriggerMessage(),
	    		contextHandler);
    TemplateDetails templateDetails = TriggerHandlerTestData.getBPMNTemplateDetails();
    templateDetails.setDefinitionType(DefinitionType.SYSTEM);

    DefinitionDetails definitionDetails =
        TriggerHandlerTestData.getDefinitionDetails(bpmnTemplateDetail);
    Mockito.when(
            definitionDetailsRepository.findEnabledSystemDefinition(
                ModelType.BPMN, singletonList(templateDetails)))
        .thenReturn(Optional.of(singletonList(definitionDetails)));
    List<DefinitionDetails> defnDetailsOpt =
        v3RunTimeHelper.getEnabledSystemDefinition(
            transactionEntity, singletonList(templateDetails));

    Assert.assertNotNull(defnDetailsOpt);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testgetEnabledSystemDefinitionNotPresent() {

    TransactionEntity transactionEntity =
		TransactionEntityFactory.getInstanceOf(
	    		TriggerHandlerTestData.prepareV3TriggerMessage(UPDATED_EVENT).getTriggerMessage(),
	    		contextHandler);
    TemplateDetails templateDetails = TriggerHandlerTestData.getBPMNTemplateDetails();
    templateDetails.setDefinitionType(DefinitionType.SYSTEM);

    Mockito.when(
            definitionDetailsRepository.findEnabledSystemDefinition(
                ModelType.BPMN, singletonList(templateDetails)))
        .thenReturn(Optional.ofNullable(null));

    v3RunTimeHelper.getEnabledSystemDefinition(transactionEntity, singletonList(templateDetails));
  }

  @Test
  public void testEmptyEntityObjectVariableMap() {
    TransactionEntity transactionEntity =
    	TransactionEntityFactory.getInstanceOf(
    		TriggerHandlerTestData.prepareV3TriggerMessage("globalScoping").getTriggerMessage(),
    		contextHandler);
    Map<String, Object> initialStartEventExtensionProperties = TriggerHandlerTestData.getStartEventExtensionPropertiesOfTheTemplate();
    DefinitionDetails definitionDetails =
        TriggerHandlerTestData.getDefinitionDetails(
            TriggerHandlerTestData.getBPMNTemplateDetails());
    definitionDetails.setPlaceholderValue(null);
    Map<String, Object> processVar = v3RunTimeHelper.getVariablesMap(transactionEntity, null, true, initialStartEventExtensionProperties, definitionDetails, true, null, false);
    Assert.assertNotNull(processVar);
    Assert.assertNotNull(processVar.get(WorkflowConstants.BPMN_DMN_VARIABLES));
  }

  @Test
  public void testGetVariableMapForCustomWorkflowSystemDefinition() {
    Mockito.when(authHelper.getAuthValueForKey(WorkflowConstants.INTUIT_USERID)).thenReturn("1234");
    TransactionEntity transactionEntity =
        TransactionEntityFactory.getInstanceOf(
            TriggerHandlerTestData.prepareV3TriggerMessage("updated").getTriggerMessage(),
            contextHandler);

    TemplateDetails templateDetails = TriggerHandlerTestData.getBPMNTemplateDetails();
    templateDetails.setTemplateName("customApproval");
    Map<String, Object> initialStartEventExtensionProperties = TriggerHandlerTestData.getStartEventExtensionPropertiesOfTheTemplate();
    DefinitionDetails definitionDetails =
        TriggerHandlerTestData.getDefinitionDetails(
            templateDetails);

    definitionDetails.setOwnerId(Long.valueOf(WorkflowConstants.SYSTEM_OWNER_ID));
    definitionDetails.setRecordType(null);


    Map<String, Object> entityObjectMap = transactionEntity.getEntityFromTransaction(
        Optional.ofNullable(transactionEntity)
            .map(TransactionEntity::getEntityObj)
            .filter(org.apache.commons.collections4.MapUtils::isNotEmpty)
            .orElse(Collections.emptyMap()),
        transactionEntity.getEntityType().toString());

    Map<String, Object> processVar =
        v3RunTimeHelper.getVariablesMap(
            transactionEntity, entityObjectMap,
            true, initialStartEventExtensionProperties,
            definitionDetails, true, null, true);
    Assert.assertNotNull(processVar);
    Map<String,Object> processVarMap = (Map<String, Object>)(processVar.get(WorkflowConstants.BPMN_DMN_VARIABLES));
    Assert.assertEquals(34,processVarMap.size());

  }

  @SuppressWarnings("unchecked")
  @Test
  public void testGetVariableMap() {
    Mockito.when(authHelper.getAuthValueForKey(WorkflowConstants.INTUIT_USERID)).thenReturn("1234");
    TransactionEntity transactionEntity =
        TransactionEntityFactory.getInstanceOf(
            TriggerHandlerTestData.prepareV3TriggerMessage("globalScoping").getTriggerMessage(),
            contextHandler);

    Map<String, Object> initialStartEventExtensionProperties = TriggerHandlerTestData.getStartEventExtensionPropertiesOfTheTemplate();
    DefinitionDetails definitionDetails =
        TriggerHandlerTestData.getDefinitionDetails(
            TriggerHandlerTestData.getBPMNTemplateDetails());
    Map<String, Object> processVar =
        v3RunTimeHelper.getVariablesMap(
            transactionEntity, transactionEntity.getVariables().getGlobal(),
            true, initialStartEventExtensionProperties,
            definitionDetails, false, null, false);
    Assert.assertNotNull(processVar);
    Map<String, Object> camundaProcessVar =
        (Map<String, Object>) processVar.get(WorkflowConstants.BPMN_DMN_VARIABLES);
    Assert.assertEquals(
        "Invoice", ((HashMap) camundaProcessVar.get(WorkflowConstants.ENTITY_TYPE)).get("value"));
    Assert.assertNotNull(camundaProcessVar);
    Assert.assertNotNull(camundaProcessVar.get(StringUtils.capitalize(WorkflowConstants.ID)));
    ObjectMapper oMapper = new ObjectMapper();
    Map<String, Map<String, Object>> variables = oMapper.convertValue(processVar.get("variables"),
            Map.class);
    Assert.assertEquals(variables.get(WorkflowConstants.INTUIT_USERID).get("value"), "1234");
  }

  @SuppressWarnings("unchecked")
  @Test
  public void testGetVariableMap_whenFF_OnChildProcessSignal() {

    Mockito.when(authHelper.getAuthValueForKey(WorkflowConstants.INTUIT_USERID)).thenReturn("1234");
    TransactionEntity transactionEntity =
        TransactionEntityFactory.getInstanceOf(
            TriggerHandlerTestData.prepareV3TriggerMessage("globalScoping").getTriggerMessage(),
            contextHandler);

    Map<String, Object> initialStartEventExtensionProperties = TriggerHandlerTestData.getStartEventExtensionPropertiesOfTheTemplate();
    DefinitionDetails definitionDetails =
        TriggerHandlerTestData.getDefinitionDetails(
            TriggerHandlerTestData.getBPMNTemplateDetails());

    ProcessDetails processDetails =
        ProcessDetails.builder()
            .processId("childProcess1")
            .parentId("parentProcess1")
            .parentProcessDetails(
                ProcessDetails.builder()
                    .processId("parentProcess1")
                    .definitionDetails(
                        TriggerHandlerTestData.getDefinitionDetails(
                            TriggerHandlerTestData.getBPMNTemplateDetails())
                    )
                    .build()
            )
            .build();

    Map<String, Object> processVar = v3RunTimeHelper.getVariablesMap(
            transactionEntity, transactionEntity.getVariables().getGlobal(),
            true, initialStartEventExtensionProperties,
            definitionDetails, false, processDetails, false
        );

    Assert.assertNotNull(processVar);
    Map<String, Object> camundaProcessVar =
        (Map<String, Object>) processVar.get(WorkflowConstants.BPMN_DMN_VARIABLES);
    Assert.assertEquals(
        "Invoice", ((HashMap) camundaProcessVar.get(WorkflowConstants.ENTITY_TYPE)).get("value"));
    Assert.assertNotNull(camundaProcessVar);
    Assert.assertNotNull(camundaProcessVar.get(StringUtils.capitalize(WorkflowConstants.ID)));
    ObjectMapper oMapper = new ObjectMapper();
    Map<String, Map<String, Object>> variables = oMapper.convertValue(processVar.get("variables"),
        Map.class);

    Assert.assertEquals(variables.get(WorkflowConstants.INTUIT_USERID).get("value"), "1234");
  }


  @SuppressWarnings("unchecked")
  @Test
  public void testGetVariableMap_whenFF_OnChildProcessWithNullParentSignal() {
    Mockito.when(authHelper.getAuthValueForKey(WorkflowConstants.INTUIT_USERID)).thenReturn("1234");
    TransactionEntity transactionEntity =
        TransactionEntityFactory.getInstanceOf(
            TriggerHandlerTestData.prepareV3TriggerMessage("globalScoping").getTriggerMessage(),
            contextHandler);

    Map<String, Object> initialStartEventExtensionProperties = TriggerHandlerTestData.getStartEventExtensionPropertiesOfTheTemplate();
    DefinitionDetails definitionDetails =
        TriggerHandlerTestData.getDefinitionDetails(
            TriggerHandlerTestData.getBPMNTemplateDetails());

    ProcessDetails processDetails =
        ProcessDetails.builder()
            .processId("childProcess1")
            .parentId("parentProcess1")
            .parentProcessDetails(
                ProcessDetails.builder()
                    .processId("parentProcess1")
                    .definitionDetails(definitionDetails)
                    .build()
            )
            .build();

    Map<String, Object> processVar = v3RunTimeHelper.getVariablesMap(
            transactionEntity, transactionEntity.getVariables().getGlobal(),
            true, initialStartEventExtensionProperties,
            definitionDetails, false, processDetails, false
        );

    Assert.assertNotNull(processVar);
    Map<String, Object> camundaProcessVar =
        (Map<String, Object>) processVar.get(WorkflowConstants.BPMN_DMN_VARIABLES);
    Assert.assertEquals(
        "Invoice", ((HashMap) camundaProcessVar.get(WorkflowConstants.ENTITY_TYPE)).get("value"));
    Assert.assertNotNull(camundaProcessVar);
    Assert.assertNotNull(camundaProcessVar.get(StringUtils.capitalize(WorkflowConstants.ID)));

    ObjectMapper oMapper = new ObjectMapper();
    Map<String, Map<String, Object>> variables = oMapper.convertValue(processVar.get("variables"),
            Map.class);
    Assert.assertEquals(variables.get(WorkflowConstants.INTUIT_USERID).get("value"), "1234");
  }

  @SuppressWarnings("unchecked")
  @Test
  public void testGetVariableMap_whenFF_OnParentProcessSignal() {

    Mockito.when(authHelper.getAuthValueForKey(WorkflowConstants.INTUIT_USERID)).thenReturn("1234");
    TransactionEntity transactionEntity =
        TransactionEntityFactory.getInstanceOf(
            TriggerHandlerTestData.prepareV3TriggerMessage("globalScoping").getTriggerMessage(),
            contextHandler);

    Map<String, Object> initialStartEventExtensionProperties = TriggerHandlerTestData.getStartEventExtensionPropertiesOfTheTemplate();
    DefinitionDetails definitionDetails =
        TriggerHandlerTestData.getDefinitionDetails(
            TriggerHandlerTestData.getBPMNTemplateDetails());

    ProcessDetails processDetails =
        ProcessDetails.builder()
            .processId("childProcess1")
            .parentProcessDetails(ProcessDetails.builder().definitionDetails(definitionDetails).build())
            .build();

    Map<String, Object> processVar =
        v3RunTimeHelper.getVariablesMap(transactionEntity,
            transactionEntity.getVariables().getGlobal(),
            true, initialStartEventExtensionProperties,
            definitionDetails, false, processDetails, false
        );

    Assert.assertNotNull(processVar);
    Map<String, Object> camundaProcessVar =
        (Map<String, Object>) processVar.get(WorkflowConstants.BPMN_DMN_VARIABLES);
    Assert.assertEquals(
        "Invoice", ((HashMap) camundaProcessVar.get(WorkflowConstants.ENTITY_TYPE)).get("value"));
    Assert.assertNotNull(camundaProcessVar);
    Assert.assertNotNull(camundaProcessVar.get(StringUtils.capitalize(WorkflowConstants.ID)));
    ObjectMapper oMapper = new ObjectMapper();
    Map<String, Map<String, Object>> variables = oMapper.convertValue(processVar.get("variables"),
            Map.class);
    Assert.assertEquals(variables.get(WorkflowConstants.INTUIT_USERID).get("value"), "1234");
  }


  @Test
  public void testGetVariableMapFromDefinitionPlaceHoldersFFOn() {
    TransactionEntity transactionEntity =
    		TransactionEntityFactory.getInstanceOf(
    	    		TriggerHandlerTestData.prepareV3TriggerMessage("globalScoping").getTriggerMessage(),
    	    		contextHandler);

    Map<String, Object> initialStartEventExtensionProperties = TriggerHandlerTestData.getStartEventExtensionPropertiesOfTheTemplate();
    DefinitionDetails definitionDetails =
            TriggerHandlerTestData.getDefinitionDetails(
                    TriggerHandlerTestData.getBPMNTemplateDetails());
    definitionDetails.setPlaceholderValue(TestHelper.readResourceAsString(PLACEHOLDER_VALUES_PATH));
//    Mockito.when(customWorkflowConfig.getOldConfig()).thenReturn(customWorkflowConfig1.getOldConfig()) ;
    Map<String, Object> processVar =
            v3RunTimeHelper.getVariablesMap(
                    transactionEntity,
                    transactionEntity.getVariables().getGlobal(),
                    true,
                    initialStartEventExtensionProperties,
                    definitionDetails,
                    false,
                null,
                false);
    Assert.assertNotNull(processVar);
    Map<String, Object> camundaProcessVar =
            (Map<String, Object>) processVar.get(WorkflowConstants.BPMN_DMN_VARIABLES);
    Assert.assertEquals(
            "Invoice", ((HashMap) camundaProcessVar.get(WorkflowConstants.ENTITY_TYPE)).get("value"));
    Assert.assertNotNull(camundaProcessVar);
    Assert.assertNotNull(camundaProcessVar.get(StringUtils.capitalize(WorkflowConstants.ID)));
  }

  @Test
  public void testGetVariableMapFromDefinitionPlaceHoldersFFOn_WithEmptyApproverIdInGlobalVariables() {
    TransactionEntity transactionEntity =
        TransactionEntityFactory.getInstanceOf(
            TriggerHandlerTestData.prepareV3TriggerMessage("globalScopingWithEmptyApproverId").getTriggerMessage(),
            contextHandler);

    Map<String, Object> initialStartEventExtensionProperties = TriggerHandlerTestData.getStartEventExtensionPropertiesOfTheTemplate();
    DefinitionDetails definitionDetails =
        TriggerHandlerTestData.getDefinitionDetails(
            TriggerHandlerTestData.getBPMNTemplateDetails());
    definitionDetails.setPlaceholderValue(TestHelper.readResourceAsString(PLACEHOLDER_VALUES_PATH));

    Mockito.when(authHelper.getAuthValueForKey(WorkflowConstants.INTUIT_USERID)).thenReturn("1234");

    Map<String, Object> processVar =
        v3RunTimeHelper.getVariablesMap(
            transactionEntity,
            transactionEntity.getVariables().getGlobal(),
            false,
            initialStartEventExtensionProperties,
            definitionDetails,
            false,
            ProcessDetails.builder().definitionDetails(definitionDetails).build(),
            false);
    Assert.assertNotNull(processVar);
    Assert.assertEquals("1234", ((HashMap)((HashMap) processVar.get("variables")).get(APPROVER_ID)).get("value"));
    Map<String, Object> camundaProcessVar =
        (Map<String, Object>) processVar.get(WorkflowConstants.BPMN_DMN_VARIABLES);
    Assert.assertEquals(
        "Invoice", ((HashMap) camundaProcessVar.get(WorkflowConstants.ENTITY_TYPE)).get("value"));
    Assert.assertNotNull(camundaProcessVar);
    Assert.assertNotNull(camundaProcessVar.get(StringUtils.capitalize(WorkflowConstants.ID)));
  }

  @Test
  public void testGetVariableMapFromDefinitionPlaceHoldersFFOn_WithNonEmptyApproverIdInGlobalVariablesButNotInEntityObjectMap() {
    TransactionEntity transactionEntity =
        TransactionEntityFactory.getInstanceOf(
            TriggerHandlerTestData.prepareV3TriggerMessage("globalScopingWithNonEmptyApproverId").getTriggerMessage(),
            contextHandler);

    Map<String, Object> initialStartEventExtensionProperties = TriggerHandlerTestData.getStartEventExtensionPropertiesOfTheTemplate();
    DefinitionDetails definitionDetails =
        TriggerHandlerTestData.getDefinitionDetails(
            TriggerHandlerTestData.getBPMNTemplateDetails());
    definitionDetails.setPlaceholderValue(TestHelper.readResourceAsString(PLACEHOLDER_VALUES_PATH));

    Mockito.when(authHelper.getAuthValueForKey(WorkflowConstants.INTUIT_USERID)).thenReturn("1234");

    Map<String, Object> entityObjectDetails = new HashMap<>(transactionEntity.getVariables().getGlobal());
    entityObjectDetails.remove(APPROVER_ID);

    Map<String, Object> processVar =
        v3RunTimeHelper.getVariablesMap(
            transactionEntity,
            entityObjectDetails,
            false,
            initialStartEventExtensionProperties,
            definitionDetails,
            false,
            ProcessDetails.builder().definitionDetails(definitionDetails).build(),
            false);
    Assert.assertNotNull(processVar);
    Assert.assertEquals("5678", ((HashMap)((HashMap) processVar.get("variables")).get(APPROVER_ID)).get("value"));
    Map<String, Object> camundaProcessVar =
        (Map<String, Object>) processVar.get(WorkflowConstants.BPMN_DMN_VARIABLES);
    Assert.assertEquals(
        "Invoice", ((HashMap) camundaProcessVar.get(WorkflowConstants.ENTITY_TYPE)).get("value"));
    Assert.assertNotNull(camundaProcessVar);
    Assert.assertNotNull(camundaProcessVar.get(StringUtils.capitalize(WorkflowConstants.ID)));
  }

  @Test
  public void testGetVariableMapFromDefinitionPlaceHoldersFFOn_WithEmptyApproverIdInLocalVariables() {
    TransactionEntity transactionEntity =
        TransactionEntityFactory.getInstanceOf(
            TriggerHandlerTestData.prepareV3TriggerMessage("localScopingWithEmptyApproverId").getTriggerMessage(),
            contextHandler);

    Map<String, Object> initialStartEventExtensionProperties = TriggerHandlerTestData.getStartEventExtensionPropertiesOfTheTemplate();
    DefinitionDetails definitionDetails =
        TriggerHandlerTestData.getDefinitionDetails(
            TriggerHandlerTestData.getBPMNTemplateDetails());
    definitionDetails.setPlaceholderValue(TestHelper.readResourceAsString(PLACEHOLDER_VALUES_PATH));

    Mockito.when(authHelper.getAuthValueForKey(WorkflowConstants.INTUIT_USERID)).thenReturn("1234");

    Map<String, Object> processVar =
        v3RunTimeHelper.getVariablesMap(
            transactionEntity,
            transactionEntity.getVariables().getLocal(),
            false,
            initialStartEventExtensionProperties,
            definitionDetails,
            false,
            ProcessDetails.builder().definitionDetails(definitionDetails).build(),
            true);

    Assert.assertNotNull(processVar);
    Assert.assertEquals("1234", ((HashMap)((HashMap) processVar.get("variables")).get(APPROVER_ID)).get("value"));
    Map<String, Object> camundaProcessVar =
        (Map<String, Object>) processVar.get(WorkflowConstants.BPMN_DMN_VARIABLES);
    Assert.assertEquals(
        "Invoice", ((HashMap) camundaProcessVar.get(WorkflowConstants.ENTITY_TYPE)).get("value"));
    Assert.assertNotNull(camundaProcessVar);
    Assert.assertNotNull(camundaProcessVar.get(StringUtils.capitalize(WorkflowConstants.ID)));
  }

  @Test
  public void testGetVariableMapFromDefinitionPlaceHoldersFFOn_WithNonEmptyApproverIdInLocalVariables() {
    TransactionEntity transactionEntity =
        TransactionEntityFactory.getInstanceOf(
            TriggerHandlerTestData.prepareV3TriggerMessage("localScopingWithNonEmptyApproverId").getTriggerMessage(),
            contextHandler);

    Map<String, Object> initialStartEventExtensionProperties = TriggerHandlerTestData.getStartEventExtensionPropertiesOfTheTemplate();
    DefinitionDetails definitionDetails =
        TriggerHandlerTestData.getDefinitionDetails(
            TriggerHandlerTestData.getBPMNTemplateDetails());
    definitionDetails.setPlaceholderValue(TestHelper.readResourceAsString(PLACEHOLDER_VALUES_PATH));

    Mockito.when(authHelper.getAuthValueForKey(WorkflowConstants.INTUIT_USERID)).thenReturn("1234");

    Map<String, Object> processVar =
        v3RunTimeHelper.getVariablesMap(
            transactionEntity,
            transactionEntity.getVariables().getLocal(),
            false,
            initialStartEventExtensionProperties,
            definitionDetails,
            false,
            ProcessDetails.builder().definitionDetails(definitionDetails).build(),
            true);

    Assert.assertNotNull(processVar);
    Assert.assertEquals("5678", ((HashMap)((HashMap) processVar.get("variables")).get(APPROVER_ID)).get("value"));
    Map<String, Object> camundaProcessVar =
        (Map<String, Object>) processVar.get(WorkflowConstants.BPMN_DMN_VARIABLES);
    Assert.assertEquals(
        "Invoice", ((HashMap) camundaProcessVar.get(WorkflowConstants.ENTITY_TYPE)).get("value"));
    Assert.assertNotNull(camundaProcessVar);
    Assert.assertNotNull(camundaProcessVar.get(StringUtils.capitalize(WorkflowConstants.ID)));
  }


  @Test
  public void testGetVariableMapFromDefinitionPlaceHoldersFFOff() {
    TransactionEntity transactionEntity =
    		TransactionEntityFactory.getInstanceOf(
    	    		TriggerHandlerTestData.prepareV3TriggerMessage("globalScoping").getTriggerMessage(),
    	    		contextHandler);

    Map<String, Object> initialStartEventExtensionProperties = TriggerHandlerTestData.getStartEventExtensionPropertiesOfTheTemplate();
    DefinitionDetails definitionDetails =
        TriggerHandlerTestData.getDefinitionDetails(
            TriggerHandlerTestData.getBPMNTemplateDetails());
    definitionDetails.setPlaceholderValue(TestHelper.readResourceAsString(PLACEHOLDER_VALUES_PATH));
//    Mockito.when(customWorkflowConfig.getOldConfig()).thenReturn(customWorkflowConfig1.getOldConfig()) ;
    Map<String, Object> processVar =
        v3RunTimeHelper.getVariablesMap(transactionEntity,
            transactionEntity.getVariables().getGlobal(),
            true, initialStartEventExtensionProperties, definitionDetails,
                false, null, false);
    Assert.assertNotNull(processVar);
    Map<String, Object> camundaProcessVar =
        (Map<String, Object>) processVar.get(WorkflowConstants.BPMN_DMN_VARIABLES);
    Assert.assertEquals(
        "Invoice", ((HashMap) camundaProcessVar.get(WorkflowConstants.ENTITY_TYPE)).get("value"));
    Assert.assertNotNull(camundaProcessVar);
    Assert.assertNotNull(camundaProcessVar.get(StringUtils.capitalize(WorkflowConstants.ID)));
  }

  @Test
  public void testGetSequenceVariableMapFromDefinitionPlaceHoldersFFOn() {
    TransactionEntity transactionEntity =
    		TransactionEntityFactory.getInstanceOf(
    	    		TriggerHandlerTestData.prepareV3TriggerMessage("globalScoping").getTriggerMessage(),
    	    		contextHandler);

    Map<String, Object> initialStartEventExtensionProperties = TriggerHandlerTestData.getStartEventExtensionPropertiesOfTheTemplate();
    DefinitionDetails definitionDetails =
            TriggerHandlerTestData.getDefinitionDetails(
                    TriggerHandlerTestData.getBPMNTemplateDetails());
    definitionDetails.setPlaceholderValue(TestHelper.readResourceAsString(PLACEHOLDER_VALUES_PATH));
    Map<String, Object> processVar =
            v3RunTimeHelper.getVariablesMap(
                    transactionEntity,
                    transactionEntity.getVariables().getGlobal(),
                    true,
                    initialStartEventExtensionProperties,
                    definitionDetails,
                    false,
                null, false);
    Assert.assertNotNull(processVar);
    Map<String, Object> camundaProcessVar =
            (Map<String, Object>) processVar.get(WorkflowConstants.BPMN_DMN_VARIABLES);

    Assert.assertEquals(
            "true", ((HashMap) camundaProcessVar.get(WorkflowConstants.CREATE_TASK)).get("value"));
    Assert.assertEquals(
            "true", ((HashMap) camundaProcessVar.get(WorkflowConstants.SEND_EXTERNAL_TASK)).get("value"));

  }

  @Test
  public void testGetVariableMapFromDefinitionWithoutRelaxedProcessVariables() {
    TransactionEntity transactionEntity =
        TransactionEntityFactory.getInstanceOf(
            TriggerHandlerTestData.prepareV3TriggerMessage(UPDATED_EVENT).getTriggerMessage(),
            contextHandler);

    Map<String, Object> initialStartEventExtensionProperties = TriggerHandlerTestData.getStartEventExtensionPropertiesOfTheTemplate();
    DefinitionDetails definitionDetails =
        TriggerHandlerTestData.getDefinitionDetails(
            TriggerHandlerTestData.getBPMNTemplateDetails());
    definitionDetails.setPlaceholderValue(TestHelper.readResourceAsString(PLACEHOLDER_VALUES_PATH));

    Map<String, Object> processVar =
        v3RunTimeHelper.getVariablesMap(
            transactionEntity,
            (Map<String, Object>) transactionEntity.getEntityObj().get("Invoice"),
            true,
            initialStartEventExtensionProperties,
            definitionDetails,
                true,
            null,
            false);
    Assert.assertNotNull(processVar);
    Map<String, Object> camundaProcessVar =
        (Map<String, Object>) processVar.get(WorkflowConstants.BPMN_DMN_VARIABLES);
    Assert.assertEquals(
        "Invoice", ((HashMap) camundaProcessVar.get(WorkflowConstants.ENTITY_TYPE)).get("value"));

    // assert that the extra variables in the trigger body are not present
    Assert.assertNull(camundaProcessVar.get("AllowOnlinePayment"));
    Assert.assertNull(camundaProcessVar.get("EmailStatus"));
    Assert.assertNull(camundaProcessVar.get("Balance"));
    Assert.assertNull(camundaProcessVar.get("DueDate"));
    Assert.assertNull(camundaProcessVar.get("CustomerRef"));
    Assert.assertNull(camundaProcessVar.get("CustomField"));

    // assert allowed variable in start event
    Assert.assertNotNull(camundaProcessVar);
    Assert.assertNotNull(camundaProcessVar.get(StringUtils.capitalize(WorkflowConstants.ID)));
  }

  @Test
  public void testGetVariableMapFromDefinitionWithRelaxedProcessVariables() {
    TransactionEntity transactionEntity =
        TransactionEntityFactory.getInstanceOf(
            TriggerHandlerTestData.prepareV3TriggerMessage(UPDATED_EVENT).getTriggerMessage(),
            contextHandler);

    Map<String, Object> initialStartEventExtensionProperties = TriggerHandlerTestData.getStartEventExtensionPropertiesOfTheTemplateWithRelaxedProcessVar();
    DefinitionDetails definitionDetails =
        TriggerHandlerTestData.getDefinitionDetails(
            TriggerHandlerTestData.getBPMNTemplateDetails());
    definitionDetails.setPlaceholderValue(TestHelper.readResourceAsString(PLACEHOLDER_VALUES_PATH));
    Map<String, Object> processVar =
        v3RunTimeHelper.getVariablesMap(
            transactionEntity,
            (Map<String, Object>) transactionEntity.getEntityObj().get("Invoice"),
            true,
            initialStartEventExtensionProperties,
            definitionDetails,
            true,
            null,
            false);
    Assert.assertNotNull(processVar);
    Map<String, Object> camundaProcessVar =
        (Map<String, Object>) processVar.get(WorkflowConstants.BPMN_DMN_VARIABLES);
    Assert.assertEquals(
        "Invoice", ((HashMap) camundaProcessVar.get(WorkflowConstants.ENTITY_TYPE)).get("value"));

    // assert non-allowed variable in start event

    // Allowed Datatypes examples
    // Gets Boolean as its supported
    //"AllowOnlinePayment": false,
    Assert.assertEquals(
        false, ((HashMap) camundaProcessVar.get("AllowOnlinePayment")).get("value"));
    Assert.assertEquals(
        "Boolean", ((HashMap) camundaProcessVar.get("AllowOnlinePayment")).get("type"));
    // Gets String as its supported
    //"EmailStatus": "NotSet",
    Assert.assertEquals(
        "NotSet", ((HashMap) camundaProcessVar.get("EmailStatus")).get("value"));
    Assert.assertEquals(
        "String", ((HashMap) camundaProcessVar.get("EmailStatus")).get("type"));
    // Gets Int as its supported
    //"Balance": 128,
    Assert.assertEquals(
        128, ((HashMap) camundaProcessVar.get("Balance")).get("value"));
    Assert.assertEquals(
        "Integer", ((HashMap) camundaProcessVar.get("Balance")).get("type"));

    // Non-Allowed Datatypes
    // Date type defaults to String
    //"DueDate": "2020-01-11",
    Assert.assertEquals(
        "2020-01-11", ((HashMap) camundaProcessVar.get("DueDate")).get("value"));
    Assert.assertEquals(
        "String", ((HashMap) camundaProcessVar.get("DueDate")).get("type"));
    // Map defaults to Object
    /* "CustomerRef": {
      "value": "1",
      "name": "C1"
    },*/
    Assert.assertEquals(
        "Object", ((HashMap) camundaProcessVar.get("CustomerRef")).get("type"));
    // List defaults to Object
    /*    "CustomField": [],
     */
    Assert.assertEquals(
        "Object", ((HashMap) camundaProcessVar.get("CustomField")).get("type"));

    // assert allowed variable in start event
    Assert.assertEquals(
        128, ((HashMap) camundaProcessVar.get("TotalAmt")).get("value"));
    Assert.assertNotNull(camundaProcessVar);
    Assert.assertNotNull(camundaProcessVar.get(StringUtils.capitalize(WorkflowConstants.ID)));
  }

  @Test
  public void testGetVariableMapFromDefinitionWithRelaxedNonEntityProcessVariables() {
    TransactionEntity transactionEntity =
            TransactionEntityFactory.getInstanceOf(
                    TriggerHandlerTestData.prepareV3TriggerMessage("typedVariablesPayload").getTriggerMessage(),
                    contextHandler);

    Map<String, Object> initialStartEventExtensionProperties = TriggerHandlerTestData.getStartEventExtensionPropertiesOfTheTemplateWithRelaxedNonEntityProcessVar();
    DefinitionDetails definitionDetails =
            TriggerHandlerTestData.getDefinitionDetails(
                    TriggerHandlerTestData.getBPMNTemplateDetails());
    definitionDetails.setPlaceholderValue(TestHelper.readResourceAsString(PLACEHOLDER_VALUES_PATH));
    Map<String, Object> processVar =
            v3RunTimeHelper.getVariablesMap(
                    transactionEntity,
                    transactionEntity.getVariables().getLocal(),
                    true,
                    initialStartEventExtensionProperties,
                    definitionDetails,
                    false,
                null,
                false);
    Assert.assertNotNull(processVar);
    Map<String, Object> camundaProcessVar =
            (Map<String, Object>) processVar.get(WorkflowConstants.BPMN_DMN_VARIABLES);

    Assert.assertEquals(
            "Invoice", ((HashMap) camundaProcessVar.get(WorkflowConstants.ENTITY_TYPE)).get("value"));

    // assert non-allowed variable in start event

    // Map defaults to Object
    /* "CustomerRef": {
      "value": "1",
      "name": "C1"
    },*/
    Assert.assertEquals(
            "Object", ((HashMap) camundaProcessVar.get("CustomerRef")).get("type"));

    // assert allowed variable in start event
    Assert.assertEquals(
            80000, ((HashMap) camundaProcessVar.get("TotalAmt")).get("value"));
    Assert.assertNotNull(camundaProcessVar);
    Assert.assertNull(camundaProcessVar.get(StringUtils.capitalize(WorkflowConstants.ID)));

    Map departmentValue = ((HashMap) camundaProcessVar.get("DepartmentRef"));
    Assert.assertEquals("String", departmentValue.get("type"));
    Assert.assertEquals(true, ((HashMap) departmentValue.get("valueInfo")).get("transient"));
  }

  @Test
  public void testGetVariableMapFromTypedVariableWithoutRelaxedProcessVariables() {
    TransactionEntity transactionEntity =
            TransactionEntityFactory.getInstanceOf(
                    TriggerHandlerTestData.prepareV3TriggerMessage("typedVariablesPayload").getTriggerMessage(),
                    contextHandler);

    Map<String, Object> initialStartEventExtensionProperties = TriggerHandlerTestData.getStartEventExtensionPropertiesOfTheTemplate();
    DefinitionDetails definitionDetails =
            TriggerHandlerTestData.getDefinitionDetails(
                    TriggerHandlerTestData.getBPMNTemplateDetails());
    definitionDetails.setPlaceholderValue(TestHelper.readResourceAsString(PLACEHOLDER_VALUES_PATH));
    Map<String, Object> processVar =
            v3RunTimeHelper.getVariablesMap(
                    transactionEntity,
                    transactionEntity.getVariables().getLocal(),
                    true,
                    initialStartEventExtensionProperties,
                    definitionDetails,
                    false,
                null,
                false);
    Assert.assertNotNull(processVar);
    Map<String, Object> camundaProcessVar =
            (Map<String, Object>) processVar.get(WorkflowConstants.BPMN_DMN_VARIABLES);
    Assert.assertEquals(
            "Invoice", ((HashMap) camundaProcessVar.get(WorkflowConstants.ENTITY_TYPE)).get("value"));

    // assert that the extra variables in the trigger body are not present
    Assert.assertNull(camundaProcessVar.get("AllowOnlinePayment"));
    Assert.assertNull(camundaProcessVar.get("EmailStatus"));
    Assert.assertNull(camundaProcessVar.get("Balance"));
    Assert.assertNull(camundaProcessVar.get("DueDate"));
    Assert.assertNull(camundaProcessVar.get("CustomerRef"));
    Assert.assertNull(camundaProcessVar.get("CustomField"));
    Assert.assertNull(camundaProcessVar.get("DepartmentRef"));

    Assert.assertNotNull(camundaProcessVar);
    Assert.assertNotNull(camundaProcessVar.get(StringUtils.capitalize(WorkflowConstants.ID)));
  }

  @Test
  public void testGetVariableMapFromTypedVariablesWithRelaxedProcessVariables() {
    TransactionEntity transactionEntity =
            TransactionEntityFactory.getInstanceOf(
                    TriggerHandlerTestData.prepareV3TriggerMessage("typedVariablesPayload").getTriggerMessage(),
                    contextHandler);

    Map<String, Object> initialStartEventExtensionProperties = TriggerHandlerTestData.getStartEventExtensionPropertiesOfTheTemplateWithRelaxedProcessVar();
    DefinitionDetails definitionDetails =
            TriggerHandlerTestData.getDefinitionDetails(
                    TriggerHandlerTestData.getBPMNTemplateDetails());
    definitionDetails.setPlaceholderValue(TestHelper.readResourceAsString(PLACEHOLDER_VALUES_PATH));
    Map<String, Object> processVar =
            v3RunTimeHelper.getVariablesMap(
                    transactionEntity,
                    transactionEntity.getVariables().getLocal(),
                    true,
                    initialStartEventExtensionProperties,
                    definitionDetails,
                    false,
                null,
                false);
    Assert.assertNotNull(processVar);
    Map<String, Object> camundaProcessVar =
            (Map<String, Object>) processVar.get(WorkflowConstants.BPMN_DMN_VARIABLES);

    Assert.assertEquals(
            "Invoice", ((HashMap) camundaProcessVar.get(WorkflowConstants.ENTITY_TYPE)).get("value"));

    // assert non-allowed variable in start event

    // Map defaults to Object
    /* "CustomerRef": {
      "value": "1",
      "name": "C1"
    },*/
    Assert.assertEquals(
            "Object", ((HashMap) camundaProcessVar.get("CustomerRef")).get("type"));

    // assert allowed variable in start event
    Assert.assertEquals(
            80000, ((HashMap) camundaProcessVar.get("TotalAmt")).get("value"));
    Assert.assertNotNull(camundaProcessVar);
    Assert.assertNull(camundaProcessVar.get(StringUtils.capitalize(WorkflowConstants.ID)));

    Map departmentValue = ((HashMap) camundaProcessVar.get("DepartmentRef"));
    Assert.assertEquals("String", departmentValue.get("type"));
    Assert.assertEquals(true, ((HashMap) departmentValue.get("valueInfo")).get("transient"));
  }

  @Test
  public void testGetVariableMapFromDefinitionWithRelaxedProcessVariablesWithEmptyProcessVariables() {
    TransactionEntity transactionEntity =
        TransactionEntityFactory.getInstanceOf(
            TriggerHandlerTestData.prepareV3TriggerMessage(UPDATED_EVENT).getTriggerMessage(),
            contextHandler);

    Map<String, Object> initialStartEventExtensionProperties = TriggerHandlerTestData.getStartEventExtensionPropertiesOfTheTemplateWithRelaxedProcessVarWithEmptyProcessDetails();

    DefinitionDetails definitionDetails =
        TriggerHandlerTestData.getDefinitionDetails(
            TriggerHandlerTestData.getBPMNTemplateDetails());
    definitionDetails.setPlaceholderValue(TestHelper.readResourceAsString(PLACEHOLDER_VALUES_PATH));
    Map<String, Object> processVar =
        v3RunTimeHelper.getVariablesMap(
            transactionEntity,
            (Map<String, Object>) transactionEntity.getEntityObj().get("Invoice"),
            true,
            initialStartEventExtensionProperties,
            definitionDetails,
            true,
            null,
            false);
    Assert.assertNotNull(processVar);
    Map<String, Object> camundaProcessVar =
        (Map<String, Object>) processVar.get(WorkflowConstants.BPMN_DMN_VARIABLES);

    // Gets Boolean as its supported
    //"AllowOnlinePayment": false,
    Assert.assertEquals(
        false, ((HashMap) camundaProcessVar.get("AllowOnlinePayment")).get("value"));
    Assert.assertEquals(
        "Boolean", ((HashMap) camundaProcessVar.get("AllowOnlinePayment")).get("type"));
    // Gets String as its supported
    //"EmailStatus": "NotSet",
    Assert.assertEquals(
        "NotSet", ((HashMap) camundaProcessVar.get("EmailStatus")).get("value"));
    Assert.assertEquals(
        "String", ((HashMap) camundaProcessVar.get("EmailStatus")).get("type"));
    // Gets Int as its supported
    //"Balance": 128,
    Assert.assertEquals(
        128, ((HashMap) camundaProcessVar.get("Balance")).get("value"));
    Assert.assertEquals(
        "Integer", ((HashMap) camundaProcessVar.get("Balance")).get("type"));

    // Date type defaults to String
    //"DueDate": "2020-01-11",
    Assert.assertEquals(
        "2020-01-11", ((HashMap) camundaProcessVar.get("DueDate")).get("value"));
    Assert.assertEquals(
        "String", ((HashMap) camundaProcessVar.get("DueDate")).get("type"));
    // Map defaults to Object
    /* "CustomerRef": {
      "value": "1",
      "name": "C1"
    },*/
    Assert.assertEquals(
        "Object", ((HashMap) camundaProcessVar.get("CustomerRef")).get("type"));
    // List defaults to Object
    /*    "CustomField": [],
     */
    Assert.assertEquals(
        "Object", ((HashMap) camundaProcessVar.get("CustomField")).get("type"));

    Assert.assertEquals(
        128, ((HashMap) camundaProcessVar.get("TotalAmt")).get("value"));
    Assert.assertNotNull(camundaProcessVar);
    Assert.assertNotNull(camundaProcessVar.get(StringUtils.capitalize(WorkflowConstants.ID)));
  }

  @Test
  public void testGetEligibleDefinitionEnabledEligibleDefintionList() {

    transactionEntityUpdated =
    	TransactionEntityFactory.getInstanceOf(
    		TriggerHandlerTestData.prepareV3TriggerMessage(CREATED).getTriggerMessage(),
    		contextHandler);
    bpmnTemplateDetail.setTemplateName(TEMPLATE_NAME);
    final DefinitionDetails definitionDetails =
        TriggerHandlerTestData.getDefinitionDetails(bpmnTemplateDetail);
    final List<DefinitionDetails> definitionDetailsList =
        Collections.singletonList(definitionDetails);

    Mockito.when(templateQueryCapabilityFactory.getTemplateQueryCapability(Mockito.any()))
        .thenReturn(defaultTemplateQueryCapability);
    doReturn(definitionDetailsList)
        .when(defaultTemplateQueryCapability)
        .getEnabledDefinitions(Mockito.any(), anyBoolean());
    final List<DefinitionDetails> definitionDetailsListResponse =
        v3RunTimeHelper.getEligibleDefinitions(transactionEntityUpdated, false);

    assertNotNull(definitionDetailsListResponse);
  }

  @Test
  public void testGetEligibleDefinitionEnabledAndMarkedDefinitionForWorkflow() {

    transactionEntityUpdated =
		TransactionEntityFactory.getInstanceOf(
	    		TriggerHandlerTestData.prepareV3TriggerMessage(CREATED).getTriggerMessage(),
	    		contextHandler);
    Mockito.when(templateQueryCapabilityFactory.getTemplateQueryCapability(Mockito.any()))
        .thenReturn(defaultTemplateQueryCapability);

    Mockito.when(defaultTemplateQueryCapability.getEnabledDefinitions(Mockito.any(), anyBoolean()))
        .thenReturn(
            Collections.singletonList(
                TriggerHandlerTestData.getDefinitionDetails(bpmnTemplateDetail)));
    transactionEntityUpdated.getEventHeaders().setProviderWorkflowId(WORKFLOW_ID);
    final List<DefinitionDetails> definitionDetailsListResponse =
        v3RunTimeHelper.getEligibleDefinitions(transactionEntityUpdated, false);

    assertNotNull(definitionDetailsListResponse);
  }

  @Test
  public void testMergeWithCalledProcesses() {

    List<ProcessDetails> parentProcesses = new ArrayList<>();
    parentProcesses.add(
        ProcessDetails.builder()
        .processId("parentId")
        .recordId("123")
        .ownerId(12345L)
        .processStatus(ProcessStatus.ACTIVE)
        .definitionDetails(DefinitionDetails.builder().build())
        .build()
    );

    List<DefinitionDetails> definitionDetails = new ArrayList<>();
    definitionDetails.add(DefinitionDetails.builder().build());

    Mockito.when(
            processDetailsRepository.findByProcessStatusAndParentIdIn(
                ProcessStatus.ACTIVE, List.of("parentId")
            )
        ).thenReturn(
        Optional.of(
            List.of(
                ProcessDetails.builder()
                    .processId("childId")
                    .recordId("123")
                    .ownerId(12345l)
                    .processStatus(ProcessStatus.ACTIVE)
                    .definitionDetails(DefinitionDetails.builder().build())
                    .build()
            )
        )
    );

    Assert.assertEquals(1, parentProcesses.size());
    Assert.assertEquals(1, definitionDetails.size());

    v3RunTimeHelper.mergeCalledProcesses(parentProcesses, definitionDetails);

    Assert.assertEquals(2, parentProcesses.size());
    Assert.assertEquals(2, definitionDetails.size());
  }

  @Test
  public void testMergeTriggerResponsesForParentAndItsCalledProcessesInParentProcess_NoAction() {
    testMergeTriggerResponsesForParentAndItsCalledProcessesInParentProcess(TriggerStatus.NO_ACTION);
  }

  @Test
  public void testMergeTriggerResponsesForParentAndItsCalledProcessesInParentProcess_Signal() {
    testMergeTriggerResponsesForParentAndItsCalledProcessesInParentProcess(TriggerStatus.PROCESS_SIGNALLED);
  }

  @Test
  public void testMergeTriggerResponsesForParentAndItsCalledProcessesInParentProcess_Errored() {
    testMergeTriggerResponsesForParentAndItsCalledProcessesInParentProcess(TriggerStatus.ERROR_SIGNALING_PROCESS);
  }

  private void testMergeTriggerResponsesForParentAndItsCalledProcessesInParentProcess(TriggerStatus childTriggerStatus) {

    List<WorkflowTriggerResponse> triggerResponses = getTriggerResponsesForParentAndChildWithTriggerStatus(childTriggerStatus);

    Assert.assertEquals(2, triggerResponses.size());

    List<WorkflowTriggerResponse> mergedTriggerResponses = v3RunTimeHelper.mergeTriggerResponse(
        triggerResponses, getListOfParentAndChildProcess()
    );

    Assert.assertEquals(1, mergedTriggerResponses.size());

    mergedTriggerResponses.forEach(
        triggerResponse ->
            Assert.assertEquals(childTriggerStatus, triggerResponse.getStatus())
    );
  }

  private List<WorkflowTriggerResponse> getTriggerResponsesForParentAndChildWithTriggerStatus(TriggerStatus triggerStatus) {
    List<WorkflowTriggerResponse> triggerResponses = new ArrayList<>();
    triggerResponses.add(
        WorkflowTriggerResponse.builder()
            .definitionId("parentDefinitionId")
            .processId("parentId")
            .status(TriggerStatus.NO_ACTION)
            .build()
    );

    triggerResponses.add(
        WorkflowTriggerResponse.builder()
            .definitionId("childDefinitionId")
            .processId("childId")
            .status(triggerStatus)
            .build()
    );

    return triggerResponses;
  }

  private List<ProcessDetails> getListOfParentAndChildProcess() {
    return List.of(
        ProcessDetails.builder()
            .processId("parentId")
            .recordId("123")
            .ownerId(12345l)
            .processStatus(ProcessStatus.ACTIVE)
            .build(),
        ProcessDetails.builder()
            .processId("childId")
            .parentId("parentId")
            .recordId("123")
            .ownerId(12345l)
            .processStatus(ProcessStatus.ACTIVE)
            .build()
    );
  }

  @Test
  public void testProcessDetailInstance() {

    ProcessDetails pDetails = new ProcessDetails();
    pDetails.setProcessId(PROCESS_ID);
    List<ProcessDetails> processDetails = Collections.singletonList(pDetails);
    Mockito.when(
            processDetailsRepository.findByRecordIdAndOwnerIdAndProcessStatusAndDefinitionDetails(
                anyString(), anyLong(), anyObject(), anyObject()))
        .thenReturn(Optional.of(processDetails));
    Optional<List<ProcessDetails>> definitionDetailLists =
        v3RunTimeHelper.getProcessDetailsInstance(
            ENTITY_ID,
            Long.parseLong(REALM_ID),
            ProcessStatus.ACTIVE,
            DefinitionDetails.builder().build());
    Assert.assertNotNull(definitionDetailLists);
  }

  @Test
  public void testCheckStaticMessageExistsInCorrelationMsg() {
    boolean result =
        v3RunTimeHelper.checkStaticMessageExistsInCorrelationMsg(
            "customWait", "customWait${intuit_realmId}");
    Assert.assertTrue(result);
    result = v3RunTimeHelper.checkStaticMessageExistsInCorrelationMsg(null, null);
    Assert.assertFalse(result);
    result =
        v3RunTimeHelper.checkStaticMessageExistsInCorrelationMsg(
            null, "customWait${intuit_realmId}");
    Assert.assertFalse(result);
    result = v3RunTimeHelper.checkStaticMessageExistsInCorrelationMsg("customWait", null);
    Assert.assertFalse(result);
    result =
        v3RunTimeHelper.checkStaticMessageExistsInCorrelationMsg(
            " ", "customWait${intuit_realmId}");
    Assert.assertFalse(result);

    result = v3RunTimeHelper.checkStaticMessageExistsInCorrelationMsg("customWait", "customWait");
    Assert.assertTrue(result);

    result =
        v3RunTimeHelper.checkStaticMessageExistsInCorrelationMsg(
            "customWait", "Wait${intuit_realmId}custom");
    Assert.assertFalse(result);

    result =
        v3RunTimeHelper.checkStaticMessageExistsInCorrelationMsg(
            "customWait", "custom${intuit_realmId}Wait");
    Assert.assertTrue(result);

    result =
        v3RunTimeHelper.checkStaticMessageExistsInCorrelationMsg(
            "customWait", "${intuit_realmId}customWait");
    Assert.assertTrue(result);

    result =
        v3RunTimeHelper.checkStaticMessageExistsInCorrelationMsg(
            "123customWaitABC", "${intuit_realmId}customWait${intuit_realmId}ABC");
    Assert.assertTrue(result);

    result =
        v3RunTimeHelper.checkStaticMessageExistsInCorrelationMsg(
            "123customWaitABC", "${intuit_realmId}customWait${intuit_realmId}");
    Assert.assertTrue(result);

    result =
        v3RunTimeHelper.checkStaticMessageExistsInCorrelationMsg(
            "123customWaitABC", "StartEvent");
    Assert.assertFalse(result);
  }

  @Test
  public void markProcessErrorAndEventPublish() {

    TransactionEntity transactionEntity =
        TransactionEntityFactory.getInstanceOf(
            TriggerHandlerTestData.prepareV3TriggerMessage(UPDATED_EVENT).getTriggerMessage(),
            contextHandler);

    Mockito.doNothing()
        .when(processDetailsRepository)
        .updateProcessStatusAndEntityVersion("pId", ProcessStatus.ERROR, 1);
    DomainEntityRequest<ProcessDetails> request =
        DomainEntityRequest.<ProcessDetails>builder()
            .request(
                ProcessDetails.builder()
                    .processStatus(ProcessStatus.ERROR)
                    .processId("pId")
                    .entityVersion(1)
                    .build())
            .build();
    v3RunTimeHelper.markProcessError(
        transactionEntity,
        ProcessDetails.builder()
            .processId("pId")
            .entityVersion(0)
            .processStatus(ProcessStatus.ACTIVE)
            .build());
    Mockito.verify(processDetailsRepository, Mockito.times(1))
        .updateProcessStatusAndEntityVersion(Mockito.any(), Mockito.any(), Mockito.any());
  }

  /** @return */
  @SuppressWarnings("unchecked")
  private Map<String, Object> getPayload() {

    return (Map<String, Object>)
        ((Map<String, Object>)
                TriggerHandlerTestData.prepareV3TriggerMessage(UPDATED_EVENT)
                    .getTriggerMessage()
                    .get("entity"))
            .get("Invoice");
  }

  @SuppressWarnings("unchecked")
  private Map<String, Object> getPayloadForBulk(){
    return (Map<String, Object>)
        ((Map<String, Object>)
            TriggerHandlerTestData.prepareV3TriggerMessage(OBJECT_VARIABLE_TYPE_PAYLOAD)
                .getTriggerMessage()
                .get("entity"))
            .get("BulkDelete");
  }

  @Test
  public void testFilterProcessesStartedTodayForRecurringWorkflows(){
    List<ProcessDetails> processDetails = new ArrayList<>();

    DefinitionDetails multiConditionDefinitionDetails = new DefinitionDetails();
    TemplateDetails templateDetails = new TemplateDetails();
    templateDetails.setTemplateCategory(TemplateCategory.CUSTOM.toString());
    templateDetails.setDefinitionType(DefinitionType.SINGLE);

    multiConditionDefinitionDetails.setDefinitionData(null);
    multiConditionDefinitionDetails.setTemplateDetails(templateDetails);

    processDetails.add(ProcessDetails.builder().processId("pId1").createdDate(Timestamp.valueOf(LocalDateTime.now()))
        .definitionDetails(multiConditionDefinitionDetails).build());

    DefinitionDetails definitionDetails = new DefinitionDetails();
    definitionDetails.setDefinitionData("TestXml".getBytes());
    processDetails.add(ProcessDetails.builder().processId("pId2").createdDate(Timestamp.valueOf(LocalDateTime.now().minusDays(1)))
        .definitionDetails(definitionDetails).build());
    processDetails.add(ProcessDetails.builder().processId("pId3").createdDate(Timestamp.valueOf(LocalDateTime.now().minusDays(1)))
        .definitionDetails(definitionDetails).build());

    transactionEntityUpdated.getEventHeaders().setEntityChangeType(CUSTOM_REMINDER_CUSTOM_RECUR.getEntityChangeType());

    v3RunTimeHelper.filterProcessesStartedTodayForRecurringWorkflows(processDetails, transactionEntityUpdated);
    Assert.assertEquals(2, processDetails.size());
    Assert.assertNull(processDetails.stream().filter(p -> p.getProcessId().equals("pId1")).findFirst().orElse(null));
  }
}
