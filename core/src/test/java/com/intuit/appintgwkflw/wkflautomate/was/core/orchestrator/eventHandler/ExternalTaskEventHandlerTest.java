package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.eventHandler;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.taskcompletionhandler.ExternalTaskCompletionHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.taskcompletionhandler.TaskCompletionHandlers;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.EventHeaderConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.externaltask.ExternalTaskCompleted;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.externaltask.ExternalTaskStatus;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ExternalTaskEventHandlerTest {

  @InjectMocks
  ExternalTaskEventHandler handler;

  @Mock
  ExternalTaskEventHandlerHelper externalTaskEventHandlerHelper;

  @Mock
  ExternalTaskCompletionHandler taskCompletionHandlerMock;

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
    TaskCompletionHandlers.addHandler(EventEntityType.EXTERNALTASK, taskCompletionHandlerMock);
  }

  @Test
  public void testTransform() throws Exception {

    ExternalTaskCompleted e =
        ExternalTaskCompleted.builder()
            .status(ExternalTaskStatus.SUCCESS.getStatus())
            .errorDetails("errorDetails")
            .errorMessage("errorMessage")
            .variables(Collections.EMPTY_MAP)
            .localVariables(Collections.EMPTY_MAP)
            .build();

    ExternalTaskCompleted result = handler.transform(ObjectConverter.toJson(e));
    Assert.assertNotNull(result);
    Assert.assertEquals(result.getStatus(), ExternalTaskStatus.SUCCESS.getStatus());
    Assert.assertEquals("errorDetails", result.getErrorDetails());
    Assert.assertEquals("errorMessage", result.getErrorMessage());
    Assert.assertEquals(Collections.EMPTY_MAP, result.getVariables());;
    Assert.assertEquals(Collections.EMPTY_MAP, result.getLocalVariables());
  }


  @Test(expected = WorkflowGeneralException.class)
  public void testTransform_statusNotPresent() throws Exception {

    ExternalTaskCompleted e =
        ExternalTaskCompleted.builder()
            .errorDetails("errorDetails")
            .errorMessage("errorMessage")
            .variables(Collections.EMPTY_MAP)
            .localVariables(Collections.EMPTY_MAP)
            .build();

    ExternalTaskCompleted result = handler.transform(ObjectConverter.toJson(e));
    Assert.assertNotNull(result);
    Assert.assertEquals(result.getStatus(), ExternalTaskStatus.SUCCESS.getStatus());
    Assert.assertEquals("errorDetails", result.getErrorDetails());
    Assert.assertEquals("errorMessage", result.getErrorMessage());
    Assert.assertEquals(Collections.EMPTY_MAP, result.getVariables());;
    Assert.assertEquals(Collections.EMPTY_MAP, result.getLocalVariables());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testTransformNull() throws Exception {
    handler.transform(null);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testTransformIncorrectJson() throws Exception {
    handler.transform("hello");
  }

  @Test
  public void testExecuteSuccess_taskStatusSuccess() throws Exception {
    ExternalTaskCompleted task =
        ExternalTaskCompleted.builder()
            .status(ExternalTaskStatus.SUCCESS.getStatus())
            .variables(Collections.EMPTY_MAP)
            .localVariables(Collections.EMPTY_MAP)
            .build();
    handler.execute(task, getHeaders());
    Mockito.verify(externalTaskEventHandlerHelper).handleOtherStatus(task, getHeaders(), handler.getName());
    Mockito.verify(externalTaskEventHandlerHelper, Mockito.times(0)).handleExtendLockStatus(task, getHeaders());
    Mockito.verify(externalTaskEventHandlerHelper, Mockito.times(0)).handleFailureWithRetryStatus(task, getHeaders(), handler.getName());
  }

    @Test
    public void testExecuteSuccess_TaskStatusExtendLock() throws Exception {
        ExternalTaskCompleted task =
                ExternalTaskCompleted.builder()
                        .status(ExternalTaskStatus.EXTEND_LOCK.getStatus())
                        .variables(Collections.EMPTY_MAP)
                        .localVariables(Collections.EMPTY_MAP)
                        .build();
        handler.execute(task, getHeaders());
        Mockito.verify(externalTaskEventHandlerHelper, Mockito.times(0)).handleOtherStatus(task, getHeaders(), handler.getName());
        Mockito.verify(externalTaskEventHandlerHelper, Mockito.times(1)).handleExtendLockStatus(task, getHeaders());
        Mockito.verify(externalTaskEventHandlerHelper, Mockito.times(0)).handleFailureWithRetryStatus(task, getHeaders(), handler.getName());
    }

    @Test
    public void testExecuteSuccess_TaskStatusFailedWithRetry() throws Exception {
        ExternalTaskCompleted task =
                ExternalTaskCompleted.builder()
                        .status(ExternalTaskStatus.FAIL_WITH_RETRY.getStatus())
                        .variables(Collections.EMPTY_MAP)
                        .localVariables(Collections.EMPTY_MAP)
                        .build();
        handler.execute(task, getHeaders());
        Mockito.verify(externalTaskEventHandlerHelper, Mockito.times(0)).handleOtherStatus(task, getHeaders(), handler.getName());
        Mockito.verify(externalTaskEventHandlerHelper, Mockito.times(0)).handleExtendLockStatus(task, getHeaders());
        Mockito.verify(externalTaskEventHandlerHelper, Mockito.times(1)).handleFailureWithRetryStatus(task, getHeaders(), handler.getName());
    }



  @Test(expected = NullPointerException.class)
  public void testExecute_failure() throws Exception {
    ExternalTaskCompleted task =
        ExternalTaskCompleted.builder()
            .status(ExternalTaskStatus.SUCCESS.getStatus())
            .errorMessage("errorMessage")
            .build();
    Mockito.doThrow(NullPointerException.class).when(externalTaskEventHandlerHelper).handleOtherStatus(Mockito.any(), Mockito.any(), Mockito.eq(handler.getName()));
    handler.execute(task, getHeaders());
  }

  @Test
  public void testExecuteSuccessInterfaceTransformAndExecute() {

    ExternalTaskCompleted e =
        ExternalTaskCompleted.builder()
            .status(ExternalTaskStatus.SUCCESS.getStatus())
            .errorDetails("errorDetails")
            .errorMessage("errorMessage")
            .variables(Collections.EMPTY_MAP)
            .localVariables(Collections.EMPTY_MAP)
            .build();

    handler.transformAndExecute(ObjectConverter.toJson(e), getHeaders());
    Assert.assertEquals("success", e.getStatus());
    Assert.assertEquals("errorDetails", e.getErrorDetails());
    Assert.assertEquals("errorMessage", e.getErrorMessage());
  }

  private Map<String, String> getHeaders() {
    Map<String, String> headers = new HashMap<>();
    headers.put(EventHeaderConstants.ENTITY_ID, "taskId:workerId");
    headers.put(EventHeaderConstants.DOMAIN_EVENT, EventEntityType.EXTERNALTASK.getEntityType());

    return headers;
  }



  @Test
  public void testhandleFailure() throws Exception {
    Map<String, String> header = new HashMap<>();
    header.put(EventHeaderConstants.DOMAIN_EVENT, "externalTask");
    handler.handleFailure(null, header, new Exception());
    Mockito.verify(taskCompletionHandlerMock)
        .handleFailure(Mockito.anyMap(), Mockito.any(Exception.class));
  }



  @Test
  public void testeventEntityName() throws Exception {
    EventEntityType eventEntityType = handler.getName();
    Assert.assertEquals(EventEntityType.EXTERNALTASK, eventEntityType);
  }

}