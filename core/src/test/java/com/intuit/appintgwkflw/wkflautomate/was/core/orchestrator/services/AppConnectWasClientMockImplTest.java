package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services;

import static java.lang.Boolean.TRUE;

import com.intuit.appintgwkflw.wkflautomate.was.common.util.httpClient.AppConnectWASClient;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler.RestAction;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.AppConnectMockConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.mocks.AppconnectWasClientMockImpl;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.mocks.AppconnectWorkflowHeaderActionMockImpl;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.response.AppConnectFetchTransactionsResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.response.WorkflowTaskHandlerResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;

import org.junit.Assert;
import org.junit.Test;
import org.springframework.http.HttpStatus;

/** <AUTHOR> */
public class AppConnectWasClientMockImplTest {

  private AppConnectMockConfig appConnectMockConfig = new AppConnectMockConfig();
  private RestAction action = appConnectMockConfig.appConnectWASClientMock();
  private AppConnectWASClient wasClientMock = appConnectMockConfig.wasClient();

  @Test
  public void testHttpResponse() {
    WASHttpRequest<Object, WorkflowTaskHandlerResponse> wasHttpRequest =
        WASHttpRequest.<Object, WorkflowTaskHandlerResponse>builder().build();
    WASHttpResponse<WorkflowTaskHandlerResponse> response = action.execute(wasHttpRequest);

    Assert.assertTrue(response.isSuccess2xx());
    Assert.assertEquals(HttpStatus.OK, response.getStatus());
    Assert.assertEquals(response.getResponse().getSuccess(), TRUE.toString());
  }

  @Test
  public void testHttpResponseForWasClientMock() {
    WASHttpRequest<Object, WorkflowTaskHandlerResponse> wasHttpRequest =
        WASHttpRequest.<Object, WorkflowTaskHandlerResponse>builder()
            .url("http://localhost:8080/test")
            .build();
    WASHttpResponse<WorkflowTaskHandlerResponse> response =
        wasClientMock.httpResponse(wasHttpRequest);

    Assert.assertTrue(response.isSuccess2xx());
    Assert.assertEquals(HttpStatus.OK, response.getStatus());
    Assert.assertEquals(response.getResponse().getSuccess(), TRUE.toString());

    WASHttpRequest<Object, AppConnectFetchTransactionsResponse> wasHttpRequestCR =
        WASHttpRequest.<Object, AppConnectFetchTransactionsResponse>builder()
            .url("http://localhost:8080/custom-reminder-start-process")
            .build();
    WASHttpResponse<AppConnectFetchTransactionsResponse> responseCR =
        wasClientMock.httpResponse(wasHttpRequestCR);
    Assert.assertTrue(responseCR.isSuccess2xx());
    Assert.assertEquals(HttpStatus.OK, responseCR.getStatus());
    Assert.assertEquals(responseCR.getResponse().getSuccess(), TRUE.toString());
    Assert.assertEquals(responseCR.getResponse().getOutput().size(), 5);
  }
}
