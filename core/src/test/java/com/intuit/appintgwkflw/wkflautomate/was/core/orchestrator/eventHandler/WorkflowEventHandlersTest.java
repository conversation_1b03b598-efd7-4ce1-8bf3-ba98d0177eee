package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.eventHandler;

import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

public class WorkflowEventHandlersTest {

  @Mock private WorkflowEventHandler externalTaskEventHandler;

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
    Mockito.when(externalTaskEventHandler.getName()).thenReturn(EventEntityType.EXTERNALTASK);
    WorkflowEventHandlers.addHandler(externalTaskEventHandler.getName(), externalTaskEventHandler);
  }

  @Test
  public void getTestNull() {
    WorkflowEventHandler handler = WorkflowEventHandlers.getHandler(null);
    Assert.assertNull(handler);
  }

  @Test
  public void getTestAction() {
    WorkflowEventHandler handler = WorkflowEventHandlers.getHandler(EventEntityType.EXTERNALTASK);
    Assert.assertNotNull(handler);
  }

  @Test
  public void containsFalse() {
    Assert.assertFalse(WorkflowEventHandlers.contains(null));
  }

  @Test
  public void containsTrue() {
    Assert.assertTrue(WorkflowEventHandlers.contains(EventEntityType.EXTERNALTASK));
  }
}
