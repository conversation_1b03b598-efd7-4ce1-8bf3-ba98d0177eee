package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services;

import static org.mockito.ArgumentMatchers.any;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.EventScheduleConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.offlineticket.OfflineTicketClient;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.httpClient.EventScheduleClient;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.EventScheduleServiceUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.EventScheduleConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.model.EventScheduleWorkflowActionModel;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.respone.EventScheduleData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.respone.EventScheduleResponse;
import com.intuit.v4.common.RecurrenceRule;
import com.intuit.v4.payments.schedule.ScheduleStatus;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.joda.time.LocalDate;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpStatus;

public class EventSchedulerServiceImplTest {

  @Mock private EventScheduleConfig eventScheduleConfig;
  @Mock private EventScheduleClient eventScheduleClient;
  @Mock private OfflineTicketClient offlineTicketClient;

  @InjectMocks private EventScheduleServiceImpl eventScheduleService;

  private final String realmId = "123456789";

  @Before
  public void setUp() {
    MockitoAnnotations.initMocks(this);
    Mockito.when(eventScheduleConfig.getUrl())
        .thenReturn("https://paymentschedule-qal.api.intuit.com/v4/entities");
    Mockito.when(eventScheduleConfig.getTimezone())
        .thenReturn(EventScheduleConstants.TIMEZONE);
    Mockito.when(offlineTicketClient.getSystemOfflineHeadersForOfflineJob()).thenReturn("ticket");
  }

  @Test
  public void createSchedulesForInvalidInput() {
    try {
      eventScheduleService.createSchedules(null, realmId);
      Assert.fail();

    } catch (WorkflowGeneralException e) {
      Assert.assertEquals(e.getWorkflowError(), WorkflowError.INPUT_INVALID);
    }
  }

  @Test
  public void createSchedulesHappyCase() {

    EventScheduleResponse eventScheduleResponse = new EventScheduleResponse();
    EventScheduleData eventScheduleData = new EventScheduleData();
    eventScheduleData.setId("1234-id");
    eventScheduleData.setStatus(ScheduleStatus.ACTIVE);
    eventScheduleResponse.setData(List.of(eventScheduleData));
    Mockito.when(eventScheduleClient.httpResponse(any(WASHttpRequest.class)))
        .thenReturn(
            WASHttpResponse.builder()
                .status(HttpStatus.OK)
                .response(Collections.singletonList(eventScheduleResponse))
                .isSuccess2xx(true)
                .build());
    EventScheduleWorkflowActionModel eventScheduleWorkflowActionModel =
            new EventScheduleWorkflowActionModel("customReminder_customStart", new LocalDate("2099-1-2"), new RecurrenceRule());
    List<EventScheduleResponse> responses =
        eventScheduleService.createSchedules(
            EventScheduleServiceUtil.getCreateEventSchedulesPayload(
                List.of(eventScheduleWorkflowActionModel), eventScheduleConfig),
            realmId);
    Assert.assertEquals(
        responses.get(0).getData().get(0).getId(), eventScheduleResponse.getData().get(0).getId());
  }

  @Test
  public void createSchedulesWithInternalServerException() {

    EventScheduleResponse eventScheduleResponse = new EventScheduleResponse();
    EventScheduleData eventScheduleData = new EventScheduleData();
    eventScheduleData.setId("1234-id");
    eventScheduleData.setStatus(ScheduleStatus.ACTIVE);
    eventScheduleResponse.setData(List.of(eventScheduleData));
    Mockito.when(eventScheduleClient.httpResponse(any(WASHttpRequest.class)))
        .thenReturn(
            WASHttpResponse.builder()
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .response(null)
                .isSuccess2xx(false)
                .error("Internal server error")
                .build());
    try {
      EventScheduleWorkflowActionModel eventScheduleWorkflowActionModel =
              new EventScheduleWorkflowActionModel("customReminder_customStart", new LocalDate("2099-1-2"), new RecurrenceRule());
      eventScheduleService.createSchedules(
          EventScheduleServiceUtil.getCreateEventSchedulesPayload(
              List.of(eventScheduleWorkflowActionModel), eventScheduleConfig),
          realmId);
      Assert.fail();
    } catch (WorkflowGeneralException workflowGeneralException) {
      Assert.assertEquals(
          WorkflowError.EVENT_SCHEDULE_CALL_FAILURE, workflowGeneralException.getWorkflowError());
    }
  }

  @Test
  public void createSchedulesWithNullResponse() {

    EventScheduleResponse eventScheduleResponse = new EventScheduleResponse();
    EventScheduleData eventScheduleData = new EventScheduleData();
    eventScheduleData.setId("1234-id");
    eventScheduleData.setStatus(ScheduleStatus.ACTIVE);
    eventScheduleResponse.setData(List.of(eventScheduleData));
    Mockito.when(eventScheduleClient.httpResponse(any(WASHttpRequest.class)))
        .thenReturn(
            WASHttpResponse.builder()
                .status(HttpStatus.OK)
                .response(null)
                .isSuccess2xx(true)
                .error("Error")
                .build());
    try {
      EventScheduleWorkflowActionModel eventScheduleWorkflowActionModel =
              new EventScheduleWorkflowActionModel("customReminder_customStart", new LocalDate("2099-1-2"), new RecurrenceRule());
      eventScheduleService.createSchedules(
          EventScheduleServiceUtil.getCreateEventSchedulesPayload(
              List.of(eventScheduleWorkflowActionModel), eventScheduleConfig),
          realmId);
      Assert.fail();
    } catch (WorkflowGeneralException workflowGeneralException) {
      Assert.assertEquals(
          WorkflowError.EVENT_SCHEDULE_CALL_FAILURE, workflowGeneralException.getWorkflowError());
    }
  }

  @Test
  public void createSchedulesWithEmptyResponse() {

    EventScheduleResponse eventScheduleResponse = new EventScheduleResponse();
    EventScheduleData eventScheduleData = new EventScheduleData();
    eventScheduleData.setId("1234-id");
    eventScheduleData.setStatus(ScheduleStatus.ACTIVE);
    eventScheduleResponse.setData(List.of(eventScheduleData));
    Mockito.when(eventScheduleClient.httpResponse(any(WASHttpRequest.class)))
        .thenReturn(
            WASHttpResponse.builder()
                .status(HttpStatus.OK)
                .response(new ArrayList<>())
                .isSuccess2xx(true)
                .error("Error")
                .build());
    try {
      EventScheduleWorkflowActionModel eventScheduleWorkflowActionModel =
              new EventScheduleWorkflowActionModel("customReminder_customStart", new LocalDate("2099-1-2"), new RecurrenceRule());
      eventScheduleService.createSchedules(
          EventScheduleServiceUtil.getCreateEventSchedulesPayload(
              List.of(eventScheduleWorkflowActionModel), eventScheduleConfig),
          realmId);
      Assert.fail();
    } catch (WorkflowGeneralException workflowGeneralException) {
      Assert.assertEquals(
          WorkflowError.EVENT_SCHEDULE_CALL_FAILURE, workflowGeneralException.getWorkflowError());
    }
  }


  @Test
  public void updateSchedulesForInvalidInput() {
    try {
      eventScheduleService.updateSchedules(null, realmId);
      Assert.fail();

    } catch (WorkflowGeneralException e) {
      Assert.assertEquals(e.getWorkflowError(), WorkflowError.INPUT_INVALID);
    }
  }

  @Test
  public void updateSchedulesHappyCase() {

    EventScheduleResponse eventScheduleResponse = new EventScheduleResponse();
    EventScheduleData eventScheduleData = new EventScheduleData();
    eventScheduleData.setId("1234-id");
    eventScheduleData.setStatus(ScheduleStatus.ACTIVE);
    eventScheduleResponse.setData(List.of(eventScheduleData));
    Mockito.when(eventScheduleClient.httpResponse(any(WASHttpRequest.class)))
        .thenReturn(
            WASHttpResponse.builder()
                .status(HttpStatus.OK)
                .response(Collections.singletonList(eventScheduleResponse))
                .isSuccess2xx(true)
                .build());
    EventScheduleWorkflowActionModel eventScheduleWorkflowActionModel =
            new EventScheduleWorkflowActionModel("customReminder_customStart", new LocalDate("2099-1-2"), new RecurrenceRule());
    List<EventScheduleResponse> responses =
        eventScheduleService.updateSchedules(
            EventScheduleServiceUtil.getCreateEventSchedulesPayload(
                List.of(eventScheduleWorkflowActionModel), eventScheduleConfig),
            realmId);
    Assert.assertEquals(
        responses.get(0).getData().get(0).getId(), eventScheduleResponse.getData().get(0).getId());
  }
}
