package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services;

import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.mocks.EventScheduleServiceMockImpl;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.respone.EventScheduleResponse;
import java.util.List;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

public class EventScheduleServiceMockImplTest {

  @InjectMocks private EventScheduleServiceMockImpl eventScheduleService;

  @Before
  public void setup() {
    MockitoAnnotations.initMocks(this);
  }

  @Test
  public void testCreate() {
    List<EventScheduleResponse> eventScheduleResponseList =
        eventScheduleService.createSchedules(null, null);
    Assert.assertEquals(2, eventScheduleResponseList.size());
  }

  @Test
  public void testUpdate() {
    List<EventScheduleResponse> eventScheduleResponseList =
        eventScheduleService.updateSchedules(null, null);
    Assert.assertNull(eventScheduleResponseList);
  }
}
