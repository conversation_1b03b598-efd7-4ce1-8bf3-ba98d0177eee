package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import java.util.HashMap;
import java.util.Map;
import org.camunda.bpm.engine.variable.VariableMap;
import org.camunda.bpm.engine.variable.impl.VariableMapImpl;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * Modifies the WorkflowTask request of BATCH_NOTIFICATION_TASK
 *
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class BatchNotificationTaskRequestModifierTest {

  @Mock private NotificationTaskRequestModifier notificationTaskRequestModifier;

  @InjectMocks private BatchNotificationTaskRequestModifier batchNotificationTaskRequestModifier;

  @Test
  public void testGetName() {
    Assert.assertEquals(
        TaskType.BATCH_NOTIFICATION_TASK, batchNotificationTaskRequestModifier.getName());
  }

  @Test
  public void testGetTaskRequest() {
    WorkflowTaskRequest workflowTaskRequest = new WorkflowTaskRequest();
    Map<String, String> extensionProperties = new HashMap<>();
    extensionProperties.put(
        ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE,
        String.valueOf(TaskType.NOTIFICATION_TASK));
    extensionProperties.put("serviceName", "Workflow");

    Map<String, String> variableMap = new HashMap<>();
    variableMap.put("entityType", "Invoice");
    VariableMap variableMap1 = new VariableMapImpl();
    variableMap1.putAll(variableMap);

    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("actId1")
            .extensionProperties(extensionProperties)
            .processInstanceId("processInstance1")
            .taskId("taskId1")
            .ownerId(1l)
            .workerId("worker1")
            .variableMap(variableMap1)
            .inputVariables(variableMap)
            .build();
    Mockito.when(notificationTaskRequestModifier.getTaskRequest(Mockito.any(), Mockito.any()))
        .thenReturn(workflowTaskRequest);
    WorkflowTaskRequest taskRequest =
        batchNotificationTaskRequestModifier.getTaskRequest(
            workflowTaskRequest, workerActionRequest);
    Assert.assertEquals(workflowTaskRequest, taskRequest);
  }
}
