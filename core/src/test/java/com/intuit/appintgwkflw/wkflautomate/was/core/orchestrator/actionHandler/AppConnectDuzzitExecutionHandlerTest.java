package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.INVALID_PARAMETER_DETAILS;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.INTUIT_REALMID;
import static org.mockito.ArgumentMatchers.any;

import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.TaskHandlerName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import java.util.HashMap;
import java.util.Map;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

public class AppConnectDuzzitExecutionHandlerTest {

  @Mock private AppconnectDuzzitRestExecutionHelper appconnectDuzzitRestExecutionHelper;
  @Mock private MetricLogger metricLogger;
  @InjectMocks
  private AppConnectDuzzitExecutionHandler actionHandler;

  private static Map<String, String> schema = new HashMap<>();

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
    ReflectionTestUtils.setField(actionHandler, "metricLogger", metricLogger);
  }

  private static final String parametersSchema =
      TestHelper.readResourceAsString("schema/testData/parameters.json");

  static {
    schema.put(WorkFlowVariables.PARAMETERS_KEY.getName(), parametersSchema);
    schema.put(INTUIT_REALMID, "1234");
  }

  @Test
  public void testGetName() {
    Assert.assertEquals(TaskHandlerName.EXECUTE_APPCONNECT_DUZZIT_HANDLER,actionHandler.getName());
  }


  @Test
  public void testRestExecutionSuccess() {
    String handlerId = "hId";

    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId(handlerId)
            .taskId("externalTaskId")
            .ownerId(1234L)
            .build();

    Map<String, Object> res = new HashMap<>();
    res.put(
        workerActionRequest.getActivityId()
            + WorkflowConstants.UNDERSCORE
            + WorkFlowVariables.RESPONSE.getName(),
        "true");

    Mockito.when(appconnectDuzzitRestExecutionHelper.executeWorkflowActionRequest(any(), any())).thenReturn(res);

    Map<String, Object> resp = actionHandler.executeAction(workerActionRequest);

    Assert.assertTrue(
        resp.get(
                workerActionRequest.getActivityId()
                    + WorkflowConstants.UNDERSCORE
                    + WorkFlowVariables.RESPONSE.getName())
            .equals("true"));
  }


  @Test
  public void testRestExecutionFailure() {
    String handlerId = "hId";

    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId(handlerId)
            .taskId("externalTaskId")
            .ownerId(1234L)
            .build();

    Map<String, Object> res = new HashMap<>();
    res.put(
        workerActionRequest.getActivityId()
            + WorkflowConstants.UNDERSCORE
            + WorkFlowVariables.RESPONSE.getName(),
        "false");

    Mockito.when(appconnectDuzzitRestExecutionHelper.executeWorkflowActionRequest(any(), any())).thenReturn(res);

    Map<String, Object> resp = actionHandler.executeAction(workerActionRequest);

    Assert.assertTrue(
        resp.get(
                workerActionRequest.getActivityId()
                    + WorkflowConstants.UNDERSCORE
                    + WorkFlowVariables.RESPONSE.getName())
            .equals("false"));
  }

  @Test
  public void testInvalidParameterDetails() {
    String handlerId = "hId";

    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(new HashMap<>())
            .handlerId(handlerId)
            .taskId("externalTaskId")
            .ownerId(1234L)
            .build();

    Map<String, Object> res = new HashMap<>();
    res.put(
        workerActionRequest.getActivityId()
            + WorkflowConstants.UNDERSCORE
            + WorkFlowVariables.RESPONSE.getName(),
        "false");

    Mockito.when(appconnectDuzzitRestExecutionHelper.executeWorkflowActionRequest(any(), any())).thenReturn(res);

    try {
      actionHandler.executeAction(workerActionRequest);
      Assert.fail("The above method should throw exception");
    } catch (WorkflowGeneralException workflowGeneralException) {
      Assert.assertTrue(
          workflowGeneralException.getWorkflowError().equals(INVALID_PARAMETER_DETAILS)
      );
    }
  }


  @Test(expected = Exception.class)
  public void testExecuteAppActionExecutionFailure() {
    String handlerId = "hId";

    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId(handlerId)
            .taskId("externalTaskId")
            .ownerId(1234L)
            .build();

    Map<String, Object> res = new HashMap<>();
    res.put(
        workerActionRequest.getActivityId()
            + WorkflowConstants.UNDERSCORE
            + WorkFlowVariables.RESPONSE.getName(),
        "false");

    Mockito.when(appconnectDuzzitRestExecutionHelper.executeWorkflowActionRequest(any(), any())).thenThrow(new Exception());
    actionHandler.executeAction(workerActionRequest);
  }

  @Test
  public void test_execute_logMetric() {
    String handlerId = "hId";

    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId(handlerId)
            .taskId("externalTaskId")
            .ownerId(1234L)
            .build();

    Map<String, Object> res = new HashMap<>();
    res.put(
        workerActionRequest.getActivityId()
            + WorkflowConstants.UNDERSCORE
            + WorkFlowVariables.RESPONSE.getName(),
        "false");
    Mockito.when(appconnectDuzzitRestExecutionHelper.executeWorkflowActionRequest(any(), any())).thenThrow(new WorkflowGeneralException(WorkflowError.INPUT_INVALID));
    try {
      actionHandler.execute(workerActionRequest);
      Assert.fail();
    } catch (WorkflowGeneralException workflowGeneralException) {
      Assert.assertEquals(WorkflowError.INVALID_INPUT, workflowGeneralException.getWorkflowError());
      Mockito.verify(metricLogger, Mockito.times(1))
          .logErrorMetric(any(), any(), any());
    }
  }
}
