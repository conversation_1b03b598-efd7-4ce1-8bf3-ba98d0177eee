package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders.TemplateBuilderTestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Action;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.ActionGroup;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Attribute;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.ConfigTemplate;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Parameter;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Record;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.UIActions;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.ParameterDetailsValueType;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.service.TranslationService;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.apache.commons.lang3.BooleanUtils;
import org.junit.Assert;
import org.junit.Test;

public class CustomWorkflowConfigTest {
  public static String DICTIONARY_PATH = "schema/testData/dictionary.yaml";
  public static String YAML_KEY = "templateConfig";
  public static String ENTITY_INVOICE = "invoice";
  public static String ENTITY_BILL = "bill";
  public static String APPROVAL_ACTION = "approval";
  public static String PRECANNED_TEMPLATE_ID = "invoiceapproval-multicondition";
  public static String REMINDER_PRECANNED_TEMPLATE_ID = "invoiceUnsentReminder";

  public static String BILL_REMINDER_PRECANNED_TEMPLATE_ID = "billVendorReminder";
  public static String ESTIMATE_REMINDER_PRECANNED_TEMPLATE_ID = "estimateUnsentReminder";
  public static String CALL_ACTIVITY_ID = "sendForApproval";
  private final static String DUMMY_TEXT = "dummy";
  private final static String TXNAPPROVALSTATUS = "txnApprovalStatus";
  private final static String CC = "cc";
  private final static String INVOICE = "Invoice";
  private final static int FIRST_VALUE = 0;
  private final TranslationService translationService = TestHelper.initTranslationService();

  @Test
  public void testConfigMapCreationFailed() {
    OldCustomWorkflowConfig oldCustomWorkflowConfig = new OldCustomWorkflowConfig();
    oldCustomWorkflowConfig.afterPropertiesSet();
    CustomConfigV2 customConfigV2 = new CustomConfigV2();
    CustomWorkflowConfig customWorkflowConfig = new CustomWorkflowConfig();
    customWorkflowConfig.setCustomWorkflowConfigFactory(
        new CustomWorkflowConfigFactory(
            oldCustomWorkflowConfig, customConfigV2, new MigratedConfig(Set.of(), Set.of())));
    customWorkflowConfig.setOldConfig(oldCustomWorkflowConfig);
    customWorkflowConfig.getCustomWorkflowConfigFactory().afterPropertiesSet();
    customWorkflowConfig.getOldConfig().setAttributes(null);
    Assert.assertFalse(customWorkflowConfig.getOldConfig().populateRecordConfig());
  }

  @Test
  public void testConfigMapCreatedSuccessfully() throws Exception {
    OldCustomWorkflowConfig oldCustomWorkflowConfig =
        new ObjectMapper(new YAMLFactory())
            .readValue(
                TestHelper.readResourceAsString(DICTIONARY_PATH),
                new TypeReference<Map<String, OldCustomWorkflowConfig>>() {})
            .get(YAML_KEY);
    oldCustomWorkflowConfig.afterPropertiesSet();
    CustomConfigV2 customConfigV2 = new CustomConfigV2();
    CustomWorkflowConfig customWorkflowConfig = new CustomWorkflowConfig();
    customWorkflowConfig.setCustomWorkflowConfigFactory(
        new CustomWorkflowConfigFactory(
            oldCustomWorkflowConfig, customConfigV2, new MigratedConfig(Set.of(), Set.of())));
    customWorkflowConfig.setOldConfig(oldCustomWorkflowConfig);
    customWorkflowConfig.getCustomWorkflowConfigFactory().afterPropertiesSet();

    Assert.assertTrue(customWorkflowConfig.getOldConfig().populateRecordConfig());
    Record invoice = customWorkflowConfig.getRecordObjForType("Invoice");
  }

  @Test
  public void testTriggerHandler() throws Exception {
    OldCustomWorkflowConfig oldCustomWorkflowConfig =
        new ObjectMapper(new YAMLFactory())
            .readValue(
                TestHelper.readResourceAsString(DICTIONARY_PATH),
                new TypeReference<Map<String, OldCustomWorkflowConfig>>() {})
            .get(YAML_KEY);
    oldCustomWorkflowConfig.afterPropertiesSet();
    CustomConfigV2 customConfigV2 = new CustomConfigV2();
    CustomWorkflowConfig customWorkflowConfig = new CustomWorkflowConfig();
    customWorkflowConfig.setCustomWorkflowConfigFactory(
        new CustomWorkflowConfigFactory(
            oldCustomWorkflowConfig, customConfigV2, new MigratedConfig(Set.of(), Set.of())));
    customWorkflowConfig.setOldConfig(oldCustomWorkflowConfig);
    customWorkflowConfig.getCustomWorkflowConfigFactory().afterPropertiesSet();

    Assert.assertTrue(customWorkflowConfig.getOldConfig().populateRecordConfig());
    Record invoice = customWorkflowConfig.getRecordObjForType("Invoice");
    ActionGroup reminder = invoice.getActionGroups().stream()
        .filter(actionGroup -> actionGroup.getId()
            .equalsIgnoreCase("reminder")).findFirst().get();
    Assert.assertEquals("Scheduled", reminder.getTrigger().getType());
    Assert.assertEquals("appconnect1",
        reminder.getTrigger().getHandler().getHandlerDetail().getTaskHandler());
    Assert.assertEquals("intuit-workflows/custom-reminder-start-process",
        reminder.getTrigger().getHandler().getHandlerDetail().getHandlerId());
  }

  @Test
  public void testMergeAndRemoveDuplicates() throws Exception {
    OldCustomWorkflowConfig oldCustomWorkflowConfig =
        new ObjectMapper(new YAMLFactory())
            .readValue(
                TestHelper.readResourceAsString(DICTIONARY_PATH),
                new TypeReference<Map<String, OldCustomWorkflowConfig>>() {})
            .get(YAML_KEY);
    oldCustomWorkflowConfig.afterPropertiesSet();
    CustomConfigV2 customConfigV2 = new CustomConfigV2();
    CustomWorkflowConfig customWorkflowConfig = new CustomWorkflowConfig();
    customWorkflowConfig.setCustomWorkflowConfigFactory(
        new CustomWorkflowConfigFactory(
            oldCustomWorkflowConfig, customConfigV2, new MigratedConfig(Set.of(), Set.of())));
    customWorkflowConfig.setOldConfig(oldCustomWorkflowConfig);
    customWorkflowConfig.getCustomWorkflowConfigFactory().afterPropertiesSet();
    Assert.assertTrue(customWorkflowConfig.getOldConfig().populateRecordConfig());
    List<String> actionHelpVariables =
        customWorkflowConfig
            .getRecords()
            .get(1)
            .getActionGroups()
            .get(1)
            .getActions()
            .get(1)
            .getHelpVariables();
    Assert.assertEquals(1, actionHelpVariables.size());
    Assert.assertEquals("Created By:intuit_userid:string", actionHelpVariables.get(0));
    List<String> parameterHelpVariables =
        customWorkflowConfig
            .getRecords()
            .get(1)
            .getActionGroups()
            .get(1)
            .getActions()
            .get(1)
            .getParameters()
            .get(3)
            .getHelpVariables();
    Assert.assertEquals(10, parameterHelpVariables.size());
    List<String> invoiceSubjectParameterHelpVariables =
        customWorkflowConfig
            .getRecords()
            .get(1)
            .getActionGroups()
            .get(1)
            .getActions()
            .get(1)
            .getParameters()
            .get(5)
            .getHelpVariables();
    Assert.assertEquals(11, invoiceSubjectParameterHelpVariables.size());
    List<String> billMessageParameterHelpVariables =
        customWorkflowConfig
            .getRecords()
            .get(2)
            .getActionGroups()
            .get(1)
            .getActions()
            .get(1)
            .getParameters()
            .get(3)
            .getHelpVariables();
    Assert.assertEquals(10, billMessageParameterHelpVariables.size());

  }

  @Test
  public void testEmptyGlobalActionParamList() throws Exception {
    OldCustomWorkflowConfig oldCustomWorkflowConfig =
        new ObjectMapper(new YAMLFactory())
            .readValue(
                TestHelper.readResourceAsString(DICTIONARY_PATH),
                new TypeReference<Map<String, OldCustomWorkflowConfig>>() {})
            .get(YAML_KEY);
    oldCustomWorkflowConfig.afterPropertiesSet();
    CustomConfigV2 customConfigV2 = new CustomConfigV2();
    CustomWorkflowConfig customWorkflowConfig = new CustomWorkflowConfig();
    customWorkflowConfig.setCustomWorkflowConfigFactory(
        new CustomWorkflowConfigFactory(
            oldCustomWorkflowConfig, customConfigV2, new MigratedConfig(Set.of(), Set.of())));
    customWorkflowConfig.setOldConfig(oldCustomWorkflowConfig);
    customWorkflowConfig.getCustomWorkflowConfigFactory().afterPropertiesSet();
    Assert.assertTrue(customWorkflowConfig.getOldConfig().populateRecordConfig());
    Record statement = customWorkflowConfig.getRecordObjForType("Statement");
    ActionGroup scheduledActions = statement.getActionGroups().stream()
        .filter(actionGroup -> actionGroup.getId()
            .equalsIgnoreCase("scheduledActions")).findFirst().get();
    Assert.assertEquals(1, scheduledActions.getActions().size());
    Assert.assertEquals("scheduledAction", scheduledActions.getActions().get(0).getId());
    Assert.assertEquals(3, scheduledActions.getActions().get(0).getParameters().size());
    Parameter statementType = scheduledActions.getActions().get(0).getParameters().stream()
        .filter(parameter -> parameter.getName().equalsIgnoreCase("StatementType"))
        .findFirst().orElse(null);
    Assert.assertNotNull(statementType);
    Assert.assertEquals(ParameterDetailsValueType.PROCESS_VARIABLE, statementType.getValueType());
  }

  @Test
  public void testCopyAttributeAndParameter() throws Exception {
    OldCustomWorkflowConfig oldCustomWorkflowConfig =
        new ObjectMapper(new YAMLFactory())
            .readValue(
                TestHelper.readResourceAsString(DICTIONARY_PATH),
                new TypeReference<Map<String, OldCustomWorkflowConfig>>() {})
            .get(YAML_KEY);
    oldCustomWorkflowConfig.afterPropertiesSet();
    CustomConfigV2 customConfigV2 = new CustomConfigV2();
    CustomWorkflowConfig customWorkflowConfig = new CustomWorkflowConfig();
    customWorkflowConfig.setCustomWorkflowConfigFactory(
        new CustomWorkflowConfigFactory(
            oldCustomWorkflowConfig, customConfigV2, new MigratedConfig(Set.of(), Set.of())));
    customWorkflowConfig.setOldConfig(oldCustomWorkflowConfig);
    customWorkflowConfig.getCustomWorkflowConfigFactory().afterPropertiesSet();
    Attribute attribute = new Attribute();
    attribute.setId(TXNAPPROVALSTATUS);
    attribute.setName(DUMMY_TEXT);
    attribute.setType(DUMMY_TEXT);
    attribute.setDefaultOperator(DUMMY_TEXT);
    attribute.setDefaultValue(DUMMY_TEXT);
    attribute.setMultiSelect(true);
    attribute.setRequired(true);
    attribute.setConfigurable(true);
    attribute.setFieldValueOptions(DUMMY_TEXT);
    List<String> lists = new ArrayList<>();
    lists.add(DUMMY_TEXT);
    attribute.setUnsupportedOperators(lists);
    List<Attribute> list = Collections.singletonList(attribute);
    customWorkflowConfig.getRecords().get(0).setAttributes(list);
    Parameter parameter = new Parameter();
    parameter.setId(CC);
    parameter.setName(DUMMY_TEXT);
    parameter.setFieldType(DUMMY_TEXT);
    parameter.setPossibleFieldValues(lists);
    parameter.setHandlerFieldName(DUMMY_TEXT);
    parameter.setConfigurable(true);
    parameter.setRequiredByHandler(true);
    parameter.setRequiredByUI(true);
    parameter.setHelpVariablesRequired(true);
    parameter.setMultiSelect(true);
    parameter.setActionByUI(UIActions.GET_ADMINS_ID);
    parameter.setValueType(ParameterDetailsValueType.PROCESS_VARIABLE);
    parameter.setGetOptionsForFieldValue(DUMMY_TEXT);
    List<Parameter> parameterList = Collections.singletonList(parameter);
    customWorkflowConfig.getOldConfig().getActions().get(FIRST_VALUE).setParameters(parameterList);
    Assert.assertTrue(customWorkflowConfig.getOldConfig().populateRecordConfig());
  }

  @Test
  public void testConfigForInvoice() throws Exception {
    CustomWorkflowConfig customWorkflowConfig = TemplateBuilderTestHelper.getConfig();
    Record invoice = customWorkflowConfig.getRecordObjForType("Invoice");
    Assert.assertEquals(19, invoice.getAttributes().size());
    Attribute attribute = getRecordAttribute(invoice, "txnApprovalStatus");
    Assert.assertEquals("list", attribute.getType());
    Assert.assertEquals("TxnApprovalStatus", attribute.getName());
    Assert.assertEquals("GET_APPROVAL_STATUS", attribute.getFieldValueOptions());

    ActionGroup actionGroup = invoice.getActionGroups().get(0);

    Assert.assertNull(actionGroup.getPrecannedTemplateId());
    Assert.assertNotNull(actionGroup.getActionIdMapper());

    Assert.assertEquals(5, actionGroup.getActions().size());

    //Create Task assertion
    Action action = getActionFromGroup(actionGroup, "createTask");
    Assert.assertEquals("Create a task", action.getName());

    // Test handler
    Assert.assertEquals(
        "intuit-workflows/taskmanager-create-task",
        action.getHandler().getHandlerDetail().getHandlerId());
    Assert.assertEquals(
        "executeWorkflowAction", action.getHandler().getHandlerDetail().getActionName());
    Assert.assertEquals("appconnect", action.getHandler().getHandlerDetail().getTaskHandler());
    Assert.assertEquals(
        "projectId", action.getHandler().getHandlerDetail().getResponseFields().get(0));

    Parameter parameter = getRecordAttribute(action, "ProjectType");
    Assert.assertEquals("string", parameter.getFieldType());

    parameter = getRecordAttribute(action, "intuit_realmid");
    Assert.assertEquals(ParameterDetailsValueType.PROCESS_VARIABLE, parameter.getValueType());
    Assert.assertEquals("RealmId", parameter.getHandlerFieldName());

    parameter = getRecordAttribute(action, "Assignee");
    Assert.assertEquals(UIActions.GET_ADMINS_ID, parameter.getActionByUI());

    parameter = getRecordAttribute(action, "TaskName");
    Assert.assertEquals(9, parameter.getHelpVariables().size());
    // helpVariable contains following in order separated by COLON
    // displayName : handlerFieldName : fieldType
    Assert.assertTrue(parameter.getHelpVariables().contains("Invoice Number:DocNumber:string"));
    Assert.assertTrue(parameter.getHelpVariables().contains("Balance:TxnBalanceAmount:double"));

    // for en-Us
    Assert.assertEquals(
        "Review Invoice [[Invoice Number]]",
        translationService.getFormattedString(
            parameter.getFieldValues().get(0), "en_US", RecordType.INVOICE.getDisplayValue()));

    // for fr-CA
    Assert.assertEquals(
        "Vérifier Facture [[Invoice Number]]",
        translationService.getFormattedString(
            parameter.getFieldValues().get(0), "fr_CA", RecordType.INVOICE.getDisplayValue()));
    parameter = getRecordAttribute(action, "CloseTask");
    Assert.assertEquals(3, parameter.getPossibleFieldValues().size());

    // External Email assertion
    action = getActionFromGroup(actionGroup, "sendExternalEmail");
    Assert.assertEquals("Send a customer email", action.getName());

    // Test handler
    Assert.assertEquals(
        "intuit-workflows/was-send-notification",
        action.getHandler().getHandlerDetail().getHandlerId());
    Assert.assertEquals(
        "executeWorkflowAction", action.getHandler().getHandlerDetail().getActionName());
    Assert.assertEquals("appconnect", action.getHandler().getHandlerDetail().getTaskHandler());
    Assert.assertNull(action.getHandler().getHandlerDetail().getResponseFields());

    parameter = getRecordAttribute(action, "SendTo");
    Assert.assertEquals(1, parameter.getHelpVariables().size());

    parameter = getRecordAttribute(action, "Subject");
    Assert
        .assertEquals("Invoice [[Invoice Number]] needs your attention",
            translationService.getFormattedString(parameter.getFieldValues().get(0), "en_US", RecordType.INVOICE.getDisplayValue()));
    Assert
        .assertEquals("Facture [[Invoice Number]] requiert votre attention.",
            translationService.getFormattedString(parameter.getFieldValues().get(0), "fr_CA", RecordType.INVOICE.getDisplayValue()));
    parameter = getRecordAttribute(action, "Message");
    Assert.assertEquals(
        "Hi [[Customer Name]],\n"
            + "\n"
            + "Invoice [[Invoice Number]] needs your attention. Please take a look at the attached invoice and contact us if you have any questions.\n"
            + "\n"
            + "Thanks,\n"
            + "[[Company Name]]",
        translationService.getFormattedString(
            parameter.getFieldValues().get(0), "en_US", RecordType.INVOICE.getDisplayValue()));

    Assert.assertEquals(
        "Bonjour [[Customer Name]],\n"
            + "\n"
            + "Facture [[Invoice Number]] requiert votre attention. Veuillez jeter un coup d’œil à (au) facture ci-joint(e) et communiquez avec nous si vous avez des questions.",
        translationService.getFormattedString(
            parameter.getFieldValues().get(0), "fr_CA", RecordType.INVOICE.getDisplayValue()));

    // Company Email assertion
    action = getActionFromGroup(actionGroup, "sendCompanyEmail");
    Assert.assertEquals("Send a company email", action.getName());

    // Test handler
    Assert.assertEquals(
        "intuit-workflows/was-send-notification",
        action.getHandler().getHandlerDetail().getHandlerId());
    Assert.assertEquals(
        "executeWorkflowAction", action.getHandler().getHandlerDetail().getActionName());
    Assert.assertEquals("appconnect", action.getHandler().getHandlerDetail().getTaskHandler());
    Assert.assertNull(action.getHandler().getHandlerDetail().getResponseFields());

    parameter = getRecordAttribute(action, "SendTo");
    Assert.assertEquals(1, parameter.getHelpVariables().size());

    parameter = getRecordAttribute(action, "Subject");
    Assert.assertEquals(
        "Review Invoice [[Invoice Number]]",
        translationService.getFormattedString(
            parameter.getFieldValues().get(0), "en_US", RecordType.INVOICE.getDisplayValue()));
    Assert.assertEquals(
        "Vérifier Facture [[Invoice Number]]",
        translationService.getFormattedString(
            parameter.getFieldValues().get(0), "fr_CA", RecordType.INVOICE.getDisplayValue()));
    parameter = getRecordAttribute(action, "Message");
    Assert.assertEquals(
        "Hi,\n"
            + "\n"
            + "Invoice [[Invoice Number]] needs your attention. Please take a look at the invoice and complete any outstanding tasks.\n"
            + "\n"
            + "Thanks,\n"
            + "[[Company Name]]",
        translationService.getFormattedString(
            parameter.getFieldValues().get(0), "en_US", RecordType.INVOICE.getDisplayValue()));

    Assert.assertEquals(
        "Bonjour,\n"
            + "\n"
            + "Facture [[Invoice Number]] requiert votre attention. Veuillez jeter un coup d’œil à (au) facture et terminer toutes les tâches restantes.\n"
            + "\n"
            + "Merci,\n"
            + "[[Company Name]]",
        translationService.getFormattedString(
            parameter.getFieldValues().get(0), "fr_CA", RecordType.INVOICE.getDisplayValue()));

    action = getActionFromGroup(actionGroup, "sendPushNotification");
    Assert.assertEquals("Send a push notification", action.getName());
    parameter = getRecordAttribute(action, "Subject");
    Assert.assertEquals(
        "An Invoice needs your attention",
        translationService.getFormattedString(
            parameter.getFieldValues().get(0), "en_US", RecordType.INVOICE.getDisplayValue()));
    Assert.assertEquals(
        "Une facture requiert votre attention",
        translationService.getFormattedString(
            parameter.getFieldValues().get(0), "fr_CA", RecordType.INVOICE.getDisplayValue()));
  }

  @Test
  public void testConfigForInvoiceApproval() throws Exception {
    CustomWorkflowConfig customWorkflowConfig = TemplateBuilderTestHelper.getConfig();
    Record invoice = customWorkflowConfig.getRecordObjForType("Invoice");
    Assert.assertEquals(19, invoice.getAttributes().size());
    Attribute attribute = getRecordAttribute(invoice, "txnApprovalStatus");
    Assert.assertEquals("list", attribute.getType());
    Assert.assertEquals("TxnApprovalStatus", attribute.getName());
    Assert.assertEquals("GET_APPROVAL_STATUS", attribute.getFieldValueOptions());

    ActionGroup approvalActionGroup = invoice.getActionGroups().stream().filter(group ->
            group.getId().equalsIgnoreCase(APPROVAL_ACTION)).findFirst().orElse(null);
    Assert.assertNotNull(approvalActionGroup.getPrecannedTemplateId());
    Assert.assertEquals(PRECANNED_TEMPLATE_ID, approvalActionGroup.getPrecannedTemplateId());
    Assert.assertNotNull(approvalActionGroup.getActionIdMapper());
    Assert.assertEquals(CALL_ACTIVITY_ID, approvalActionGroup.getActionIdMapper().getActionId());
    Assert.assertNotNull(approvalActionGroup.getActionIdMapper().getSubActionIds());
    Assert.assertEquals(3, approvalActionGroup.getActions().size());

    Action action = getActionFromGroup(approvalActionGroup, "createTask");
    Assert.assertEquals("Create a task", action.getName());

    // Test handler
    Assert.assertEquals(
            "intuit-workflows/taskmanager-create-task",
            action.getHandler().getHandlerDetail().getHandlerId());
    Assert.assertEquals(
            "executeWorkflowAction", action.getHandler().getHandlerDetail().getActionName());
    Assert.assertEquals("appconnect", action.getHandler().getHandlerDetail().getTaskHandler());
    Assert.assertEquals(
            "projectId", action.getHandler().getHandlerDetail().getResponseFields().get(0));

    Parameter parameter = getRecordAttribute(action, "ProjectType");
    Assert.assertEquals("string", parameter.getFieldType());

    parameter = getRecordAttribute(action, "intuit_realmid");
    Assert.assertEquals(ParameterDetailsValueType.PROCESS_VARIABLE, parameter.getValueType());
    Assert.assertEquals("RealmId", parameter.getHandlerFieldName());

    parameter = getRecordAttribute(action, "Assignee");
    Assert.assertEquals(UIActions.GET_ADMINS_ID, parameter.getActionByUI());

    parameter = getRecordAttribute(action, "TaskName");
    Assert.assertEquals(9, parameter.getHelpVariables().size());
    // helpVariable contains following in order separated by COLON
    // displayName : handlerFieldName : fieldType
    Assert.assertTrue(parameter.getHelpVariables().contains("Invoice Number:DocNumber:string"));
    Assert.assertTrue(parameter.getHelpVariables().contains("Balance:TxnBalanceAmount:double"));

    Assert.assertEquals(
            "Review Invoice [[Invoice Number]]",
            translationService.getFormattedString(
                    parameter.getFieldValues().get(0), "en_US", RecordType.INVOICE.getDisplayValue()));

    // for fr-CA
    Assert.assertEquals(
            "Vérifier Facture [[Invoice Number]]",
            translationService.getFormattedString(
                    parameter.getFieldValues().get(0), "fr_CA", RecordType.INVOICE.getDisplayValue()));
    parameter = getRecordAttribute(action, "CloseTask");

    // External Email assertion
    action = getActionFromGroup(approvalActionGroup, "sendCompanyEmail");
    Assert.assertEquals("Send a company email", action.getName());

    // Test handler
    Assert.assertEquals(
            "intuit-workflows/was-send-notification",
            action.getHandler().getHandlerDetail().getHandlerId());
    Assert.assertEquals(
            "executeWorkflowAction", action.getHandler().getHandlerDetail().getActionName());
    Assert.assertEquals("appconnect", action.getHandler().getHandlerDetail().getTaskHandler());
    Assert.assertNull(action.getHandler().getHandlerDetail().getResponseFields());

    parameter = getRecordAttribute(action, "SendTo");
    Assert.assertEquals(1, parameter.getHelpVariables().size());

    parameter = getRecordAttribute(action, "Subject");
    Assert
            .assertEquals("Review Invoice [[Invoice Number]]",
                    translationService.getFormattedString(parameter.getFieldValues().get(0), "en_US", RecordType.INVOICE.getDisplayValue()));
    Assert
            .assertEquals("Vérifier Facture [[Invoice Number]]",
                    translationService.getFormattedString(parameter.getFieldValues().get(0), "fr_CA", RecordType.INVOICE.getDisplayValue()));
    parameter = getRecordAttribute(action, "Message");
  }
  @Test
  public void testConfigForBill() throws Exception {
    CustomWorkflowConfig customWorkflowConfig = TemplateBuilderTestHelper.getConfig();
    Record bill = customWorkflowConfig.getRecordObjForType("Bill");
    Assert.assertEquals(10, bill.getAttributes().size());
    Attribute attribute = getRecordAttribute(bill, "TxnUpdateStatus");
    Assert.assertEquals("list", attribute.getType());
    Assert.assertEquals("TxnUpdateStatus", attribute.getName());
    Assert.assertEquals("GET_UPDATE_STATUS", attribute.getFieldValueOptions());
    Assert.assertNull(attribute.getHidden());

    attribute = getRecordAttribute(bill, "userId");
    Assert.assertEquals("string", attribute.getType());
    Assert.assertEquals("intuit_userid", attribute.getName());
    Assert.assertTrue(attribute.getHidden());

    ActionGroup actionGroup = bill.getActionGroups().get(0);
    Assert.assertEquals(2, actionGroup.getActions().size());
    Action action = actionGroup.getActions().get(0);
    Assert.assertEquals("createTask", action.getId());
    Assert.assertEquals("Create a task", action.getName());

    Parameter parameter = getRecordAttribute(action, "ProjectType");
    Assert.assertEquals("string", parameter.getFieldType());

    parameter = getRecordAttribute(action, "intuit_realmid");
    Assert.assertEquals(ParameterDetailsValueType.PROCESS_VARIABLE, parameter.getValueType());
    Assert.assertEquals("RealmId", parameter.getHandlerFieldName());

    parameter = getRecordAttribute(action, "Assignee");
    Assert.assertEquals(UIActions.GET_ADMINS_ID, parameter.getActionByUI());

    parameter = getRecordAttribute(action, "TaskName");
    Assert.assertEquals(9, parameter.getHelpVariables().size());
    Assert.assertEquals(
        "Review Bill [[Bill Number]]",
        translationService.getFormattedString(
            parameter.getFieldValues().get(0), "en_US", RecordType.BILL.getDisplayValue()));
    Assert.assertEquals(
        "Vérifier Facture à payer [[Bill Number]]",
        translationService.getFormattedString(
            parameter.getFieldValues().get(0), "fr_CA", RecordType.BILL.getDisplayValue()));
    parameter = getRecordAttribute(action, "CloseTask");
    Assert.assertEquals(2, parameter.getPossibleFieldValues().size());

    action = getActionFromGroup(actionGroup, "sendCompanyEmail");
    Assert.assertEquals("Send a company email", action.getName());

    parameter = getRecordAttribute(action, "SendTo");
    Assert.assertEquals(1, parameter.getHelpVariables().size());

    parameter = getRecordAttribute(action, "Subject");
    Assert
        .assertEquals("Review Bill [[Bill Number]]",
            translationService.getFormattedString(
                parameter.getFieldValues().get(0), "en_US", RecordType.BILL.getDisplayValue()));
    Assert
        .assertEquals("Vérifier Facture à payer [[Bill Number]]",
            translationService.getFormattedString(
                parameter.getFieldValues().get(0), "fr_CA", RecordType.BILL.getDisplayValue()));

    parameter = getRecordAttribute(action, "Message");
    Assert.assertEquals(
        "Hi,\n"
            + "\n"
            + "Bill [[Bill Number]] needs your attention. Please take a look at the bill and complete any outstanding tasks.\n"
            + "\n"
            + "Thanks,\n"
            + "[[Company Name]]",
        translationService.getFormattedString(
            parameter.getFieldValues().get(0), "en_US", RecordType.BILL.getDisplayValue()));

    Assert.assertEquals(
        "Bonjour,\n"
            + "\n"
            + "Facture à payer [[Bill Number]] requiert votre attention. Veuillez jeter un coup d’œil à (au) facture à payer et terminer toutes les tâches restantes.\n"
            + "\n"
            + "Merci,\n"
            + "[[Company Name]]",
        translationService.getFormattedString(
            parameter.getFieldValues().get(0), "fr_CA", RecordType.BILL.getDisplayValue()));
   /** Test approval create task handler */
    actionGroup = bill.getActionGroups().get(1);
    Assert.assertEquals(2, actionGroup.getActions().size());
    action = actionGroup.getActions().get(0);
    Assert.assertEquals("createTask", action.getId());

    Assert.assertEquals(
        "intuit-workflows/was-create-task-and-add-constraint",
        action.getHandler().getHandlerDetail().getHandlerId());

    Assert.assertEquals(
        "executeWorkflowAction", action.getHandler().getHandlerDetail().getActionName());
    Assert.assertEquals("appconnect", action.getHandler().getHandlerDetail().getTaskHandler());
    Assert.assertEquals(3, action.getHandler().getHandlerDetail().getResponseFields().size());
  }


  @Test
  public void testConfigForEstimate() throws Exception {
    CustomWorkflowConfig customWorkflowConfig = TemplateBuilderTestHelper.getConfig();
    Record invoice = customWorkflowConfig.getRecordObjForType(RecordType.ESTIMATE.getRecordType());
    Assert.assertEquals(11, invoice.getAttributes().size());

    Attribute txnStatus = getRecordAttribute(invoice, "TxnStatus");
    Assert.assertEquals("GET_ESTIMATE_STATUS", txnStatus.getFieldValueOptions());

    ActionGroup actionGroup = invoice.getActionGroups().get(0);
    Assert.assertEquals(5, actionGroup.getActions().size());

    //Create Task assertion
    Action action = getActionFromGroup(actionGroup, "createTask");

    Parameter parameter = getRecordAttribute(action, "TaskName");
    Assert.assertEquals(9, parameter.getHelpVariables().size());
    Assert.assertEquals("Review Estimate [[Estimate Number]]", translationService.getFormattedString(parameter.getFieldValues().get(0), "en_US", RecordType.ESTIMATE.getDisplayValue()));
    Assert.assertEquals("Vérifier Devis [[Estimate Number]]", translationService.getFormattedString(parameter.getFieldValues().get(0), "fr_CA", RecordType.ESTIMATE.getDisplayValue()));
    parameter = getRecordAttribute(action, "CloseTask");
    Assert.assertEquals(3, parameter.getPossibleFieldValues().size());

    // External Email assertion
    action = getActionFromGroup(actionGroup, "sendExternalEmail");

    parameter = getRecordAttribute(action, "Subject");
    Assert
        .assertEquals("Estimate [[Estimate Number]] needs your attention",
           translationService.getFormattedString( parameter.getFieldValues().get(0),"en_US", RecordType.ESTIMATE.getDisplayValue()));

    Assert
        .assertEquals("Devis [[Estimate Number]] requiert votre attention.",
            translationService.getFormattedString( parameter.getFieldValues().get(0),"fr_CA", RecordType.ESTIMATE.getDisplayValue()));


    parameter = getRecordAttribute(action, "Message");
    Assert.assertEquals(
        "Hi [[Customer Name]],\n"
            + "\n"
            + "Estimate [[Estimate Number]] needs your attention. Please take a look at the estimate and contact us if you have any questions.\n"
            + "\n"
            + "Thanks,\n"
            + "[[Company Name]]",
        translationService.getFormattedString(
            parameter.getFieldValues().get(0), "en_US", RecordType.ESTIMATE.getDisplayValue()));

    Assert.assertEquals(
        "Bonjour [[Customer Name]],\n"
            + "\n"
            + "Devis [[Estimate Number]] requiert votre attention. Veuillez jeter un coup d’œil à (au)  et communiquez avec nous si vous avez des questions.",
        translationService.getFormattedString(
            parameter.getFieldValues().get(0), "fr_CA", RecordType.ESTIMATE.getDisplayValue()));

    parameter = getRecordAttribute(action, "SendAttachment");
    Assert
        .assertEquals("false", parameter.getFieldValues().get(0));

    // Company Email assertion
    action = getActionFromGroup(actionGroup, "sendCompanyEmail");
    Assert.assertEquals("Send a company email", action.getName());

    parameter = getRecordAttribute(action, "Subject");
    Assert.assertEquals(
        "Review Estimate [[Estimate Number]]",
        translationService.getFormattedString(
            parameter.getFieldValues().get(0), "en_US", RecordType.ESTIMATE.getDisplayValue()));

    Assert.assertEquals(
        "Vérifier Devis [[Estimate Number]]",
        translationService.getFormattedString(
            parameter.getFieldValues().get(0), "fr_CA", RecordType.ESTIMATE.getDisplayValue()));

    parameter = getRecordAttribute(action, "Message");
    Assert.assertEquals(
        "Hi,\n"
            + "\n"
            + "Estimate [[Estimate Number]] needs your attention. Please take a look at the estimate and complete any outstanding tasks.\n"
            + "\n"
            + "Thanks,\n"
            + "[[Company Name]]",
        translationService.getFormattedString(
            parameter.getFieldValues().get(0), "en_US", RecordType.ESTIMATE.getDisplayValue()));
    Assert.assertEquals(
        "Bonjour,\n"
            + "\n"
            + "Devis [[Estimate Number]] requiert votre attention. Veuillez jeter un coup d’œil à (au) devis et terminer toutes les tâches restantes.\n"
            + "\n"
            + "Merci,\n"
            + "[[Company Name]]",
        translationService.getFormattedString(
            parameter.getFieldValues().get(0), "fr_CA", RecordType.ESTIMATE.getDisplayValue()));
  }

  private Attribute getRecordAttribute(Record record, String attributeId) {
    return record.getAttributes().stream()
        .filter(attribute -> attribute.getId().equalsIgnoreCase(attributeId))
        .findFirst()
        .get();
  }

  private Parameter getRecordAttribute(Action action, String parameterName) {
    return action.getParameters().stream()
        .filter(parameter -> parameter.getName().equalsIgnoreCase(parameterName))
        .findFirst()
        .get();
  }

  private Action getActionFromGroup(ActionGroup actionGroup, String actionId) {
    return actionGroup.getActions().stream()
        .filter(action -> action.getId().equalsIgnoreCase(actionId))
        .findFirst()
        .get();
  }

  @Test
  public void testConfigurationDetails() throws Exception {
    CustomWorkflowConfig customWorkflowConfig = TemplateBuilderTestHelper.getConfig();
    Assert.assertNotNull(customWorkflowConfig);
    Assert.assertNotNull(customWorkflowConfig.getAttributes());
    Assert.assertNotNull(customWorkflowConfig.getRecords());
    Assert.assertNotNull(customWorkflowConfig.getOldConfig().getActionGroups());
    Assert.assertNotNull(customWorkflowConfig.getOldConfig().getActions());
    Assert.assertNotNull(customWorkflowConfig.getDataTypes());
    Assert.assertEquals(ENTITY_INVOICE, customWorkflowConfig.getRecords().get(1).getId());
    Assert.assertEquals(ENTITY_BILL, customWorkflowConfig.getRecords().get(2).getId());
  }

  @Test
  public void testOpenPOReminderTemplate() throws Exception {
    CustomWorkflowConfig customWorkflowConfig = TemplateBuilderTestHelper.getConfig();
    ConfigTemplate invoiceUnsentReminder = customWorkflowConfig.getConfigTemplates().stream()
        .filter(configTemplate -> configTemplate.getId().equalsIgnoreCase("openPurchaseorderReminder"))
        .findFirst().get();
    ActionGroup actionGroup = invoiceUnsentReminder.getActionGroups().get(0);
    Assert.assertNull(invoiceUnsentReminder.getSteps());
    Assert.assertEquals(3, actionGroup.getActions().size());
    Action sendCompanyEmail = getActionFromGroup(actionGroup, "sendCompanyEmail");
    Assert.assertEquals(true, sendCompanyEmail.getSelected());
    Action createTask = getActionFromGroup(actionGroup, "createTask");
    Assert.assertEquals(true, createTask.getSelected());
    Action sendExternalEmail = getActionFromGroup(actionGroup, "sendExternalEmail");
    Assert.assertEquals(false, BooleanUtils.toBoolean(sendExternalEmail.getSelected()));
  }

  @Test
  public void testInvoiceApprovalMultiConditionPrecannedTemplate() throws Exception {
    CustomWorkflowConfig customWorkflowConfig = TemplateBuilderTestHelper.getConfig();
    ConfigTemplate invoiceapproval = customWorkflowConfig.getConfigTemplates().stream()
            .filter(configTemplate -> configTemplate.getId().equalsIgnoreCase(PRECANNED_TEMPLATE_ID))
            .findFirst().get();
    ActionGroup actionGroup = invoiceapproval.getActionGroups().get(0);
    Assert.assertNotNull(invoiceapproval.getSteps());
    Assert.assertEquals(6, invoiceapproval.getSteps().size());
    Assert.assertEquals(1, invoiceapproval.getSteps().get(0).getStepId().intValue());
    Assert.assertEquals("condition", invoiceapproval.getSteps().get(0).getStepType());
    Assert.assertNotNull(invoiceapproval.getSteps().get(0).getAttributes());
    Assert.assertNotNull(invoiceapproval.getSteps().get(0).getAttributes().get(0).getId());
    Assert.assertNotNull(invoiceapproval.getSteps().get(0).getAttributes().get(0).getName());
    Assert.assertNotNull(invoiceapproval.getSteps().get(0).getAttributes().get(0).getType());
    Assert.assertNotNull(invoiceapproval.getSteps().get(0).getAttributes().get(0).getDefaultValue());
    Assert.assertNotNull(invoiceapproval.getSteps().get(0).getNexts());
    Assert.assertNotNull(invoiceapproval.getSteps().get(0).getNexts().get(0).getStepId());
    Assert.assertNotNull(invoiceapproval.getSteps().get(0).getNexts().get(0).getLabel());
    Assert.assertEquals(3, actionGroup.getActions().size());
    Action sendCompanyEmail = getActionFromGroup(actionGroup, "sendCompanyEmail");
    Assert.assertEquals(true, sendCompanyEmail.getSelected());
    Action createTask = getActionFromGroup(actionGroup, "createTask");
    Assert.assertEquals(true, createTask.getSelected());
  }

  @Test
  public void testInvoiceReminderPrecannedTemplate() throws Exception {
    CustomWorkflowConfig customWorkflowConfig = TemplateBuilderTestHelper.getConfig();
    ConfigTemplate invoicereminder = customWorkflowConfig.getConfigTemplates().stream()
            .filter(configTemplate -> configTemplate.getId().equalsIgnoreCase(REMINDER_PRECANNED_TEMPLATE_ID))
            .findFirst().get();
    ActionGroup actionGroup = invoicereminder.getActionGroups().get(0);
    Assert.assertNull(invoicereminder.getSteps());
    Assert.assertEquals(5, actionGroup.getActions().size());
    Action sendCompanyEmail = getActionFromGroup(actionGroup, "sendCompanyEmail");
    Assert.assertEquals(true, sendCompanyEmail.getSelected());
    Action createTask = getActionFromGroup(actionGroup, "createTask");
    Assert.assertEquals(true, createTask.getSelected());
  }

  @Test
  public void testBillReminderPrecannedTemplate() throws Exception {
    CustomWorkflowConfig customWorkflowConfig = TemplateBuilderTestHelper.getConfig();
    ConfigTemplate invoicereminder = customWorkflowConfig.getConfigTemplates().stream()
            .filter(configTemplate -> configTemplate.getId().equalsIgnoreCase(BILL_REMINDER_PRECANNED_TEMPLATE_ID))
            .findFirst().get();
    ActionGroup actionGroup = invoicereminder.getActionGroups().get(0);
    Assert.assertNull(invoicereminder.getSteps());
    Assert.assertEquals(2, actionGroup.getActions().size());
    Action sendCompanyEmail = getActionFromGroup(actionGroup, "sendCompanyEmail");
    Assert.assertEquals(true, sendCompanyEmail.getSelected());
    Action createTask = getActionFromGroup(actionGroup, "createTask");
    Assert.assertEquals(true, createTask.getSelected());
  }

  @Test
  public void testEstimateReminderPrecannedTemplate() throws Exception {
    CustomWorkflowConfig customWorkflowConfig = TemplateBuilderTestHelper.getConfig();
    ConfigTemplate invoicereminder = customWorkflowConfig.getConfigTemplates().stream()
            .filter(configTemplate -> configTemplate.getId().equalsIgnoreCase(ESTIMATE_REMINDER_PRECANNED_TEMPLATE_ID))
            .findFirst().get();
    ActionGroup actionGroup = invoicereminder.getActionGroups().get(0);
    Assert.assertNull(invoicereminder.getSteps());
    Assert.assertEquals(5, actionGroup.getActions().size());
    Action sendCompanyEmail = getActionFromGroup(actionGroup, "sendCompanyEmail");
    Assert.assertEquals(true, sendCompanyEmail.getSelected());
    Action createTask = getActionFromGroup(actionGroup, "createTask");
    Assert.assertEquals(true, createTask.getSelected());
  }

  @Test
  public void testTriggerInPrecannedTemplateSuccess() throws Exception {
    OldCustomWorkflowConfig oldCustomWorkflowConfig =
        new ObjectMapper(new YAMLFactory())
            .readValue(
                TestHelper.readResourceAsString(DICTIONARY_PATH),
                new TypeReference<Map<String, OldCustomWorkflowConfig>>() {})
            .get(YAML_KEY);

    CustomConfigV2 customConfigV2 = new CustomConfigV2();
    CustomWorkflowConfig customWorkflowConfig = new CustomWorkflowConfig();
    customWorkflowConfig.setCustomWorkflowConfigFactory(
        new CustomWorkflowConfigFactory(
            oldCustomWorkflowConfig, customConfigV2, new MigratedConfig(Set.of(), Set.of())));
    customWorkflowConfig.setOldConfig(oldCustomWorkflowConfig);
    customWorkflowConfig.getCustomWorkflowConfigFactory().afterPropertiesSet();
    Assert.assertTrue(customWorkflowConfig.getOldConfig().populateTemplateConfig());
    ConfigTemplate configTemplate = customWorkflowConfig.getTemplateMap().get("billApproval");
    Assert.assertNotNull("Trigger should not null: ", configTemplate.getActionGroups().get(0).getTrigger());
  }

  @Test
  public void testPrecannedTriggerDetails() throws Exception {
    OldCustomWorkflowConfig oldCustomWorkflowConfig =
        new ObjectMapper(new YAMLFactory())
            .readValue(
                TestHelper.readResourceAsString(DICTIONARY_PATH),
                new TypeReference<Map<String, OldCustomWorkflowConfig>>() {})
            .get(YAML_KEY);
    oldCustomWorkflowConfig.afterPropertiesSet();
    CustomConfigV2 customConfigV2 = new CustomConfigV2();
    CustomWorkflowConfig customWorkflowConfig = new CustomWorkflowConfig();
    customWorkflowConfig.setCustomWorkflowConfigFactory(
        new CustomWorkflowConfigFactory(
            oldCustomWorkflowConfig, customConfigV2, new MigratedConfig(Set.of(), Set.of())));
    customWorkflowConfig.setOldConfig(oldCustomWorkflowConfig);
    customWorkflowConfig.getCustomWorkflowConfigFactory().afterPropertiesSet();
    ConfigTemplate configTemplate = customWorkflowConfig.getTemplateMap()
        .get("estimateUpdateNotification");
    ActionGroup actionGroup = configTemplate.getActionGroups().get(0);
    List<Parameter> parameters = actionGroup.getTrigger().getParameters();

    //parameter value in precanned config takes precedence
    Assert.assertEquals(parameters.get(0).getFieldValues().get(0), "Update");
    //if the parameter is not present in precannedconfig the parameter is read from templateconfig
    Assert.assertEquals(parameters.get(0).getPossibleFieldValues().size(), 2);
    Assert.assertNotNull(parameters.get(1));
    HandlerDetails handlerDetails = actionGroup.getTrigger().getHandler().getHandlerDetail();
    Assert.assertEquals(handlerDetails.getHandlerId(),
        "intuit-workflows/entity-created-custom-workflow");

    configTemplate = customWorkflowConfig.getTemplateMap().get("billUpdateNotification");
    actionGroup = configTemplate.getActionGroups().get(0);
    handlerDetails = actionGroup.getTrigger().getHandler().getHandlerDetail();
    Assert.assertEquals(handlerDetails.getHandlerId(),
        "intuit-workflows/entity-created-custom-workflow");
    configTemplate = customWorkflowConfig.getTemplateMap().get("purchaseorderUpdateNotification");
    actionGroup = configTemplate.getActionGroups().get(0);
    parameters = actionGroup.getTrigger().getParameters();
    Assert.assertEquals(parameters.get(0).getFieldValues().get(0), "Create");
    Assert.assertEquals(parameters.get(0).getPossibleFieldValues().size(), 2);
    handlerDetails = actionGroup.getTrigger().getHandler().getHandlerDetail();
    Assert.assertEquals(handlerDetails.getHandlerId(),
        "intuit-workflows/entity-created-custom-workflow");
  }

  @Test
  public void testTriggerInPrecannedTemplateFailed() throws Exception {
    OldCustomWorkflowConfig oldCustomWorkflowConfig =
        new ObjectMapper(new YAMLFactory())
            .readValue(
                TestHelper.readResourceAsString(DICTIONARY_PATH),
                new TypeReference<Map<String, OldCustomWorkflowConfig>>() {})
            .get(YAML_KEY);
    CustomConfigV2 customConfigV2 = new CustomConfigV2();
    CustomWorkflowConfig customWorkflowConfig = new CustomWorkflowConfig();
    customWorkflowConfig.setCustomWorkflowConfigFactory(
        new CustomWorkflowConfigFactory(
            oldCustomWorkflowConfig, customConfigV2, new MigratedConfig(Set.of(), Set.of())));
    customWorkflowConfig.setOldConfig(oldCustomWorkflowConfig);
    customWorkflowConfig.getCustomWorkflowConfigFactory().afterPropertiesSet();
    //populating actions
    Assert.assertTrue(customWorkflowConfig.getOldConfig().populateTemplateConfig());
    ConfigTemplate configTemplate = customWorkflowConfig.getTemplateMap().get("openPurchaseorderReminder");
    Assert.assertNull("Trigger should null: ", configTemplate.getActionGroups().get(0).getTrigger());
  }
}
