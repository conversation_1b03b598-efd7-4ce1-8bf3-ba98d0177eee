package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.publisher;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.helper.RecurrenceUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.executor.actions.SchedulingReminderProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.executor.actions.SchedulingScheduledActionsProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.SchedulingService;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.SchedulingServiceUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.listner.EventScheduleMessageData;
import com.intuit.async.execution.impl.RxExecutionChain;
import com.intuit.async.execution.request.State;
import com.intuit.v4.common.RecurrenceRule;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.history.TriggerNowRequest;
import com.intuit.v4.common.TimeDuration;
import com.intuit.v4.workflows.trigger.Trigger;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.*;
import org.springframework.test.util.ReflectionTestUtils;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.executor.actions.CustomReminderScheduleActionProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.executor.actions.factory.WorkflowScheduleActionProcessorFactory;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.SchedulerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.SchedulerDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.SchedulerAction;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.Status;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.WorkflowNameEnum;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.PublishResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.ResponseStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowGenericResponse;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

public class ScheduleNowServiceTest {

	  @InjectMocks
	  private ScheduleNowService scheduleNowService;

	  @Mock
	  private DefinitionDetailsRepository definitionDetailsRepository;

	  @Mock
	  private SchedulerDetailsRepository schedulerDetailsRepository;

	  @Mock
	  private WASContextHandler wasContextHandler;

	  @Mock
	  private SchedulingService schedulingService;
	  
	  @Mock
	  private CustomReminderScheduleActionProcessor customReminderScheduleProcessor;

	  @Mock
	  private SchedulingScheduledActionsProcessor schedulingScheduledActionsProcessor;

	  @Mock
	  private SchedulingReminderProcessor schedulingReminderProcessor;

	  @Mock
	  private SchedulingRunNowTask schedulingRunNowTask;

	  private final String DEFINITION_ID = "defId";
	  private final Long OWNER_ID = 123L;
	  private final TemplateDetails templateDetails = mock(TemplateDetails.class);
	  
	  private final DefinitionDetails definitionDetails = DefinitionDetails.builder()
	      .definitionId(DEFINITION_ID).status(Status.ENABLED)
	      .templateDetails(templateDetails)
	      .build();

	  @Before
	  public void init() {
	    MockitoAnnotations.initMocks(this);
	    ReflectionTestUtils.setField(
	    		scheduleNowService, "definitionDetailsRepository", definitionDetailsRepository);
	    ReflectionTestUtils.setField(scheduleNowService, "schedulerDetailsRepository",
	        schedulerDetailsRepository);
	    ReflectionTestUtils.setField(scheduleNowService, "wasContextHandler",
	        wasContextHandler);ReflectionTestUtils.setField(scheduleNowService, "schedulingService", schedulingService);
		when(wasContextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("" + OWNER_ID);

		WorkflowScheduleActionProcessorFactory.addProcessor(
				WorkflowNameEnum.CUSTOM_REMINDER, customReminderScheduleProcessor);
		WorkflowScheduleActionProcessorFactory.addProcessor(
				WorkflowNameEnum.SCHEDULED_ACTIONS, schedulingScheduledActionsProcessor
		);
		WorkflowScheduleActionProcessorFactory.addProcessor(WorkflowNameEnum.REMINDER, schedulingReminderProcessor);
	}
	  
	  @Test
	  public void testrunNow() {
	    when(definitionDetailsRepository.findByDefinitionIdAndOwnerId(DEFINITION_ID, OWNER_ID))
	        .thenReturn(
	            Optional.of(definitionDetails));
	    when(schedulerDetailsRepository.findByDefinitionDetails(Mockito.any()))
	        .thenReturn(Optional.of(
	            List.of(SchedulerDetails.builder().schedulerId("sched123")
	                .definitionDetails(definitionDetails).schedulerAction(
	                    SchedulerAction.CUSTOM_REMINDER_CUSTOM_START).build())));
	    when(customReminderScheduleProcessor.process((SchedulerDetails) Mockito.any(),Mockito.any())).thenReturn(Collections.emptyMap());
	    when(templateDetails.getTemplateName()).thenReturn("customReminder");
		WorkflowGenericResponse response = scheduleNowService.runNow(new TriggerNowRequest(DEFINITION_ID));

		Mockito.verify(definitionDetailsRepository, Mockito.times(1))
				.findByDefinitionIdAndOwnerId(DEFINITION_ID, OWNER_ID);

		Assert.assertNotNull(response);
		Assert.assertEquals(ResponseStatus.SUCCESS,response.getStatus());
		Assert.assertNotNull(((PublishResponse)response.getResponse()).getActionTidMap());
		Assert.assertEquals(1,((PublishResponse)response.getResponse()).getActionTidMap().size());

	  }

	@Test
	public void testHandleRunNowForESS() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
		when(definitionDetailsRepository.findByDefinitionIdAndOwnerId(DEFINITION_ID, OWNER_ID))
				.thenReturn(
						Optional.of(definitionDetails));
		when(schedulerDetailsRepository.findByDefinitionDetails(Mockito.any()))
				.thenReturn(Optional.of(
						List.of(SchedulerDetails.builder().schedulerId("sched123")
								.definitionDetails(definitionDetails).schedulerAction(
										SchedulerAction.CUSTOM_REMINDER_CUSTOM_START).build())));

		when(schedulingService.isEnabled((DefinitionDetails) any(), Mockito.any())).thenReturn(false);
		definitionDetails.setDefinitionKey("key");
		TemplateDetails templateDetails = mock(TemplateDetails.class);
		when(templateDetails.getTemplateName()).thenReturn("customReminder");
		definitionDetails.setTemplateDetails(templateDetails);

		WorkflowGenericResponse response = scheduleNowService.runNow(new TriggerNowRequest(DEFINITION_ID));

		Mockito.verify(definitionDetailsRepository, Mockito.times(1))
				.findByDefinitionIdAndOwnerId(
						DEFINITION_ID, OWNER_ID);
		Assert.assertNotNull(response);
		Assert.assertEquals(ResponseStatus.SUCCESS,response.getStatus());
		Assert.assertNotNull(((PublishResponse)response.getResponse()).getActionTidMap());
	}


	@Test
	public void testRunNowSchedulingFlow() {
		when(definitionDetailsRepository.findByDefinitionIdAndOwnerId(DEFINITION_ID, OWNER_ID))
				.thenReturn(
						Optional.of(definitionDetails));
		when(schedulingScheduledActionsProcessor.process((DefinitionDetails) Mockito.any(),Mockito.any())).thenReturn(Collections.emptyMap());
		when(schedulingService.isEnabled((DefinitionDetails) any(), Mockito.any())).thenReturn(true);
		definitionDetails.setDefinitionKey("key");
		TemplateDetails templateDetails = mock(TemplateDetails.class);
		when(templateDetails.getTemplateName()).thenReturn("customReminder");
		definitionDetails.setTemplateDetails(templateDetails);
		RecurrenceRule recurrenceRule = new RecurrenceRule();
		recurrenceRule.setTimeZone("Asia/Kolkata");
		TimeDuration timeDuration = new TimeDuration();
		timeDuration.setHours(1);
		timeDuration.setMinutes(30);
		recurrenceRule.setRecurrenceTime(timeDuration);
		WorkflowGenericResponse response = scheduleNowService.runNow(new TriggerNowRequest(DEFINITION_ID));

		Mockito.verify(definitionDetailsRepository, Mockito.times(1))
				.findByDefinitionIdAndOwnerId(
						DEFINITION_ID, OWNER_ID);
		Assert.assertNotNull(response);
		Assert.assertEquals(ResponseStatus.SUCCESS,response.getStatus());
		Assert.assertNotNull(((PublishResponse)response.getResponse()).getActionTidMap());
	}

	@Test
	public void testRunNowSchedulingFlowFailed(){
		when(definitionDetailsRepository.findByDefinitionIdAndOwnerId(DEFINITION_ID, OWNER_ID))
				.thenReturn(
						Optional.of(definitionDetails));
		definitionDetails.setDefinitionKey("key");
		when(schedulingService.isEnabled((DefinitionDetails) any(), Mockito.any())).thenReturn(true);
		TemplateDetails templateDetails = mock(TemplateDetails.class);
		when(templateDetails.getTemplateName()).thenReturn("customReminder");
		definitionDetails.setTemplateDetails(templateDetails);
		// Mock the static method
		try (MockedStatic<SchedulingServiceUtil> mockedStatic = mockStatic(SchedulingServiceUtil.class)) {
			mockedStatic.when(() -> SchedulingServiceUtil.getScheduleIds(anyString(), anyString()))
					.thenThrow(WorkflowGeneralException.class);

			try {
				scheduleNowService.runNow(new TriggerNowRequest(DEFINITION_ID));
				Assert.fail("Exception Should be thrown");
			} catch (WorkflowGeneralException workflowGeneralException) {
				Mockito.verify(definitionDetailsRepository, Mockito.times(1))
						.findByDefinitionIdAndOwnerId(
								DEFINITION_ID, OWNER_ID);
			}
		}
	}

	@Test
	public void testrunNowWithTriggerDetails() {
		when(definitionDetailsRepository.findByDefinitionIdAndOwnerId(DEFINITION_ID, OWNER_ID))
				.thenReturn(
						Optional.of(definitionDetails));
		when(schedulerDetailsRepository.findByDefinitionDetails(Mockito.any()))
				.thenReturn(Optional.of(
						List.of(SchedulerDetails.builder().schedulerId("sched123")
								.definitionDetails(definitionDetails).schedulerAction(
										SchedulerAction.CUSTOM_REMINDER_CUSTOM_START).build())));

		when(customReminderScheduleProcessor.process((SchedulerDetails) Mockito.any(),Mockito.any())).thenReturn(Collections.emptyMap());
		when(templateDetails.getTemplateName()).thenReturn("customReminder");
		Trigger trigger =  new Trigger();
		trigger.setDefinitionId(DEFINITION_ID);
		Trigger response = scheduleNowService.runNow(trigger);

		Mockito.verify(definitionDetailsRepository, Mockito.times(1))
				.findByDefinitionIdAndOwnerId(
						DEFINITION_ID, OWNER_ID);
		Assert.assertNotNull(response);
		Assert.assertNotNull(response.getEntity());
		Assert.assertEquals(1,response.getEntity().size());
	}

	  @Test
	  public void testNoDefinitionFound() {
	    when(definitionDetailsRepository.findByDefinitionIdAndOwnerId(DEFINITION_ID, OWNER_ID))
	        .thenReturn(
	            Optional.empty());
	    try {
	    	scheduleNowService.runNow(new TriggerNowRequest(DEFINITION_ID));
	      Assert.fail("Exception Should be thrown");
	    } catch (WorkflowGeneralException workflowGeneralException) {
	      Mockito.verify(definitionDetailsRepository, Mockito.times(1))
	          .findByDefinitionIdAndOwnerId(
	              DEFINITION_ID, OWNER_ID);
	      Mockito.verify(schedulerDetailsRepository, Mockito.times(0))
	          .findByDefinitionDetails(
	              definitionDetails);
	    }
	  }

	  @Test
	  public void testPublishNoScheduleDetails() {
	    when(definitionDetailsRepository.findByDefinitionIdAndOwnerId(DEFINITION_ID, OWNER_ID))
	        .thenReturn(
	            Optional.of(definitionDetails));
	    when(schedulerDetailsRepository.findByDefinitionDetails(definitionDetails))
	        .thenReturn(Optional.empty());
	    try {
			scheduleNowService.runNow(new TriggerNowRequest(DEFINITION_ID));
			Assert.fail("Exception Should be thrown");
	    } catch (WorkflowGeneralException workflowGeneralException) {
	      Mockito.verify(definitionDetailsRepository, Mockito.times(1))
	          .findByDefinitionIdAndOwnerId(
	              DEFINITION_ID, OWNER_ID);
	    }
	  }
}
