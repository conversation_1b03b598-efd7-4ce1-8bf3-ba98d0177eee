package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services;

import static java.util.Collections.singletonList;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyObject;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doCallRealMethod;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.verify;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.OnDemandApprovalConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.IXPManager;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WASContext;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.TemplateService;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.impl.AuthDetailsServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.publisher.ScheduleNowService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler.DefaultRuleHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler.DefaultTriggerHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler.MultipleUserDefinitionTriggerHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler.RuleEvaluationHandlers;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler.SystemDefinitionTriggerHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler.TriggerHandlers;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler.V3RunTimeHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.OnDemandHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DefinitionType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.EvaluationResult;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.PublishResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.ResponseStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.TriggerStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowEvaluateRuleResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowGenericResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowGenericResponse.WorkflowGenericResponseBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowTriggerResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowTriggersResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.history.TriggerNowRequest;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter.TransactionEntity;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

/**
 * <AUTHOR>
 */
public class RunTimeServiceImplTest {

  @Mock
  private DefaultRuleHandler ruleHandler;

  @Mock
  private DefaultTriggerHandler triggerHandler;

  @Mock
  private MultipleUserDefinitionTriggerHandler multipleUserDefinitionTriggerHandler;

  @Mock
  private V3RunTimeHelper runTimeHelper;

  @Mock private SystemDefinitionTriggerHandler systemDefinitionTriggerHandler;

  @Mock
  private DefinitionDetailsRepository definitionDetailsRepository;

  @Mock
  private WASContextHandler contextHandler;

  @Mock
  private TemplateService templateService;

  @Mock
  private AuthDetailsServiceHelper authDetailsServiceHelper;

  @InjectMocks
  private RunTimeServiceImpl runTimeServiceImpl;

  @Mock
  private OnDemandHelper onDemandHelper;

  @Mock
  private OnDemandApprovalConfig onDemandApprovalConfig;

  @Mock
  private ProcessDetailsRepository processDetailsRepository;
  
  @Mock
  private ScheduleNowService scheduleNowService;

  @Mock
  private IXPManager ixpManager;

  private static final String authHeader =
          "Intuit_IAM_Authentication intuit_appid=Intuit.appintgwkflw.wkflautomate.workflowappconnect,intuit_app_secret=xxxx,intuit_token=xxxx,intuit_userid=9130347715753436,intuit_token_type=IAM-Ticket,intuit_realmid=1234567";

  @Before
  public void init() {

    MockitoAnnotations.initMocks(this);
    RuleEvaluationHandlers.addHandler(DefaultRuleHandler.class.getSimpleName(), ruleHandler);
    TriggerHandlers.addHandler(DefaultTriggerHandler.class.getSimpleName(), triggerHandler);
    TriggerHandlers.addHandler(DefinitionType.USER.name(), multipleUserDefinitionTriggerHandler);
    TriggerHandlers.addHandler(DefinitionType.SYSTEM.name(), systemDefinitionTriggerHandler);
  }

  @Test
  public void test() {

    WorkflowGenericResponseBuilder response =
        WorkflowGenericResponse.builder()
            .status(ResponseStatus.SUCCESS)
            .response(WorkflowTriggerResponse.builder().build());
    Mockito.when(triggerHandler.executeTrigger(Mockito.any())).thenReturn(response.build());
    Mockito.when(onDemandApprovalConfig.isEnabled()).thenReturn(false);
    assertNotNull(runTimeServiceImpl.processTriggerMessage(getTriggerPayload()));
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testTriggerMessageV2InvalidPayload() {

    runTimeServiceImpl.processTriggerMessageV2(new HashMap<>());
  }

  @Test
  public void testTriggerMessageV2() {

    WorkflowGenericResponseBuilder response =
        WorkflowGenericResponse.builder()
            .status(ResponseStatus.SUCCESS)
            .response(WorkflowTriggerResponse.builder().build());
    TemplateDetails templateDetails = new TemplateDetails();
    templateDetails.setDefinitionType(DefinitionType.USER);
    Mockito.when(runTimeHelper.getTemplateDetails(Mockito.any()))
        .thenReturn(singletonList(templateDetails));
    Mockito.when(multipleUserDefinitionTriggerHandler.executeTrigger(Mockito.any()))
        .thenReturn(response.build());
    assertNotNull(runTimeServiceImpl.processTriggerMessageV2(getTriggerPayload()));
  }

  @Test
  public void testTriggerMessageV2WithProviderWorkflowId() {
    WorkflowGenericResponseBuilder response =
            WorkflowGenericResponse.builder()
                    .status(ResponseStatus.SUCCESS)
                    .response(WorkflowTriggerResponse.builder().build());
    TemplateDetails templateDetails = new TemplateDetails();
    templateDetails.setDefinitionType(DefinitionType.USER);
    DefinitionDetails definitionDetails = new DefinitionDetails();
    definitionDetails.setTemplateDetails(templateDetails);
    Mockito.when(definitionDetailsRepository.findEnabledDefinitionForWorkflowId(Mockito.any(),Mockito.anyString()))
            .thenReturn(definitionDetails);
    Mockito.when(runTimeHelper.getTemplateDetails(Mockito.any()))
        .thenReturn(singletonList(templateDetails));
    Mockito.when(multipleUserDefinitionTriggerHandler.executeTrigger(Mockito.any()))
            .thenReturn(response.build());
    Mockito.when(runTimeHelper.getEligibleDefinitions(Mockito.any(), anyBoolean()))
            .thenReturn(Collections.singletonList(definitionDetails));
    assertNotNull(runTimeServiceImpl.processTriggerMessageV2(getTriggerPayloadWithProviderWorkflowId()));
  }
  
  @Test
  public void testTriggerMessageV2_No_definitionDetails() {

    WorkflowGenericResponseBuilder response =
        WorkflowGenericResponse.builder()
            .status(ResponseStatus.SUCCESS)
            .response(WorkflowTriggerResponse.builder().build());
    TemplateDetails templateDetails = new TemplateDetails();
    templateDetails.setDefinitionType(null);
    Mockito.when(runTimeHelper.getTemplateDetails(Mockito.any()))
        .thenReturn(singletonList(templateDetails));
    Mockito.when(multipleUserDefinitionTriggerHandler.executeTrigger(Mockito.any()))
        .thenReturn(response.build());
    assertNotNull(runTimeServiceImpl.processTriggerMessageV2(getTriggerPayload()));
  }

  @Test
  public void testTriggerMessageV2_SystemDefn() {

    WorkflowGenericResponseBuilder response =
        WorkflowGenericResponse.builder()
            .status(ResponseStatus.SUCCESS)
            .response(WorkflowTriggerResponse.builder().build());
    TemplateDetails templateDetails = new TemplateDetails();
    templateDetails.setDefinitionType(DefinitionType.SYSTEM);
    Mockito.when(runTimeHelper.getTemplateDetails(Mockito.any()))
        .thenReturn(singletonList(templateDetails));
    Mockito.when(systemDefinitionTriggerHandler.executeTrigger(Mockito.any()))
        .thenReturn(response.build());
    assertNotNull(runTimeServiceImpl.processTriggerMessageV2(getTriggerPayload()));
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testTriggerMessageV2NullTemplateDetails() {

    WorkflowGenericResponseBuilder response =
        WorkflowGenericResponse.builder()
            .status(ResponseStatus.SUCCESS)
            .response(WorkflowTriggerResponse.builder().build());
    TemplateDetails templateDetails = new TemplateDetails();
    templateDetails.setDefinitionType(DefinitionType.SYSTEM);

    Mockito.when(runTimeHelper.getTemplateDetails(Mockito.any()))
        .thenReturn(null);
    Mockito.when(systemDefinitionTriggerHandler.executeTrigger(Mockito.any()))
        .thenReturn(response.build());
    assertNotNull(runTimeServiceImpl.processTriggerMessageV2(getTriggerPayload()));
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testTriggerMessageV2EmptyTemplateDetails() {

    WorkflowGenericResponseBuilder response =
        WorkflowGenericResponse.builder()
            .status(ResponseStatus.SUCCESS)
            .response(WorkflowTriggerResponse.builder().build());
    TemplateDetails templateDetails = new TemplateDetails();
    templateDetails.setDefinitionType(DefinitionType.SYSTEM);

    Mockito.when(runTimeHelper.getTemplateDetails(Mockito.any()))
        .thenReturn(new ArrayList<TemplateDetails>());
    Mockito.when(systemDefinitionTriggerHandler.executeTrigger(Mockito.any()))
        .thenReturn(response.build());
    assertNotNull(runTimeServiceImpl.processTriggerMessageV2(getTriggerPayload()));
  }

  @Test
  public void testTriggerMessageV2DefinitionTypeNull() {

    WorkflowGenericResponseBuilder response =
        WorkflowGenericResponse.builder()
            .status(ResponseStatus.SUCCESS)
            .response(WorkflowTriggerResponse.builder().build());
    TemplateDetails templateDetails = new TemplateDetails();
    Mockito.when(runTimeHelper.getTemplateDetails(Mockito.any()))
        .thenReturn(Collections.singletonList(templateDetails));
    Mockito.when(multipleUserDefinitionTriggerHandler.executeTrigger(Mockito.any()))
        .thenReturn(response.build());
    assertNotNull(runTimeServiceImpl.processTriggerMessageV2(getTriggerPayload()));
  }

  @Test
  public void whenProcessEvaluateAndTriggerMessage_andEvaluationTrue_thenSuccess() {

    WorkflowGenericResponseBuilder evaluationResponse =
        WorkflowGenericResponse.builder()
            .status(ResponseStatus.SUCCESS)
            .response(new WorkflowEvaluateRuleResponse(getDummyEvaluationResult(true)));
    doReturn(evaluationResponse.build()).when(ruleHandler).evaluateRules(any());
    WorkflowGenericResponseBuilder triggerResponseV2 =
        WorkflowGenericResponse.builder()
            .status(ResponseStatus.SUCCESS)
            .response(WorkflowTriggerResponse.builder()
                .status(TriggerStatus.PROCESS_STARTED)
                .build());
    Mockito.when(runTimeHelper.getTemplateDetails(Mockito.any()))
        .thenReturn(singletonList(new TemplateDetails()));
    Mockito.when(multipleUserDefinitionTriggerHandler.executeTrigger(Mockito.any()))
        .thenReturn(triggerResponseV2.build());

    WorkflowGenericResponse response = runTimeServiceImpl
        .processEvaluateAndTriggerMessage(getTriggerPayload());

    assertNotNull(response);
    WorkflowTriggerResponse triggerResponse = (WorkflowTriggerResponse) response.getResponse();
    assertEquals(TriggerStatus.PROCESS_STARTED, triggerResponse.getStatus());
  }

  @Test
  public void whenProcessEvaluateAndTriggerMessage_andEvaluationTrueWithMultipleDefinition_thenSuccess() {

    WorkflowGenericResponseBuilder evaluationResponse =
        WorkflowGenericResponse.builder()
            .status(ResponseStatus.SUCCESS)
            .response(new WorkflowEvaluateRuleResponse(getDummyEvaluationResultForMultiple(false,true)));
    doReturn(evaluationResponse.build()).when(ruleHandler).evaluateRules(any());
    WorkflowGenericResponseBuilder triggerResponseV2 =
        WorkflowGenericResponse.builder()
            .status(ResponseStatus.SUCCESS)
            .response(WorkflowTriggerResponse.builder()
                .status(TriggerStatus.PROCESS_STARTED)
                .build());
    Mockito.when(runTimeHelper.getTemplateDetails(Mockito.any()))
        .thenReturn(singletonList(new TemplateDetails()));
    Mockito.when(multipleUserDefinitionTriggerHandler.executeTrigger(Mockito.any()))
        .thenReturn(triggerResponseV2.build());

    WorkflowGenericResponse response = runTimeServiceImpl
        .processEvaluateAndTriggerMessage(getTriggerPayloadForNotification());

    assertNotNull(response);
    Mockito.verify(multipleUserDefinitionTriggerHandler, Mockito.times(1))
        .executeTrigger(anyObject());
  }

  @Test
  public void whenProcessEvaluateAndTriggerMessage_andEvaluationFalse_thenSuccess() {

    WorkflowGenericResponseBuilder evaluationResponse =
        WorkflowGenericResponse.builder()
            .status(ResponseStatus.SUCCESS)
            .response(new WorkflowEvaluateRuleResponse(getDummyEvaluationResult(false)));
    doReturn(evaluationResponse.build()).when(ruleHandler).evaluateRules(any());
    doCallRealMethod().when(runTimeHelper).getTriggerResponse(any(), any(), any());

    WorkflowGenericResponse response = runTimeServiceImpl
        .processEvaluateAndTriggerMessage(getTriggerPayload());

    assertNotNull(response);
    WorkflowTriggerResponse triggerResponse = (WorkflowTriggerResponse) response.getResponse();
    assertEquals(TriggerStatus.NO_ACTION, triggerResponse.getStatus());
  }

  @Test
  public void whenProcessEvaluateAndTriggerMessage_andEvaluationFalseWithMultipleDefinition_thenSuccess() {

    WorkflowGenericResponseBuilder evaluationResponse =
        WorkflowGenericResponse.builder()
            .status(ResponseStatus.SUCCESS)
            .response(new WorkflowEvaluateRuleResponse(getDummyEvaluationResult(false)));
    doReturn(evaluationResponse.build()).when(ruleHandler).evaluateRules(any());
    doCallRealMethod().when(runTimeHelper).getTriggerResponse(any(), any(), any());

    WorkflowGenericResponse response = runTimeServiceImpl
        .processEvaluateAndTriggerMessage(getTriggerPayloadForNotification());

    assertNotNull(response);
    WorkflowTriggerResponse triggerResponse = (WorkflowTriggerResponse) response.getResponse();
    assertEquals(TriggerStatus.NO_ACTION, triggerResponse.getStatus());
  }

  @Test
  public void whenNonRealmSystemUserAndDefinitionTypeNotSystem(){
    WASContext.setNonRealmSystemUserContext(true);
    WorkflowGenericResponseBuilder response =
        WorkflowGenericResponse.builder()
            .status(ResponseStatus.SUCCESS)
            .response(WorkflowTriggerResponse.builder().build());
    TemplateDetails templateDetails = new TemplateDetails();
    templateDetails.setDefinitionType(DefinitionType.USER);
    Mockito.when(runTimeHelper.getTemplateDetails(Mockito.any()))
        .thenReturn(singletonList(templateDetails));
    Mockito.when(multipleUserDefinitionTriggerHandler.executeTrigger(Mockito.any()))
        .thenReturn(response.build());
    try {
      runTimeServiceImpl.processTriggerMessageV2(getTriggerPayload());
    }
    catch (Exception e){
      Assert.assertEquals(WorkflowError.INVALID_REALM_ID.getErrorMessage(), e.getMessage());
      if(!e.getMessage().equals(WorkflowError.INVALID_REALM_ID.getErrorMessage())){
        throw e;
      }
    }
    finally {
      WASContext.clear();
    }
  }

  @Test
  public void testOnDemandTriggerTrue() {
    String processId = String.valueOf(UUID.randomUUID());
    WorkflowGenericResponseBuilder response =
            WorkflowGenericResponse.builder()
                    .status(ResponseStatus.SUCCESS)
                    .response(new WorkflowTriggersResponse(Collections.singletonList(WorkflowTriggerResponse.builder().definitionId("1234")
                            .definitionName("customApproval")
                            .status(TriggerStatus.PROCESS_SIGNALLED)
                            .processId(processId)
                            .build())));
    Mockito.when(onDemandHelper.checkIfOnDemand(any(TransactionEntity.class), any())).thenReturn(true);
    Mockito.when(systemDefinitionTriggerHandler.executeTrigger(Mockito.any(), Mockito.any())).thenReturn(response.build());
    Mockito.when(onDemandApprovalConfig.isEnabled()).thenReturn(true);
    Mockito.when(contextHandler.get(WASContextEnums.AUTHORIZATION_HEADER)).thenReturn(authHeader);
    Mockito.when(runTimeHelper.getTriggerResponse(any(),any(),any())).thenReturn(WorkflowGenericResponse.builder().response(WorkflowTriggerResponse.builder().processId(processId).status(TriggerStatus.PROCESS_SIGNALLED).build()).build());
    Mockito.when(ixpManager.getBoolean(any(),eq(false))).thenReturn(true);
    WorkflowGenericResponse workflowGenericResponse = runTimeServiceImpl.processTriggerMessage(getTriggerPayload());
    assertNotNull(workflowGenericResponse);
    assertTrue(workflowGenericResponse.getResponse() instanceof WorkflowTriggerResponse);
    Mockito.verify(authDetailsServiceHelper, Mockito.times(1)).populateAuthDetailsSync(any(),eq(true));

  }

  @Test
  public void testOnDemandWithNoTriggerResponse(){
      Mockito.when(onDemandHelper.checkIfOnDemand(any(TransactionEntity.class), any())).thenReturn(true);
      WorkflowGenericResponse dummyResponse = WorkflowGenericResponse.builder().response(new WorkflowTriggersResponse(Collections.emptyList())).build();
      Mockito.when(systemDefinitionTriggerHandler.executeTrigger(Mockito.any(), Mockito.any())).thenReturn(dummyResponse);
      Mockito.when(onDemandApprovalConfig.isEnabled()).thenReturn(true);
      Mockito.when(contextHandler.get(WASContextEnums.AUTHORIZATION_HEADER)).thenReturn(authHeader);
      Mockito.when(processDetailsRepository.findByRecordIdAndOwnerIdAndProcessStatusInAndInternalStatusIsNullAndParentIdIsNull(anyString(),anyLong(),anyList())).thenReturn(
          Optional.of(Collections.singletonList(ProcessDetails.builder().definitionDetails(DefinitionDetails.builder().ownerId(
              Long.valueOf(WorkflowConstants.SYSTEM_OWNER_ID)).build()).build())));
      Mockito.when(ixpManager.getBoolean(any(),eq(false))).thenReturn(true);
      WorkflowGenericResponse workflowGenericResponse = runTimeServiceImpl.processTriggerMessage(getTriggerPayload());
      assertEquals(dummyResponse,workflowGenericResponse);
    }

  @Test
  public void testOnDemandTriggerFalse() {

    WorkflowGenericResponseBuilder response =
            WorkflowGenericResponse.builder()
                    .status(ResponseStatus.SUCCESS)
                    .response(WorkflowTriggerResponse.builder().definitionId("1234")
                            .definitionName("customApproval")
                            .status(TriggerStatus.PROCESS_SIGNALLED)
                            .build());
    Mockito.when(processDetailsRepository.findByRecordIdAndOwnerIdAndProcessStatusInAndInternalStatusIsNullAndParentIdIsNull(anyString(),anyLong(),anyList())).thenReturn(
        Optional.of(Collections.singletonList(ProcessDetails.builder().definitionDetails(DefinitionDetails.builder().definitionKey("customReminder").ownerId(
            123L).build()).build())));
    Mockito.when(onDemandApprovalConfig.isEnabled()).thenReturn(true);
    Mockito.when(triggerHandler.executeTrigger(Mockito.any())).thenReturn(response.build());
    Mockito.doReturn(authHeader).when(contextHandler).get(WASContextEnums.AUTHORIZATION_HEADER);
    assertNotNull(runTimeServiceImpl.processTriggerMessage(getTriggerPayload()));
    verify(triggerHandler, Mockito.times(1)).executeTrigger(any());

  }

  @Test
  public void testOnDemandTriggerFalseWhenNoProcessFound() {
    WorkflowGenericResponseBuilder response =
        WorkflowGenericResponse.builder()
            .status(ResponseStatus.SUCCESS)
            .response(WorkflowTriggerResponse.builder().definitionId("1234")
                .definitionName("customApproval")
                .status(TriggerStatus.PROCESS_SIGNALLED)
                .build());
    Mockito.when(processDetailsRepository.findByRecordIdAndOwnerIdAndProcessStatusInAndInternalStatusIsNullAndParentIdIsNull(anyString(),anyLong(),anyList())).thenReturn(
       Optional.empty());
    Mockito.when(onDemandApprovalConfig.isEnabled()).thenReturn(true);
    Mockito.when(triggerHandler.executeTrigger(Mockito.any())).thenReturn(response.build());
    Mockito.doReturn(authHeader).when(contextHandler).get(WASContextEnums.AUTHORIZATION_HEADER);
    runTimeServiceImpl.processTriggerMessage(getTriggerPayload());
    verify(triggerHandler, Mockito.times(1)).executeTrigger(any());
  }

  
  @Test
  public void testRunNow() {
	WorkflowGenericResponse response = WorkflowGenericResponse.builder().status(ResponseStatus.SUCCESS)
				.response(PublishResponse.builder().actionTidMap(Collections.emptyMap())
						.triggerDetails(Collections.emptyMap()).build()).build();
    Mockito.when(scheduleNowService.runNow(any(TriggerNowRequest.class))).thenReturn(response);
    assertNotNull(runTimeServiceImpl.runNow(new TriggerNowRequest("defId")));

  }

  private Map<String, Object> getTriggerPayload() {

    Map<String, Object> trigger = new HashMap<>();
    Map<String, Object> eventHeaders = new HashMap<>();
    eventHeaders.put("workflow", "approval");
    eventHeaders.put("entityType", RecordType.INVOICE);
    eventHeaders.put("entityChangeType", "created");
    eventHeaders.put("entityId", "35");
    trigger.put("eventHeaders", eventHeaders);
    Map<String, Object> entity = new HashMap<>();
    Map<String, Object> entityObj = new HashMap<>();
    entityObj.put("id", "35");
    entity.put("Invoice", entityObj);
    trigger.put("entity", entity);
    return trigger;
  }

  private Map<String, Object> getTriggerPayloadForNotification() {

    Map<String, Object> trigger = new HashMap<>();
    Map<String, Object> eventHeaders = new HashMap<>();
    eventHeaders.put("workflow", "customNotification");
    eventHeaders.put("entityType", RecordType.INVOICE);
    eventHeaders.put("entityChangeType", "Create");
    eventHeaders.put("entityId", "40");
    trigger.put("eventHeaders", eventHeaders);
    Map<String, Object> entity = new HashMap<>();
    Map<String, Object> entityObj = new HashMap<>();
    entityObj.put("id", "35");
    entity.put("Invoice", entityObj);
    trigger.put("entity", entity);
    return trigger;
  }

  private Map<String, Object> getTriggerPayloadWithProviderWorkflowId() {

    Map<String, Object> trigger = new HashMap<>();
    Map<String, Object> eventHeaders = new HashMap<>();
    eventHeaders.put("workflow", "approval");
    eventHeaders.put("entityType", RecordType.INVOICE);
    eventHeaders.put("entityChangeType", "created");
    eventHeaders.put("entityId", "35");
    eventHeaders.put("providerWorkflowId",65312);
    trigger.put("eventHeaders", eventHeaders);
    Map<String, Object> entity = new HashMap<>();
    Map<String, Object> entityObj = new HashMap<>();
    entityObj.put("id", "35");
    entity.put("Invoice", entityObj);
    trigger.put("entity", entity);
    return trigger;
  }

  private EvaluationResult getDummyEvaluationResult(boolean expectedResult) {

    final Map<String, Object> result = ImmutableMap.of("details",
        ImmutableList.of(
            ImmutableMap.of("approvalRequired",
                ImmutableMap.of("value", expectedResult))));
    return new EvaluationResult(null, ImmutableList.of(result), null);
  }

  private EvaluationResult getDummyEvaluationResultForMultiple(Boolean eval1, Boolean eval2) {

    final List<Map<String, Object>> result = ImmutableList.of(ImmutableMap.of("details",
        ImmutableList.of(
            ImmutableMap.of("decisionResult",
                ImmutableMap.of("value", eval1)))),
        ImmutableMap.of("details",
            ImmutableList.of(
                ImmutableMap.of("decisionResult",
                    ImmutableMap.of("value", eval2))))
        ) ;
    return new EvaluationResult(null, result, null);
  }
}
