package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.executor.message;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WasUtils;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.executor.actions.CustomReminderScheduleActionProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.executor.actions.factory.WorkflowScheduleActionProcessorFactory;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.executor.message.ESSScheduleMessageExecutor;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.SchedulerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.SchedulerDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.InternalStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.SchedulerAction;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.Status;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.WorkflowNameEnum;
import java.util.Optional;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(MockitoJUnitRunner.class)
public class ESSScheduleMessageExecutorTest {

  @InjectMocks private ESSScheduleMessageExecutor messageProcessor;
  @Mock private SchedulerDetailsRepository schedulerDetailsRepository;
  @Mock private WASContextHandler wasContextHandler;
  public final String MESSAGE_PATH = "eventScheduler/message.json";
  public final String MESSAGE = TestHelper.readResourceAsString(MESSAGE_PATH);
  private CustomReminderScheduleActionProcessor customReminderScheduleProcessor;
  private MockedStatic<WasUtils> mocked;
  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
    ReflectionTestUtils.setField(
        messageProcessor, "schedulerDetailsRepository", schedulerDetailsRepository);
    customReminderScheduleProcessor = Mockito.mock(CustomReminderScheduleActionProcessor.class);
    WorkflowScheduleActionProcessorFactory.addProcessor(
        WorkflowNameEnum.CUSTOM_REMINDER, customReminderScheduleProcessor);
    mocked = Mockito.mockStatic(WasUtils.class);
  }
  @After
  public void deregister() {
    WorkflowScheduleActionProcessorFactory.addProcessor(
        WorkflowNameEnum.CUSTOM_REMINDER, null);
    mocked.reset();
    mocked.close();
  }

  @Test
  public void test_processMessage() {
    Mockito.when(schedulerDetailsRepository.findById(Mockito.any()))
        .thenReturn(Optional.of(getSchedulerDetails(Status.ENABLED, null,123L)));
    messageProcessor.transformAndExecute(MESSAGE);
    Mockito.verify(schedulerDetailsRepository, Mockito.times(1)).findById(Mockito.any());
    Mockito.verify(customReminderScheduleProcessor, Mockito.times(1)).process((SchedulerDetails) Mockito.any(), Mockito.any());
    Mockito.verify(wasContextHandler, Mockito.times(1)).addKey(WASContextEnums.WORKFLOW, "customReminder");
  }

  @Test
  public void test_processMessage_DefinitionDisabled() {
    Mockito.when(schedulerDetailsRepository.findById(Mockito.any()))
        .thenReturn(
            Optional.of(getSchedulerDetails(Status.DISABLED, InternalStatus.MARKED_FOR_DISABLE, 123L)));
    messageProcessor.transformAndExecute(MESSAGE);
    Mockito.verify(schedulerDetailsRepository, Mockito.times(1)).findById(Mockito.any());
    Mockito.verify(customReminderScheduleProcessor, Mockito.times(0))
        .process((SchedulerDetails) Mockito.any(), Mockito.any());
    Mockito.verify(wasContextHandler, Mockito.times(0)).addKey(WASContextEnums.WORKFLOW, "customReminder");
  }

  @Test
  public void test_processMessage_testDriveRealm() {
    Mockito.when(schedulerDetailsRepository.findById(Mockito.any()))
        .thenReturn(
            Optional.of(getSchedulerDetails(Status.ENABLED, InternalStatus.MARKED_FOR_DISABLE, 4623456L)));
    messageProcessor.transformAndExecute(MESSAGE);
    Mockito.verify(schedulerDetailsRepository, Mockito.times(1)).findById(Mockito.any());
    Mockito.verify(customReminderScheduleProcessor, Mockito.times(0))
        .process((SchedulerDetails) Mockito.any(), Mockito.any());
    Mockito.verify(wasContextHandler, Mockito.times(0)).addKey(WASContextEnums.WORKFLOW, "customReminder");
    mocked.verify(Mockito.times(1), () -> WasUtils.isTestDriveRealm("4623456"));
  }

  @Test
  public void test_processMessage_StaleDefinition() {
    Mockito.when(schedulerDetailsRepository.findById(Mockito.any()))
        .thenReturn(
            Optional.of(getSchedulerDetails(Status.ENABLED, InternalStatus.STALE_DEFINITION, 12345L)));
    messageProcessor.transformAndExecute(MESSAGE);
    Mockito.verify(schedulerDetailsRepository, Mockito.times(1)).findById(Mockito.any());
    Mockito.verify(customReminderScheduleProcessor, Mockito.times(0))
        .process((SchedulerDetails) Mockito.any(), Mockito.any());
  }

  @Test
  public void test_processMessageNullOrBlank() {
    messageProcessor.transformAndExecute(null);
    Mockito.verify(schedulerDetailsRepository, Mockito.times(0)).findById(Mockito.any());
    messageProcessor.transformAndExecute("");
    Mockito.verify(schedulerDetailsRepository, Mockito.times(0)).findById(Mockito.any());
  }

  @Test
  public void test_processMessageScheduleData() {
    Mockito.when(schedulerDetailsRepository.findById(Mockito.any())).thenReturn(Optional.empty());
    messageProcessor.transformAndExecute(MESSAGE);
    Mockito.verify(schedulerDetailsRepository, Mockito.times(1)).findById(Mockito.any());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void test_processMessageScheduleData_exception() {
    Mockito.when(schedulerDetailsRepository.findById(Mockito.any())).thenThrow(WorkflowGeneralException.class);
    messageProcessor.transformAndExecute(MESSAGE);
  }

  private SchedulerDetails getSchedulerDetails(Status definitionStatus, InternalStatus internalStatus, Long ownerId) {
    return SchedulerDetails.builder()
        .schedulerId("sche123")
        .definitionDetails(
            DefinitionDetails.builder()
                .definitionId("def123")
                .status(definitionStatus)
                .internalStatus(internalStatus)
                .templateDetails(
                    TemplateDetails.builder()
                        .templateName(WorkflowNameEnum.CUSTOM_REMINDER.getName())
                        .build())
                .build())
        .schedulerAction(SchedulerAction.CUSTOM_REMINDER_CUSTOM_START)
        .ownerId(ownerId)
        .build();
  }

  @Test
  public void test_processWrongMessagePayload() {
    messageProcessor.transformAndExecute("{\n" + "  \"Type\": \"Notification");
    Mockito.verify(schedulerDetailsRepository, Mockito.times(0)).findById(Mockito.any());
  }

  @Test
  public void test_processNoMessageMetaData() {
    messageProcessor.transformAndExecute("{\"key\":\"value\"}");
    Mockito.verify(schedulerDetailsRepository, Mockito.times(0)).findById(Mockito.any());
  }
}