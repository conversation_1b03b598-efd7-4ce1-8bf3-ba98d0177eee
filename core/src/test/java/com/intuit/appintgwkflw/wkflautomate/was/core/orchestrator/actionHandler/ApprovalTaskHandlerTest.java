package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.AppConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.connector.ConnectorImpl;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.OnDemandHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionActivityDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.impl.ProcessDetailsRepoService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ApprovalTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.TaskHandlerName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails.ParameterDetails;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

/**
 * <AUTHOR>
 */
public class ApprovalTaskHandlerTest {
  @Mock
  private AppConfig appConfig;
  @Mock
  private ConnectorImpl connector;
  @Mock
  private DefinitionActivityDetailsRepository definitionActivityDetailsRepository;
  @Mock
  private ProcessDetailsRepoService processDetailsRepoService;
  @Mock
  private MetricLogger metricLogger;
  @Mock
  private OnDemandHelper onDemandHelper;

  @InjectMocks
  private ApprovalTaskHandler approvalTaskHandler;

  public static String PLACEHOLDER_VALUES_PATH = "placeholder/approval_placeholder.json";
  public static String PLACEHOLDER_VALUES_PATH_MULTI = "placeholder/multi_level_approval_placeholder.json";

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
    ReflectionTestUtils.setField(approvalTaskHandler, "metricLogger", metricLogger);
    TemplateDetails templateDetails =
        TemplateDetails.builder()
            .id(DefinitionTestConstants.TEMPLATE_ID)
            .templateName("customApproval")
            .ownerId(Long.valueOf("9999"))
            .allowMultipleDefinitions(true)
            .build();

    DefinitionDetails mockedDefinitionDetails =
        DefinitionDetails.builder()
            .definitionId("d1")
            .templateDetails(templateDetails)
            .build();
    Mockito.when(processDetailsRepoService.findByProcessIdWithoutDefinitionData(any()))
        .thenReturn(Optional.of(mockedDefinitionDetails));
  }

  @Test
  public void test_Success() {
    Map<String, String> processVariable = new HashMap<>();
    processVariable.put("key2", "12345");
    processVariable.put("entityType", "INVOICE");
    processVariable.put(WorkflowConstants.ACTIVITY_ID, "act123");

    WorkerActionRequest workerActionRequest = WorkerActionRequest.builder().inputVariables(processVariable).rootProcessInstanceId("323-3424-4253-12321").activityId("act223231").build();

    Mockito.when(definitionActivityDetailsRepository.findDefinitionActivityDetailByActivityIdAndDefinitionDetails_DefinitionId(
            any(), any()
        ))
        .thenReturn(
            Optional.of(
                DefinitionActivityDetail.builder()
                    .userAttributes(TestHelper.readResourceAsString(PLACEHOLDER_VALUES_PATH))
                    .build()
            )
        );

    Mockito.when(appConfig.getEnv()).thenReturn("test");
    Map<String, Object> res = approvalTaskHandler.executeAction(workerActionRequest);
    Assertions.assertEquals(workerActionRequest.getInputVariables().get(ApprovalTaskHandler.REQUEST_TYPE),
			WorkflowConstants.CUSTOM_WORKFLOW_REQUEST_TYPE);
    Assertions.assertEquals(res.get("act223231_response"), Boolean.TRUE.toString());
  }

  @Test
  public void test_Success_with_ondemand() {
    Map<String, String> processVariable = new HashMap<>();
    processVariable.put("key2", "12345");
    processVariable.put("entityType", "bill");
    processVariable.put(WorkflowConstants.ACTIVITY_ID, "act123");
    processVariable.put(WorkflowConstants.ON_DEMAND_APPROVAL, "true");

    WorkerActionRequest workerActionRequest = WorkerActionRequest.builder().inputVariables(processVariable).rootProcessInstanceId("323-3424-4253-12321").activityId("act223231").build();

    Mockito.when(definitionActivityDetailsRepository.findDefinitionActivityDetailByActivityIdAndDefinitionDetails_DefinitionId(
                    any(), any()
            ))
            .thenReturn(
                    Optional.of(
                            DefinitionActivityDetail.builder()
                                    .userAttributes(TestHelper.readResourceAsString(PLACEHOLDER_VALUES_PATH))
                                    .build()
                    )
            );

    Mockito.when(appConfig.getEnv()).thenReturn("test");
    Mockito.when(onDemandHelper.isRequestTypeOnDemandAproval(any())).thenReturn(true).thenReturn(false);
    Map<String, Object> res = approvalTaskHandler.executeAction(workerActionRequest);
    Assertions.assertEquals(workerActionRequest.getInputVariables().get(ApprovalTaskHandler.REQUEST_TYPE),
			WorkflowConstants.ON_DEMAND_APPROVAL_REQUEST_TYPE);
    Assertions.assertEquals(res.get("act223231_response"), Boolean.TRUE.toString());

    processVariable.put("entityType", "test");
    res = approvalTaskHandler.executeAction(workerActionRequest);
    Assertions.assertEquals(workerActionRequest.getInputVariables().get(ApprovalTaskHandler.REQUEST_TYPE),
        WorkflowConstants.CUSTOM_WORKFLOW_REQUEST_TYPE);


  }
  
  @Test
  public void test_Success_with_3p_txn() {
    Map<String, String> processVariable = new HashMap<>();
    processVariable.put("key2", "12345");
    processVariable.put("entityType", "INVOICE");
    processVariable.put(WorkflowConstants.ACTIVITY_ID, "act123");
    processVariable.put(WorkFlowVariables.IS_THIRD_PARTY_TXN.getName(), "true");

    WorkerActionRequest workerActionRequest = WorkerActionRequest.builder().inputVariables(processVariable).rootProcessInstanceId("323-3424-4253-12321").activityId("act223231").build();

    Mockito.when(definitionActivityDetailsRepository.findDefinitionActivityDetailByActivityIdAndDefinitionDetails_DefinitionId(
                    any(), any()
            ))
            .thenReturn(
                    Optional.of(
                            DefinitionActivityDetail.builder()
                                    .userAttributes(TestHelper.readResourceAsString(PLACEHOLDER_VALUES_PATH))
                                    .build()
                    )
            );

    Mockito.when(appConfig.getEnv()).thenReturn("test");
    Map<String, Object> res = approvalTaskHandler.executeAction(workerActionRequest);
	Assertions.assertEquals(workerActionRequest.getInputVariables().get(ApprovalTaskHandler.REQUEST_TYPE),
			WorkflowConstants.THIRD_PARTY_TXN_REQUEST_TYPE);
    Assertions.assertEquals(res.get("act223231_response"), Boolean.TRUE.toString());
  }
  
  @Test
  public void test_Success_with_3p_txn_false() {
    Map<String, String> processVariable = new HashMap<>();
    processVariable.put("key2", "12345");
    processVariable.put("entityType", "INVOICE");
    processVariable.put(WorkflowConstants.ACTIVITY_ID, "act123");
    processVariable.put(WorkFlowVariables.IS_THIRD_PARTY_TXN.getName(), "false");

    WorkerActionRequest workerActionRequest = WorkerActionRequest.builder().inputVariables(processVariable).rootProcessInstanceId("323-3424-4253-12321").activityId("act223231").build();

    Mockito.when(definitionActivityDetailsRepository.findDefinitionActivityDetailByActivityIdAndDefinitionDetails_DefinitionId(
                    any(), any()
            ))
            .thenReturn(
                    Optional.of(
                            DefinitionActivityDetail.builder()
                                    .userAttributes(TestHelper.readResourceAsString(PLACEHOLDER_VALUES_PATH))
                                    .build()
                    )
            );

    Mockito.when(appConfig.getEnv()).thenReturn("test");
    Map<String, Object> res = approvalTaskHandler.executeAction(workerActionRequest);
	Assertions.assertEquals(workerActionRequest.getInputVariables().get(ApprovalTaskHandler.REQUEST_TYPE),
			WorkflowConstants.CUSTOM_WORKFLOW_REQUEST_TYPE);
    Assertions.assertEquals(res.get("act223231_response"), Boolean.TRUE.toString());
  }
  
  @Test
  public void test_Success_with_3p_txn_null_status() {
    Map<String, String> processVariable = new HashMap<>();
    processVariable.put("key2", "12345");
    processVariable.put("entityType", "INVOICE");
    processVariable.put(WorkflowConstants.ACTIVITY_ID, "act123");
    processVariable.put(WorkFlowVariables.IS_THIRD_PARTY_TXN.getName(), "false");

    WorkerActionRequest workerActionRequest = WorkerActionRequest.builder().inputVariables(processVariable).rootProcessInstanceId("323-3424-4253-12321").activityId("act223231").build();

    Mockito.when(definitionActivityDetailsRepository.findDefinitionActivityDetailByActivityIdAndDefinitionDetails_DefinitionId(
                    any(), any()
            ))
            .thenReturn(
                    Optional.of(
                            DefinitionActivityDetail.builder()
                                    .userAttributes(TestHelper.readResourceAsString(PLACEHOLDER_VALUES_PATH))
                                    .build()
                    )
            );

    Mockito.when(appConfig.getEnv()).thenReturn("test");
    Map<String, Object> res = approvalTaskHandler.executeAction(workerActionRequest);
	Assertions.assertEquals(workerActionRequest.getInputVariables().get(ApprovalTaskHandler.REQUEST_TYPE),
			WorkflowConstants.CUSTOM_WORKFLOW_REQUEST_TYPE);
    Assertions.assertEquals(res.get("act223231_response"), Boolean.TRUE.toString());
  }

  @Test
  public void test_entityTypeWithDisplayValue_success() {
    Map<String, String> processVariable = new HashMap<>();
    processVariable.put("key2", "12345");
    processVariable.put("entityType", "Purchase order");
    processVariable.put(WorkflowConstants.ACTIVITY_ID, "act123");

    Map<String, Object>  parameterDetails = new HashMap<>();
    // Add `Assignee` field with its value
    Map<String, Object> assignee = new HashMap<>();
    assignee.put("fieldValue", Arrays.asList("9341454046708343", "9341454047031381"));
    parameterDetails.put("Assignee", assignee);
    // Add `approvalType` field with an empty list
    Map<String, Object> approvalType = new HashMap<>();
    approvalType.put("fieldValue", Arrays.asList());
    parameterDetails.put("approvalType", approvalType);

    WorkerActionRequest workerActionRequest = WorkerActionRequest.builder().inputVariables(processVariable).rootProcessInstanceId("323-3424-4253-12321").activityId("act223231").build();

    Mockito.when(definitionActivityDetailsRepository.findDefinitionActivityDetailByActivityIdAndDefinitionDetails_DefinitionId(
            any(), any()
        ))
        .thenReturn(
            Optional.of(
                DefinitionActivityDetail.builder()
                    .userAttributes(TestHelper.readResourceAsString(PLACEHOLDER_VALUES_PATH))
                    .build()
            )
        );

    Mockito.when(appConfig.getEnv()).thenReturn("test");
    Map<String, Object> res = approvalTaskHandler.getDerivedVariables(workerActionRequest, parameterDetails);
    Assertions.assertEquals(res.get(ApprovalTaskConstants.ENTITY_TYPE), "PURCHASE_ORDER");
  }

  @Test
  public void test_generateDerivedVariablesForOn_Demand_Approval() {
    Map<String, String> processVariable = new HashMap<>();
    processVariable.put("entityType", "CAS");
    processVariable.put(WorkflowConstants.APPROVER_DETAILS,"[{\"approvalType\":\"SEQUENTIAL\",\"approverDetails\":[{\"approverId\":\"9341451993702998\"},{\"approverId\":\"9341452549046354\"}]},\r\n   {\"approvalType\":\"ANY_ONE\",\"approverDetails\":[{\"approverId\":\"9341451993702998\"},{\"approverId\":\"9341452549046354\"}]}]");

    WorkerActionRequest workerActionRequest = WorkerActionRequest.builder().inputVariables(processVariable).rootProcessInstanceId("323-3424-4253-12321").build();
    Map<String, Object>  parameterDetails = new HashMap<>();

    Map<String, Object> res = approvalTaskHandler.getDerivedVariables(workerActionRequest,parameterDetails);
    Assertions.assertEquals(res.get(ApprovalTaskConstants.ENTITY_TYPE), "CAS");
    List<Map<String, String>> approverDetails = (java.util.List<Map<String,String>>)(res.get(ApprovalTaskConstants.APPROVER_DETAILS));
    Assertions.assertEquals(2, approverDetails.size());

  }

  @Test
  public void test_entityTypeWithDisplayValue_success_DerivedVariablesForMultiLevel() {
    Map<String, String> processVariable = new HashMap<>();
    processVariable.put("key2", "12345");
    processVariable.put("entityType", "Purchase order");
    processVariable.put(WorkflowConstants.ACTIVITY_ID, "act123");

    Map<String, ParameterDetails> parameterDetails = new HashMap<>();
    // Assignee
    ParameterDetails assigneeDetails = new ParameterDetails();
    assigneeDetails.setFieldValue(Arrays.asList(
        "Level1:9341454046708343",
        "Level1:93414540466967",
        "Level2:9341454047031381"
    ));
    // Approval Type
    ParameterDetails approvalTypeDetails = new ParameterDetails();
    approvalTypeDetails.setFieldValue(Arrays.asList(
        "Level1:Sequential",
        "Level2:Parallel"
    ));
    // Populate map
    parameterDetails.put("Assignee", assigneeDetails);
    parameterDetails.put("approvalType", approvalTypeDetails);


    WorkerActionRequest workerActionRequest = WorkerActionRequest.builder().inputVariables(processVariable).rootProcessInstanceId("323-3424-4253-12321").activityId("act223231").build();

    Mockito.when(definitionActivityDetailsRepository.findDefinitionActivityDetailByActivityIdAndDefinitionDetails_DefinitionId(
            any(), any()
        ))
        .thenReturn(
            Optional.of(
                DefinitionActivityDetail.builder()
                    .userAttributes(TestHelper.readResourceAsString(PLACEHOLDER_VALUES_PATH))
                    .build()
            )
        );

    Mockito.when(appConfig.getEnv()).thenReturn("test");
    Map<String, Object> res = approvalTaskHandler.getDerivedVariablesForMultiLevel(workerActionRequest, parameterDetails);
    Assertions.assertEquals(res.get(ApprovalTaskConstants.ENTITY_TYPE), "PURCHASE_ORDER");
  }

  @Test
  public void test_noRootActivityId_notThrowError() {

    Map<String, String> processVariable = new HashMap<>();
    processVariable.put("key2", "12345");
    processVariable.put("entityType", "INVOICE");

    WorkerActionRequest workerActionRequest = WorkerActionRequest.builder().inputVariables(processVariable).rootProcessInstanceId("323-3424-4253-12321").activityId("act223231").build();
    Mockito.when(appConfig.getEnv()).thenReturn("test");

    Map<String, Object> res = approvalTaskHandler.executeAction(workerActionRequest);
    Assertions.assertEquals(res.get("act223231_response"), Boolean.TRUE.toString());
  }

  @Test
  public void test_noRootProcessInstanceId_throwError() {
    Map<String, String> processVariable = new HashMap<>();
    processVariable.put("key2", "12345");
    processVariable.put("entityType", "invoice");
    processVariable.put(WorkflowConstants.ACTIVITY_ID, "act123");

    WorkerActionRequest workerActionRequest = WorkerActionRequest.builder().inputVariables(processVariable).activityId("act223231").build();
    Mockito.when(appConfig.getEnv()).thenReturn("test");

    Exception exception = Assertions.assertThrows(RuntimeException.class, () -> {
      approvalTaskHandler.executeAction(workerActionRequest);
    });
    Assertions.assertEquals(exception.getMessage(),
        WorkflowError.INVALID_ROOT_PROCESS_INSTANCE_ID.getErrorMessage());
  }

  @Test
  public void test_findDefinitionDetails_throwError() {
    Map<String, String> processVariable = new HashMap<>();
    processVariable.put("key2", "12345");
    processVariable.put("entityType", "invoice");
    processVariable.put(WorkflowConstants.ACTIVITY_ID, "act123");

    WorkerActionRequest workerActionRequest = WorkerActionRequest.builder().inputVariables(processVariable).rootProcessInstanceId("323-3424-4253-12321").activityId("act223231").build();
    Mockito.when(appConfig.getEnv()).thenReturn("test");
    Mockito.when(processDetailsRepoService.
        findByProcessIdWithoutDefinitionData(any())).thenReturn(Optional.empty());

    Exception exception = Assertions.assertThrows(RuntimeException.class, () -> {
      approvalTaskHandler.executeAction(workerActionRequest);
    });
    Assertions.assertEquals(exception.getMessage(),
        WorkflowError.DEFINITION_NOT_FOUND.getErrorMessage());
  }

  @Test
  public void test_findParentTaskDetails_throwError() {
    Map<String, String> processVariable = new HashMap<>();
    processVariable.put("key2", "12345");
    processVariable.put("entityType", "invoice");
    processVariable.put(WorkflowConstants.ACTIVITY_ID, "act123");

    WorkerActionRequest workerActionRequest = WorkerActionRequest.builder().inputVariables(processVariable).rootProcessInstanceId("323-3424-4253-12321").activityId("act223231").build();
    Mockito.when(appConfig.getEnv()).thenReturn("test");
    Mockito.when(definitionActivityDetailsRepository.
        findDefinitionActivityDetailByActivityIdAndDefinitionDetails_DefinitionId(
            any(), any())).thenReturn(Optional.empty());

    Exception exception = Assertions.assertThrows(RuntimeException.class, () -> {
      approvalTaskHandler.executeAction(workerActionRequest);
    });
    Assertions.assertEquals(exception.getMessage(),
        WorkflowError.DEFINITION_ACTIVITY_DETAILS_NOT_FOUND.getErrorMessage());
  }

  @Test
  public void testGetName() {
    Assert.assertEquals(
        TaskHandlerName.WAS_APPROVAL_HANDLER, approvalTaskHandler.getName());
  }

  @Test
  public void testMetricLogger() {
    Map<String, String> schemaTest = new HashMap<>();
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId(null)
            .processInstanceId("iId")
            .inputVariables(schemaTest)
            .handlerId("hId")
            .ownerId(1234L)
            .build();

    try {
      approvalTaskHandler.execute(workerActionRequest);
      Assert.fail();
    } catch (WorkflowGeneralException e) {
      Assert.assertEquals(WorkflowError.INVALID_INPUT.getErrorMessage(), e.getMessage());
      Mockito.verify(metricLogger, Mockito.times(1)).logErrorMetric(any(), any(), any());
    }
  }
  @Test
  public void testExecuteAction_WithMultiLevelHybridApproval_Success() {
    // Arrange
    Map<String, String> inputVariables = new HashMap<>();
    inputVariables.put("key1", "value1");
    inputVariables.put("activityId", "activity2965");
    inputVariables.put("entityType", "invoice");

    // Create parameterDetails
    Map<String, ParameterDetails> parameterDetails = new HashMap<>();
    ParameterDetails assigneeDetails = new ParameterDetails();
    assigneeDetails.setFieldValue(Arrays.asList(
        "Level1:9341454046708343",
        "Level1:93414540466967",
        "Level2:9341454047031381"
    ));
    ParameterDetails approvalTypeDetails = new ParameterDetails();
    approvalTypeDetails.setFieldValue(Arrays.asList(
        "Level1:Sequential",
        "Level2:Parallel"
    ));
    parameterDetails.put("Assignee", assigneeDetails);
    parameterDetails.put("approvalType", approvalTypeDetails);

    // Serialize parameterDetails to JSON and add to inputVariables
    String parameterDetailsJson = ObjectConverter.toJson(parameterDetails);
    inputVariables.put("parameterDetails", parameterDetailsJson);

    WorkerActionRequest workerActionRequest = WorkerActionRequest.builder()
        .inputVariables(inputVariables).
        rootProcessInstanceId("323-3424-4253-12234")
        .activityId("activity2965").
        handlerId("createApprovalRequest")
        .build();

    when(appConfig.getEnv()).thenReturn("test1");
    Mockito.when(definitionActivityDetailsRepository.findDefinitionActivityDetailByActivityIdAndDefinitionDetails_DefinitionId(
            any(), any()
        ))
        .thenReturn(
            Optional.of(
                DefinitionActivityDetail.builder()
                    .userAttributes(TestHelper.readResourceAsString(PLACEHOLDER_VALUES_PATH_MULTI))
                    .build()
            )
        );

    // Act
    Map<String, Object> result = approvalTaskHandler.executeAction(workerActionRequest);

    // Assert
    assertNotNull(result);
    assertEquals("true", result.get("activity2965_response"));
    assertEquals("CUSTOM_WORKFLOW", workerActionRequest.getInputVariables().get("requestType"));
    verify(connector, times(1)).execute(anyString(), anyString(), any());
  }
}
