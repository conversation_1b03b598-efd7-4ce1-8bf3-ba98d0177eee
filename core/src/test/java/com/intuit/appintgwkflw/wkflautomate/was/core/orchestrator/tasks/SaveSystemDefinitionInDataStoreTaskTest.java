package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doThrow;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.TemplateModelInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.DefinitionServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.DefinitionServiceHelperTest;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.DeployDefinitionResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DefinitionType;
import com.intuit.async.execution.request.State;
import java.util.ArrayList;
import java.util.List;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.dmn.Dmn;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;

public class SaveSystemDefinitionInDataStoreTaskTest {

  private SaveSystemDefinitionInDataStoreTask saveSystemDefinitionInDataStoreTask;

  private DefinitionServiceHelper definitionServiceHelper =
      Mockito.mock(DefinitionServiceHelper.class);

  private State state = new State();

  private TemplateDetails templateDetails = new TemplateDetails();
  private DeployDefinitionResponse response = Mockito.mock(DeployDefinitionResponse.class);

  private TemplateModelInstance templateModelInstance;
  private BpmnModelInstance bpmnModelInstance;
  private List<DmnModelInstance> dmnModelInstanceList = new ArrayList<>();

  private ArgumentCaptor<DeployDefinitionResponse> deployDefinitionResponseArgumentCaptor;

  private static final String INVOICE_APPROVAL_BPMN = "bpmn/invoiceapproval.bpmn";
  private static final String INVOICE_APPROVAL_DMN = "dmn/decision_invoiceapproval.dmn";
  private static final String WORKFLOW_ID = "id";

  @Before
  public void setUp() {
    deployDefinitionResponseArgumentCaptor =
        ArgumentCaptor.forClass(DeployDefinitionResponse.class);

    bpmnModelInstance = readBPMNFile(INVOICE_APPROVAL_BPMN);
    DmnModelInstance dmnModelInstance = readDMNFile(INVOICE_APPROVAL_DMN);
    dmnModelInstanceList.add(dmnModelInstance);

    templateModelInstance =
        new TemplateModelInstance(bpmnModelInstance, dmnModelInstanceList, false);
    saveSystemDefinitionInDataStoreTask = new SaveSystemDefinitionInDataStoreTask(
        definitionServiceHelper, templateModelInstance);

    state.addValue(AsyncTaskConstants.WORKFLOW_ID_KEY, WORKFLOW_ID);
    state.addValue(AsyncTaskConstants.BPMN_ENGINE_DEPLOY_RESPONSE_KEY, response);
  }

  @Test
  public void testExecute() {
    templateDetails.setDefinitionType(DefinitionType.SYSTEM);
    state.addValue(AsyncTaskConstants.SAVE_TEMPLATE_RESPONSE_KEY, templateDetails);
    saveSystemDefinitionInDataStoreTask.execute(state);
    Mockito.verify(definitionServiceHelper).saveUpdateDefinitionDetailsFromTemplate(eq(response),
        eq(templateDetails), eq(templateModelInstance),
        eq(WorkflowConstants.SYSTEM_OWNER_ID));
  }

  @Test
  public void testExecuteTestCustomApproval() {
    templateDetails.setDefinitionType(DefinitionType.SINGLE);
    templateDetails.setTemplateName(WorkflowConstants.CUSTOM_APPROVAL_TEMPLATE);
    state.addValue(AsyncTaskConstants.SAVE_TEMPLATE_RESPONSE_KEY, templateDetails);
    saveSystemDefinitionInDataStoreTask.execute(state);
    Mockito.verify(definitionServiceHelper).saveUpdateDefinitionDetailsFromTemplate(eq(response),
            eq(templateDetails), eq(templateModelInstance),
            eq(WorkflowConstants.SYSTEM_OWNER_ID));
  }

  @Test
  public void testExecuteTestSingleDefinitionNotCustomApproval() {
    templateDetails.setDefinitionType(DefinitionType.SINGLE);
    templateDetails.setTemplateName(WorkflowConstants.CUSTOM_REMINDER);
    state.addValue(AsyncTaskConstants.SAVE_TEMPLATE_RESPONSE_KEY, templateDetails);
    saveSystemDefinitionInDataStoreTask.execute(state);
    Mockito.verifyNoInteractions(definitionServiceHelper);
  }

  @Test
  public void testExecuteException(){
    doThrow(new WorkflowGeneralException("")).when(definitionServiceHelper).saveUpdateDefinitionDetailsFromTemplate(eq(response),
            eq(templateDetails), eq(templateModelInstance),
            eq(WorkflowConstants.SYSTEM_OWNER_ID));
    State result = saveSystemDefinitionInDataStoreTask.execute(state);
    Assert.assertNotNull(result);
    Assert.assertEquals(result.getValue(AsyncTaskConstants.SAVE_SYSTEM_DEF_TASK_FAILURE), true);
  }

  private static BpmnModelInstance readBPMNFile(String fileName) {
    return Bpmn.readModelFromStream(
        DefinitionServiceHelperTest.class.getClassLoader().getResourceAsStream(fileName));
  }

  private static DmnModelInstance readDMNFile(String fileName) {
    return Dmn.readModelFromStream(
        DefinitionServiceHelperTest.class.getClassLoader().getResourceAsStream(fileName));
  }
}
