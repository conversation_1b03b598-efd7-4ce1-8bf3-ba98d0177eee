package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.capability;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;

import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler.TransactionEntityFactory;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler.TriggerHandlerTestData;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.AuthHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ActivityDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.BpmnComponentType;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter.TransactionEntity;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import javax.persistence.NoResultException;

import org.javatuples.Pair;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class WorkflowProviderTemplateQueryCapabilityTest {

  private static final String ACTIVITY_ATTRIBUTES = "{\"modelAttributes\": {\"stepDetails\": \"{\\\"startEvent\\\":[\\\"startEvent\\\",\\\"Account-Manager\\\"] }\", \"taskDetails\": \"{ \\\"required\\\": true }\", \"activityName\": \"startEvent\", \"handlerDetails\": \"{\\\"taskHandler\\\":\\\"was\\\",\\\"recordType\\\":\\\"test\\\"}\", \"startableEvents\": \"[\\\"created\\\", \\\"updated\\\"]\", \"currentStepDetails\": \"{ \\\"required\\\": true }\", \"processVariablesDetails\": \"[{\\\"variableName\\\":\\\"userId\\\",\\\"variableType\\\":\\\"String\\\"},{\\\"variableName\\\":\\\"assigneeId\\\",\\\"variableType\\\":\\\"String\\\"}]\", \"ignoreProcessVariablesDetails\": \"true\"}}";

  private static final String ACTIVITY_ATTRIBUTES_WITHOUT_STARTABLE_EVENTS = "{\"modelAttributes\": {\"stepDetails\": \"{\\\"startEvent\\\":[\\\"startEvent\\\",\\\"Account-Manager\\\"] }\", \"taskDetails\": \"{ \\\"required\\\": true }\", \"activityName\": \"startEvent\", \"handlerDetails\": \"{\\\"taskHandler\\\":\\\"was\\\",\\\"recordType\\\":\\\"test\\\"}\", \"currentStepDetails\": \"{ \\\"required\\\": true }\", \"processVariablesDetails\": \"[{\\\"variableName\\\":\\\"userId\\\",\\\"variableType\\\":\\\"String\\\"},{\\\"variableName\\\":\\\"assigneeId\\\",\\\"variableType\\\":\\\"String\\\"}]\", \"ignoreProcessVariablesDetails\": \"true\"}}";

  private static final String UPDATED_EVENT = "updated";

  private static final String REALM_ID = "12345";

  private static final String WORKFLOW_ID = UUID.randomUUID().toString();

  private final TemplateDetails bpmnTemplateDetail = TriggerHandlerTestData.getBPMNTemplateDetails();

  @Mock
  private AuthHelper authHelper;

  @Mock
  private DefinitionDetailsRepository definitionDetailsRepository;

  @Mock
  private WASContextHandler contextHandler;

  @Mock
  private ActivityDetailsRepository activityDetailsRepository;

  @InjectMocks
  private WorkflowProviderTemplateQueryCapability workflowProviderTemplateQueryCapability;

  private TransactionEntity transactionEntityUpdated;

  @Before
  public void prepareMockData() {

    transactionEntityUpdated =
    	TransactionEntityFactory.getInstanceOf(
			TriggerHandlerTestData.prepareV3TriggerMessage(UPDATED_EVENT).getTriggerMessage(),
        contextHandler);
    Mockito.when(authHelper.getOwnerId()).thenReturn("12345");
  }

  @Test
  public void whenGetEnabledDefinitionForWorkflow_withEnabledDefinition_thenSuccess() {

    transactionEntityUpdated =
    	TransactionEntityFactory.getInstanceOf(
			TriggerHandlerTestData.prepareV3TriggerMessage("created").getTriggerMessage(),
        contextHandler);
    doReturn(TriggerHandlerTestData.getDefinitionDetails(bpmnTemplateDetail))
        .when(definitionDetailsRepository)
        .findEnabledDefinitionForWorkflowId(anyLong(), anyString());

    final DefinitionDetails definitionDetails =
        workflowProviderTemplateQueryCapability
            .getEnabledDefinitionForWorkflow(REALM_ID, WORKFLOW_ID);

    assertNotNull(definitionDetails);
  }

  @Test
  public void whenGetEnabledDefinitionForWorkflow_withNoEnabledDefinition_thenThrowException() {

    doThrow(NoResultException.class)
        .when(definitionDetailsRepository)
        .findEnabledDefinitionForWorkflowId(anyLong(), anyString());

    try {
      workflowProviderTemplateQueryCapability.getEnabledDefinitionForWorkflow(REALM_ID, WORKFLOW_ID);
      Assert.fail("WorkflowGeneralException expected");
    } catch (WorkflowGeneralException e) {
      Assert.assertEquals(
          e.getWorkflowError(), WorkflowError.ENABLED_DEFINITION_NOT_FOUND);
    }
  }

  @Test
  public void whenGetEnabledAndMarkedDefinitionForWorkflow_withEnabledDefinition_thenSuccess() {

    transactionEntityUpdated =
    		TransactionEntityFactory.getInstanceOf(
    				TriggerHandlerTestData.prepareV3TriggerMessage("created").getTriggerMessage(),
    	        contextHandler);
    doReturn(
            Collections.singletonList(
                TriggerHandlerTestData.getDefinitionDetails(bpmnTemplateDetail)))
        .when(definitionDetailsRepository)
        .findEnabledAndMarkedDefinitionForWorkflow(anyLong(), anyString(), anyBoolean());

    final List<DefinitionDetails> definitionDetails =
        workflowProviderTemplateQueryCapability.getEnabledAndMarkedDefinitionForWorkflow(
            REALM_ID, WORKFLOW_ID, false);

    assertNotNull(definitionDetails);
  }

  @Test
  public void
      whenGetEnabledAndMarkedDefinitionForWorkflow_withNoEnabledDefinition_thenThrowException() {

    doThrow(NoResultException.class)
        .when(definitionDetailsRepository)
        .findEnabledAndMarkedDefinitionForWorkflow(anyLong(), anyString(), anyBoolean());
    try {
      workflowProviderTemplateQueryCapability.getEnabledAndMarkedDefinitionForWorkflow(
          REALM_ID, WORKFLOW_ID, false);
      Assert.fail("WorkflowGeneralException expected");
    } catch (WorkflowGeneralException e) {
      Assert.assertEquals(
              WorkflowError.ENABLED_DEFINITION_NOT_FOUND, e.getWorkflowError());
    }
  }

  @Test
  public void getTemplateDetailsTestSuccess() {
    transactionEntityUpdated =
		TransactionEntityFactory.getInstanceOf(
				TriggerHandlerTestData.prepareV3TriggerMessage("created").getTriggerMessage(),
	        contextHandler);
    doReturn(TriggerHandlerTestData.getDefinitionDetails(bpmnTemplateDetail))
        .when(definitionDetailsRepository)
        .findEnabledDefinitionForWorkflowId(anyLong(), Mockito.any());

    List<TemplateDetails> templateDetailsList =
        workflowProviderTemplateQueryCapability.getTemplateDetails(transactionEntityUpdated);

    assertEquals(1, templateDetailsList.size());
  }

  @Test
  public void getTemplateDataTestSuccess() {
    transactionEntityUpdated =
		TransactionEntityFactory.getInstanceOf(
				TriggerHandlerTestData.prepareV3TriggerMessage("created").getTriggerMessage(),
	        contextHandler);
    DefinitionDetails definitionDetails =
        TriggerHandlerTestData.getDefinitionDetails(bpmnTemplateDetail);
    definitionDetails.setDefinitionData(definitionDetails.getTemplateDetails().getTemplateData());
    doReturn(definitionDetails)
        .when(definitionDetailsRepository)
        .findEnabledDefinitionForWorkflowId(anyLong(), Mockito.any());
    byte[] templateData =
        workflowProviderTemplateQueryCapability.getTemplateData(transactionEntityUpdated);
    assertTrue(templateData.length > 0);
  }

  @Test
  public void getTemplateDataNullTestSuccess() {
    transactionEntityUpdated =
        TransactionEntityFactory.getInstanceOf(
            TriggerHandlerTestData.prepareV3TriggerMessage("created").getTriggerMessage(),
            contextHandler);
    DefinitionDetails definitionDetails =
        TriggerHandlerTestData.getDefinitionDetails(bpmnTemplateDetail);
    definitionDetails.setDefinitionData(null);
    definitionDetails.getTemplateDetails().setTemplateData(new byte[]{0, 1, 2});
    doReturn(definitionDetails)
        .when(definitionDetailsRepository)
        .findEnabledDefinitionForWorkflowId(anyLong(), Mockito.any());
    byte[] templateData =
        workflowProviderTemplateQueryCapability.getTemplateData(transactionEntityUpdated);
    assertTrue(templateData.length > 0);
  }

  @Test
  public void getDmnTemplateDetailsSuccess() {
    DefinitionDetails definitionDetails =
        TriggerHandlerTestData.getDefinitionDetails(bpmnTemplateDetail);
    definitionDetails.setDefinitionData(definitionDetails.getTemplateDetails().getTemplateData());
    transactionEntityUpdated =
		TransactionEntityFactory.getInstanceOf(
				TriggerHandlerTestData.prepareV3TriggerMessage("created").getTriggerMessage(),
	        contextHandler);;
    doReturn(Optional.ofNullable(definitionDetails))
        .when(definitionDetailsRepository)
        .findDmnDefinitionDetailsByWorkflowIdAndOwnerId(Mockito.any(), Mockito.any());

    Pair<String, byte[]> templateDetails =
        workflowProviderTemplateQueryCapability.getDmnTemplateDetails(
            transactionEntityUpdated.getEventHeaders().getProviderWorkflowId(), Collections.emptyList());

    assertEquals("invoiceapproval_triggerTest.bpmn", templateDetails.getValue0());
    assertTrue(templateDetails.getValue1().length > 0);
  }

  @Test
  public void getEnabledDefinitionsSuccess() {
    transactionEntityUpdated =
		TransactionEntityFactory.getInstanceOf(
				TriggerHandlerTestData.prepareV3TriggerMessage("created").getTriggerMessage(),
	        contextHandler);;
    DefinitionDetails definitionDetails =
        TriggerHandlerTestData.getDefinitionDetails(bpmnTemplateDetail);
    definitionDetails.setDefinitionData(definitionDetails.getTemplateDetails().getTemplateData());
    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();
    definitionDetailsList.add(definitionDetails);
    doReturn(definitionDetailsList)
        .when(definitionDetailsRepository)
        .findEnabledAndMarkedDefinitionForWorkflow(anyLong(), Mockito.any(), anyBoolean());

    List<DefinitionDetails> defList =
        workflowProviderTemplateQueryCapability.getEnabledDefinitions(transactionEntityUpdated, true);

    assertEquals(1, defList.size());
  }

  @Test
  public void getTagVersionPayloadSuccess() {

    TransactionEntity transactionEntity =
        TransactionEntityFactory.getInstanceOf(
            TriggerHandlerTestData.prepareV3TriggerMessage("templateTagPayload")
                .getTriggerMessage(),
            contextHandler);
    Assert.assertNotNull(transactionEntity.getEventHeaders().getTags());
  }

  @Test
  public void getTagVersionWithoutTagVersionCase() {
    TransactionEntity transactionEntity =
        TransactionEntityFactory.getInstanceOf(
            TriggerHandlerTestData.prepareV3TriggerMessage("templateWithoutTagPayload")
                .getTriggerMessage(),
            contextHandler);

    Assert.assertNotNull(transactionEntity.getEventHeaders());
  }

  @Test
  public void getTagVersionWithoutTagKey() {
    TransactionEntity transactionEntity =
        TransactionEntityFactory.getInstanceOf(
            TriggerHandlerTestData.prepareV3TriggerMessage("templateWithoutTagKeyPayload")
                .getTriggerMessage(),
            contextHandler);

    Assert.assertNotNull(transactionEntity.getEventHeaders());
  }

  @Test
  public void testFetchInitialStartEventActivityDetail() {
    DefinitionDetails definitionDetails =
            TriggerHandlerTestData.getDefinitionDetails(bpmnTemplateDetail);

    List<ActivityDetail> activityDetailList = new ArrayList<>();
    ActivityDetail activityDetail = new ActivityDetail();
    activityDetail.setId(1L);
    activityDetail.setActivityId("startEvent");
    activityDetail.setActivityType(BpmnComponentType.START_EVENT.getName());
    activityDetail.setAttributes(ACTIVITY_ATTRIBUTES);
    ActivityDetail activityDetail1 = new ActivityDetail();
    activityDetail1.setId(2L);
    activityDetail1.setActivityId("startEvent");
    activityDetail1.setActivityType(BpmnComponentType.START_EVENT.getName());
    activityDetail1.setAttributes(ACTIVITY_ATTRIBUTES_WITHOUT_STARTABLE_EVENTS);

    activityDetailList.add(activityDetail);
    activityDetailList.add(activityDetail1);

    transactionEntityUpdated.getEventHeaders().setProviderWorkflowId("workflowId");
    Mockito.when(definitionDetailsRepository.findDefinitionsWithCustomFilterAndSelection(ArgumentMatchers.anyMap(),
                    ArgumentMatchers.anyList()))
            .thenReturn(Optional.of(Collections.singletonList(definitionDetails)));

    Mockito.when(activityDetailsRepository.findByTemplateIdAndActivityType(bpmnTemplateDetail.getId(), BpmnComponentType.START_EVENT.getName()))
            .thenReturn(activityDetailList);

    // Act
    ActivityDetail actualProperties = workflowProviderTemplateQueryCapability
            .fetchInitialStartEventActivityDetail(transactionEntityUpdated);

    Assert.assertNotNull(actualProperties);
    // Assert
    Assert.assertEquals(activityDetail, actualProperties);

    Mockito.verify(authHelper).getOwnerId();
    Mockito.verify(activityDetailsRepository).findByTemplateIdAndActivityType(bpmnTemplateDetail.getId(), BpmnComponentType.START_EVENT.getName());
  }

  @Test
  public void testFetchInitialStartEventActivityDetailWithNoTemplateId() {
    transactionEntityUpdated.getEventHeaders().setProviderWorkflowId("workflowId");
    Mockito.when(definitionDetailsRepository.findDefinitionsWithCustomFilterAndSelection(ArgumentMatchers.anyMap(),
                    ArgumentMatchers.anyList()))
            .thenReturn(Optional.empty());

    // Act
    Assert.assertThrows(WorkflowGeneralException.class, () -> workflowProviderTemplateQueryCapability
            .fetchInitialStartEventActivityDetail(transactionEntityUpdated));

    Mockito.verify(authHelper, Mockito.times(2)).getOwnerId();
    Mockito.verify(definitionDetailsRepository).findDefinitionsWithCustomFilterAndSelection(ArgumentMatchers.anyMap(),
            ArgumentMatchers.anyList());
  }

}
