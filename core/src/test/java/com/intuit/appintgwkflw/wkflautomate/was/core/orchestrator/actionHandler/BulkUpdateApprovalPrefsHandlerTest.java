package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables.RESPONSE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.UNDERSCORE;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyLong;

import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.AppConnectConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.AuthDetailsService;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.AuthDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.TaskHandlerName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CustomWorkflowType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ModelType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import com.intuit.appintgwkflw.wkflautomate.was.workflowvariability.DefinitionPendingDeletion;
import com.intuit.v4.Authorization;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.camunda.bpm.engine.variable.VariableMap;
import org.camunda.bpm.engine.variable.impl.VariableMapImpl;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

public class BulkUpdateApprovalPrefsHandlerTest {
  @InjectMocks private BulkUpdateApprovalPrefsHandler bulkUpdateApprovalPrefsHandler;
  @Mock private MetricLogger metricLogger;
  @Mock private DefinitionDetailsRepository definitionDetailsRepository;
  @Mock private AuthDetailsService authDetailsService;
  @Mock private AppConnectConfig appConnectConfig;
  @Mock private AppconnectDuzzitRestExecutionHelper appconnectDuzzitRestExecutionHelper;
  @Mock private TemplateDetails bpmnTemplateDetail;
  private Authorization authorization = TestHelper.mockAuthorization("123");

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
    ReflectionTestUtils.setField(bulkUpdateApprovalPrefsHandler, "metricLogger", metricLogger);
    Mockito.when(appConnectConfig.getConnectorEndpoint())
        .thenReturn("https://e2e.api.intuit.com/appconnect/connector");
    Mockito.when(appConnectConfig.getProviderAppId()).thenReturn("AP13702");
  }

  @Test
  public void testGetName() {
    Assert.assertEquals(
        TaskHandlerName.BULK_UPDATE_APPROVAL_PREFS_HANDLER,
        bulkUpdateApprovalPrefsHandler.getName());
  }

  @Test
  public void testMetricLogger() {
    Map<String, String> testSchema = new HashMap<>();
    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail, authorization, "123");
    definitionDetail.setTemplateDetails(
        new TemplateDetails().builder().templateName("customApproval").build());
    DefinitionPendingDeletion definitionPendingDeletion =
        DefinitionPendingDeletion.buildFromDefinitionDetails(definitionDetail);
    testSchema.put(
        WorkflowConstants.DEFINITION_DETAILS_PENDING_DELETION,
        ObjectConverter.toJson(Arrays.asList(definitionPendingDeletion)));
    Mockito.when(
            definitionDetailsRepository.findAllEnabledDefinitionsForOwnerIdAndTemplateName(
                anyLong(), any(), any(), anyBoolean()))
        .thenThrow(WorkflowGeneralException.class);
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId(null)
            .processInstanceId("iId")
            .inputVariables(testSchema)
            .handlerId("hId")
            .ownerId(1234L)
            .build();

    try {
      bulkUpdateApprovalPrefsHandler.execute(workerActionRequest);
      Assert.fail();
    } catch (Exception e) {
      Mockito.verify(metricLogger, Mockito.times(1)).logErrorMetric(any(), any(), any());
    }
  }

  @Test
  public void testExecute_Success_NoDefinitions() {
    Map<String, String> testSchema = new HashMap<>();
    testSchema.put(WorkflowConstants.DEFINITION_DETAILS_PENDING_DELETION, "");
    VariableMap variableMap = new VariableMapImpl();
    List<Map<String, String>> workflowFilters= List.of(Map.of("recordType","invoice","workflowType","approval"));
    variableMap.put(WorkflowConstants.WORKFLOW_FILTER,workflowFilters);
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId(null)
            .processInstanceId("iId")
            .inputVariables(testSchema)
            .variableMap(variableMap)
            .handlerId("hId")
            .ownerId(1234L)
            .build();

    Map<String, Object> response = bulkUpdateApprovalPrefsHandler.execute(workerActionRequest);

    Mockito.verify(authDetailsService, Mockito.times(1)).getAuthDetailsFromRealmId(any());
    Mockito.verify(definitionDetailsRepository, Mockito.never())
        .findAllEnabledDefinitionsForOwnerIdAndTemplateName(anyLong(), any(), any(), anyBoolean());
    Mockito.verify(appconnectDuzzitRestExecutionHelper, Mockito.times(1))
        .executeAppConnectRequest(any(), any(), any());
  }

  @Test
  public void test_disableSalesSettingsTrue() {
    Map<String, String> testSchema = new HashMap<>();
    testSchema.put(WorkflowConstants.DEFINITION_DETAILS_PENDING_DELETION, "");

    VariableMap variableMap = new VariableMapImpl();
    List<Map<String, String>> workflowFilters = List.of(
        Map.of("recordType", "invoice", "workflowType", "approval"));
    variableMap.put(WorkflowConstants.WORKFLOW_FILTER, workflowFilters);
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId(null)
            .processInstanceId("iId")
            .inputVariables(testSchema)
            .variableMap(variableMap)
            .handlerId("hId")
            .ownerId(1234L)
            .build();

    Map<String, Object> response = bulkUpdateApprovalPrefsHandler.execute(workerActionRequest);

    Mockito.verify(authDetailsService, Mockito.times(1)).getAuthDetailsFromRealmId(any());
    Mockito.verify(appconnectDuzzitRestExecutionHelper, Mockito.times(1))
        .executeAppConnectRequest(any(), any(), any());

  }

  @Test
  public void test_disableSalesSettingsFalse() {
    Map<String, String> testSchema = new HashMap<>();
    testSchema.put(WorkflowConstants.DEFINITION_DETAILS_PENDING_DELETION, "");

    VariableMap variableMap = new VariableMapImpl();

    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId(null)
            .processInstanceId("iId")
            .inputVariables(testSchema)
            .variableMap(variableMap)
            .handlerId("hId")
            .ownerId(1234L)
            .build();

    Map<String, Object> response = bulkUpdateApprovalPrefsHandler.execute(workerActionRequest);

    Mockito.verify(authDetailsService, Mockito.times(0)).getAuthDetailsFromRealmId(any());
    Mockito.verify(appconnectDuzzitRestExecutionHelper, Mockito.times(0))
        .executeAppConnectRequest(any(), any(), any());

  }
  @Test
  public void testExecute_Success_NoApprovalWorkflows() {
    Map<String, String> testSchema = new HashMap<>();
    testSchema.put(WorkflowConstants.INTUIT_REALMID, "1234");
    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail, authorization, "123");
    definitionDetail.setTemplateDetails(
        new TemplateDetails().builder().templateName("customReminder").build());
    DefinitionPendingDeletion definitionPendingDeletion =
        DefinitionPendingDeletion.buildFromDefinitionDetails(definitionDetail);
    testSchema.put(
        WorkflowConstants.DEFINITION_DETAILS_PENDING_DELETION,
        ObjectConverter.toJson(Arrays.asList(definitionPendingDeletion)));

    Mockito.when(
            definitionDetailsRepository.findAllEnabledDefinitionsForOwnerIdAndTemplateName(
                1234L, ModelType.BPMN, CustomWorkflowType.APPROVAL.getTemplateName(), false))
        .thenReturn(Optional.of(Collections.emptyList()));
    VariableMap variableMap = new VariableMapImpl();
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId(null)
            .processInstanceId("iId")
            .inputVariables(testSchema)
            .variableMap(variableMap)
            .handlerId("hId")
            .ownerId(1234L)
            .build();

    Map<String, Object> response = bulkUpdateApprovalPrefsHandler.execute(workerActionRequest);

    Mockito.verify(authDetailsService, Mockito.times(0)).getAuthDetailsFromRealmId(any());

    Mockito.verify(appconnectDuzzitRestExecutionHelper, Mockito.times(0))
        .executeAppConnectRequest(any(), any(), any());
  }

  @Test
  public void testExecute_Success_ApprovalWorkflowsToBeDeleted() {
    Map<String, String> testSchema = new HashMap<>();
    testSchema.put(WorkflowConstants.INTUIT_REALMID, "1234");
    DefinitionDetails definitionDetail1 =
        TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail, authorization, "123");
    definitionDetail1.setDefinitionKey("key1");
    definitionDetail1.setTemplateDetails(
        new TemplateDetails().builder().templateName("customApproval").build());

    DefinitionDetails definitionDetail2 =
        TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail, authorization, "1234");
    definitionDetail2.setDefinitionKey("key2");
    definitionDetail2.setTemplateDetails(
        new TemplateDetails().builder().templateName("customReminder").build());
    DefinitionPendingDeletion definitionPendingDeletion1 =
        DefinitionPendingDeletion.buildFromDefinitionDetails(definitionDetail1);
    DefinitionPendingDeletion definitionPendingDeletion2 =
        DefinitionPendingDeletion.buildFromDefinitionDetails(definitionDetail2);
    testSchema.put(
        WorkflowConstants.DEFINITION_DETAILS_PENDING_DELETION,
        ObjectConverter.toJson(
            Arrays.asList(definitionPendingDeletion1, definitionPendingDeletion2)));
    VariableMap variableMap = new VariableMapImpl();
    List<Map<String, String>> workflowFilters= List.of(Map.of("recordType","invoice","workflowType","approval"));
    variableMap.put(WorkflowConstants.WORKFLOW_FILTER,workflowFilters);
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId(null)
            .processInstanceId("iId")
            .inputVariables(testSchema)
            .variableMap(variableMap)
            .handlerId("hId")
            .ownerId(1234L)
            .build();

    Mockito.when(authDetailsService.getAuthDetailsFromRealmId(any())).thenReturn(new AuthDetails());
    Mockito.when(
            definitionDetailsRepository.findAllEnabledDefinitionsForOwnerIdAndTemplateName(
                anyLong(), any(), any(), anyBoolean()))
        .thenReturn(Optional.of(Arrays.asList(definitionDetail1)));

    Map<String, Object> appconnectResponse = new HashMap<>();
    appconnectResponse.put(
        new StringBuilder("aaId").append(UNDERSCORE).append(RESPONSE.getName()).toString(), true);

    Mockito.when(appconnectDuzzitRestExecutionHelper.executeAppConnectRequest(any(), any(), any()))
        .thenReturn(appconnectResponse);

    Map<String, Object> response = bulkUpdateApprovalPrefsHandler.execute(workerActionRequest);

    Mockito.verify(authDetailsService, Mockito.times(1)).getAuthDetailsFromRealmId(any());

    Mockito.verify(appconnectDuzzitRestExecutionHelper, Mockito.times(1))
        .executeAppConnectRequest(any(), any(), any());
    Assert.assertEquals(response.get("aaId_response"), Boolean.TRUE);
  }
}
