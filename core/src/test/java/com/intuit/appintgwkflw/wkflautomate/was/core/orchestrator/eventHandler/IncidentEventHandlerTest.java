package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.eventHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.ENTITY_TYPE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType.INVOICE;
import static org.junit.jupiter.api.Assertions.assertEquals;

import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.impl.ProcessDomainEventHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.common.IncidentTaskManager;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.impl.ProcessDetailsRepoService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.WorkflowMetaData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.incident.WorkflowIncident;
import com.intuit.appintgwkflw.wkflautomate.was.event.api.EventPublisherCapability;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import org.hibernate.exception.ConstraintViolationException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class IncidentEventHandlerTest {

  @Mock
  private ProcessDetailsRepository processDetailsRepository;
  @Mock
  private EventPublisherCapability eventPublisherCapability;
  @Mock
  private ProcessDomainEventHandler processDomainEventHandler;
  @Mock
  private ProcessDetailsRepoService processDetailsRepoService;
  @Mock
  private MetricLogger metricsLogger;
  @Mock
  private IncidentTaskManager incidentTaskManager;
  @InjectMocks
  private IncidentEventHandler incidentEventHandler;
  @Mock
  private WASContextHandler wasContextHandler;

  private Optional<ProcessDetails> processDetails;
  private Optional<ProcessDetails> parentProcessDetails;

  @Before
  public void init() {
    MockitoAnnotations.openMocks(this);
    incidentEventHandler =
            new IncidentEventHandler(processDetailsRepository, processDetailsRepoService,eventPublisherCapability, processDomainEventHandler,
                    metricsLogger, incidentTaskManager, wasContextHandler);
    TemplateDetails templateDetails = TemplateDetails.builder()
            .templateName("templateName")
            .build();

    DefinitionDetails definitionDetails = DefinitionDetails.builder()
            .recordType(INVOICE)
            .templateDetails(templateDetails)
            .build();
    processDetails = Optional.of(
            ProcessDetails.builder()
                    .definitionDetails(definitionDetails)
                    .processId("p11")
                    .ownerId(123L)
                    .recordId("rId")
                    .build());
    parentProcessDetails = Optional.of(
        ProcessDetails.builder()
            .definitionDetails(definitionDetails)
            .processId("p11")
            .ownerId(123L)
            .recordId("prID")
            .build());
  }

  @Test
  public void testTransformEvent() {
    WorkflowIncident workflowIncident = getIncidentPayload();
    WorkflowIncident event = incidentEventHandler
            .transform(ObjectConverter.toJson(workflowIncident));

    Assert.assertNotNull(event);
    Assert.assertNotNull(event.getWorkflowMetadata());
    Assert.assertEquals("p11", event.getWorkflowMetadata().getProcessInstanceId());
    Assert.assertEquals("owww1", event.getWorkflowMetadata().getWorkflowOwnerId());
    Assert.assertEquals("abc", event.getWorkflowMetadata().getWorkflowName());
    Assert.assertEquals(
            "a11", event.getActivityName());

    Assert.assertEquals("eng1", event.getBusinessEntityId());
    Assert.assertEquals("engagement", event.getBusinessEntityType());
    Assert.assertEquals("failed task", event.getIncidentMsg());
  }
  @Test(expected = WorkflowGeneralException.class)
  public void testTransformNullEvent() {
    incidentEventHandler.transform(null);
  }
  @Test(expected = WorkflowGeneralException.class)
  public void testTransformIncorrectEvent() {
    incidentEventHandler.transform("hello");
  }
  @Test
  public void testExecuteValidCase() {
    WorkflowIncident event = getIncidentPayload();
    Mockito
            .when(processDetailsRepository.findByIdWithoutDefinitionData(event.getWorkflowMetadata().getProcessInstanceId()))
            .thenReturn(processDetails);

    Mockito.doNothing().when(incidentTaskManager).execute(Mockito.any(),
            Mockito.any(), Mockito.any());

    Mockito
            .when(processDetailsRepoService.updateEntityVersion(processDetails.get()))
            .thenReturn(processDetails.get());

    incidentEventHandler.execute(event, new HashMap<>());

    Mockito.verify(incidentTaskManager, Mockito.times(1)).execute(Mockito.any(),
            Mockito.any(), Mockito.any());

    Mockito.verify(eventPublisherCapability, Mockito.times(1))
            .publish(Mockito.any(), Mockito.any());

    Mockito.verify(processDomainEventHandler, Mockito.times(1))
            .publish(Mockito.any());
  }

  @Test
  public void testExecuteODA() {
    WorkflowIncident event = getIncidentPayload();
    event.setVariables(Map.of(ENTITY_TYPE,INVOICE.getRecordType()));
    processDetails = Optional.of(
        ProcessDetails.builder()
            .definitionDetails(DefinitionDetails.builder().templateDetails(TemplateDetails.builder().templateName("test").build()).build())
            .processId("p11")
            .ownerId(123L)
            .recordId("rId")
            .build());
    Mockito
        .when(processDetailsRepository.findByIdWithoutDefinitionData(event.getWorkflowMetadata().getProcessInstanceId()))
        .thenReturn(processDetails);

    Mockito.doNothing().when(incidentTaskManager).execute(Mockito.any(),
        Mockito.any(), Mockito.any());

    Mockito
        .when(processDetailsRepoService.updateEntityVersion(processDetails.get()))
        .thenReturn(processDetails.get());

    incidentEventHandler.execute(event, new HashMap<>());

    assertEquals(INVOICE.getRecordType(),event.getBusinessEntityType());

    Mockito.verify(eventPublisherCapability, Mockito.times(1))
        .publish(Mockito.any(), Mockito.any());


  }

  @Test
  public void testExecuteWithParentProcess() {
    processDetails.get().setParentId(parentProcessDetails.get().getProcessId());
    processDetails.get().setRecordId(null);
    WorkflowIncident event = getIncidentPayload();

    Mockito.when(processDetailsRepository.findByProcessId(parentProcessDetails.get().getProcessId())).thenReturn(parentProcessDetails);
    Mockito
        .when(processDetailsRepository.findByIdWithoutDefinitionData(event.getWorkflowMetadata().getProcessInstanceId()))
        .thenReturn(processDetails);

    Mockito.doNothing().when(incidentTaskManager).execute(Mockito.any(),
        Mockito.any(), Mockito.any());

    Mockito
        .when(processDetailsRepoService.updateEntityVersion(processDetails.get()))
        .thenReturn(processDetails.get());

    incidentEventHandler.execute(event, new HashMap<>());

    Mockito.verify(incidentTaskManager, Mockito.times(1)).execute(Mockito.any(),
        Mockito.any(), Mockito.any());

    Mockito.verify(eventPublisherCapability, Mockito.times(1))
        .publish(Mockito.any(), Mockito.any());

    Mockito.verify(processDomainEventHandler, Mockito.times(1))
        .publish(Mockito.any());

    Assert.assertNotNull(event.getBusinessEntityType());
    Assert.assertEquals(event.getBusinessEntityType(), INVOICE.getRecordType());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testExecuteWithChildProcessThrowingConstraintViolation() {
    processDetails.get().setParentId(parentProcessDetails.get().getProcessId());
    processDetails.get().setRecordId(null);
    WorkflowIncident event = getIncidentPayload();

    Mockito.when(
            processDetailsRepository.findByProcessId(parentProcessDetails.get().getProcessId()))
        .thenThrow(ConstraintViolationException.class);
    Mockito
        .when(processDetailsRepository.findByIdWithoutDefinitionData(
            event.getWorkflowMetadata().getProcessInstanceId()))
        .thenReturn(processDetails);

    incidentEventHandler.execute(event, new HashMap<>());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testExecuteNullDBObject() {
    Mockito.when(processDetailsRepository.findByIdWithoutDefinitionData("p11"))
            .thenReturn(Optional.empty());

    WorkflowIncident event = getIncidentPayload();
    incidentEventHandler.execute(event, null);

    Mockito.verify(eventPublisherCapability, Mockito.times(1))
            .publish(Mockito.any(), Mockito.any());
  }
  @Test
  public void testGetName() {
    EventEntityType eventEntityType = incidentEventHandler.getName();

    Assert.assertEquals(eventEntityType.getEntityType(), EventEntityType.INCIDENT.getEntityType());
  }
  private WorkflowIncident getIncidentPayload() {
    WorkflowMetaData workflowMetaData = WorkflowMetaData.builder()
            .workflowName("abc")
            .processInstanceId("p11")
            .workflowOwnerId("owww1")
            .build();

    return WorkflowIncident.builder().activityName("a11")
            .incidentMsg("failed task")
            .businessEntityId("eng1")
            .businessEntityType("engagement")
            .workflowMetadata(workflowMetaData)
            .build();
  }

}