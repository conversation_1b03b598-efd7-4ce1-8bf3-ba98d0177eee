package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services;



import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.mocks.MockHelper;
import java.io.IOException;
import java.io.InputStream;
import org.junit.Assert;
import org.junit.Test;

public class MockHelperTest {

  @Test
  public void readResourceAsString() {
    String data = MockHelper.readResourceAsString("duzzitOutputs/appconnectCRResponse.json");
    Assert.assertNotNull(data);
  }

  @Test
  public void readResourceAsStringThrowsException() {
    try {
      InputStream in = mock(InputStream.class);
      when(in.read()).thenThrow(new IOException());
      MockHelper.readResourceAsString("duzzitOutputs/appconnectCRResponse.json1");
      Assert.fail();
    } catch (Exception e) {

    }
  }
}
