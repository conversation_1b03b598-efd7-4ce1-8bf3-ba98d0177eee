package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import static org.mockito.ArgumentMatchers.any;

import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.AuthDetailsService;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.AppConnectService;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.WorkerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.AuthDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.TaskHandlerName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import java.util.HashMap;
import java.util.Map;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

public class AppConnectWorkflowDisableHandlerTest {

    @Mock
    private WorkerUtil workerUtil;

    @Mock
    AuthDetailsService authDetailsService;

    @Mock AppConnectService appConnectService;
    @Mock
    MetricLogger metricLogger;

    @InjectMocks
    private AppConnectWorkflowDisableHandler actionHandler;

    private static Map<String, String> schema = new HashMap<>();

    @Before
    public void init() {
        MockitoAnnotations.initMocks(this);
        ReflectionTestUtils.setField(actionHandler, "metricLogger", metricLogger);
    }

    private static final String parametersSchema =
            TestHelper.readResourceAsString("schema/testData/parameters.json");

    static {
        schema.put(WorkFlowVariables.PARAMETERS_KEY.getName(), parametersSchema);
    }

    @SuppressWarnings("unchecked")
    @Test
    public void testExecuteAppActionSuccess() {
        WorkerActionRequest workerActionRequest =
                WorkerActionRequest.builder()
                        .activityId("aId")
                        .processDefinitionId("dId")
                        .processInstanceId("iId")
                        .inputVariables(schema)
                        .handlerId("hId")
                        .ownerId(1234L)
                        .build();

        AuthDetails authDetails = new AuthDetails();
        authDetails.setSubscriptionId("123");
        Mockito.when(authDetailsService.getAuthDetailsFromRealmId("1234")).thenReturn(authDetails);
        Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);

        Map<String, Object> resp = actionHandler.executeAction(workerActionRequest);

    Assert.assertTrue((Boolean) resp.get(WorkFlowVariables.RESPONSE.getName()));
    }

    @Test
    public void testExecuteAppActionExceptionIgnored() {
        WorkerActionRequest workerActionRequest =
                WorkerActionRequest.builder()
                        .activityId("aId")
                        .processDefinitionId("dId")
                        .processInstanceId("iId")
                        .inputVariables(schema)
                        .handlerId("hId")
                        .ownerId(1234L)
                        .build();

        AuthDetails authDetails = new AuthDetails();
        authDetails.setSubscriptionId("123");
        Mockito.when(authDetailsService.getAuthDetailsFromRealmId("1234")).thenReturn(authDetails);
        Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);
        Mockito.when(appConnectService.disableAppConnectWorkflow(Mockito.anyString(), Mockito.any()))
            .thenThrow(new WorkflowGeneralException(
                WorkflowError.ACTIVATE_DEACTIVATE_WORKFLOW_FAIL, "dId",
                WorkflowConstants.WORKFLOW_ALREADY_INACTIVE));
        try {
            Map<String, Object> resp = actionHandler.executeAction(workerActionRequest);
        } catch (Exception e) {
            Assert.fail();
        }
    }

    @Test
    public void testExecuteAppActionExceptionThrown() {
        WorkerActionRequest workerActionRequest =
                WorkerActionRequest.builder()
                        .activityId("aId")
                        .processDefinitionId("dId")
                        .processInstanceId("iId")
                        .inputVariables(schema)
                        .handlerId("hId")
                        .ownerId(1234L)
                        .build();

        AuthDetails authDetails = new AuthDetails();
        authDetails.setSubscriptionId("123");
        Mockito.when(authDetailsService.getAuthDetailsFromRealmId("1234")).thenReturn(authDetails);
        Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);
        Mockito.when(appConnectService.disableAppConnectWorkflow(Mockito.anyString(), Mockito.any())).thenThrow(WorkflowGeneralException.class);

        try {
            Map<String, Object> resp = actionHandler.executeAction(workerActionRequest);
            Assert.fail();
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), null);
        }
    }

  @Test
  public void testGetName() {
    Assert.assertEquals(
        TaskHandlerName.WAS_APP_CONNECT_WORKFLOW_DISABLE_HANDLER, actionHandler.getName());
  }

    @Test
    public void testExecuteAppActionInvalidParameterException() {
        Map<String, String> schemaTest = new HashMap<>();
        WorkerActionRequest workerActionRequest =
            WorkerActionRequest.builder()
                .activityId("aId")
                .processDefinitionId(null)
                .processInstanceId("iId")
                .inputVariables(schemaTest)
                .handlerId("hId")
                .ownerId(1234L)
                .build();

        AuthDetails authDetails = new AuthDetails();
        authDetails.setSubscriptionId("123");
        Mockito.when(authDetailsService.getAuthDetailsFromRealmId("1234"))
            .thenThrow(WorkflowGeneralException.class);
        Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);

    try {
      actionHandler.executeAction(workerActionRequest);
      Assert.fail();
    } catch (WorkflowGeneralException e) {
      Assert.assertEquals(
          WorkflowError.INVALID_PARAMETER_DETAILS.getErrorMessage(), e.getMessage());
    }
  }

  @Test
  public void testMetricLogger() {
    Map<String, String> schemaTest = new HashMap<>();
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId(null)
            .processInstanceId("iId")
            .inputVariables(schemaTest)
            .handlerId("hId")
            .ownerId(1234L)
            .build();

    AuthDetails authDetails = new AuthDetails();
    authDetails.setSubscriptionId("123");
    Mockito.when(authDetailsService.getAuthDetailsFromRealmId("1234"))
        .thenThrow(WorkflowGeneralException.class);
    Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);

    try {
      actionHandler.execute(workerActionRequest);
      Assert.fail();
    } catch (WorkflowGeneralException e) {
      Assert.assertEquals(WorkflowError.INVALID_INPUT.getErrorMessage(), e.getMessage());
      Mockito.verify(metricLogger, Mockito.times(1)).logErrorMetric(any(), any(), any());
    }
  }
}
