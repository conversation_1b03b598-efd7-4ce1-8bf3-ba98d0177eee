package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent;

import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.impl.CustomReminderFetchTransactionsProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DefinitionEventType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.DefinitionEvent;
import java.util.Collections;
import java.util.Map;
import java.util.Optional;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

/** <AUTHOR> */
@RunWith(MockitoJUnitRunner.class)
public class DefinitionEventHandlerTest {
  @InjectMocks private DefinitionEventHandler handler;
  @Mock private DefinitionDetailsRepository definitionDetailsRepository;
  @Mock private CustomReminderFetchTransactionsProcessor customReminderFetchTransactionsProcessor;
  @Mock private MetricLogger metricLogger;
  @Mock private WASContextHandler wasContextHandler;

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
    ReflectionTestUtils.setField(
        handler, "definitionDetailsRepository", definitionDetailsRepository);
    ReflectionTestUtils.setField(handler, "metricLogger", metricLogger);
    ReflectionTestUtils.setField(handler, "wasContextHandler", wasContextHandler);
    DefinitionDetails definitionDetails = new DefinitionDetails();
    definitionDetails.setDefinitionId("def123");
    Mockito.when(definitionDetailsRepository.findByDefinitionId("def123"))
        .thenReturn(Optional.of(definitionDetails));
  }

  @Test
  public void testTransform() {
    DefinitionEvent e =
        DefinitionEvent.builder()
            .definitionId("def123")
            .handlerType(DefinitionEventType.CUSTOM_REMINDER_FETCH_TRANSACTIONS.getEventType())
            .metaData(Collections.EMPTY_MAP)
            .build();
    DefinitionEvent result = handler.transform(ObjectConverter.toJson(e));
    Assert.assertNotNull(result);
    Assert.assertEquals("def123", result.getDefinitionId());
    Assert.assertEquals(
        DefinitionEventType.CUSTOM_REMINDER_FETCH_TRANSACTIONS.getEventType(),
        result.getHandlerType());
    Assert.assertEquals(Collections.EMPTY_MAP, result.getMetaData());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testTransformNull() {
    handler.transform(null);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testTransformIncorrectJson() {
    handler.transform("hello");
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testExecuteWithNullHandleDefinitionDetails() {
    handler.execute(null, null);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testExecuteWithEmptyDefinitionId() {
    handler.execute(DefinitionEvent.builder().build(), null);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testExecuteWithEmptyHandlerType() {
    handler.execute(DefinitionEvent.builder().definitionId("def123").build(), null);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testExecuteDefinitionNotFound() {
    handler.execute(
        DefinitionEvent.builder()
            .definitionId("def124")
            .handlerType(DefinitionEventType.CUSTOM_REMINDER_FETCH_TRANSACTIONS.getEventType())
            .build(),
        null);
  }

  @Test
  public void testExecute() {
    DefinitionEventProcessors.addProcessor(
        DefinitionEventType.CUSTOM_REMINDER_FETCH_TRANSACTIONS,
        customReminderFetchTransactionsProcessor);
    handler.execute(
        DefinitionEvent.builder()
            .definitionId("def123")
            .handlerType(DefinitionEventType.CUSTOM_REMINDER_FETCH_TRANSACTIONS.getEventType())
            .build(),
        null);
    verify(definitionDetailsRepository, times(1)).findByDefinitionId("def123");
  }

  @Test
  public void testExecuteWithMetaData() {
    DefinitionEventProcessors.addProcessor(
        DefinitionEventType.CUSTOM_REMINDER_FETCH_TRANSACTIONS,
        customReminderFetchTransactionsProcessor);
    handler.execute(
        DefinitionEvent.builder()
            .definitionId("def123")
            .handlerType(DefinitionEventType.CUSTOM_REMINDER_FETCH_TRANSACTIONS.getEventType())
            .build(),
        Map.of("offeringId", "off123"));
    verify(definitionDetailsRepository, times(1)).findByDefinitionId("def123");
  }

  @Test
  public void testHandlerFailure() {
    handler.handleFailure("error", Map.of(), new Exception("test"));
  }
}
