package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.DEFINITION_NOT_FOUND;
import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.EXTERNAL_TASK_ACCESS_FAILURE;
import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.EXTERNAL_TASK_HANDLER_ERROR;
import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.INVALID_PARAMETER_DETAILS;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;

import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.AppConnectDuzzitException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.retry.WorkerRetryHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.camunda.template.schema.SchemaDecoder;
import com.intuit.appintgwkflw.wkflautomate.was.core.config.HandlerConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.common.IncidentTaskManager;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.WorkerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.impl.ProcessDetailsRepoService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.request.AppConnectTaskHandlerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.request.WorkflowTaskHandlerAction;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.request.WorkflowTaskHandlerInput;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.response.WorkflowTaskHandlerResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.TaskHandlerName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails.TaskDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qbo.oinp.adapter.AppConnectOINPBridge;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.mockito.ArgumentCaptor;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpStatus;
import org.springframework.test.util.ReflectionTestUtils;

/** <AUTHOR> */
public class AppConnectWorkflowTaskHandlerTest {

  private static final String parametersSchema =
      TestHelper.readResourceAsString("schema/testData/parameters.json");
  private static final Map<String, String> schema = new HashMap<>();

  static {
    schema.put(WorkFlowVariables.PARAMETERS_KEY.getName(), parametersSchema);
    schema.put("handlerDetails","{\"taskHandler\":\"appconnect\",\"actionName\":\"executeWorkflowAction\",\"handlerId\":\"intuit-workflows/was-send-notification\",\"recordType\":null,\"responseFields\":null,\"handlerScope\":null}");
    schema.put(WorkflowConstants.DEFINITION_KEY, "2121312213");
    schema.put(WorkflowConstants.INTUIT_REALMID, "362727827");
    schema.put(WorkFlowVariables.TASK_DETAILS_KEY.getName(), "{ \"required\": true}");
  }

  @Mock private AppConnectWorkflowTaskHandlerHelper actionHandlerHelper;
  @InjectMocks private AppConnectWorkflowTaskHandler actionHandler;
  @Mock private AppconnectWorkflowHeaderAction workflowTaskHandlerAction;
  @Mock private WorkerUtil workerUtil;
  @Mock private WASContextHandler contextHandler;
  @Mock private ProcessDetailsRepoService processDetailsRepoService;
  @Mock private AppConnectOINPBridge appConnectOINPBridge;
  @Mock private MetricLogger metricLogger;
  @Mock private IncidentTaskManager incidentTaskManager;
  @Mock private WorkerRetryHelper workerRetryHelper;
  @Mock private HandlerConfig handlerConfig;
  @InjectMocks private DefaultParameterDetailsExtractor defaultParameterDetailsExtractor;

  @Mock
  private ParameterDetailsExtractorHelper parameterDetailsExtractorHelper;
  @Before
  public void init() {
    MockitoAnnotations.openMocks(this);
    TemplateDetails templateDetails =
            TemplateDetails.builder()
                    .id(DefinitionTestConstants.TEMPLATE_ID)
                    .allowMultipleDefinitions(true)
                    .build();
    DefinitionDetails mockedDefinitionDetails =
        DefinitionDetails.builder().templateDetails(templateDetails).build();
    Mockito.when(processDetailsRepoService.findByProcessIdWithoutDefinitionData(any()))
            .thenReturn(Optional.of(mockedDefinitionDetails));
    Mockito.when(appConnectOINPBridge.initiateBridge(any(), any())).thenReturn(false);
    ReflectionTestUtils.setField(actionHandler, "metricLogger", metricLogger);
    ReflectionTestUtils.setField(actionHandler, "incidentTaskManager", incidentTaskManager);
    ReflectionTestUtils.setField(actionHandler, "workerRetryHelper", workerRetryHelper);
    ReflectionTestUtils.setField(actionHandler, "handlerConfig", handlerConfig);
    Mockito.when(handlerConfig.getHandlers()).thenReturn(new HashMap<>());
  }

  @Test
  public void testInvalidParameterDetails() {
    Map<String, String> testSchema = new HashMap<>();
    testSchema.put(
        "handlerDetails",
        "{\"taskHandler\":\"appconnect\",\"actionName\":\"executeWorkflowAction\",\"handlerId\":\"intuit-workflows/was-send-notification\",\"recordType\":null,\"responseFields\":null,\"handlerScope\":null}");
    testSchema.put(WorkflowConstants.TASK_DETAILS, "{}");
    testSchema.put(WorkflowConstants.DEFINITION_KEY, "2121312213");
    testSchema .put(WorkflowConstants.INTUIT_REALMID, "362727827");
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(testSchema)
            .build();
    Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);
    Mockito.when(parameterDetailsExtractorHelper.getParameterDetails(any(), any())).thenReturn(defaultParameterDetailsExtractor.getParameterDetails(workerActionRequest));
    try {
      actionHandler.executeAction(workerActionRequest);
      Assert.fail("The above method should throw exception");
    } catch (WorkflowGeneralException e) {
      Assert.assertTrue(e.getMessage().contains(INVALID_PARAMETER_DETAILS.getErrorMessage()));
    }
  }

  @Test
  public void testInvalidProcessDetails() {
    Mockito.when(processDetailsRepoService.findByProcessIdWithoutDefinitionData(any()))
        .thenReturn(Optional.empty());
    String handlerId = "hid";
    String dependency = "";
    String responseError = "";
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId("hId")
            .build();
    AppConnectTaskHandlerRequest actionRequest =
        AppConnectTaskHandlerRequest.builder()
            .endpoint("endpoint")
            .instanceId(workerActionRequest.getProcessInstanceId())
            .providerAppId("AP123")
            .providerWorkflowId(workerActionRequest.getHandlerId())
            .workflowId(workerActionRequest.getProcessDefinitionId())
            .build();

    WorkflowTaskHandlerResponse handlerResponse = new WorkflowTaskHandlerResponse();
    handlerResponse.setSuccess("true");
    WASHttpResponse<WorkflowTaskHandlerResponse> response =
        WASHttpResponse.<WorkflowTaskHandlerResponse>builder()
            .status(HttpStatus.OK)
            .response(handlerResponse)
            .isSuccess2xx(true)
            .build();

    Mockito.when(actionHandlerHelper.prepareTaskHandlerRequest(Mockito.any(), Mockito.any(),Mockito.any()))
        .thenReturn(actionRequest);
    Map<String, Object> res = new HashMap<>();
    res.put(
        workerActionRequest.getActivityId()
            + WorkflowConstants.UNDERSCORE
            + WorkFlowVariables.RESPONSE.getName(),
        "true");
    Mockito.when(actionHandlerHelper.getAppConnectDuzzitException(any(), any()))
        .thenReturn(
            new AppConnectDuzzitException(
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorCode(),
                    dependency.toUpperCase(),
                    formatHandlerId(handlerId)),
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorMessage(),
                    dependency,
                    handlerId,
                    responseError)));
    Mockito.when(
            actionHandlerHelper.prepareTaskHandlerResponse(
                Mockito.any(WorkerActionRequest.class), Mockito.any(WASHttpResponse.class)))
        .thenReturn(res);
    Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);
    Mockito.when(actionHandlerHelper.executeAction(any(), anyString())).thenReturn(response);

    try {
      actionHandler.executeAction(workerActionRequest);
      Assert.fail("The above method should throw exception");
    } catch (WorkflowGeneralException e) {
      Assert.assertTrue(e.getMessage().contains(DEFINITION_NOT_FOUND.getErrorMessage()));
    }
  }

  @Test
  public void testExecuteAppActionFailure() {
    String handlerId = "hid";
    String dependency = "";
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId(handlerId)
            .build();
    AppConnectTaskHandlerRequest actionRequest =
        AppConnectTaskHandlerRequest.builder()
            .endpoint("endpoint")
            .instanceId(workerActionRequest.getProcessInstanceId())
            .providerAppId("AP123")
            .providerWorkflowId(workerActionRequest.getHandlerId())
            .workflowId(workerActionRequest.getProcessDefinitionId())
            .build();

    WorkflowTaskHandlerResponse handlerResponse = new WorkflowTaskHandlerResponse();
    handlerResponse.setSuccess("false");
    handlerResponse.setError("error");
    WASHttpResponse<WorkflowTaskHandlerResponse> response =
        WASHttpResponse.<WorkflowTaskHandlerResponse>builder()
            .status(HttpStatus.OK)
            .response(handlerResponse)
            .build();
    Mockito.when(actionHandlerHelper.getAppConnectDuzzitException(handlerId, response))
            .thenReturn(
                    new AppConnectDuzzitException(
                            String.format(
                                    EXTERNAL_TASK_HANDLER_ERROR.getErrorCode(),
                                    dependency.toUpperCase(),
                                    formatHandlerId(handlerId)),
                            String.format(
                                    EXTERNAL_TASK_HANDLER_ERROR.getErrorMessage(),
                                    dependency,
                                    handlerId,
                                    handlerResponse.getError())));
    Mockito.when(actionHandlerHelper.prepareTaskHandlerRequest(Mockito.any(), Mockito.any(), Mockito.any()))
        .thenReturn(actionRequest);
    Mockito.when(parameterDetailsExtractorHelper.getParameterDetails(any(), any())).thenReturn(defaultParameterDetailsExtractor.getParameterDetails(workerActionRequest));
    Mockito.when(actionHandlerHelper.executeAction(any(), anyString())).thenReturn(response);
    Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);
    try {
      actionHandler.executeAction(workerActionRequest);
      Assert.fail("The above method should throw exception");
    } catch (AppConnectDuzzitException e) {
      Assert.assertTrue(e.getErrorCode().contains("EXTERNAL_TASK_HANDLER_ERROR"));
    }
  }

  @SuppressWarnings("unchecked")
  @Test
  public void testExecuteAppActionSuccess() {
    String handlerId = "hid";
    String dependency = "";
    String responseError = "";
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId("hId")
            .build();
    AppConnectTaskHandlerRequest actionRequest =
        AppConnectTaskHandlerRequest.builder()
            .endpoint("endpoint")
            .instanceId(workerActionRequest.getProcessInstanceId())
            .providerAppId("AP123")
            .providerWorkflowId(workerActionRequest.getHandlerId())
            .workflowId(workerActionRequest.getProcessDefinitionId())
            .build();

    WorkflowTaskHandlerResponse handlerResponse = new WorkflowTaskHandlerResponse();
    handlerResponse.setSuccess("true");
    Map<String, Object> data = new HashMap<>();
    data.put("dummy",new HashMap<>().put("key","value"));
    handlerResponse.setData(data);

    WASHttpResponse<WorkflowTaskHandlerResponse> response =
        WASHttpResponse.<WorkflowTaskHandlerResponse>builder()
            .status(HttpStatus.OK)
            .response(handlerResponse)
            .isSuccess2xx(true)
            .build();

    Mockito.when(actionHandlerHelper.prepareTaskHandlerRequest(Mockito.any(),Mockito.any(), Mockito.any()))
        .thenReturn(actionRequest);
    Map<String, Object> res = new HashMap<>();
    res.put(
        workerActionRequest.getActivityId()
            + WorkflowConstants.UNDERSCORE
            + WorkFlowVariables.RESPONSE.getName(),
        "true");
    Mockito.when(actionHandlerHelper.getAppConnectDuzzitException(any(), any()))
            .thenReturn(
                    new AppConnectDuzzitException(
                            String.format(
                                    EXTERNAL_TASK_HANDLER_ERROR.getErrorCode(),
                                    dependency.toUpperCase(),
                                    formatHandlerId(handlerId)),
                            String.format(
                                    EXTERNAL_TASK_HANDLER_ERROR.getErrorMessage(),
                                    dependency,
                                    handlerId,
                                    responseError)));
    Mockito.when(
            actionHandlerHelper.prepareTaskHandlerResponse(
                Mockito.any(WorkerActionRequest.class), Mockito.any(WASHttpResponse.class)))
        .thenReturn(res);
    Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);
    Mockito.when(actionHandlerHelper.executeAction(any(), anyString())).thenReturn(response);
    Mockito.when(parameterDetailsExtractorHelper.getParameterDetails(any(), any())).thenReturn(defaultParameterDetailsExtractor.getParameterDetails(workerActionRequest));
    Map<String, Object> resp = actionHandler.executeAction(workerActionRequest);

    Assert.assertEquals(
        "true",
        resp.get(
            workerActionRequest.getActivityId()
                + WorkflowConstants.UNDERSCORE
                + WorkFlowVariables.RESPONSE.getName()));
  }

  @SuppressWarnings("unchecked")
  @Test
  public void testExecuteAppResponseDataFailure() {
    String handlerId = "hid";
    String dependency = "";
    String responseError = "";
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId("hId")
            .build();
    AppConnectTaskHandlerRequest actionRequest =
        AppConnectTaskHandlerRequest.builder()
            .endpoint("endpoint")
            .instanceId(workerActionRequest.getProcessInstanceId())
            .providerAppId("AP123")
            .providerWorkflowId(workerActionRequest.getHandlerId())
            .workflowId(workerActionRequest.getProcessDefinitionId())
            .build();

    WorkflowTaskHandlerResponse handlerResponse = new WorkflowTaskHandlerResponse();
    handlerResponse.setSuccess("true");

    WASHttpResponse<WorkflowTaskHandlerResponse> response =
        WASHttpResponse.<WorkflowTaskHandlerResponse>builder()
            .status(HttpStatus.OK)
            .response(handlerResponse)
            .isSuccess2xx(true)
            .build();

    Mockito.when(actionHandlerHelper.prepareTaskHandlerRequest(Mockito.any(),Mockito.any(), Mockito.any()))
        .thenReturn(actionRequest);
    Map<String, Object> res = new HashMap<>();
    res.put(
        workerActionRequest.getActivityId()
            + WorkflowConstants.UNDERSCORE
            + WorkFlowVariables.RESPONSE.getName(),
        "true");
    Mockito.when(actionHandlerHelper.getAppConnectDuzzitException(any(), any()))
        .thenReturn(
            new AppConnectDuzzitException(
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorCode(),
                    dependency.toUpperCase(),
                    formatHandlerId(handlerId)),
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorMessage(),
                    dependency,
                    handlerId,
                    responseError)));
    Mockito.when(
            actionHandlerHelper.prepareTaskHandlerResponse(
                Mockito.any(WorkerActionRequest.class), Mockito.any(WASHttpResponse.class)))
        .thenReturn(res);
    Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);
    Mockito.when(actionHandlerHelper.executeAction(any(), anyString())).thenReturn(response);
    Mockito.when(parameterDetailsExtractorHelper.getParameterDetails(any(), any())).thenReturn(defaultParameterDetailsExtractor.getParameterDetails(workerActionRequest));
    Map<String, Object> resp = actionHandler.executeAction(workerActionRequest);

    Assert.assertEquals(
        "true",
        resp.get(
            workerActionRequest.getActivityId()
                + WorkflowConstants.UNDERSCORE
                + WorkFlowVariables.RESPONSE.getName()));
  }


  @Test
  public void testExecuteAppActionCreateTaskFailure() {
    String handlerId = "intuit-workflows/was-create-task";
    String dependency = WorkflowConstants.PROJECT_SERVICE;
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId(handlerId)
            .build();
    AppConnectTaskHandlerRequest actionRequest =
        AppConnectTaskHandlerRequest.builder()
            .endpoint("endpoint")
            .instanceId(workerActionRequest.getProcessInstanceId())
            .providerAppId("AP123")
            .providerWorkflowId(workerActionRequest.getHandlerId())
            .workflowId(workerActionRequest.getProcessDefinitionId())
            .build();

    WorkflowTaskHandlerResponse handlerResponse = new WorkflowTaskHandlerResponse();
    handlerResponse.setSuccess("false");
    handlerResponse.setError("error");
    WASHttpResponse<WorkflowTaskHandlerResponse> response =
        WASHttpResponse.<WorkflowTaskHandlerResponse>builder()
            .status(HttpStatus.OK)
            .response(handlerResponse)
            .error("error")
            .build();

    Mockito.when(actionHandlerHelper.getAppConnectDuzzitException(handlerId, response))
            .thenReturn(
                    new AppConnectDuzzitException(
                            String.format(
                                    EXTERNAL_TASK_HANDLER_ERROR.getErrorCode(),
                                    dependency.toUpperCase(),
                                    formatHandlerId(handlerId)),
                            String.format(
                                    EXTERNAL_TASK_HANDLER_ERROR.getErrorMessage(),
                                    dependency,
                                    handlerId,
                                    handlerResponse.getError())));
    Mockito.when(actionHandlerHelper.prepareTaskHandlerRequest(Mockito.any(),Mockito.any(), Mockito.any()))
        .thenReturn(actionRequest);

    Mockito.when(actionHandlerHelper.executeAction(any(), anyString())).thenReturn(response);
    Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);
    Mockito.when(parameterDetailsExtractorHelper.getParameterDetails(any(), any())).thenReturn(defaultParameterDetailsExtractor.getParameterDetails(workerActionRequest));
    try {
      actionHandler.executeAction(workerActionRequest);
      Assert.fail("The above method should throw exception");
    } catch (AppConnectDuzzitException e) {
      Assert.assertTrue(e.getMessage().contains(handlerId));
    }
  }

  @Test
  public void testExecuteAppActionUpdateInvoiceStatusFailure() {
    String handlerId = "intuit-workflows/was-update-invoice-status";
    String dependency = WorkflowConstants.QBO;
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId(handlerId)
            .build();
    AppConnectTaskHandlerRequest actionRequest =
        AppConnectTaskHandlerRequest.builder()
            .endpoint("endpoint")
            .instanceId(workerActionRequest.getProcessInstanceId())
            .providerAppId("AP123")
            .providerWorkflowId(workerActionRequest.getHandlerId())
            .workflowId(workerActionRequest.getProcessDefinitionId())
            .build();

    WorkflowTaskHandlerResponse handlerResponse = new WorkflowTaskHandlerResponse();
    handlerResponse.setSuccess("false");
    handlerResponse.setError("error");
    WASHttpResponse<WorkflowTaskHandlerResponse> response =
        WASHttpResponse.<WorkflowTaskHandlerResponse>builder()
            .status(HttpStatus.OK)
            .response(handlerResponse)
            .error("error")
            .build();
    Mockito.when(actionHandlerHelper.getAppConnectDuzzitException(handlerId, response))
        .thenReturn(
            new AppConnectDuzzitException(
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorCode(),
                    dependency.toUpperCase(),
                    formatHandlerId(handlerId)),
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorMessage(),
                    dependency,
                    handlerId,
                    handlerResponse.getError())));
    Mockito.when(actionHandlerHelper.prepareTaskHandlerRequest(Mockito.any(), Mockito.any(), Mockito.any()))
        .thenReturn(actionRequest);
    Mockito.when(parameterDetailsExtractorHelper.getParameterDetails(any(), any())).thenReturn(defaultParameterDetailsExtractor.getParameterDetails(workerActionRequest));
    Mockito.when(actionHandlerHelper.executeAction(any(), anyString())).thenReturn(response);
    Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);
    try {
      actionHandler.executeAction(workerActionRequest);
      Assert.fail("The above method should throw exception");
    } catch (AppConnectDuzzitException e) {
      Assert.assertTrue(e.getMessage().contains(handlerId));
    }
  }

  @Test
  public void testExecuteAppActionSendInvoiceApprovalFailure() {
    String handlerId = "intuit-workflows/was-send-invoice-approval-notification";
    String dependency = WorkflowConstants.OINP;
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId(handlerId)
            .build();
    AppConnectTaskHandlerRequest actionRequest =
        AppConnectTaskHandlerRequest.builder()
            .endpoint("endpoint")
            .instanceId(workerActionRequest.getProcessInstanceId())
            .providerAppId("AP123")
            .providerWorkflowId(workerActionRequest.getHandlerId())
            .workflowId(workerActionRequest.getProcessDefinitionId())
            .build();

    WorkflowTaskHandlerResponse handlerResponse = new WorkflowTaskHandlerResponse();
    handlerResponse.setSuccess("false");
    handlerResponse.setError("error");
    WASHttpResponse<WorkflowTaskHandlerResponse> response =
        WASHttpResponse.<WorkflowTaskHandlerResponse>builder()
            .status(HttpStatus.OK)
            .response(handlerResponse)
            .error("error")
            .build();

    Mockito.when(actionHandlerHelper.getAppConnectDuzzitException(handlerId, response))
        .thenReturn(
            new AppConnectDuzzitException(
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorCode(),
                    dependency.toUpperCase(),
                    formatHandlerId(handlerId)),
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorMessage(),
                    dependency,
                    handlerId,
                    handlerResponse.getError())));
    Mockito.when(actionHandlerHelper.prepareTaskHandlerRequest(Mockito.any(), Mockito.any(), Mockito.any()))
        .thenReturn(actionRequest);

    Mockito.when(actionHandlerHelper.executeAction(any(), anyString())).thenReturn(response);
    Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);
    Mockito.when(parameterDetailsExtractorHelper.getParameterDetails(any(), any())).thenReturn(defaultParameterDetailsExtractor.getParameterDetails(workerActionRequest));
    try {
      actionHandler.executeAction(workerActionRequest);
      Assert.fail("The above method should throw exception");
    } catch (AppConnectDuzzitException e) {
      Assert.assertTrue(e.getMessage().contains(handlerId));
    }
  }

  @Test
  public void testExecuteAppActionSendInvoiceApprovalToCreatorFailure() {
    String handlerId = "intuit-workflows/was-send-invoice-notification-to-creator";
    String dependency = WorkflowConstants.OINP;
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId(handlerId)
            .build();
    AppConnectTaskHandlerRequest actionRequest =
        AppConnectTaskHandlerRequest.builder()
            .endpoint("endpoint")
            .instanceId(workerActionRequest.getProcessInstanceId())
            .providerAppId("AP123")
            .providerWorkflowId(workerActionRequest.getHandlerId())
            .workflowId(workerActionRequest.getProcessDefinitionId())
            .build();

    WorkflowTaskHandlerResponse handlerResponse = new WorkflowTaskHandlerResponse();
    handlerResponse.setSuccess("false");
    handlerResponse.setError("error");
    WASHttpResponse<WorkflowTaskHandlerResponse> response =
        WASHttpResponse.<WorkflowTaskHandlerResponse>builder()
            .status(HttpStatus.OK)
            .response(handlerResponse)
            .error("error")
            .build();

    Mockito.when(actionHandlerHelper.getAppConnectDuzzitException(handlerId, response))
        .thenReturn(
            new AppConnectDuzzitException(
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorCode(),
                    dependency.toUpperCase(),
                    formatHandlerId(handlerId)),
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorMessage(),
                    dependency,
                    handlerId,
                    handlerResponse.getError())));
    Mockito.when(actionHandlerHelper.prepareTaskHandlerRequest(Mockito.any(), Mockito.any(), Mockito.any()))
        .thenReturn(actionRequest);

    Mockito.when(actionHandlerHelper.executeAction(any(), anyString())).thenReturn(response);
    Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);
    Mockito.when(parameterDetailsExtractorHelper.getParameterDetails(any(), any())).thenReturn(defaultParameterDetailsExtractor.getParameterDetails(workerActionRequest));
    try {
      actionHandler.executeAction(workerActionRequest);
      Assert.fail("The above method should throw exception");
    } catch (AppConnectDuzzitException e) {
      Assert.assertTrue(e.getMessage().contains(handlerId));
    }
  }

  @Test
  public void testExecuteAppActionReminderSendNotificationFailure() {
    String handlerId = "intuit-workflows/was-reminder-send-notification";
    String dependency = WorkflowConstants.OINP;
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId(handlerId)
            .build();
    AppConnectTaskHandlerRequest actionRequest =
        AppConnectTaskHandlerRequest.builder()
            .endpoint("endpoint")
            .instanceId(workerActionRequest.getProcessInstanceId())
            .providerAppId("AP123")
            .providerWorkflowId(workerActionRequest.getHandlerId())
            .workflowId(workerActionRequest.getProcessDefinitionId())
            .build();

    WorkflowTaskHandlerResponse handlerResponse = new WorkflowTaskHandlerResponse();
    handlerResponse.setSuccess("false");
    handlerResponse.setError("error");
    WASHttpResponse<WorkflowTaskHandlerResponse> response =
        WASHttpResponse.<WorkflowTaskHandlerResponse>builder()
            .status(HttpStatus.OK)
            .response(handlerResponse)
            .error("error")
            .build();

    Mockito.when(actionHandlerHelper.getAppConnectDuzzitException(handlerId, response))
        .thenReturn(
            new AppConnectDuzzitException(
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorCode(),
                    dependency.toUpperCase(),
                    formatHandlerId(handlerId)),
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorMessage(),
                    dependency,
                    handlerId,
                    handlerResponse.getError())));
    Mockito.when(actionHandlerHelper.prepareTaskHandlerRequest(Mockito.any(), Mockito.any(), Mockito.any()))
        .thenReturn(actionRequest);

    Mockito.when(actionHandlerHelper.executeAction(any(), anyString())).thenReturn(response);
    Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);
    Mockito.when(parameterDetailsExtractorHelper.getParameterDetails(any(), any())).thenReturn(defaultParameterDetailsExtractor.getParameterDetails(workerActionRequest));
    try {
      actionHandler.executeAction(workerActionRequest);
      Assert.fail("The above method should throw exception");
    } catch (AppConnectDuzzitException e) {
      Assert.assertTrue(e.getMessage().contains(handlerId));
    }
  }

  @Test
  public void testExecuteAppUnDepositedFundsWaitFailure() {
    String handlerId = "intuit-workflows/undeposited-funds-wait";
    String dependency = WorkflowConstants.QBO;
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId(handlerId)
            .build();
    AppConnectTaskHandlerRequest actionRequest =
        AppConnectTaskHandlerRequest.builder()
            .endpoint("endpoint")
            .instanceId(workerActionRequest.getProcessInstanceId())
            .providerAppId("AP123")
            .providerWorkflowId(workerActionRequest.getHandlerId())
            .workflowId(workerActionRequest.getProcessDefinitionId())
            .build();

    WorkflowTaskHandlerResponse handlerResponse = new WorkflowTaskHandlerResponse();
    handlerResponse.setSuccess("false");
    handlerResponse.setError("error");
    WASHttpResponse<WorkflowTaskHandlerResponse> response =
        WASHttpResponse.<WorkflowTaskHandlerResponse>builder()
            .status(HttpStatus.OK)
            .response(handlerResponse)
            .error("error")
            .build();
    Mockito.when(actionHandlerHelper.getAppConnectDuzzitException(handlerId, response))
        .thenReturn(
            new AppConnectDuzzitException(
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorCode(),
                    dependency.toUpperCase(),
                    formatHandlerId(handlerId)),
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorMessage(),
                    dependency,
                    handlerId,
                    handlerResponse.getError())));
    Mockito.when(actionHandlerHelper.prepareTaskHandlerRequest(Mockito.any(), Mockito.any(), Mockito.any()))
        .thenReturn(actionRequest);

    Mockito.when(actionHandlerHelper.executeAction(any(), anyString())).thenReturn(response);
    Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);
    Mockito.when(parameterDetailsExtractorHelper.getParameterDetails(any(), any())).thenReturn(defaultParameterDetailsExtractor.getParameterDetails(workerActionRequest));
    try {
      actionHandler.executeAction(workerActionRequest);
      Assert.fail("The above method should throw exception");
    } catch (AppConnectDuzzitException e) {
      Assert.assertTrue(e.getMessage().contains(handlerId));
    }
  }

  @Test
  public void testExecuteAppBillDueWaitFailure() {
    String handlerId = "intuit-workflows/bill-due-wait";
    String dependency = WorkflowConstants.QBO;
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId(handlerId)
            .build();
    AppConnectTaskHandlerRequest actionRequest =
        AppConnectTaskHandlerRequest.builder()
            .endpoint("endpoint")
            .instanceId(workerActionRequest.getProcessInstanceId())
            .providerAppId("AP123")
            .providerWorkflowId(workerActionRequest.getHandlerId())
            .workflowId(workerActionRequest.getProcessDefinitionId())
            .build();

    WorkflowTaskHandlerResponse handlerResponse = new WorkflowTaskHandlerResponse();
    handlerResponse.setSuccess("false");
    handlerResponse.setError("error");
    WASHttpResponse<WorkflowTaskHandlerResponse> response =
        WASHttpResponse.<WorkflowTaskHandlerResponse>builder()
            .status(HttpStatus.OK)
            .response(handlerResponse)
            .error("error")
            .build();

    Mockito.when(actionHandlerHelper.getAppConnectDuzzitException(handlerId, response))
        .thenReturn(
            new AppConnectDuzzitException(
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorCode(),
                    dependency.toUpperCase(),
                    formatHandlerId(handlerId)),
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorMessage(),
                    dependency,
                    handlerId,
                    handlerResponse.getError())));
    Mockito.when(actionHandlerHelper.prepareTaskHandlerRequest(Mockito.any(), Mockito.any(), Mockito.any()))
        .thenReturn(actionRequest);

    Mockito.when(actionHandlerHelper.executeAction(any(), anyString())).thenReturn(response);
    Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);
    Mockito.when(parameterDetailsExtractorHelper.getParameterDetails(any(), any())).thenReturn(defaultParameterDetailsExtractor.getParameterDetails(workerActionRequest));
    try {
      actionHandler.executeAction(workerActionRequest);
      Assert.fail("The above method should throw exception");
    } catch (AppConnectDuzzitException e) {
      Assert.assertTrue(e.getMessage().contains(handlerId));
    }
  }

  @Test
  public void testExecuteAppReceiptNotificationFailure() {
    String handlerId = "intuit-workflows/receipt-notification";
    String dependency = WorkflowConstants.OINP;
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId(handlerId)
            .build();
    AppConnectTaskHandlerRequest actionRequest =
        AppConnectTaskHandlerRequest.builder()
            .endpoint("endpoint")
            .instanceId(workerActionRequest.getProcessInstanceId())
            .providerAppId("AP123")
            .providerWorkflowId(workerActionRequest.getHandlerId())
            .workflowId(workerActionRequest.getProcessDefinitionId())
            .build();

    WorkflowTaskHandlerResponse handlerResponse = new WorkflowTaskHandlerResponse();
    handlerResponse.setSuccess("false");
    handlerResponse.setError("error");
    WASHttpResponse<WorkflowTaskHandlerResponse> response =
        WASHttpResponse.<WorkflowTaskHandlerResponse>builder()
            .status(HttpStatus.OK)
            .response(handlerResponse)
            .error("error")
            .build();

    Mockito.when(actionHandlerHelper.getAppConnectDuzzitException(handlerId, response))
        .thenReturn(
            new AppConnectDuzzitException(
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorCode(),
                    dependency.toUpperCase(),
                    formatHandlerId(handlerId)),
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorMessage(),
                    dependency,
                    handlerId,
                    handlerResponse.getError())));
    Mockito.when(actionHandlerHelper.prepareTaskHandlerRequest(Mockito.any(), Mockito.any(), Mockito.any()))
        .thenReturn(actionRequest);

    Mockito.when(actionHandlerHelper.executeAction(any(), anyString())).thenReturn(response);
    Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);
    Mockito.when(parameterDetailsExtractorHelper.getParameterDetails(any(), any())).thenReturn(defaultParameterDetailsExtractor.getParameterDetails(workerActionRequest));
    try {
      actionHandler.executeAction(workerActionRequest);
      Assert.fail("The above method should throw exception");
    } catch (AppConnectDuzzitException e) {
      Assert.assertTrue(e.getMessage().contains(handlerId));
    }
  }

  @Test
  public void testExecuteAppOverdueInvoiceWaitFailure() {
    String handlerId = "intuit-workflows/overdue-invoice-wait";
    String dependency = WorkflowConstants.QBO;
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId(handlerId)
            .build();
    AppConnectTaskHandlerRequest actionRequest =
        AppConnectTaskHandlerRequest.builder()
            .endpoint("endpoint")
            .instanceId(workerActionRequest.getProcessInstanceId())
            .providerAppId("AP123")
            .providerWorkflowId(workerActionRequest.getHandlerId())
            .workflowId(workerActionRequest.getProcessDefinitionId())
            .build();

    WorkflowTaskHandlerResponse handlerResponse = new WorkflowTaskHandlerResponse();
    handlerResponse.setSuccess("false");
    handlerResponse.setError("error");
    WASHttpResponse<WorkflowTaskHandlerResponse> response =
        WASHttpResponse.<WorkflowTaskHandlerResponse>builder()
            .status(HttpStatus.OK)
            .response(handlerResponse)
            .error("error")
            .build();

    Mockito.when(actionHandlerHelper.getAppConnectDuzzitException(handlerId, response))
        .thenReturn(
            new AppConnectDuzzitException(
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorCode(),
                    dependency.toUpperCase(),
                    formatHandlerId(handlerId)),
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorMessage(),
                    dependency,
                    handlerId,
                    handlerResponse.getError())));
    Mockito.when(actionHandlerHelper.prepareTaskHandlerRequest(Mockito.any(), Mockito.any(), Mockito.any()))
        .thenReturn(actionRequest);

    Mockito.when(actionHandlerHelper.executeAction(any(), anyString())).thenReturn(response);
    Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);
    Mockito.when(parameterDetailsExtractorHelper.getParameterDetails(any(), any())).thenReturn(defaultParameterDetailsExtractor.getParameterDetails(workerActionRequest));
    try {
      actionHandler.executeAction(workerActionRequest);
      Assert.fail("The above method should throw exception");
    } catch (AppConnectDuzzitException e) {
      Assert.assertTrue(e.getMessage().contains(handlerId));
    }
  }

  @Test
  public void testExecuteAppDeleteQBOWebhookFailure() {
    String handlerId = "intuit-workflows/Delete-QBO-Webhook";
    String dependency = WorkflowConstants.QBO;
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId(handlerId)
            .build();
    AppConnectTaskHandlerRequest actionRequest =
        AppConnectTaskHandlerRequest.builder()
            .endpoint("endpoint")
            .instanceId(workerActionRequest.getProcessInstanceId())
            .providerAppId("AP123")
            .providerWorkflowId(workerActionRequest.getHandlerId())
            .workflowId(workerActionRequest.getProcessDefinitionId())
            .build();

    WorkflowTaskHandlerResponse handlerResponse = new WorkflowTaskHandlerResponse();
    handlerResponse.setSuccess("false");
    handlerResponse.setError("error");
    WASHttpResponse<WorkflowTaskHandlerResponse> response =
        WASHttpResponse.<WorkflowTaskHandlerResponse>builder()
            .status(HttpStatus.OK)
            .response(handlerResponse)
            .error("error")
            .build();

    Mockito.when(actionHandlerHelper.getAppConnectDuzzitException(handlerId, response))
        .thenReturn(
            new AppConnectDuzzitException(
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorCode(),
                    dependency.toUpperCase(),
                    formatHandlerId(handlerId)),
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorMessage(),
                    dependency,
                    handlerId,
                    handlerResponse.getError())));
    Mockito.when(actionHandlerHelper.prepareTaskHandlerRequest(Mockito.any(), Mockito.any(), Mockito.any()))
        .thenReturn(actionRequest);

    Mockito.when(actionHandlerHelper.executeAction(any(), anyString())).thenReturn(response);
    Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);
    Mockito.when(parameterDetailsExtractorHelper.getParameterDetails(any(), any())).thenReturn(defaultParameterDetailsExtractor.getParameterDetails(workerActionRequest));
    try {
      actionHandler.executeAction(workerActionRequest);
      Assert.fail("The above method should throw exception");
    } catch (AppConnectDuzzitException e) {
      Assert.assertTrue(e.getMessage().contains(handlerId));
    }
  }

  @Test
  public void testExecuteAppGetStageConnectionFailure() {
    String handlerId = "intuit-workflows/get-stage-connection-entities";
    String dependency = WorkflowConstants.STAGE;
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId(handlerId)
            .build();
    AppConnectTaskHandlerRequest actionRequest =
        AppConnectTaskHandlerRequest.builder()
            .endpoint("endpoint")
            .instanceId(workerActionRequest.getProcessInstanceId())
            .providerAppId("AP123")
            .providerWorkflowId(workerActionRequest.getHandlerId())
            .workflowId(workerActionRequest.getProcessDefinitionId())
            .build();

    WorkflowTaskHandlerResponse handlerResponse = new WorkflowTaskHandlerResponse();
    handlerResponse.setSuccess("false");
    handlerResponse.setError("error");
    WASHttpResponse<WorkflowTaskHandlerResponse> response =
        WASHttpResponse.<WorkflowTaskHandlerResponse>builder()
            .status(HttpStatus.OK)
            .response(handlerResponse)
            .error("error")
            .build();

    Mockito.when(actionHandlerHelper.getAppConnectDuzzitException(handlerId, response))
        .thenReturn(
            new AppConnectDuzzitException(
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorCode(),
                    dependency.toUpperCase(),
                    formatHandlerId(handlerId)),
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorMessage(),
                    dependency,
                    handlerId,
                    handlerResponse.getError())));
    Mockito.when(actionHandlerHelper.prepareTaskHandlerRequest(Mockito.any(), Mockito.any(), Mockito.any()))
        .thenReturn(actionRequest);

    Mockito.when(actionHandlerHelper.executeAction(any(), anyString())).thenReturn(response);
    Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);
    Mockito.when(parameterDetailsExtractorHelper.getParameterDetails(any(), any())).thenReturn(defaultParameterDetailsExtractor.getParameterDetails(workerActionRequest));
    try {
      actionHandler.executeAction(workerActionRequest);
      Assert.fail("The above method should throw exception");
    } catch (AppConnectDuzzitException e) {
      Assert.assertTrue(e.getMessage().contains(handlerId));
    }
  }

  @Test
  public void testExecuteAppStageDisconnectFailure() {
    String handlerId = "intuit-workflows/stage-disconnect";
    String dependency = WorkflowConstants.STAGE;
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId(handlerId)
            .build();
    AppConnectTaskHandlerRequest actionRequest =
        AppConnectTaskHandlerRequest.builder()
            .endpoint("endpoint")
            .instanceId(workerActionRequest.getProcessInstanceId())
            .providerAppId("AP123")
            .providerWorkflowId(workerActionRequest.getHandlerId())
            .workflowId(workerActionRequest.getProcessDefinitionId())
            .build();

    WorkflowTaskHandlerResponse handlerResponse = new WorkflowTaskHandlerResponse();
    handlerResponse.setSuccess("false");
    handlerResponse.setError("error");
    WASHttpResponse<WorkflowTaskHandlerResponse> response =
        WASHttpResponse.<WorkflowTaskHandlerResponse>builder()
            .status(HttpStatus.OK)
            .response(handlerResponse)
            .error("error")
            .build();

    Mockito.when(actionHandlerHelper.getAppConnectDuzzitException(handlerId, response))
        .thenReturn(
            new AppConnectDuzzitException(
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorCode(),
                    dependency.toUpperCase(),
                    formatHandlerId(handlerId)),
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorMessage(),
                    dependency,
                    handlerId,
                    handlerResponse.getError())));
    Mockito.when(actionHandlerHelper.prepareTaskHandlerRequest(Mockito.any(), Mockito.any(), Mockito.any()))
        .thenReturn(actionRequest);

    Mockito.when(actionHandlerHelper.executeAction(any(), anyString())).thenReturn(response);
    Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);
    Mockito.when(parameterDetailsExtractorHelper.getParameterDetails(any(), any())).thenReturn(defaultParameterDetailsExtractor.getParameterDetails(workerActionRequest));
    try {
      actionHandler.executeAction(workerActionRequest);
      Assert.fail("The above method should throw exception");
    } catch (AppConnectDuzzitException e) {
      Assert.assertTrue(e.getMessage().contains(handlerId));
    }
  }

  @Test
  public void testExecuteAppExcludeStageEntityFailure() {
    String handlerId = "intuit-workflows/exclude-stage-entity";
    String dependency = WorkflowConstants.STAGE;
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId(handlerId)
            .build();
    AppConnectTaskHandlerRequest actionRequest =
        AppConnectTaskHandlerRequest.builder()
            .endpoint("endpoint")
            .instanceId(workerActionRequest.getProcessInstanceId())
            .providerAppId("AP123")
            .providerWorkflowId(workerActionRequest.getHandlerId())
            .workflowId(workerActionRequest.getProcessDefinitionId())
            .build();

    WorkflowTaskHandlerResponse handlerResponse = new WorkflowTaskHandlerResponse();
    handlerResponse.setSuccess("false");
    handlerResponse.setError("error");
    WASHttpResponse<WorkflowTaskHandlerResponse> response =
        WASHttpResponse.<WorkflowTaskHandlerResponse>builder()
            .status(HttpStatus.OK)
            .response(handlerResponse)
            .error("error")
            .build();

    Mockito.when(actionHandlerHelper.getAppConnectDuzzitException(handlerId, response))
        .thenReturn(
            new AppConnectDuzzitException(
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorCode(),
                    dependency.toUpperCase(),
                    formatHandlerId(handlerId)),
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorMessage(),
                    dependency,
                    handlerId,
                    handlerResponse.getError())));
    Mockito.when(actionHandlerHelper.prepareTaskHandlerRequest(Mockito.any(), Mockito.any(), Mockito.any()))
        .thenReturn(actionRequest);

    Mockito.when(actionHandlerHelper.executeAction(any(), anyString())).thenReturn(response);
    Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);
    Mockito.when(parameterDetailsExtractorHelper.getParameterDetails(any(), any())).thenReturn(defaultParameterDetailsExtractor.getParameterDetails(workerActionRequest));
    try {
      actionHandler.executeAction(workerActionRequest);
      Assert.fail("The above method should throw exception");
    } catch (AppConnectDuzzitException e) {
      Assert.assertTrue(e.getMessage().contains(handlerId));
    }
  }

  @Test
  public void testExecuteAppProjectTaskUpdateFailure() {
    String handlerId = "intuit-workflows/project-task-update";
    String dependency = WorkflowConstants.PROJECT_SERVICE;
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId(handlerId)
            .build();
    AppConnectTaskHandlerRequest actionRequest =
        AppConnectTaskHandlerRequest.builder()
            .endpoint("endpoint")
            .instanceId(workerActionRequest.getProcessInstanceId())
            .providerAppId("AP123")
            .providerWorkflowId(workerActionRequest.getHandlerId())
            .workflowId(workerActionRequest.getProcessDefinitionId())
            .build();

    WorkflowTaskHandlerResponse handlerResponse = new WorkflowTaskHandlerResponse();
    handlerResponse.setSuccess("false");
    handlerResponse.setError("error");
    WASHttpResponse<WorkflowTaskHandlerResponse> response =
        WASHttpResponse.<WorkflowTaskHandlerResponse>builder()
            .status(HttpStatus.OK)
            .response(handlerResponse)
            .error("error")
            .build();

    Mockito.when(actionHandlerHelper.getAppConnectDuzzitException(handlerId, response))
        .thenReturn(
            new AppConnectDuzzitException(
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorCode(),
                    dependency.toUpperCase(),
                    formatHandlerId(handlerId)),
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorMessage(),
                    dependency,
                    handlerId,
                    handlerResponse.getError())));
    Mockito.when(actionHandlerHelper.prepareTaskHandlerRequest(Mockito.any(), Mockito.any(), Mockito.any()))
        .thenReturn(actionRequest);

    Mockito.when(actionHandlerHelper.executeAction(any(), anyString())).thenReturn(response);
    Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);
    Mockito.when(parameterDetailsExtractorHelper.getParameterDetails(any(), any())).thenReturn(defaultParameterDetailsExtractor.getParameterDetails(workerActionRequest));
    try {
      actionHandler.executeAction(workerActionRequest);
      Assert.fail("The above method should throw exception");
    } catch (AppConnectDuzzitException e) {
      Assert.assertTrue(e.getMessage().contains(handlerId));
    }
  }

  @Test
  public void testExecuteAppUnsentInvoiceReminderWaitFailure() {
    String handlerId = "intuit-workflows/unsent-invoice-reminder-wait";
    String dependency = WorkflowConstants.OINP;
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId(handlerId)
            .build();
    AppConnectTaskHandlerRequest actionRequest =
        AppConnectTaskHandlerRequest.builder()
            .endpoint("endpoint")
            .instanceId(workerActionRequest.getProcessInstanceId())
            .providerAppId("AP123")
            .providerWorkflowId(workerActionRequest.getHandlerId())
            .workflowId(workerActionRequest.getProcessDefinitionId())
            .build();

    WorkflowTaskHandlerResponse handlerResponse = new WorkflowTaskHandlerResponse();
    handlerResponse.setSuccess("false");
    handlerResponse.setError("error");
    WASHttpResponse<WorkflowTaskHandlerResponse> response =
        WASHttpResponse.<WorkflowTaskHandlerResponse>builder()
            .status(HttpStatus.OK)
            .response(handlerResponse)
            .error("error")
            .build();
    Mockito.when(actionHandlerHelper.getAppConnectDuzzitException(handlerId, response))
        .thenReturn(
            new AppConnectDuzzitException(
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorCode(),
                    dependency.toUpperCase(),
                    formatHandlerId(handlerId)),
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorMessage(),
                    dependency,
                    handlerId,
                    handlerResponse.getError())));
    Mockito.when(actionHandlerHelper.prepareTaskHandlerRequest(Mockito.any(), Mockito.any(), Mockito.any()))
        .thenReturn(actionRequest);

    Mockito.when(actionHandlerHelper.executeAction(any(), anyString())).thenReturn(response);
    Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);
    Mockito.when(parameterDetailsExtractorHelper.getParameterDetails(any(), any())).thenReturn(defaultParameterDetailsExtractor.getParameterDetails(workerActionRequest));
    try {
      actionHandler.executeAction(workerActionRequest);
      Assert.fail("The above method should throw exception");
    } catch (AppConnectDuzzitException e) {
      Assert.assertTrue(e.getMessage().contains(handlerId));
    }
  }

  @Test
  public void testExecuteAppAccessFailure() {
    String handlerId = "hId";
    String dependency = "";
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId(handlerId)
            .build();
    AppConnectTaskHandlerRequest actionRequest =
        AppConnectTaskHandlerRequest.builder()
            .endpoint("endpoint")
            .instanceId(workerActionRequest.getProcessInstanceId())
            .providerAppId("AP123")
            .providerWorkflowId(workerActionRequest.getHandlerId())
            .workflowId(workerActionRequest.getProcessDefinitionId())
            .build();

    WorkflowTaskHandlerResponse handlerResponse = new WorkflowTaskHandlerResponse();
    handlerResponse.setSuccess("false");
    handlerResponse.setError("error");
    WASHttpResponse<WorkflowTaskHandlerResponse> response =
        WASHttpResponse.<WorkflowTaskHandlerResponse>builder()
            .status(HttpStatus.BAD_REQUEST)
            .response(handlerResponse)
            .error("IAC-001002")
            .build();
    Mockito.when(actionHandlerHelper.getAppConnectDuzzitException(handlerId, response))
        .thenReturn(
            new AppConnectDuzzitException(
                EXTERNAL_TASK_ACCESS_FAILURE.name(),
                String.format(
                    EXTERNAL_TASK_ACCESS_FAILURE.getErrorMessage(),
                    dependency,
                    handlerId,
                    handlerResponse.getError())));
    Mockito.when(actionHandlerHelper.prepareTaskHandlerRequest(Mockito.any(), Mockito.any(), Mockito.any()))
        .thenReturn(actionRequest);

    Mockito.when(actionHandlerHelper.executeAction(any(), anyString())).thenReturn(response);
    Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);
    Mockito.when(parameterDetailsExtractorHelper.getParameterDetails(any(), any())).thenReturn(defaultParameterDetailsExtractor.getParameterDetails(workerActionRequest));
    try {
      actionHandler.executeAction(workerActionRequest);
      Assert.fail("The above method should throw exception");
    } catch (AppConnectDuzzitException e) {
      Assert.assertTrue(e.getErrorCode().contains("EXTERNAL_TASK_ACCESS_FAILURE"));
    }
  }

  @Test
  public void testExecuteAppActionWASUpdateTaskFailure() {
    String handlerId = "intuit-workflows/was-update-task";
    String dependency = WorkflowConstants.PROJECT_SERVICE;
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId(handlerId)
            .build();
    AppConnectTaskHandlerRequest actionRequest =
        AppConnectTaskHandlerRequest.builder()
            .endpoint("endpoint")
            .instanceId(workerActionRequest.getProcessInstanceId())
            .providerAppId("AP123")
            .providerWorkflowId(workerActionRequest.getHandlerId())
            .workflowId(workerActionRequest.getProcessDefinitionId())
            .build();

    WorkflowTaskHandlerResponse handlerResponse = new WorkflowTaskHandlerResponse();
    handlerResponse.setSuccess("false");
    handlerResponse.setError("error");
    WASHttpResponse<WorkflowTaskHandlerResponse> response =
        WASHttpResponse.<WorkflowTaskHandlerResponse>builder()
            .status(HttpStatus.OK)
            .response(handlerResponse)
            .error("error")
            .build();

    Mockito.when(actionHandlerHelper.getAppConnectDuzzitException(handlerId, response))
        .thenReturn(
            new AppConnectDuzzitException(
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorCode(),
                    dependency.toUpperCase(),
                    formatHandlerId(handlerId)),
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorMessage(),
                    dependency,
                    handlerId,
                    handlerResponse.getError())));
    Mockito.when(actionHandlerHelper.prepareTaskHandlerRequest(Mockito.any(), Mockito.any(), Mockito.any()))
        .thenReturn(actionRequest);

    Mockito.when(actionHandlerHelper.executeAction(any(), anyString())).thenReturn(response);
    Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);
    Mockito.when(parameterDetailsExtractorHelper.getParameterDetails(any(), any())).thenReturn(defaultParameterDetailsExtractor.getParameterDetails(workerActionRequest));
    try {
      actionHandler.executeAction(workerActionRequest);
      Assert.fail("The above method should throw exception");
    } catch (AppConnectDuzzitException e) {
      Assert.assertTrue(e.getMessage().contains(handlerId));
    }
  }

  @Test
  public void testExecuteAppActionSuccess_oinpBridge_Success() {

    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId("hId")
            .build();
    String responseKey = workerActionRequest.getActivityId()
        + WorkflowConstants.UNDERSCORE
        + WorkFlowVariables.RESPONSE.getName();
    ArgumentCaptor<Map<String, String>> argumentCaptor = ArgumentCaptor.forClass(Map.class);
    Mockito.when(appConnectOINPBridge.initiateBridge(any(), any())).thenReturn(true);
    Mockito.when(appConnectOINPBridge.executeNotificationAction(any(), argumentCaptor.capture())).thenReturn(new HashMap<>(){{
      put(responseKey, "true");
    }});

    List<WorkflowTaskHandlerInput> workflowTaskHandlerInputs = new ArrayList<>();
    WorkflowTaskHandlerInput workflowTaskHandlerInput = new WorkflowTaskHandlerInput();
    workflowTaskHandlerInput.setName("name");
    workflowTaskHandlerInput.setValue("value");
    workflowTaskHandlerInputs.add(workflowTaskHandlerInput);
    WorkflowTaskHandlerAction workflowTaskHandlerAction = WorkflowTaskHandlerAction.builder()
        .inputs(workflowTaskHandlerInputs).build();
    AppConnectTaskHandlerRequest actionRequest =
        AppConnectTaskHandlerRequest.builder()
            .action(workflowTaskHandlerAction)
            .endpoint("endpoint")
            .instanceId(workerActionRequest.getProcessInstanceId())
            .providerAppId("AP123")
            .providerWorkflowId(workerActionRequest.getHandlerId())
            .workflowId(workerActionRequest.getProcessDefinitionId())
            .build();

    Mockito.when(actionHandlerHelper.prepareTaskHandlerRequest(Mockito.any(),Mockito.any(), Mockito.any()))
        .thenReturn(actionRequest);

    Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);
    Mockito.when(parameterDetailsExtractorHelper.getParameterDetails(any(), any())).thenReturn(defaultParameterDetailsExtractor.getParameterDetails(workerActionRequest));

    Map<String, Object> resp = actionHandler.executeAction(workerActionRequest);

    Assert.assertEquals("value", argumentCaptor.getValue().get("name"));

    Mockito.verify(actionHandlerHelper, Mockito.times(0)).executeAction(any(), any());

    Assert.assertEquals("true", resp.get(responseKey));
  }

  @Test
  public void testExecuteAppActionSuccess_oinpBridge_Failed() {

    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId("hId")
            .build();
    Mockito.when(appConnectOINPBridge.initiateBridge(any(), any())).thenReturn(true);
    Mockito.when(appConnectOINPBridge.executeNotificationAction(any(), any())).thenThrow(new RuntimeException("Gone"));

    List<WorkflowTaskHandlerInput> workflowTaskHandlerInputs = new ArrayList<>();
    WorkflowTaskHandlerInput workflowTaskHandlerInput = new WorkflowTaskHandlerInput();
    workflowTaskHandlerInput.setName("name");
    workflowTaskHandlerInput.setValue("value");
    workflowTaskHandlerInputs.add(workflowTaskHandlerInput);
    WorkflowTaskHandlerAction workflowTaskHandlerAction = WorkflowTaskHandlerAction.builder()
        .inputs(workflowTaskHandlerInputs).build();
    AppConnectTaskHandlerRequest actionRequest =
        AppConnectTaskHandlerRequest.builder()
            .action(workflowTaskHandlerAction)
            .endpoint("endpoint")
            .instanceId(workerActionRequest.getProcessInstanceId())
            .providerAppId("AP123")
            .providerWorkflowId(workerActionRequest.getHandlerId())
            .workflowId(workerActionRequest.getProcessDefinitionId())
            .build();

    Mockito.when(actionHandlerHelper.prepareTaskHandlerRequest(Mockito.any(),Mockito.any(), Mockito.any()))
        .thenReturn(actionRequest);

    Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);
    Mockito.when(parameterDetailsExtractorHelper.getParameterDetails(any(), any())).thenReturn(defaultParameterDetailsExtractor.getParameterDetails(workerActionRequest));
    Exception e = Assertions.assertThrows(RuntimeException.class ,
        () -> actionHandler.executeAction(workerActionRequest));
    Assert.assertEquals("Gone", e.getMessage());
    TaskDetails taskDetails = SchemaDecoder.getTaskDetails(workerActionRequest.getInputVariables()).get();
    Assert.assertTrue(taskDetails.getFatal());

    Mockito.verify(actionHandlerHelper, Mockito.times(0)).executeAction(any(), any());

  }

  public String formatHandlerId(String handlerId) {
    int splitCount = handlerId.split(WorkflowConstants.BACKSLASH).length - 1;
    if (Integer.parseInt(WorkflowConstants.ZERO) != splitCount) {
      handlerId =
              handlerId
                      .split(WorkflowConstants.BACKSLASH)[splitCount]
                      .replace(WorkflowConstants.HYPHEN, WorkflowConstants.UNDERSCORE)
                      .toUpperCase();
    } else {
      handlerId = WorkflowError.EXTERNAL_TASK_HANDLER_ERROR.name();
    }
    return handlerId;
  }

  @Test
  public void test_getName() {
    Assert.assertEquals(
        TaskHandlerName.APP_CONNECT_WORKFLOW_TASK_ACTION_HANDLER, actionHandler.getName());
  }

  @Test
  public void test_MetricLogger() {
    String handlerId = "intuit-workflows/was-update-task";
    String dependency = WorkflowConstants.PROJECT_SERVICE;
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId(handlerId)
            .build();
    AppConnectTaskHandlerRequest actionRequest =
        AppConnectTaskHandlerRequest.builder()
            .endpoint("endpoint")
            .instanceId(workerActionRequest.getProcessInstanceId())
            .providerAppId("AP123")
            .providerWorkflowId(workerActionRequest.getHandlerId())
            .workflowId(workerActionRequest.getProcessDefinitionId())
            .build();

    WorkflowTaskHandlerResponse handlerResponse = new WorkflowTaskHandlerResponse();
    handlerResponse.setSuccess("false");
    handlerResponse.setError("error");
    WASHttpResponse<WorkflowTaskHandlerResponse> response =
        WASHttpResponse.<WorkflowTaskHandlerResponse>builder()
            .status(HttpStatus.OK)
            .response(handlerResponse)
            .error("error")
            .build();

    Mockito.when(actionHandlerHelper.getAppConnectDuzzitException(handlerId, response))
        .thenReturn(
            new AppConnectDuzzitException(
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorCode(),
                    dependency.toUpperCase(),
                    formatHandlerId(handlerId)),
                String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorMessage(),
                    dependency,
                    handlerId,
                    handlerResponse.getError())));
    Mockito.when(
            actionHandlerHelper.prepareTaskHandlerRequest(
                Mockito.any(), Mockito.any(), Mockito.any()))
        .thenReturn(actionRequest);
    Mockito.when(actionHandlerHelper.executeAction(any(), anyString())).thenReturn(response);
    Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);
    Mockito.when(workerRetryHelper.getRetryConfig(any(), any(), any())).thenReturn(null);
    actionHandler.execute(workerActionRequest);
    Mockito.verify(metricLogger, Mockito.times(1)).logErrorMetric(any(), any(), any());
  }

  @Test
  public void testExecuteAction_withOINPBridge_initiateBridgeTrue() {
    // ========================= ARRANGE =========================
    // 1. Definition details is present
    WorkerActionRequest workerActionRequest = WorkerActionRequest.builder()
            .activityId("testActivityId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(new HashMap<>()) // Provide if needed
            .handlerId("hId")
            .build();

    DefinitionDetails definitionDetails = DefinitionDetails.builder()
            .workflowId("testWorkflowId")
            .recordType(RecordType.PROJECT) // or any other type
            .build();

    Mockito.when(processDetailsRepoService.findByProcessIdWithoutDefinitionData("iId"))
            .thenReturn(Optional.of(definitionDetails));

    // 2. Validate request
    Mockito.when(workerUtil.validate(any(WorkerActionRequest.class)))
            .thenReturn(workerActionRequest);

    // 3. Parameter details
    Map<String, HandlerDetails.ParameterDetails> mockParamDetails = new HashMap<>();
    mockParamDetails.put("key", new HandlerDetails.ParameterDetails());
    Mockito.when(parameterDetailsExtractorHelper.getParameterDetails(any(), any()))
            .thenReturn(Optional.of(mockParamDetails));

    // 4. OINP bridge mocking
    Mockito.when(appConnectOINPBridge.initiateBridge(any(), any())).thenReturn(true);

    // The map that OINP bridge action will use
    Map<String, Object> onipActionResponseMap = new HashMap<>();
    onipActionResponseMap.put("OINPKey", "OINPValue");

    Mockito.when(appConnectOINPBridge.executeNotificationAction(any(), any()))
            .thenReturn(onipActionResponseMap);

    // 5. Prepare the actionRequest
    WorkflowTaskHandlerAction workflowTaskHandlerAction = WorkflowTaskHandlerAction.builder()
            .inputs(Collections.singletonList(
                    new WorkflowTaskHandlerInput("existingName", "existingValue")))
            .build();

    AppConnectTaskHandlerRequest actionRequest = AppConnectTaskHandlerRequest.builder()
            .action(workflowTaskHandlerAction)
            .endpoint("endpoint")
            .workflowId("wId")
            .providerWorkflowId("pWId")
            .instanceId("iId")
            .build();

    Mockito.when(actionHandlerHelper.prepareTaskHandlerRequest(any(), any(), any()))
            .thenReturn(actionRequest);

    // ========================= ACT =========================
    Map<String, Object> responseMap = actionHandler.executeAction(workerActionRequest);

    // ========================= ASSERT =========================
    // Verify that OINP was used, so no call to external AC action
    Mockito.verify(actionHandlerHelper, Mockito.times(0)).executeAction(any(), anyString());

    // Confirm the returned map is the OINP response
    Assert.assertEquals("OINPValue", responseMap.get("OINPKey"));
  }

  @Test
  public void testExecuteAction_withOINPBridge_initiateBridgeFalse_additionalInputsNotAvaliable() {
    // ========================= ARRANGE =========================
    // 1. Definition details is present
    WorkerActionRequest workerActionRequest = WorkerActionRequest.builder()
            .activityId("testActivityId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(new HashMap<>()) // Provide if needed
            .handlerId("hId")
            .build();

    DefinitionDetails definitionDetails = DefinitionDetails.builder()
            .workflowId("testWorkflowId")
            .recordType(RecordType.PROJECT)
            .build();

    Mockito.when(processDetailsRepoService.findByProcessIdWithoutDefinitionData("iId"))
            .thenReturn(Optional.of(definitionDetails));

    // 2. Validate request
    Mockito.when(workerUtil.validate(any(WorkerActionRequest.class)))
            .thenReturn(workerActionRequest);

    // 3. Parameter details
    Map<String, HandlerDetails.ParameterDetails> mockParamDetails = new HashMap<>();
    mockParamDetails.put("key", new HandlerDetails.ParameterDetails());
    Mockito.when(parameterDetailsExtractorHelper.getParameterDetails(any(), any()))
            .thenReturn(Optional.of(mockParamDetails));

    // 4. OINP bridge returns false (i.e., not used)
    Mockito.when(appConnectOINPBridge.initiateBridge(any(), any())).thenReturn(false);

    // 5. Prepare actionRequest with existing inputs
    List<WorkflowTaskHandlerInput> originalInputs = new ArrayList<>();
    originalInputs.add(new WorkflowTaskHandlerInput("existingName", "existingValue"));
    WorkflowTaskHandlerAction workflowTaskHandlerAction = WorkflowTaskHandlerAction.builder()
            .inputs(originalInputs)
            .build();

    AppConnectTaskHandlerRequest actionRequest = AppConnectTaskHandlerRequest.builder()
            .action(workflowTaskHandlerAction)
            .endpoint("endpoint")
            .workflowId("wId")
            .providerWorkflowId("pWId")
            .instanceId("iId")
            .build();

    Mockito.when(actionHandlerHelper.prepareTaskHandlerRequest(any(), any(), any()))
            .thenReturn(actionRequest);

    // 6. Handler config-based additional inputs
    List<WorkflowTaskHandlerInput> handlerInputs = new ArrayList<>();
    handlerInputs.add(new WorkflowTaskHandlerInput("newName1", "newValue1"));
    handlerInputs.add(new WorkflowTaskHandlerInput("existingName", "ignoredValue")); // same name as existing
    handlerInputs.add(new WorkflowTaskHandlerInput("newName2", "newValue2"));

    // So that getTaskParametersFromConfig returns the additional inputs
    Mockito.when(handlerConfig.getHandlers()).thenReturn(null);
    // Set up execution response
    WorkflowTaskHandlerResponse taskHandlerResponse = new WorkflowTaskHandlerResponse();
    taskHandlerResponse.setSuccess("true");
    WASHttpResponse<WorkflowTaskHandlerResponse> wasHttpResponse = WASHttpResponse
            .<WorkflowTaskHandlerResponse>builder()
            .status(HttpStatus.OK)
            .response(taskHandlerResponse)
            .isSuccess2xx(true)
            .build();
    String handlerId = "hid";
    String dependency = "";
    String responseError = "";
    Mockito.when(actionHandlerHelper.getAppConnectDuzzitException(any(), any()))
            .thenReturn(
                    new AppConnectDuzzitException(
                            String.format(
                                    EXTERNAL_TASK_HANDLER_ERROR.getErrorCode(),
                                    dependency.toUpperCase(),
                                    formatHandlerId(handlerId)),
                            String.format(
                                    EXTERNAL_TASK_HANDLER_ERROR.getErrorMessage(),
                                    dependency,
                                    handlerId,
                                    responseError)));

    Mockito.when(actionHandlerHelper.executeAction(any(), anyString()))
            .thenReturn(wasHttpResponse);

    // ========================= ACT =========================
    Map<String, Object> responseMap = actionHandler.executeAction(workerActionRequest);

    // ========================= ASSERT =========================
    // 1. OINP was not used
    Mockito.verify(appConnectOINPBridge, Mockito.never()).executeNotificationAction(any(), any());

    // 2. Additional inputs merged (only newName1 and newName2 should be added)
    List<WorkflowTaskHandlerInput> actualInputs = workflowTaskHandlerAction.getInputs();
    // We expect 3 in total: [existingName, newName1, newName2]
    Assert.assertEquals(1, actualInputs.size());

    Set<String> inputNames = actualInputs.stream()
            .map(WorkflowTaskHandlerInput::getName)
            .collect(Collectors.toSet());
    Assert.assertTrue("existingName should be present", inputNames.contains("existingName"));

    // 3. The existingName value remains "existingValue"
    // because we do not override if the name is already present
    WorkflowTaskHandlerInput existingNameInput = actualInputs.stream()
            .filter(input -> "existingName".equals(input.getName()))
            .findFirst()
            .orElseThrow(() -> new AssertionError("Expected 'existingName' to be in inputs"));
    Assert.assertEquals("existingValue", existingNameInput.getValue());

    // 4. The final call to external action was made
    Mockito.verify(actionHandlerHelper, Mockito.times(1)).executeAction(any(), ArgumentMatchers.eq("hId"));
    Assert.assertNotNull(responseMap);
    Mockito.verify(handlerConfig, Mockito.times(1)).getHandlers();

  }

  @Test
  public void testExecuteAction_withOINPBridge_initiateBridgeFalse_recordTypeIsNull_OnDemand() {
    // ========================= ARRANGE =========================
    // 1. Definition details is present
    WorkerActionRequest workerActionRequest = WorkerActionRequest.builder()
            .activityId("testActivityId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(new HashMap<>()) // Provide if needed
            .handlerId("hId")
            .build();

    DefinitionDetails definitionDetails = DefinitionDetails.builder()
            .workflowId("testWorkflowId")
            .build();

    Mockito.when(processDetailsRepoService.findByProcessIdWithoutDefinitionData("iId"))
            .thenReturn(Optional.of(definitionDetails));

    // 2. Validate request
    Mockito.when(workerUtil.validate(any(WorkerActionRequest.class)))
            .thenReturn(workerActionRequest);

    // 3. Parameter details
    Map<String, HandlerDetails.ParameterDetails> mockParamDetails = new HashMap<>();
    mockParamDetails.put("key", new HandlerDetails.ParameterDetails());
    Mockito.when(parameterDetailsExtractorHelper.getParameterDetails(any(), any()))
            .thenReturn(Optional.of(mockParamDetails));

    // 4. OINP bridge returns false (i.e., not used)
    Mockito.when(appConnectOINPBridge.initiateBridge(any(), any())).thenReturn(false);

    // 5. Prepare actionRequest with existing inputs
    List<WorkflowTaskHandlerInput> originalInputs = new ArrayList<>();
    originalInputs.add(new WorkflowTaskHandlerInput("existingName", "existingValue"));
    WorkflowTaskHandlerAction workflowTaskHandlerAction = WorkflowTaskHandlerAction.builder()
            .inputs(originalInputs)
            .build();

    AppConnectTaskHandlerRequest actionRequest = AppConnectTaskHandlerRequest.builder()
            .action(workflowTaskHandlerAction)
            .endpoint("endpoint")
            .workflowId("wId")
            .providerWorkflowId("pWId")
            .instanceId("iId")
            .build();

    Mockito.when(actionHandlerHelper.prepareTaskHandlerRequest(any(), any(), any()))
            .thenReturn(actionRequest);

    // 6. Handler config-based additional inputs
    List<WorkflowTaskHandlerInput> handlerInputs = new ArrayList<>();
    handlerInputs.add(new WorkflowTaskHandlerInput("newName1", "newValue1"));
    handlerInputs.add(new WorkflowTaskHandlerInput("existingName", "ignoredValue")); // same name as existing
    handlerInputs.add(new WorkflowTaskHandlerInput("newName2", "newValue2"));

    // So that getTaskParametersFromConfig returns the additional inputs
    Mockito.when(handlerConfig.getHandlers()).thenReturn(null);
    // Set up execution response
    WorkflowTaskHandlerResponse taskHandlerResponse = new WorkflowTaskHandlerResponse();
    taskHandlerResponse.setSuccess("true");
    WASHttpResponse<WorkflowTaskHandlerResponse> wasHttpResponse = WASHttpResponse
            .<WorkflowTaskHandlerResponse>builder()
            .status(HttpStatus.OK)
            .response(taskHandlerResponse)
            .isSuccess2xx(true)
            .build();
    String handlerId = "hid";
    String dependency = "";
    String responseError = "";
    Mockito.when(actionHandlerHelper.getAppConnectDuzzitException(any(), any()))
            .thenReturn(
                    new AppConnectDuzzitException(
                            String.format(
                                    EXTERNAL_TASK_HANDLER_ERROR.getErrorCode(),
                                    dependency.toUpperCase(),
                                    formatHandlerId(handlerId)),
                            String.format(
                                    EXTERNAL_TASK_HANDLER_ERROR.getErrorMessage(),
                                    dependency,
                                    handlerId,
                                    responseError)));

    Mockito.when(actionHandlerHelper.executeAction(any(), anyString()))
            .thenReturn(wasHttpResponse);

    // ========================= ACT =========================
    Map<String, Object> responseMap = actionHandler.executeAction(workerActionRequest);

    // ========================= ASSERT =========================
    // 1. OINP was not used
    Mockito.verify(appConnectOINPBridge, Mockito.never()).executeNotificationAction(any(), any());

    // 2. Additional inputs merged (only newName1 and newName2 should be added)
    List<WorkflowTaskHandlerInput> actualInputs = workflowTaskHandlerAction.getInputs();
    Assert.assertEquals(1, actualInputs.size());

    Set<String> inputNames = actualInputs.stream()
            .map(WorkflowTaskHandlerInput::getName)
            .collect(Collectors.toSet());
    Assert.assertTrue("existingName should be present", inputNames.contains("existingName"));

    // 3. The existingName value remains "existingValue"
    // because we do not override if the name is already present
    WorkflowTaskHandlerInput existingNameInput = actualInputs.stream()
            .filter(input -> "existingName".equals(input.getName()))
            .findFirst()
            .orElseThrow(() -> new AssertionError("Expected 'existingName' to be in inputs"));
    Assert.assertEquals("existingValue", existingNameInput.getValue());

    // 4. The final call to external action was made
    Mockito.verify(actionHandlerHelper, Mockito.times(1)).executeAction(any(), ArgumentMatchers.eq("hId"));
    Mockito.verify(handlerConfig, Mockito.times(0)).getHandlers();
    Assert.assertNotNull(responseMap);
    // Since success is "true", confirm we got back the normal response map from prepareTaskHandlerResponse
  }

}