package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.core.util.SystemTagUtil.definitionLookupComparator;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.SYSTEM_TAG;
import static java.util.Collections.singletonList;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.tags.SystemTags;
import com.intuit.appintgwkflw.wkflautomate.was.core.camunda.service.CamundaRunTimeServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.TemplateService;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ProcessStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.ResponseStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.TriggerStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowGenericResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowTriggerResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowTriggersResponse;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter.TransactionEntity;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

/** <AUTHOR> */
@RunWith(MockitoJUnitRunner.class)
public class SystemDefinitionTriggerHandlerTest {

  @Mock private ProcessDetailsRepository processDetailsRepository;

  @Mock private WASContextHandler contextHandler;

  @Mock private V3StartProcess v3StartProcess;

  @Mock private V3SignalProcess v3SignalProcess;

  @Mock private TemplateService templateService;

  @Mock private V3RunTimeHelper runtimeHelper;

  @Mock private CamundaRunTimeServiceRest camundaRest;

  @InjectMocks private SystemDefinitionTriggerHandler systemDefinitionTriggerHandler;

  private TemplateDetails templateDetails;

  private final String authHeader =
      "Intuit_IAM_Authentication intuit_appid=xxx,intuit_app_secret=xxxx,intuit_token=xxxx,intuit_userid=9130347715753436,intuit_token_type=IAM-Ticket,intuit_realmid=9130347798120106";

  private static final String NOT_ID ="notId";
  private static final String PROCESS_ID ="processId";

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
    templateDetails = TriggerHandlerTestData.getBPMNTemplateDetails();
    Mockito.when(runtimeHelper.getTemplateData(Mockito.any()))
        .thenReturn(templateDetails.getTemplateData());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testNulTemplateDetails() {

    TransactionEntity transactionEntity =
    	TransactionEntityFactory.getInstanceOf(getTriggerPayload().getTriggerMessage(),
    		contextHandler);
    Mockito.when(runtimeHelper.getTemplateData(transactionEntity)).thenReturn(null);
    BpmnModelInstance bpmnModelInstance = systemDefinitionTriggerHandler.getBpmnModelInstance(transactionEntity);
    systemDefinitionTriggerHandler.getStartEventOfTheTemplate(bpmnModelInstance);
  }

  @Test
  public void testProcessStart() {

    TemplateDetails templateDetails = TriggerHandlerTestData.getBPMNTemplateDetails();
    DefinitionDetails definitionDetail =
        TriggerHandlerTestData.getDefinitionDetails(templateDetails);

    Mockito.when(runtimeHelper.getEnabledSystemDefinition(Mockito.any(), Mockito.any()))
        .thenReturn(singletonList(definitionDetail));
    Mockito.when(runtimeHelper.fetchInitialStartEventActivityDetail(Mockito.any(TransactionEntity.class)))
            .thenReturn(TestHelper.getTestStartEventActivityDetail());    Mockito.when(contextHandler.get(WASContextEnums.AUTHORIZATION_HEADER)).thenReturn(authHeader);
    Mockito.when(v3StartProcess.startProcess(Mockito.any(), Mockito.any(), Mockito.any()))
        .thenReturn(Collections.singletonMap("id", "processId"));

    Mockito.when(
            runtimeHelper.getTriggerResponse(
                Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
        .thenReturn(
            WorkflowGenericResponse.builder()
                .status(ResponseStatus.SUCCESS)
                .response(
                    new WorkflowTriggersResponse(
                        Collections.singletonList(
                            WorkflowTriggerResponse.builder()
                                .processId("processId")
                                .status(TriggerStatus.PROCESS_STARTED)
                                .build())))
                .build());

    WorkflowGenericResponse workflowGenericResponse =
        systemDefinitionTriggerHandler.executeTrigger(getTriggerPayload());

    Assert.assertNotNull(workflowGenericResponse);
    Assert.assertEquals(
        TriggerStatus.PROCESS_STARTED,
        ((WorkflowTriggersResponse) workflowGenericResponse.getResponse())
            .getTriggers()
            .stream()
            .findFirst()
            .get()
            .getStatus());
  }

  @Test
  public void testProcessStartWithoutBpmnParsing() {
    TemplateDetails templateDetails = TriggerHandlerTestData.getBPMNTemplateDetails();
    DefinitionDetails definitionDetail =
            TriggerHandlerTestData.getDefinitionDetails(templateDetails);

    Mockito.when(runtimeHelper.getEnabledSystemDefinition(Mockito.any(), Mockito.any()))
            .thenReturn(singletonList(definitionDetail));
    Mockito.when(runtimeHelper.fetchInitialStartEventActivityDetail(Mockito.any(TransactionEntity.class)))
            .thenReturn(TestHelper.getTestStartEventActivityDetail());    Mockito.when(contextHandler.get(WASContextEnums.AUTHORIZATION_HEADER)).thenReturn(authHeader);
    Mockito.when(v3StartProcess.startProcess(Mockito.any(), Mockito.any(), Mockito.any()))
            .thenReturn(Collections.singletonMap("id", "processId"));

    Mockito.when(
                    runtimeHelper.getTriggerResponse(
                            Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
            .thenReturn(
                    WorkflowGenericResponse.builder()
                            .status(ResponseStatus.SUCCESS)
                            .response(
                                    new WorkflowTriggersResponse(
                                            Collections.singletonList(
                                                    WorkflowTriggerResponse.builder()
                                                            .processId("processId")
                                                            .status(TriggerStatus.PROCESS_STARTED)
                                                            .build())))
                            .build());

    WorkflowGenericResponse workflowGenericResponse =
            systemDefinitionTriggerHandler.executeTrigger(getTriggerPayload());

    Mockito.verify(runtimeHelper, Mockito.times(0)).getTemplateData(Mockito.any());
    Mockito.verify(runtimeHelper, Mockito.times(1)).fetchInitialStartEventActivityDetail(Mockito.any());

    Assert.assertNotNull(workflowGenericResponse);
    Assert.assertEquals(
            TriggerStatus.PROCESS_STARTED,
            ((WorkflowTriggersResponse) workflowGenericResponse.getResponse())
                    .getTriggers()
                    .stream()
                    .findFirst()
                    .get()
                    .getStatus());
  }

  @Test
  public void testProcessStartWithNoActivityDetail() {
    TemplateDetails testTemplateDetails = TriggerHandlerTestData.getBPMNTemplateDetails();
    DefinitionDetails definitionDetail =
            TriggerHandlerTestData.getDefinitionDetails(testTemplateDetails);

    Mockito.when(runtimeHelper.getEnabledSystemDefinition(Mockito.any(), Mockito.any()))
            .thenReturn(singletonList(definitionDetail));
    Mockito.when(runtimeHelper.fetchInitialStartEventActivityDetail(Mockito.any(TransactionEntity.class))).thenReturn(null);
    Mockito.when(contextHandler.get(WASContextEnums.AUTHORIZATION_HEADER)).thenReturn(authHeader);

    WorkflowGeneralException workflowGeneralException = Assert.assertThrows(WorkflowGeneralException.class, () -> {
      systemDefinitionTriggerHandler.executeTrigger(getTriggerPayload());
    } );

    Assert.assertEquals(WorkflowError.INVALID_INPUT, workflowGeneralException.getWorkflowError());
    Mockito.verify(runtimeHelper, Mockito.times(0)).getTemplateData(Mockito.any());
    Mockito.verify(runtimeHelper, Mockito.times(1)).fetchInitialStartEventActivityDetail(Mockito.any());
  }

  @Test
  public void testProcessStartWithOnDemandSuccess() {

    TemplateDetails templateDetails = TriggerHandlerTestData.getBPMNTemplateDetails();
    DefinitionDetails definitionDetail =
            TriggerHandlerTestData.getDefinitionDetails(templateDetails);

    Mockito.when(runtimeHelper.getEnabledSystemDefinition(Mockito.any(), Mockito.any()))
            .thenReturn(List.of(definitionDetail, DefinitionDetails.builder().definitionId("456").build()));
    Mockito.when(runtimeHelper.fetchInitialStartEventActivityDetail(Mockito.any(TransactionEntity.class)))
            .thenReturn(TestHelper.getTestStartEventActivityDetail());
    Mockito.when(contextHandler.get(WASContextEnums.AUTHORIZATION_HEADER)).thenReturn(authHeader);
    Mockito.when(v3StartProcess.startProcess(Mockito.any(), Mockito.any(), Mockito.any()))
            .thenReturn(Collections.singletonMap("id", "processId"));

    Mockito.when(
                    runtimeHelper.getTriggerResponse(
                            Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
            .thenReturn(
                    WorkflowGenericResponse.builder()
                            .status(ResponseStatus.SUCCESS)
                            .response(
                                    new WorkflowTriggersResponse(
                                            Collections.singletonList(
                                                    WorkflowTriggerResponse.builder()
                                                            .processId("processId")
                                                            .status(TriggerStatus.PROCESS_STARTED)
                                                            .build())))
                            .build());

    WorkflowGenericResponse workflowGenericResponse =
            systemDefinitionTriggerHandler.executeTrigger(getTriggerPayloadOnDemand());

    Assert.assertNotNull(workflowGenericResponse);
    Assert.assertEquals(
            TriggerStatus.PROCESS_STARTED,
            ((WorkflowTriggersResponse) workflowGenericResponse.getResponse())
                    .getTriggers()
                    .stream()
                    .findFirst()
                    .get()
                    .getStatus());

    // Signal process running on older definition
    ProcessDetails processDetails = ProcessDetails.builder().processId("processId").definitionDetails(DefinitionDetails.builder().definitionId("456").build()).build();
    Mockito.when(v3SignalProcess.signalProcessById(
        Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(true);

    Mockito.when(
        runtimeHelper.mergeTriggerResponse(
            Mockito.any(), Mockito.any()))
        .thenReturn(
            List.of(WorkflowTriggerResponse.builder()
                .processId("processId")
                .status(TriggerStatus.PROCESS_SIGNALLED)
                .build()));

    workflowGenericResponse =
        systemDefinitionTriggerHandler.executeTrigger(getTriggerPayloadOnDemand(), Optional.of(processDetails));

    Assert.assertNotNull(workflowGenericResponse);
    Assert.assertEquals(
        TriggerStatus.PROCESS_SIGNALLED,
        ((WorkflowTriggersResponse) workflowGenericResponse.getResponse())
            .getTriggers()
            .stream()
            .findFirst()
            .get()
            .getStatus());

  }

  @Test
  public void testProcessStartWithTagVersion() throws JsonProcessingException {

    TemplateDetails templateDetails = TriggerHandlerTestData.getBPMNTemplateDetails();
    DefinitionDetails definitionDetail =
            TriggerHandlerTestData.getDefinitionDetails(templateDetails);


    List<DefinitionDetails> definitionDetails = new ArrayList<DefinitionDetails>();

    definitionDetails.add(definitionDetail);

    Mockito.when(runtimeHelper.getEnabledSystemDefinition(Mockito.any(), Mockito.any()))
            .thenReturn(definitionDetails);
    Mockito.when(runtimeHelper.fetchInitialStartEventActivityDetail(Mockito.any(TransactionEntity.class)))
            .thenReturn(TestHelper.getTestStartEventActivityDetail());
    Mockito.when(contextHandler.get(WASContextEnums.AUTHORIZATION_HEADER)).thenReturn(authHeader);
    Mockito.when(v3StartProcess.startProcess(Mockito.any(), Mockito.any(), Mockito.any()))
            .thenReturn(Collections.singletonMap("id", "processId"));

    Mockito.when(
                    runtimeHelper.getTriggerResponse(
                            Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
            .thenReturn(
                    WorkflowGenericResponse.builder()
                            .status(ResponseStatus.SUCCESS)
                            .response(
                                    new WorkflowTriggersResponse(
                                            Collections.singletonList(
                                                    WorkflowTriggerResponse.builder()
                                                            .processId("processId")
                                                            .status(TriggerStatus.PROCESS_STARTED)
                                                            .build())))
                            .build());

    TriggerProcessDetails triggerProcessDetails = getTriggerPayload();
    WorkflowGenericResponse workflowGenericResponse =
            systemDefinitionTriggerHandler.executeTrigger(triggerProcessDetails);

    Assert.assertNotNull(workflowGenericResponse);
    Assert.assertEquals(
            TriggerStatus.PROCESS_STARTED,
            ((WorkflowTriggersResponse) workflowGenericResponse.getResponse())
                    .getTriggers()
                    .stream()
                    .findFirst()
                    .get()
                    .getStatus());

    DefinitionDetails definitionDetailWithTag =
            TriggerHandlerTestData.getDefinitionDetails(templateDetails);
    SystemTags tags = new SystemTags();
    tags.addSystemTag(SYSTEM_TAG, "1.2.3");
    definitionDetailWithTag.setLookupKeys(  tags.getTagsMapInstance().get(SYSTEM_TAG) != null
            ? new ObjectMapper().writeValueAsString(tags.tagsMapInstance)
            : null);

    Assert.assertEquals(definitionLookupComparator(definitionDetailWithTag, definitionDetail), -1);
    Assert.assertEquals(definitionLookupComparator( definitionDetail, definitionDetailWithTag), 1);
    Assert.assertEquals(definitionLookupComparator( definitionDetailWithTag, definitionDetailWithTag), 1);
    definitionDetails.add(definitionDetailWithTag);
    TriggerProcessDetails triggerProcessDetailsWithTag = getTriggerPayloadWithTag("1.2.3");
    workflowGenericResponse =
            systemDefinitionTriggerHandler.executeTrigger(triggerProcessDetailsWithTag);

    Assert.assertNotNull(workflowGenericResponse);
    Assert.assertEquals(
            TriggerStatus.PROCESS_STARTED,
            ((WorkflowTriggersResponse) workflowGenericResponse.getResponse())
                    .getTriggers()
                    .stream()
                    .findFirst()
                    .get()
                    .getStatus());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testDuplicateProcessStart() {

    TemplateDetails templateDetails = TriggerHandlerTestData.getBPMNTemplateDetails();
    DefinitionDetails definitionDetail =
        TriggerHandlerTestData.getDefinitionDetails(templateDetails);

    Mockito.when(runtimeHelper.getEnabledSystemDefinition(Mockito.any(), Mockito.any()))
        .thenReturn(singletonList(definitionDetail));

    Mockito.when(contextHandler.get(WASContextEnums.AUTHORIZATION_HEADER)).thenReturn(authHeader);

    systemDefinitionTriggerHandler.executeTrigger(getTriggerPayload());
    Mockito.verify(camundaRest).deleteProcessInstance(Mockito.anyString());
  }

  @Test
  public void testProcessListEmpty_skipProcessDelete() {

    TemplateDetails templateDetails = TriggerHandlerTestData.getBPMNTemplateDetails();
    DefinitionDetails definitionDetail =
        TriggerHandlerTestData.getDefinitionDetails(templateDetails);

    Mockito.when(runtimeHelper.getEnabledSystemDefinition(Mockito.any(), Mockito.any()))
        .thenReturn(singletonList(definitionDetail));
    Mockito.when(runtimeHelper.fetchInitialStartEventActivityDetail(Mockito.any(TransactionEntity.class)))
            .thenReturn(TestHelper.getTestStartEventActivityDetail());
    Mockito.when(contextHandler.get(WASContextEnums.AUTHORIZATION_HEADER)).thenReturn(authHeader);
    Mockito.when(v3StartProcess.startProcess(Mockito.any(), Mockito.any(), Mockito.any()))
        .thenReturn(Collections.singletonMap("id", "processId"));


    Optional<List<ProcessDetails>> processDetails =
        Optional.of(new ArrayList<>());
    Mockito.when(
            runtimeHelper.getProcessDetailsInstance(
                Mockito.any(), Mockito.anyLong(), Mockito.any(), Mockito.any()))
        .thenReturn(processDetails);

    systemDefinitionTriggerHandler.executeTrigger(getTriggerPayload());
    Mockito.verify(camundaRest, Mockito.times(0)).deleteProcessInstance(Mockito.anyString());
  }

  @Test
  public void testProcessSignal() {

    TemplateDetails templateDetails = TriggerHandlerTestData.getBPMNTemplateDetails();
    DefinitionDetails definitionDetail =
        TriggerHandlerTestData.getDefinitionDetails(templateDetails);

    Mockito.when(runtimeHelper.getEnabledSystemDefinition(Mockito.any(), Mockito.any()))
        .thenReturn(singletonList(definitionDetail));
    Mockito.when(runtimeHelper.fetchInitialStartEventActivityDetail(Mockito.any(TransactionEntity.class)))
        .thenReturn(TestHelper.getTestStartEventActivityDetail());
    Mockito.when(contextHandler.get(Mockito.any())).thenReturn(authHeader);

    Mockito.when(
            v3SignalProcess.signalProcessById(
                Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
        .thenReturn(true);

    ProcessDetails proccessDetails = new ProcessDetails();
    proccessDetails.setProcessId("pId");

    proccessDetails.setDefinitionDetails(definitionDetail);

    Mockito.when(
            processDetailsRepository
                .findByRecordIdAndOwnerIdAndProcessStatusInAndInternalStatusIsNullAndDefinitionDetailsInAndParentIdIsNull(
                    "35",
                    Long.parseLong("9130347798120106"),
                    Arrays.asList(ProcessStatus.ACTIVE, ProcessStatus.ERROR),
                    singletonList(definitionDetail)))
        .thenReturn(Optional.of(proccessDetails));

    Mockito.when(
            runtimeHelper.getWorkflowTriggerResponse(
                Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
        .thenReturn(
            WorkflowTriggerResponse.builder()
                .processId("pId")
                .status(TriggerStatus.PROCESS_SIGNALLED)
                .build()
        );

    Mockito
        .doCallRealMethod()
        .when(runtimeHelper)
        .mergeTriggerResponse(
            Mockito.any(), Mockito.any()
        );

    WorkflowGenericResponse workflowGenericResponse =
        systemDefinitionTriggerHandler.executeTrigger(getTriggerPayload());

    Assert.assertNotNull(workflowGenericResponse);
    Assert.assertEquals(
        TriggerStatus.PROCESS_SIGNALLED,
        ((WorkflowTriggersResponse) workflowGenericResponse.getResponse())
            .getTriggers()
            .stream()
            .findFirst()
            .get()
            .getStatus());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testProcessSignaError() {

    TemplateDetails templateDetails = TriggerHandlerTestData.getBPMNTemplateDetails();
    DefinitionDetails definitionDetail =
        TriggerHandlerTestData.getDefinitionDetails(templateDetails);

    Mockito.when(runtimeHelper.getEnabledSystemDefinition(Mockito.any(), Mockito.any()))
        .thenReturn(singletonList(definitionDetail));
    Mockito.when(runtimeHelper.fetchInitialStartEventActivityDetail(Mockito.any(TransactionEntity.class)))
            .thenReturn(TestHelper.getTestStartEventActivityDetail());
    Mockito.when(contextHandler.get(Mockito.any())).thenReturn(authHeader);

    Mockito.when(
            v3SignalProcess.signalProcessById(
                Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
        .thenThrow(new WorkflowGeneralException(WorkflowError.TRIGGER_SIGNAL_PROCESS_ERROR));

    ProcessDetails proccessDetails = new ProcessDetails();
    proccessDetails.setProcessId("pId");

    proccessDetails.setDefinitionDetails(definitionDetail);

    Mockito.when(
            processDetailsRepository
                .findByRecordIdAndOwnerIdAndProcessStatusInAndInternalStatusIsNullAndDefinitionDetailsInAndParentIdIsNull(
                    "35",
                    Long.parseLong("9130347798120106"),
                    Arrays.asList(ProcessStatus.ACTIVE, ProcessStatus.ERROR),
                    singletonList(definitionDetail)))
        .thenReturn(Optional.of(proccessDetails));

    try {

      systemDefinitionTriggerHandler.executeTrigger(getTriggerPayload());
    } catch (Exception ex) {
      Mockito.verify(runtimeHelper, Mockito.times(1))
          .markProcessError(Mockito.any(), Mockito.any());
      throw ex;
    }
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testProcessSignalIncorrectDefError() {

    TemplateDetails templateDetails = TriggerHandlerTestData.getBPMNTemplateDetails();
    DefinitionDetails definitionDetail =
        TriggerHandlerTestData.getDefinitionDetails(templateDetails);

    Mockito.when(runtimeHelper.getEnabledSystemDefinition(Mockito.any(), Mockito.any()))
        .thenReturn(singletonList(definitionDetail));

    Mockito.when(contextHandler.get(Mockito.any())).thenReturn(authHeader);

    ProcessDetails proccessDetails = new ProcessDetails();
    proccessDetails.setProcessId("pId");

    proccessDetails.setDefinitionDetails(new DefinitionDetails());

    Mockito.when(processDetailsRepository
        .findByRecordIdAndOwnerIdAndProcessStatusInAndInternalStatusIsNullAndDefinitionDetailsInAndParentIdIsNull(
            "35", Long.parseLong("9130347798120106"),
            Arrays.asList(ProcessStatus.ACTIVE, ProcessStatus.ERROR),
            singletonList(definitionDetail)))
        .thenReturn(Optional.of(proccessDetails));

    WorkflowGenericResponse workflowGenericResponse =
        systemDefinitionTriggerHandler.executeTrigger(getTriggerPayload());

    Assert.assertNotNull(workflowGenericResponse);
    Assert.assertEquals(TriggerStatus.PROCESS_SIGNALLED,
        ((WorkflowTriggersResponse) workflowGenericResponse.getResponse()).getTriggers().stream()
            .findFirst().get().getStatus());
  }

  @SuppressWarnings("unchecked")
  @Test(expected = WorkflowGeneralException.class)
  public void testProcessSignaErrorProcessNotMarkedInErrorStateTest() {

    TemplateDetails templateDetails = TriggerHandlerTestData.getBPMNTemplateDetails();
    DefinitionDetails definitionDetail =
        TriggerHandlerTestData.getDefinitionDetails(templateDetails);

    Mockito.when(runtimeHelper.getEnabledSystemDefinition(Mockito.any(), Mockito.any()))
        .thenReturn(singletonList(definitionDetail));

    Mockito.when(contextHandler.get(Mockito.any())).thenReturn(authHeader);

    ProcessDetails proccessDetails = new ProcessDetails();
    proccessDetails.setProcessId("pId");

    proccessDetails.setDefinitionDetails(definitionDetail);

    Mockito.when(
            processDetailsRepository
                .findByRecordIdAndOwnerIdAndProcessStatusInAndInternalStatusIsNullAndDefinitionDetailsInAndParentIdIsNull(
                    "35",
                    Long.parseLong("9130347798120106"),
                    Arrays.asList(ProcessStatus.ACTIVE, ProcessStatus.ERROR),
                    singletonList(definitionDetail)))
        .thenReturn(Optional.of(proccessDetails));

    TriggerProcessDetails triggerPayload = getTriggerPayload();
    ((Map<String, Object>) (triggerPayload.getTriggerMessage().get("eventHeaders")))
        .put("blockProcessOnSignalFailure", false);
    try {

      systemDefinitionTriggerHandler.executeTrigger(triggerPayload);
    } catch (Exception ex) {
      Mockito.verify(processDetailsRepository, Mockito.times(0))
          .updateProcessStatus(Mockito.any(), Mockito.any());
      throw ex;
    }
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testEmptyDefinition() {

    TemplateDetails templateDetails = TriggerHandlerTestData.getBPMNTemplateDetails();
    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();

    Mockito.when(runtimeHelper.getEnabledSystemDefinition(Mockito.any(), Mockito.any()))
            .thenReturn(definitionDetailsList);

    Mockito.when(contextHandler.get(WASContextEnums.AUTHORIZATION_HEADER)).thenReturn(authHeader);

    systemDefinitionTriggerHandler.executeTrigger(getTriggerPayload());
  }

  @Test
  public void testStartProcessEmpty() {

    TemplateDetails templateDetails = TriggerHandlerTestData.getBPMNTemplateDetails();
    DefinitionDetails definitionDetail =
            TriggerHandlerTestData.getDefinitionDetails(templateDetails);

    Mockito.when(runtimeHelper.getEnabledSystemDefinition(Mockito.any(), Mockito.any()))
            .thenReturn(singletonList(definitionDetail));
    Mockito.when(runtimeHelper.fetchInitialStartEventActivityDetail(Mockito.any(TransactionEntity.class)))
            .thenReturn(TestHelper.getTestStartEventActivityDetail());
    Mockito.when(contextHandler.get(WASContextEnums.AUTHORIZATION_HEADER)).thenReturn(authHeader);
    Mockito.when(v3StartProcess.startProcess(Mockito.any(), Mockito.any(), Mockito.any()))
            .thenReturn(Collections.singletonMap(NOT_ID, PROCESS_ID));

    Mockito.when(
            runtimeHelper.getTriggerResponse(
                    Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
            .thenReturn(
                    WorkflowGenericResponse.builder()
                            .status(ResponseStatus.SUCCESS)
                            .response(
                                    new WorkflowTriggersResponse(
                                            Collections.singletonList(
                                                    WorkflowTriggerResponse.builder()
                                                            .processId(PROCESS_ID)
                                                            .status(TriggerStatus.NO_ACTION)
                                                            .build())))
                            .build());

    WorkflowGenericResponse workflowGenericResponse =
            systemDefinitionTriggerHandler.executeTrigger(getTriggerPayload());

    Assert.assertNotNull(workflowGenericResponse);
    Assert.assertEquals(
            TriggerStatus.NO_ACTION,
            ((WorkflowTriggersResponse) workflowGenericResponse.getResponse())
                    .getTriggers()
                    .stream()
                    .findFirst()
                    .get()
                    .getStatus());
  }

  @SuppressWarnings("serial")
  private TriggerProcessDetails getTriggerPayload() {

    Map<String, Object> trigger = new HashMap<>();
    Map<String, Object> eventHeaders = new HashMap<>();
    Map<String, Object> entityPayload = new HashMap<>();
    eventHeaders.put("workflow", "approval");
    eventHeaders.put("entityType", RecordType.INVOICE);
    eventHeaders.put("entityChangeType", "created");
    eventHeaders.put("entityId", "35");
    trigger.put("eventHeaders", eventHeaders);
    entityPayload.put(
        "Invoice",
        new HashMap<String, String>() {
          {
            put("a", "b");
          }
        });
    trigger.put("entity", entityPayload);
    TemplateDetails templateDetail = TriggerHandlerTestData.getBPMNTemplateDetails();
    return TriggerProcessDetails.builder().triggerMessage(trigger).templateDetails(singletonList(templateDetails)).build();
  }

  private TriggerProcessDetails getTriggerPayloadOnDemand() {

    TriggerProcessDetails triggerProcessDetails = getTriggerPayload();
    triggerProcessDetails.getTriggerMessage().put(WorkflowConstants.ON_DEMAND_APPROVAL, true);
    return triggerProcessDetails;

  }

  private TriggerProcessDetails getTriggerPayloadWithTag(String tag) {

    Map<String, Object> trigger = new HashMap<>();
    Map<String, Object> eventHeaders = new HashMap<>();
    Map<String, Object> entityPayload = new HashMap<>();
    Map<String, Object> version = new HashMap<>();
    version.put("version", tag);
    eventHeaders.put("workflow", "approval");
    eventHeaders.put("entityType", RecordType.INVOICE);
    eventHeaders.put("entityChangeType", "created");
    eventHeaders.put("entityId", "35");
    eventHeaders.put("tags", version);
    trigger.put("eventHeaders", eventHeaders);
    entityPayload.put(
            "Invoice",
            new HashMap<String, String>() {
              {
                put("a", "b");
              }
            });
    trigger.put("entity", entityPayload);
    TemplateDetails templateDetail = TriggerHandlerTestData.getBPMNTemplateDetails();
    return TriggerProcessDetails.builder().triggerMessage(trigger).templateDetails(singletonList(templateDetails)).build();
  }
}
