package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.connector;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.INTUIT_TID;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.offlineticket.OfflineTicketClient;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.httpClient.WASHttpClient;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Objects;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;

public class ConnectorImplTest {
  @Mock
  private WASHttpClient client;
  @Mock
  private OfflineTicketClient offlineTicketClient;
  @Mock
  private WASContextHandler wasContextHandler;
  @InjectMocks
  public ConnectorImpl connectorImpl;

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
  }

  @Test
  public void test_populateHeader(){
    Mockito.when(offlineTicketClient.getSystemOfflineHeaderWithContextRealmForOfflineJob(Mockito.any())).thenReturn("test");
    Mockito.when(wasContextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn("test123");

    HttpHeaders header = connectorImpl.populateHeader();
    Assertions.assertEquals(Objects.requireNonNull(header.get(HttpHeaders.CONTENT_TYPE)).stream().findFirst().orElse(null), "application/json");
    Assertions.assertEquals(Objects.requireNonNull(header.get(HttpHeaders.AUTHORIZATION)).get(0), "test");
    Assertions.assertEquals(Objects.requireNonNull(header.get(INTUIT_TID)).get(0), "test123");
  }

  @Test
  public void test_executeRequest_success(){
    Mockito.when(offlineTicketClient.getSystemOfflineHeaderWithContextRealmForOfflineJob("12334")).thenReturn("test");
    Mockito.when(wasContextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn("test123");
    Mockito.when(client.httpResponse(Mockito.any())).thenReturn(WASHttpResponse.builder().isSuccess2xx(true).build());

    WASHttpResponse<Object> response = connectorImpl.executeRequest(
        "POST", new ArrayList<>(), new ArrayList<>(), new HashMap<>(), "test");
    Assertions.assertTrue(response.isSuccess2xx());
  }

  @Test
  public void test_executeRequestTidNull_populateVal(){
    Mockito.when(offlineTicketClient.getSystemOfflineHeaderWithContextRealmForOfflineJob("12334")).thenReturn("test");
    ArgumentCaptor<WASHttpRequest<Object, Object>> httpRequestCaptor = ArgumentCaptor.forClass(WASHttpRequest.class);
    Mockito.when(client.httpResponse(httpRequestCaptor.capture())).thenReturn(WASHttpResponse.builder().isSuccess2xx(true).build());

    WASHttpResponse<Object> response = connectorImpl.executeRequest(
        "POST", new ArrayList<>(), new ArrayList<>(), new HashMap<>(), "test");

    Assertions.assertTrue(response.isSuccess2xx());
    Assertions.assertTrue(
        Objects.requireNonNull(
            Objects.requireNonNull(httpRequestCaptor.getValue().getRequestHeaders().get(INTUIT_TID))
                .stream().findFirst().orElse(null)).startsWith("connector-"));
  }

  @Test
  public void test_executeRequest_failure(){
    Mockito.when(offlineTicketClient.getSystemOfflineHeaderWithContextRealmForOfflineJob("12334")).thenReturn("test");
    Mockito.when(wasContextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn("test123");
    Mockito.when(client.httpResponse(Mockito.any())).thenReturn(WASHttpResponse.builder().isSuccess2xx(false).build());

    Exception exception = Assertions.assertThrows(WorkflowGeneralException.class, () -> {
      connectorImpl.executeRequest(
          "POST", new ArrayList<>(), new ArrayList<>(), new HashMap<>(), "test");
    });
    Assertions.assertEquals(exception.getMessage(),
        WorkflowError.APPROVAL_SERVICE_CALL_FAILED.getErrorMessage());
  }

  @Test
  public void testUrlNull_executeRequest_failure(){
    Mockito.when(offlineTicketClient.getSystemOfflineHeaderWithContextRealmForOfflineJob("12334")).thenReturn("test");
    Mockito.when(wasContextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn("test123");
    Mockito.when(client.httpResponse(Mockito.any())).thenReturn(WASHttpResponse.builder().isSuccess2xx(false).build());

    Exception exception = Assertions.assertThrows(WorkflowGeneralException.class, () -> {
      connectorImpl.executeRequest(
          "POST", new ArrayList<>(), new ArrayList<>(), new HashMap<>(), null);
    });
    Assertions.assertEquals(exception.getMessage(),
        WorkflowError.APPROVAL_SERVICE_CALL_FAILED.getErrorMessage());
  }

  @Test
  public void test_handleFailure_failure_A30(){
    // Approval Service Throws BAD Request
    // But error code is not A30
    String downstreamError = "{\"status\":\"BAD_REQUEST\",\"data\":null,\"errorDetails\":{\"errorCode\":\"A30\",\"errorMessage\":\"No approval lifecycle found for this entityId=1001, entityType=invoice\",\"errorDescription\":\"No approval lifecycle found for this entity\"}}\n";
    WASHttpResponse<Object> response = WASHttpResponse.builder().status(HttpStatus.BAD_REQUEST).downstreamError(downstreamError).isSuccess2xx(false).build();
    Assertions.assertEquals(connectorImpl.handleFailure(response), response);
  }

@Test(expected = WorkflowGeneralException.class)
  public void test_handleFailure_failure_not_A30(){
    // Approval Service Throws BAD Request
    // But error code is not A30
    WASHttpResponse<Object> response = WASHttpResponse.builder().status(HttpStatus.BAD_REQUEST).isSuccess2xx(false).build();
    connectorImpl.handleFailure(response);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void test_handleFailure_failure5xx(){
    // Approval Service Throws 5xx Request
    WASHttpResponse<Object> response = WASHttpResponse.builder().status(HttpStatus.GATEWAY_TIMEOUT).isSuccess2xx(false).build();
    Assertions.assertEquals(connectorImpl.handleFailure(response), response);
  }
}