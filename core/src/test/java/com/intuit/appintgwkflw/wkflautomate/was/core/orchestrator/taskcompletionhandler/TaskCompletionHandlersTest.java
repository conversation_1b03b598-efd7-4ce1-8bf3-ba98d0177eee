package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.taskcompletionhandler;

import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

public class TaskCompletionHandlersTest {

  @Mock private TaskCompletionHandler taskCompletionHandler;

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
    Mockito.when(taskCompletionHandler.getName()).thenReturn(EventEntityType.EXTERNALTASK);
    TaskCompletionHandlers.addHandler(taskCompletionHandler.getName(), taskCompletionHandler);
  }

  @Test
  public void getTestNull() {
    TaskCompletionHandler handler = TaskCompletionHandlers.getHandler(null);
    Assert.assertNull(handler);
  }

  @Test
  public void getTestAction() {
    TaskCompletionHandler handler = TaskCompletionHandlers.getHandler(EventEntityType.EXTERNALTASK);
    Assert.assertNotNull(handler);
  }

  @Test
  public void containsFalse() {
    Assert.assertFalse(TaskCompletionHandlers.contains(null));
  }

  @Test
  public void containsTrue() {
    Assert.assertTrue(TaskCompletionHandlers.contains(EventEntityType.EXTERNALTASK));
  }
}
