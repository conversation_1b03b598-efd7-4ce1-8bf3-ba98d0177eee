package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.MAX_TRIES;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.PROCESS_STATUS_CLOSE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CustomWorkflowType.APPROVAL;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;

import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureManager;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.OnDemandHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.impl.ProcessDetailsRepoService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.InternalStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ProcessStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import com.intuit.appintgwkflw.wkflautomate.was.workflowvariability.DefinitionPendingDeletion;
import com.intuit.v4.Authorization;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

/**
 * <AUTHOR>
 */
public class ProcessStatusCheckHandlerTest {

  @InjectMocks private ProcessStatusCheckHandler processStatusCheckHandler;

  @Mock private ProcessDetailsRepoService processDetailsRepoService;
  @Mock private DefinitionDetailsRepository definitionDetailsRepository;
  @Mock private OnDemandHelper onDemandHelper;
  @Mock private TemplateDetails bpmnTemplateDetail;
  private Authorization authorization = TestHelper.mockAuthorization("123");

  @Mock private MetricLogger metricLogger;
  private static final String parametersSchema =
      TestHelper.readResourceAsString("schema/testData/parameters.json");
  private static Map<String, String> schema = new HashMap<>();

  @Mock private FeatureManager featureManager;

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
    ReflectionTestUtils.setField(processStatusCheckHandler, "metricLogger", metricLogger);
  }

  static {
    schema.put(WorkFlowVariables.PARAMETERS_KEY.getName(), parametersSchema);
    schema.put(WorkflowConstants.INTUIT_REALMID, "123");
    schema.put(MAX_TRIES, null);
  }

  @Test
  public void testSuccess() {
    String processInstanceID = "iId";
    Mockito.when(processDetailsRepoService.findIncompleteProcesses(123L, processInstanceID))
        .thenReturn(Optional.empty());
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId(processInstanceID)
            .inputVariables(schema)
            .build();
    Map<String, Object> response = processStatusCheckHandler.executeAction(workerActionRequest);
    Assert.assertNotNull(response);
    Assert.assertEquals(3, response.size());
    Assert.assertEquals(
        Boolean.TRUE,
        response.entrySet().stream()
            .filter(
                stringObjectEntry ->
                    stringObjectEntry.getKey().equalsIgnoreCase(PROCESS_STATUS_CLOSE))
            .map(t -> t.getValue())
            .findFirst()
            .get());
    Assert.assertTrue((Boolean) response.get(PROCESS_STATUS_CLOSE));
  }

  @Test
  public void testStillWaitingUseCaseSuccess() {
    List<ProcessDetails> processDetailsList =
        Collections.singletonList(
            ProcessDetails.builder()
                .ownerId(123L)
                .processId("pid1")
                .processStatus(ProcessStatus.ACTIVE)
                .build());
    String processInstanceID = "iId";

    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .ownerId(123L)
            .processDefinitionId("dId")
            .processInstanceId(processInstanceID)
            .inputVariables(schema)
            .build();

    Mockito.when(
            processDetailsRepoService.findIncompleteProcesses(
                123L, workerActionRequest.getProcessInstanceId()))
        .thenReturn(Optional.of(processDetailsList));
    Map<String, Object> response = processStatusCheckHandler.executeAction(workerActionRequest);
    Assert.assertNotNull(response);
    Assert.assertEquals(3, response.size());
    Assert.assertFalse((Boolean) response.get(PROCESS_STATUS_CLOSE));
  }
  
  @Test
  public void testSuccessIgnoreAllreadyErrorProcesses() {
    List<ProcessDetails> processDetailsList =
        Arrays.asList(
            ProcessDetails.builder()
                .ownerId(123L)
                .processId("pid1")
                .processStatus(ProcessStatus.ACTIVE)
                .internalStatus(InternalStatus.ALREADY_ERROR)
                .build());
    String processInstanceID = "iId";

    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .ownerId(123L)
            .processDefinitionId("dId")
            .processInstanceId(processInstanceID)
            .inputVariables(schema)
            .build();

    Mockito.when(
            processDetailsRepoService.findIncompleteProcesses(
                123L, workerActionRequest.getProcessInstanceId()))
        .thenReturn(Optional.of(processDetailsList));
    Map<String, Object> response = processStatusCheckHandler.executeAction(workerActionRequest);
    Assert.assertNotNull(response);
    Assert.assertEquals(3, response.size());
    Assert.assertTrue((Boolean) response.get(PROCESS_STATUS_CLOSE));
  }

  @Test
  public void testFailure() {
    String processInstanceID = "iId";

    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .ownerId(123L)
            .processDefinitionId("dId")
            .processInstanceId(processInstanceID)
            .inputVariables(schema)
            .build();

    Mockito.doThrow(RuntimeException.class)
        .when(processDetailsRepoService)
        .findIncompleteProcesses(123L, workerActionRequest.getProcessInstanceId());

    Map<String, Object> response = processStatusCheckHandler.executeAction(workerActionRequest);

    Assert.assertNotNull(response);
    Assert.assertNotNull(response.get(PROCESS_STATUS_CLOSE));
    Assert.assertEquals(false, response.get(PROCESS_STATUS_CLOSE));
    Assert.assertNotNull(response.get(MAX_TRIES));
    Assert.assertEquals(1, response.get(MAX_TRIES));
  }

  @Test
  public void testStillWaitingUseCaseMultipleProcess() {
    ProcessDetails process1 =
        ProcessDetails.builder()
            .ownerId(123L)
            .processId("pid")
            .processStatus(ProcessStatus.ACTIVE)
            .build();
    ProcessDetails process2 =
        ProcessDetails.builder()
            .ownerId(123L)
            .processId("pid2")
            .processStatus(ProcessStatus.ACTIVE)
            .build();
    List<ProcessDetails> processDetailsList = new ArrayList<>();
    processDetailsList.add(process1);
    processDetailsList.add(process2);

    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .ownerId(123L)
            .processDefinitionId("dId")
            .processInstanceId("pid")
            .inputVariables(schema)
            .build();

    workerActionRequest.getInputVariables().remove(WorkflowConstants.SUBSCRIPTIONS);

    Mockito.when(
            processDetailsRepoService.findIncompleteProcesses(
                123L, workerActionRequest.getProcessInstanceId()))
        .thenReturn(Optional.of(processDetailsList));
    Map<String, Object> response = processStatusCheckHandler.executeAction(workerActionRequest);
    Assert.assertNotNull(response);
    Assert.assertEquals(3, response.size());
    Assert.assertFalse((Boolean) response.get(PROCESS_STATUS_CLOSE));
  }

  @Test
  public void testLogErrorMetric() {
    WorkerActionRequest workerActionRequest = Mockito.mock(WorkerActionRequest.class);
    Mockito.when(workerActionRequest.getInputVariables()).thenThrow(WorkflowGeneralException.class);
    try {
      processStatusCheckHandler.execute(workerActionRequest);
      Assert.fail("Exception should be thrown");
    } catch (WorkflowGeneralException workflowGeneralException) {
      Mockito.verify(metricLogger, Mockito.times(1)).logErrorMetric(any(), any(), any());
    }
  }

  @Test
  public void testSuccess_withActiveSubscriptions() {
    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail, authorization, "123");
    DefinitionPendingDeletion definitionPendingDeletion =
        DefinitionPendingDeletion.buildFromDefinitionDetails(definitionDetail);

    String processInstanceID = "iId";
    Mockito.when(
            processDetailsRepoService.findByDefinitionDetailsListAndProcessStatus(
                Arrays.asList(definitionDetail), ProcessStatus.ACTIVE))
        .thenReturn(Optional.empty());

    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId(processInstanceID)
            .inputVariables(schema)
            .build();
    workerActionRequest
        .getInputVariables()
        .put(
            WorkflowConstants.SUBSCRIPTIONS,
            "[{\"offeringId\": \"salsa\", \"offeringType\": \"QBO_PLUS\"}]");

    Mockito.when(
        featureManager.getBoolean(
            eq(WorkflowConstants.WORKFLOW_VARIABILITY_DOWNGRADE_FF), anyString()
        )).thenReturn(true);

    workerActionRequest
        .getInputVariables()
        .put(
            WorkflowConstants.DEFINITION_DETAILS_PENDING_DELETION,
            ObjectConverter.toJson(Arrays.asList(definitionPendingDeletion)));

    Map<String, Object> response = processStatusCheckHandler.executeAction(workerActionRequest);
    Assert.assertNotNull(response);
    Assert.assertEquals(3, response.size());
    Assert.assertEquals(
        Boolean.TRUE,
        response.entrySet().stream()
            .filter(
                stringObjectEntry ->
                    stringObjectEntry.getKey().equalsIgnoreCase(PROCESS_STATUS_CLOSE))
            .map(t -> t.getValue())
            .findFirst()
            .get());
    Assert.assertTrue((Boolean) response.get(PROCESS_STATUS_CLOSE));
    Mockito.verify(processDetailsRepoService, Mockito.never())
        .findIncompleteProcesses(any(), any());
  }

  @Test
  public void testSuccess_withActiveSubscriptions_VariabilityFFDisabled() {
    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail, authorization, "123");
    DefinitionPendingDeletion definitionPendingDeletion =
        DefinitionPendingDeletion.buildFromDefinitionDetails(definitionDetail);

    String processInstanceID = "iId";
    Mockito.when(
            processDetailsRepoService.findByDefinitionDetailsListAndProcessStatus(
                Arrays.asList(definitionDetail), ProcessStatus.ACTIVE))
        .thenReturn(Optional.empty());

    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId(processInstanceID)
            .inputVariables(schema)
            .build();
    workerActionRequest
        .getInputVariables()
        .put(
            WorkflowConstants.SUBSCRIPTIONS,
            "[{\"offeringId\": \"salsa\", \"offeringType\": \"QBO_PLUS\"}]");

    Mockito.when(
        featureManager.getBoolean(
            eq(WorkflowConstants.WORKFLOW_VARIABILITY_DOWNGRADE_FF), anyString()
        )).thenReturn(false);

    workerActionRequest
        .getInputVariables()
        .put(
            WorkflowConstants.DEFINITION_DETAILS_PENDING_DELETION,
            ObjectConverter.toJson(Arrays.asList(definitionPendingDeletion)));

    Mockito.when(
            processDetailsRepoService.findIncompleteProcesses(
                123L, workerActionRequest.getProcessInstanceId()))
        .thenReturn(Optional.of(new ArrayList<>()));

    Map<String, Object> response = processStatusCheckHandler.executeAction(workerActionRequest);
    Assert.assertNotNull(response);
    Assert.assertEquals(3, response.size());
    Assert.assertEquals(
        Boolean.TRUE,
        response.entrySet().stream()
            .filter(
                stringObjectEntry ->
                    stringObjectEntry.getKey().equalsIgnoreCase(PROCESS_STATUS_CLOSE))
            .map(t -> t.getValue())
            .findFirst()
            .get());
    Assert.assertTrue((Boolean) response.get(PROCESS_STATUS_CLOSE));
    Mockito.verify(processDetailsRepoService, Mockito.times(1))
        .findIncompleteProcesses(any(), any());
  }

  @Test
  public void testSuccess_withActiveSubscriptions_NoDefinitions() {
    String processInstanceID = "iId";
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId(processInstanceID)
            .inputVariables(schema)
            .build();
    workerActionRequest
        .getInputVariables()
        .put(
            WorkflowConstants.SUBSCRIPTIONS,
            "[{\"offeringId\": \"QBO\", \"offeringType\": \"PLUS\"}]");
    workerActionRequest
        .getInputVariables()
        .put(WorkflowConstants.DEFINITION_DETAILS_PENDING_DELETION, "[]");

    Mockito.when(
        featureManager.getBoolean(
            eq(WorkflowConstants.WORKFLOW_VARIABILITY_DOWNGRADE_FF), anyString()
        )).thenReturn(true);

    Map<String, Object> response = processStatusCheckHandler.executeAction(workerActionRequest);
    Assert.assertNotNull(response);
    Assert.assertEquals(3, response.size());
    Assert.assertEquals(
        Boolean.TRUE,
        response.entrySet().stream()
            .filter(
                stringObjectEntry ->
                    stringObjectEntry.getKey().equalsIgnoreCase(PROCESS_STATUS_CLOSE))
            .map(t -> t.getValue())
            .findFirst()
            .get());
    Assert.assertTrue((Boolean) response.get(PROCESS_STATUS_CLOSE));
    Mockito.verify(processDetailsRepoService, Mockito.never())
        .findIncompleteProcesses(any(), any());
  }

  @Test
  public void testSuccess_withActiveSubscriptions_NoDefinitions_VariabilityFFDisabled() {
    String processInstanceID = "iId";
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId(processInstanceID)
            .inputVariables(schema)
            .build();
    workerActionRequest
        .getInputVariables()
        .put(
            WorkflowConstants.SUBSCRIPTIONS,
            "[{\"offeringId\": \"QBO\", \"offeringType\": \"PLUS\"}]");
    workerActionRequest
        .getInputVariables()
        .put(WorkflowConstants.DEFINITION_DETAILS_PENDING_DELETION, "[]");

    Mockito.when(
        featureManager.getBoolean(
            eq(WorkflowConstants.WORKFLOW_VARIABILITY_DOWNGRADE_FF), anyString()
        )).thenReturn(false);

    Mockito.when(
            processDetailsRepoService.findIncompleteProcesses(
                123L, workerActionRequest.getProcessInstanceId()))
        .thenReturn(Optional.of(new ArrayList<>()));

    Map<String, Object> response = processStatusCheckHandler.executeAction(workerActionRequest);
    Assert.assertNotNull(response);
    Assert.assertEquals(3, response.size());
    Assert.assertEquals(
        Boolean.TRUE,
        response.entrySet().stream()
            .filter(
                stringObjectEntry ->
                    stringObjectEntry.getKey().equalsIgnoreCase(PROCESS_STATUS_CLOSE))
            .map(t -> t.getValue())
            .findFirst()
            .get());
    Assert.assertTrue((Boolean) response.get(PROCESS_STATUS_CLOSE));
    Mockito.verify(processDetailsRepoService, Mockito.times(1))
        .findIncompleteProcesses(any(), any());
  }

  @Test
  public void testStillWaitingUseCaseSuccess_WithActiveSubscriptions() {
    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail, authorization, "123");
    DefinitionPendingDeletion definitionPendingDeletion =
        DefinitionPendingDeletion.buildFromDefinitionDetails(definitionDetail);

    List<ProcessDetails> processDetailsList =
        Collections.singletonList(
            ProcessDetails.builder()
                .ownerId(123L)
                .processId("pid1")
                .processStatus(ProcessStatus.ACTIVE)
                .build());
    String processInstanceID = "iId";

    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .ownerId(123L)
            .processDefinitionId("dId")
            .processInstanceId(processInstanceID)
            .inputVariables(schema)
            .build();

    workerActionRequest
        .getInputVariables()
        .put(
            WorkflowConstants.SUBSCRIPTIONS,
            "[{\"offeringId\": \"salsa.default\", \"offeringType\": \"QBO_PLUS\"}]");
    workerActionRequest
        .getInputVariables()
        .put(
            WorkflowConstants.DEFINITION_DETAILS_PENDING_DELETION,
            ObjectConverter.toJson(Arrays.asList(definitionPendingDeletion)));
    Mockito.when(
            processDetailsRepoService.findByDefinitionDetailsListAndProcessStatus(
                any(), any()))
        .thenReturn(Optional.of(processDetailsList));

    Mockito.when(
        featureManager.getBoolean(
            eq(WorkflowConstants.WORKFLOW_VARIABILITY_DOWNGRADE_FF), anyString()
        )).thenReturn(true);

    Map<String, Object> response = processStatusCheckHandler.executeAction(workerActionRequest);
    Assert.assertNotNull(response);
    Assert.assertEquals(3, response.size());
    Assert.assertFalse((Boolean) response.get(PROCESS_STATUS_CLOSE));
  }

  @Test
  public void testStillWaitingUseCaseSuccess_WithActiveSubscriptions_VariabilityFFDisabled() {
    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail, authorization, "123");
    DefinitionPendingDeletion definitionPendingDeletion =
        DefinitionPendingDeletion.buildFromDefinitionDetails(definitionDetail);

    List<ProcessDetails> processDetailsList =
        Collections.singletonList(
            ProcessDetails.builder()
                .ownerId(123L)
                .processId("pid1")
                .processStatus(ProcessStatus.ACTIVE)
                .build());
    String processInstanceID = "iId";

    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .ownerId(123L)
            .processDefinitionId("dId")
            .processInstanceId(processInstanceID)
            .inputVariables(schema)
            .build();

    workerActionRequest
        .getInputVariables()
        .put(
            WorkflowConstants.SUBSCRIPTIONS,
            "[{\"offeringId\": \"salsa.default\", \"offeringType\": \"QBO_PLUS\"}]");
    workerActionRequest
        .getInputVariables()
        .put(
            WorkflowConstants.DEFINITION_DETAILS_PENDING_DELETION,
            ObjectConverter.toJson(Arrays.asList(definitionPendingDeletion)));

    Mockito.when(
            processDetailsRepoService.findIncompleteProcesses(
                123L, workerActionRequest.getProcessInstanceId()))
        .thenReturn(Optional.of(processDetailsList));

    Mockito.when(
        featureManager.getBoolean(
            eq(WorkflowConstants.WORKFLOW_VARIABILITY_DOWNGRADE_FF), anyString()
        )).thenReturn(false);


    Map<String, Object> response = processStatusCheckHandler.executeAction(workerActionRequest);
    Assert.assertNotNull(response);
    Assert.assertEquals(3, response.size());
    Assert.assertFalse((Boolean) response.get(PROCESS_STATUS_CLOSE));

    Mockito.verify(processDetailsRepoService, Mockito.times(1))
        .findIncompleteProcesses(any(), any());
  }

  @Test
  public void testSuccess_withActiveSubscriptionsForOnDemandApproval_VariabilityFFEnabled(){
    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail, authorization, "123");
    DefinitionPendingDeletion definitionPendingDeletion =
        DefinitionPendingDeletion.buildFromDefinitionDetails(definitionDetail);
    DefinitionDetails onDemandApprovalDefinition = DefinitionDetails.builder()
        .definitionId("456")
        .recordType(null)
        .templateDetails(bpmnTemplateDetail)
        .workflowId("wkid")
        .definitionKey(APPROVAL.getTemplateName())
        .ownerId(Long.MIN_VALUE)
        .build();

    Mockito.when(onDemandHelper.fetchOnDemandApprovalDefinition())
        .thenReturn(List.of(onDemandApprovalDefinition));

    // Downgrade process
    ProcessDetails processDetails =
        ProcessDetails.builder()
            .ownerId(123L)
            .processId("iId")
            .processStatus(ProcessStatus.ACTIVE)
            .definitionDetails(definitionDetail)
            .build();

    // On demand approval process, ODA process
    ProcessDetails processDetails1 =
        ProcessDetails.builder()
            .ownerId(123L)
            .processId("pid")
            .processStatus(ProcessStatus.ACTIVE)
            .definitionDetails(onDemandApprovalDefinition)
            .build();

    // ODA process, different owner id
    ProcessDetails processDetails2 =
        ProcessDetails.builder()
            .ownerId(456L)
            .processId("pid1")
            .processStatus(ProcessStatus.ACTIVE)
            .definitionDetails(onDemandApprovalDefinition)
            .build();

    List<ProcessDetails> processDetailsList = new ArrayList<>();
    processDetailsList.add(processDetails);
    processDetailsList.add(processDetails1);
    processDetailsList.add(processDetails2);

    String processInstanceID = "iId";

    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .ownerId(123L)
            .processDefinitionId("dId")
            .processInstanceId(processInstanceID)
            .inputVariables(schema)
            .build();

    workerActionRequest
        .getInputVariables()
        .put(
            WorkflowConstants.SUBSCRIPTIONS,
            "[{\"offeringId\": \"Intuit.sbe.salsa.default\", \"offeringType\": \"QBO_ADVANCED\", \"status\": \"INACTIVE\"}, {\"offeringId\": \"Intuit.core.billpay\", \"offeringType\": \"BILLPAY_PREMIUM\", \"status\": \"INACTIVE\"}, {\"offeringId\": \"Intuit.midmarket.ies\", \"offeringType\": \"IES_STANDARD\", \"status\": \"ACTIVE\"}, {\"offeringId\": \"Intuit.midmarket.ies.me\", \"offeringType\": \"IESME_STANDARD\", \"status\": \"ACTIVE\"}, {\"offeringId\": \"Intuit.expert.client.bookkeeping.onetime\", \"offeringType\": \"QBLIVE_EXPERT_SERVICES\", \"status\": \"ACTIVE\"}]"
        );

    workerActionRequest
        .getInputVariables()
        .put(
            WorkflowConstants.DEFINITION_DETAILS_PENDING_DELETION,
            ObjectConverter.toJson(Arrays.asList(definitionPendingDeletion)));

    Mockito.when(
            processDetailsRepoService.findByDefinitionDetailsListAndProcessStatus(
                any(), any()))
        .thenReturn(Optional.of(processDetailsList));

    Mockito.when(
        featureManager.getBoolean(
            eq(WorkflowConstants.WORKFLOW_VARIABILITY_DOWNGRADE_FF), anyString()
        )).thenReturn(true);

    Map<String, Object> response = processStatusCheckHandler.executeAction(workerActionRequest);
    Assert.assertNotNull(response);
    Assert.assertEquals(3, response.size());
    Assert.assertFalse((Boolean) response.get(PROCESS_STATUS_CLOSE));
  }
}
