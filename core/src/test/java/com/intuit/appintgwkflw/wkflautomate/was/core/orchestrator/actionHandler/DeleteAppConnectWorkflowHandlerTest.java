package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants.DEF_ID;
import static org.mockito.ArgumentMatchers.any;

import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.AuthDetailsService;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.AppConnectService;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.AuthDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.response.AppConnectWorkflowResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import com.intuit.v4.Authorization;
import com.intuit.v4.workflows.Definition;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.camunda.bpm.engine.variable.VariableMap;
import org.camunda.bpm.engine.variable.impl.VariableMapImpl;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

/** <AUTHOR> */
public class DeleteAppConnectWorkflowHandlerTest {

  @InjectMocks private DeleteAppConnectWorkflowHandler deleteAppConnectWorkflowHandler;

  @Mock private AuthDetailsService authDetailsService;
  @Mock private AppConnectService appConnectService;
  @Mock private MetricLogger metricLogger;

  private Definition definition = TestHelper.mockDefinitionEntity();
  @Mock private TemplateDetails bpmnTemplateDetail;
  private Authorization authorization = TestHelper.mockAuthorization("123");
  private static Map<String, String> schema = new HashMap<>();
  private static final String parametersSchema =
      TestHelper.readResourceAsString("schema/testData/parameters.json");

  static {
    schema.put(WorkFlowVariables.PARAMETERS_KEY.getName(), parametersSchema);
    schema.put(WorkflowConstants.INTUIT_REALMID, "123");
  }

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
    ReflectionTestUtils.setField(deleteAppConnectWorkflowHandler, "metricLogger", metricLogger);
  }

  @Test
  public void testExecutionSkip()
    {

      VariableMap variableMap = new VariableMapImpl();
      List<Map<String, String>> workflowFilters= List.of(Map.of("recordType","invoice","workflowType","approval"));
      variableMap.put(WorkflowConstants.WORKFLOW_FILTER,workflowFilters);

      WorkerActionRequest workerActionRequest =
          WorkerActionRequest.builder()
              .activityId("aId")
              .ownerId(123L)
              .processDefinitionId("dId")
              .processInstanceId("iId")
              .inputVariables(schema)
              .variableMap(variableMap)
              .handlerId("hId")
              .build();
      Map<String, Object> resp = deleteAppConnectWorkflowHandler.executeAction(workerActionRequest);
      Mockito.verify(appConnectService, Mockito.times(0)).getAppConnectWorkflows(any(), any());

  }

  @Test
  public void testSuccessWithoutWorkflow() {

    VariableMap variableMap = new VariableMapImpl();
    List<Map<String, String>> workflowFilters= List.of(Map.of("recordType","invoice","workflowType","approval"));
    variableMap.put(WorkflowConstants.WORKFLOW_FILTER,workflowFilters);

    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .ownerId(123L)
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .variableMap(variableMap)
            .handlerId("hId")
            .build();
    definition.setId(TestHelper.getGlobalId(DEF_ID));

    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    DefinitionDetails definitionDetailNew =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();
    definitionDetailsList.add(definitionDetail);
    definitionDetailsList.add(definitionDetailNew);

    AuthDetails authDetails = AuthDetails.builder().subscriptionId("sub-id").ownerId(123L).build();
    Mockito.when(authDetailsService.getAuthDetailsFromRealmId("123")).thenReturn(authDetails);
    Mockito.when(appConnectService.getAppConnectWorkflows("123", authDetails))
        .thenReturn(Collections.emptyList());

    Map<String, Object> resp = deleteAppConnectWorkflowHandler.executeAction(workerActionRequest);
    Assert.assertTrue(
        resp.get(
                workerActionRequest.getActivityId()
                    + WorkflowConstants.UNDERSCORE
                    + WorkFlowVariables.RESPONSE.getName())
            .equals("true"));
  }

  @Test
  public void testSuccessWithAppConnectWorkflow() {
    VariableMap variableMap = new VariableMapImpl();
    List<Map<String, String>> workflowFilters= List.of(Map.of("recordType","invoice","workflowType","approval"));
    variableMap.put(WorkflowConstants.WORKFLOW_FILTER,workflowFilters);
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .ownerId(123L)
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .variableMap(variableMap)
            .handlerId("hId")
            .build();
    definition.setId(TestHelper.getGlobalId(DEF_ID));
    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    DefinitionDetails definitionDetailNew =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();
    definitionDetailsList.add(definitionDetail);
    definitionDetailsList.add(definitionDetailNew);

    AuthDetails authDetails = AuthDetails.builder().subscriptionId("sub-id").ownerId(123L).build();
    Mockito.when(authDetailsService.getAuthDetailsFromRealmId("123")).thenReturn(authDetails);

    //Active AppConnect Workflow Found
    AppConnectWorkflowResponse.Status status = new AppConnectWorkflowResponse.Status();
    status.setStatus(WorkflowConstants.ACTIVE_WORKFLOW);
    status.setWorkflowId("wid");
    AppConnectWorkflowResponse appConnectWorkflowResponse =
        AppConnectWorkflowResponse.builder()
            .workflowType(WorkflowConstants.NON_WAS_WORKFLOW_TYPE)
            .status(status)
            .build();
    Mockito.when(appConnectService.getAppConnectWorkflows("123", authDetails))
        .thenReturn(Collections.singletonList(appConnectWorkflowResponse));

    Map<String, Object> resp = deleteAppConnectWorkflowHandler.executeAction(workerActionRequest);
    Assert.assertTrue(
        resp.get(
                workerActionRequest.getActivityId()
                    + WorkflowConstants.UNDERSCORE
                    + WorkFlowVariables.RESPONSE.getName())
            .equals("true"));
  }

  @Test
  public void testSuccessWithoutAppConnectWorkflow() {
    VariableMap variableMap = new VariableMapImpl();
    List<Map<String, String>> workflowFilters= List.of(Map.of("recordType","invoice","workflowType","approval"));
    variableMap.put(WorkflowConstants.WORKFLOW_FILTER,workflowFilters);
    WorkerActionRequest workerActionRequest =
            WorkerActionRequest.builder()
                    .activityId("aId")
                    .ownerId(123L)
                    .processDefinitionId("dId")
                    .processInstanceId("iId")
                    .inputVariables(schema)
                .variableMap(variableMap)
                    .handlerId("hId")
                    .build();
    definition.setId(TestHelper.getGlobalId(DEF_ID));
    DefinitionDetails definitionDetail =
            TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    DefinitionDetails definitionDetailNew =
            TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();
    definitionDetailsList.add(definitionDetail);
    definitionDetailsList.add(definitionDetailNew);

    AuthDetails authDetails = AuthDetails.builder().subscriptionId("sub-id").ownerId(123L).build();
    Mockito.when(authDetailsService.getAuthDetailsFromRealmId("123")).thenReturn(authDetails);

    //Active AppConnect Workflow Found
    AppConnectWorkflowResponse.Status status = new AppConnectWorkflowResponse.Status();
    status.setStatus(WorkflowConstants.ACTIVE_WORKFLOW);
    status.setWorkflowId("wid");
    AppConnectWorkflowResponse appConnectWorkflowResponse =
            AppConnectWorkflowResponse.builder()
                    .workflowType("BPMN_WAS")
                    .status(status)
                    .build();
    Mockito.when(appConnectService.getAppConnectWorkflows("123", authDetails))
            .thenReturn(Collections.singletonList(appConnectWorkflowResponse));

    Map<String, Object> resp = deleteAppConnectWorkflowHandler.executeAction(workerActionRequest);
    Assert.assertTrue(
            resp.get(
                    workerActionRequest.getActivityId()
                            + WorkflowConstants.UNDERSCORE
                            + WorkFlowVariables.RESPONSE.getName())
                    .equals("true"));
  }

  @Test
  public void testGetName() {
    Assert.assertEquals(
        "was_executeDeleteActionNonWas",
        deleteAppConnectWorkflowHandler.getName().getTaskHandlerName());
  }

  @Test
  public void testLogErrorMetric() {
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .ownerId(123L)
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId("hId")
            .build();
    Mockito.when(authDetailsService.getAuthDetailsFromRealmId("123"))
        .thenThrow(new WorkflowGeneralException(WorkflowError.INVALID_INPUT));
    try {
      deleteAppConnectWorkflowHandler.execute(workerActionRequest);
      Assert.fail("Exception should be thrown");
    } catch (WorkflowGeneralException workflowGeneralException) {
      Assert.assertEquals(workflowGeneralException.getWorkflowError(), WorkflowError.INVALID_INPUT);
      Mockito.verify(metricLogger, Mockito.times(1)).logErrorMetric(any(), any(), any());
    }
  }
}
