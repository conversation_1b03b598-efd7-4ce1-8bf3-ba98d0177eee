package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.AppConnectConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.httpClient.AppConnectWASClient;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.AuthDetailsService;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.AuthDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.request.CreateWorkflowRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.request.RegisterTokenRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.response.AppConnectSaveWorkflowResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.response.AppConnectUnsubscribeResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.response.AppConnectWorkflowResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.response.CreateSubscriptionResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.response.GetSubscriptionResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import com.intuit.v4.Authorization;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.commons.utils.IoUtil;
import org.hamcrest.CoreMatchers;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpStatus;


public class AppConnectServiceImplTest {

  @Mock private AppConnectConfig appConnectConfig;

  @Mock private AppConnectWASClient wasHttpClient;

  @Mock private AuthDetailsService authDetailsService;
  @Mock private WASContextHandler wasContextHandler;

  @InjectMocks private AppConnectServiceImpl appConnectService;

  private static final String REALM_ID = "realmId";
  private static final String DEFINITION_ID = "defId";
  private static final String SUBSCRIPTION_ID = "subId";
  private static final String DEFINITION_NAME = "defName";

  private final BpmnModelInstance bpmnModelInstance =
      Bpmn.readModelFromStream(
          IoUtil.stringAsInputStream(
              TestHelper.readResourceAsString("bpmn/invoiceapprovalTest.bpmn")));

  @Before
  public void setUp() {
    MockitoAnnotations.initMocks(this);
    Mockito.when(appConnectConfig.getWorkflowCrudEndpoint())
        .thenReturn("http://example.com/subscriptions/{0}/workflows");
    Mockito.when(appConnectConfig.getSubscriptionEndpoint())
        .thenReturn("http://example.com/subscriptions");
    Mockito.when(appConnectConfig.getConnectorEndpoint())
        .thenReturn("http://example.com//appconnect/connector/");
    Mockito.when(appConnectConfig.getPollingFrequency()).thenReturn(86400l);
    Mockito.when(wasContextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn("test");
    Mockito.when(appConnectConfig.isEnableIdempotency()).thenReturn(true);
  }

  @Test
  public void testGetSubscriptionForApp() {
    GetSubscriptionResponse response = new GetSubscriptionResponse();
    String id = "123";
    response.setId(id);
    Mockito.when(wasHttpClient.httpResponse(any(WASHttpRequest.class)))
        .thenReturn(
            WASHttpResponse.builder()
                .status(HttpStatus.OK)
                .response(Collections.singletonList(response))
                .isSuccess2xx(true)
                .build());
    String getSubscriptionResponseId = appConnectService.getSubscriptionForApp(REALM_ID);
    Assert.assertNotNull(getSubscriptionResponseId);
    Assert.assertEquals(id, getSubscriptionResponseId);
    Mockito.verify(wasHttpClient).httpResponse(any(WASHttpRequest.class));
  }

  @Test
  public void testGetSubscriptionForAppFailAndCreateSubscription() {
    GetSubscriptionResponse response = new GetSubscriptionResponse();
    String id = "123";
    response.setId(id);
    Mockito.when(wasHttpClient.httpResponse(any(WASHttpRequest.class)))
        .thenReturn(
            WASHttpResponse.builder()
                .status(HttpStatus.OK)
                .response(Collections.singletonList(response))
                .isSuccess2xx(false)
                .build());
    String getSubscriptionResponseId = appConnectService.getSubscriptionForApp(REALM_ID);
    Assert.assertNull(getSubscriptionResponseId);
  }

  @Test
  public void testCreateSubscriptionForApp() {
    CreateSubscriptionResponse response = new CreateSubscriptionResponse();
    String id = "123";
    response.setId(id);
    Mockito.when(wasHttpClient.httpResponse(any(WASHttpRequest.class)))
        .thenReturn(
            WASHttpResponse.builder()
                .status(HttpStatus.OK)
                .response(response)
                .isSuccess2xx(true)
                .build());
    String createSubscriptionResponseId = appConnectService.createSubscriptionForApp(REALM_ID);
    Assert.assertNotNull(createSubscriptionResponseId);
    Assert.assertEquals(id, createSubscriptionResponseId);
  }

  @Rule
  public ExpectedException expectedException = ExpectedException.none();

  @Test
  public void testCreateSubscriptionForAppFailure() {

    expectedException.expect(WorkflowGeneralException.class);
    expectedException.expectMessage("Error in creating subscription details for realm=realmId. " +
        "Error=could not create subscription as it already exists.");
    CreateSubscriptionResponse response = new CreateSubscriptionResponse();
    String id = "123";
    response.setId(id);
    Mockito.when(wasHttpClient.httpResponse(any(WASHttpRequest.class)))
        .thenReturn(
            WASHttpResponse.builder()
                .status(HttpStatus.NOT_FOUND)
                .response(response)
                .isSuccess2xx(false)
                .error("could not create subscription as it already exists.")
                            .build());
    String createSubscriptionResponseId = appConnectService.createSubscriptionForApp(REALM_ID);
  }

  @Test
  public void testCreateWorkflow() {
    AppConnectSaveWorkflowResponse appConnectSaveWorkflowResponse =
        new AppConnectSaveWorkflowResponse();
    String id = "response-id";
    appConnectSaveWorkflowResponse.setId(id);
    Mockito.when(wasHttpClient.httpResponse(any(WASHttpRequest.class)))
        .thenReturn(
            WASHttpResponse.builder()
                .status(HttpStatus.OK)
                .response(appConnectSaveWorkflowResponse)
                .isSuccess2xx(true)
                .build());
    AppConnectSaveWorkflowResponse returnedResponse =
        appConnectService.createWorkflow(SUBSCRIPTION_ID, DEFINITION_ID, bpmnModelInstance, DEFINITION_NAME);
    Assert.assertNotNull(returnedResponse);
    Assert.assertEquals(id, returnedResponse.getId());
    ArgumentCaptor<WASHttpRequest<CreateWorkflowRequest, AppConnectSaveWorkflowResponse>> argumentCaptor = ArgumentCaptor.forClass(WASHttpRequest.class);
    Mockito.verify(wasHttpClient).httpResponse(argumentCaptor.capture());
    Assert.assertNotNull(argumentCaptor.getValue().getRequest().getStatus());
    Assert.assertEquals(
        TimeUnit.DAYS.toSeconds(1),
        argumentCaptor.getValue().getRequest().getStatus().getPollingStatus().getPollingFrequency());
  }

  @Test
  public void testCreateWorkflowFailure() {

    expectedException.expect(WorkflowGeneralException.class);
    expectedException.expectMessage(
        "Error in creating workflow for definition=defId. Error=create workflow failed");
    AppConnectSaveWorkflowResponse appConnectSaveWorkflowResponse =
        new AppConnectSaveWorkflowResponse();
    String id = "response-id";
    appConnectSaveWorkflowResponse.setId(id);
    Mockito.when(wasHttpClient.httpResponse(any(WASHttpRequest.class)))
        .thenReturn(
            WASHttpResponse.builder()
                .status(HttpStatus.OK)
                .response(appConnectSaveWorkflowResponse)
                .error("create workflow failed")
                            .isSuccess2xx(false)
                            .build());
    AppConnectSaveWorkflowResponse returnedResponse =
            appConnectService.createWorkflow(SUBSCRIPTION_ID, DEFINITION_ID, bpmnModelInstance, DEFINITION_NAME);
    ArgumentCaptor<WASHttpRequest<CreateWorkflowRequest, AppConnectSaveWorkflowResponse>> argumentCaptor = ArgumentCaptor.forClass(WASHttpRequest.class);
    Mockito.verify(wasHttpClient).httpResponse(argumentCaptor.capture());
  }

  @Test
  public void testActivateWorkflow() {
    String workflowId = "xyzx";
    AppConnectSaveWorkflowResponse appConnectSaveWorkflowResponse =
        new AppConnectSaveWorkflowResponse();
    String id = "response-id";
    appConnectSaveWorkflowResponse.setId(id);
    Mockito.when(wasHttpClient.httpResponse(any(WASHttpRequest.class)))
        .thenReturn(
            WASHttpResponse.builder()
                .status(HttpStatus.OK)
                .response(appConnectSaveWorkflowResponse)
                .isSuccess2xx(true)
                .build());
    appConnectService.activateDeactivateActionWorkflow(workflowId, SUBSCRIPTION_ID, true);
    Mockito.verify(wasHttpClient).httpResponse(any(WASHttpRequest.class));
  }

  @Test
  public void testActivateWorkflowFailure() {

    expectedException.expect(WorkflowGeneralException.class);
    expectedException.expectMessage("Error in activating/deactivating workflow for workflowId=xyzx. " +
        "Error=activate worflow error");
    String workflowId = "xyzx";
    AppConnectSaveWorkflowResponse appConnectSaveWorkflowResponse =
        new AppConnectSaveWorkflowResponse();
    String id = "response-id";
    appConnectSaveWorkflowResponse.setId(id);
    Mockito.when(wasHttpClient.httpResponse(any(WASHttpRequest.class)))
        .thenReturn(
            WASHttpResponse.builder()
                .status(HttpStatus.NOT_FOUND)
                .error("activate worflow error")
                            .response(appConnectSaveWorkflowResponse)
                            .isSuccess2xx(false)
                            .build());
    appConnectService.activateDeactivateActionWorkflow(workflowId, SUBSCRIPTION_ID, true);
  }

  @Test
  public void testDisableWorkflowWithOfflineTicket() {
    String workflowId = "xyzx";
    AppConnectSaveWorkflowResponse appConnectSaveWorkflowResponse =
        new AppConnectSaveWorkflowResponse();
    String id = "response-id";
    appConnectSaveWorkflowResponse.setId(id);
    Mockito.when(wasHttpClient.httpResponse(any(WASHttpRequest.class)))
        .thenReturn(
            WASHttpResponse.builder()
                .status(HttpStatus.OK)
                .response(appConnectSaveWorkflowResponse)
                .isSuccess2xx(true)
                .build());
    appConnectService.disableAppConnectWorkflow(
        workflowId, AuthDetails.builder().subscriptionId(SUBSCRIPTION_ID).build());
    Mockito.verify(wasHttpClient).httpResponse(any(WASHttpRequest.class));
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testActivateWorkflowFail() {
    String workflowId = "xyzx";
    AppConnectSaveWorkflowResponse appConnectSaveWorkflowResponse =
        new AppConnectSaveWorkflowResponse();
    String id = "response-id";
    appConnectSaveWorkflowResponse.setId(id);
    Mockito.when(wasHttpClient.httpResponse(any(WASHttpRequest.class)))
        .thenReturn(
            WASHttpResponse.builder()
                .status(HttpStatus.OK)
                .response(appConnectSaveWorkflowResponse)
                .isSuccess2xx(false)
                .build());
    appConnectService.activateDeactivateActionWorkflow(workflowId, SUBSCRIPTION_ID, true);
    Mockito.verify(wasHttpClient).httpResponse(any(WASHttpRequest.class));
  }

  @Test
  public void testUpdateWorkflow() {
    Mockito.when(wasHttpClient.httpResponse(any(WASHttpRequest.class)))
        .thenReturn(WASHttpResponse.builder().status(HttpStatus.OK).isSuccess2xx(true).build());
    appConnectService.updateWorkflow(
        "workflowId", SUBSCRIPTION_ID, DEFINITION_ID, bpmnModelInstance, DEFINITION_NAME);
    ArgumentCaptor<WASHttpRequest<CreateWorkflowRequest, AppConnectSaveWorkflowResponse>> argumentCaptor = ArgumentCaptor.forClass(WASHttpRequest.class);
    Mockito.verify(wasHttpClient).httpResponse(argumentCaptor.capture());
    Assert.assertNotNull(argumentCaptor.getValue().getRequest().getStatus());
    Assert.assertEquals(
        TimeUnit.DAYS.toSeconds(1),
        argumentCaptor.getValue().getRequest().getStatus().getPollingStatus().getPollingFrequency());
  }

  @Test
  public void testUpdateWorkflowFailure() {

    expectedException.expect(WorkflowGeneralException.class);
    expectedException
        .expectMessage("Error in Updating workflow for definition=defId. Error=update failure");
    Mockito.when(wasHttpClient.httpResponse(any(WASHttpRequest.class)))
        .thenReturn(WASHttpResponse.builder().status(HttpStatus.NOT_FOUND).isSuccess2xx(false).
            error("update failure").build());
    appConnectService.updateWorkflow(
            "workflowId", SUBSCRIPTION_ID, DEFINITION_ID, bpmnModelInstance, DEFINITION_NAME);
    ArgumentCaptor<WASHttpRequest<CreateWorkflowRequest, AppConnectSaveWorkflowResponse>> argumentCaptor = ArgumentCaptor.forClass(WASHttpRequest.class);
    Mockito.verify(wasHttpClient).httpResponse(argumentCaptor.capture());
  }

  @Test
  public void testRegisterToken() {
    Mockito.when(wasHttpClient.httpResponse(any(WASHttpRequest.class)))
        .thenReturn(WASHttpResponse.builder().status(HttpStatus.OK).isSuccess2xx(true).build());
    Mockito.when(appConnectConfig.getProviderAppId()).thenReturn("AP1234");
    appConnectService.registerToken("**********",
        "entityType", "entityOperation");
    ArgumentCaptor<WASHttpRequest<RegisterTokenRequest, Void>> argumentCaptor = ArgumentCaptor.forClass(
        WASHttpRequest.class);
    Mockito.verify(wasHttpClient).httpResponse(argumentCaptor.capture());
    Assert.assertTrue(argumentCaptor.getValue().getUrl().contains("entityOperations=entityOperation"));
  }

  @Test
  public void testRegisterToken_httpResponseFailure() {
    Mockito.when(wasHttpClient.httpResponse(any(WASHttpRequest.class)))
        .thenReturn(
            WASHttpResponse.builder().status(HttpStatus.INTERNAL_SERVER_ERROR).isSuccess2xx(false).
                error("register token fail").build());
    Mockito.when(appConnectConfig.getProviderAppId()).thenReturn("AP1234");
    Exception exception = assertThrows(WorkflowGeneralException.class, () -> {
      appConnectService.registerToken("**********",
          "entityType", "entityOperation");
    });
    String expectedMessage = "Error during registering token on app-connect";
    String actualMessage = exception.getMessage();
    Assert.assertThat(actualMessage, CoreMatchers.containsString(expectedMessage));
  }

  @Test
  public void testRegisterToken_nullEntityTypeFailure() {
    Mockito.when(wasHttpClient.httpResponse(any(WASHttpRequest.class)))
        .thenReturn(WASHttpResponse.builder().status(HttpStatus.OK).isSuccess2xx(true).build());
    Mockito.when(appConnectConfig.getProviderAppId()).thenReturn("AP1234");
    Exception exception = assertThrows(WorkflowGeneralException.class, () -> {
      appConnectService.registerToken("**********",
          null, "entityOperation");
    });
    String expectedMessage = "Input cannot be null or empty";
    String actualMessage = exception.getMessage();
    Assert.assertTrue(actualMessage.contains(expectedMessage));
  }

  @Test
  public void testRegisterToken_nullEntityOperationFailure() {
    Mockito.when(wasHttpClient.httpResponse(any(WASHttpRequest.class)))
        .thenReturn(WASHttpResponse.builder().status(HttpStatus.OK).isSuccess2xx(true).build());
    Mockito.when(appConnectConfig.getProviderAppId()).thenReturn("AP1234");
    Exception exception = assertThrows(WorkflowGeneralException.class, () -> {
      appConnectService.registerToken("**********",
          "entityType", null);
    });
    String expectedMessage = "Input cannot be null or empty";
    String actualMessage = exception.getMessage();
    Assert.assertTrue(actualMessage.contains(expectedMessage));
  }

  @SuppressWarnings("unchecked")
  @Test
  public void testUnsubscribe() {
    AppConnectUnsubscribeResponse response = new AppConnectUnsubscribeResponse();
    response.setStatusMessage("unsubscribed");
    response.setSuccess("true");
    Mockito.when(wasHttpClient.httpResponse(any(WASHttpRequest.class)))
        .thenReturn(
            WASHttpResponse.builder()
                .status(HttpStatus.OK)
                .isSuccess2xx(true)
                .response(response)
                .build());

    AppConnectUnsubscribeResponse resp =
        appConnectService.unsubscribe(
            AuthDetails.builder().subscriptionId(SUBSCRIPTION_ID).build(), false);
    Assert.assertNotNull(resp);
    Assert.assertEquals("true", resp.getSuccess());
  }

  @Test
  public void testUnsubscribeFailure() {
    expectedException.expect(WorkflowGeneralException.class);
    expectedException.expectMessage("Error in unsubscribing workflow for given company. Error=" +
        "Error in unsubscribing");
    AppConnectUnsubscribeResponse response = new AppConnectUnsubscribeResponse();
    response.setStatusMessage("unsubscribed");
    response.setSuccess("true");
    Mockito.when(wasHttpClient.httpResponse(any(WASHttpRequest.class)))
            .thenReturn(
                    WASHttpResponse.builder()
                            .status(HttpStatus.NOT_FOUND)
                            .isSuccess2xx(false)
                            .error("Error in unsubscribing")
                            .response(response)
                            .build());

    AppConnectUnsubscribeResponse resp =
            appConnectService.unsubscribe(
                    AuthDetails.builder().subscriptionId(SUBSCRIPTION_ID).build(), false);
    Assert.assertNotNull(resp);
    Assert.assertEquals("true", resp.getSuccess());
  }

  @SuppressWarnings("unchecked")
  @Test(expected = WorkflowGeneralException.class)
  public void testUnsubscribeException() {
    Mockito.when(wasHttpClient.httpResponse(any(WASHttpRequest.class)))
        .thenReturn(WASHttpResponse.builder().status(HttpStatus.OK).isSuccess2xx(false).build());

    appConnectService.unsubscribe(
        AuthDetails.builder().subscriptionId(SUBSCRIPTION_ID).build(), false);
  }

  @Test
  public void testDeleteWorkflow() {
    AppConnectSaveWorkflowResponse appConnectSaveWorkflowResponse =
        new AppConnectSaveWorkflowResponse();
    String id = "response-id";
    appConnectSaveWorkflowResponse.setId(id);
    Mockito.when(wasHttpClient.httpResponse(any(WASHttpRequest.class)))
        .thenReturn(
            WASHttpResponse.builder()
                .status(HttpStatus.OK)
                .response(appConnectSaveWorkflowResponse)
                .isSuccess2xx(true)
                .build());
    appConnectService.deleteWorkflow("wkid", SUBSCRIPTION_ID);
    Mockito.verify(wasHttpClient).httpResponse(any(WASHttpRequest.class));
  }

  @Test
  public void testDeleteWorkflowFailure() {

    expectedException.expect(WorkflowGeneralException.class);
    expectedException.expectMessage(
        "Error in Deleting workflow for workflowId=wkid. Error=delete workflow failed");
    AppConnectSaveWorkflowResponse appConnectSaveWorkflowResponse =
            new AppConnectSaveWorkflowResponse();
    String id = "response-id";
    appConnectSaveWorkflowResponse.setId(id);
    Mockito.when(wasHttpClient.httpResponse(any(WASHttpRequest.class)))
            .thenReturn(
                    WASHttpResponse.builder()
                            .status(HttpStatus.NOT_FOUND)
                            .response(appConnectSaveWorkflowResponse)
                            .error("delete workflow failed")
                            .isSuccess2xx(false)
                            .build());
    appConnectService.deleteWorkflow("wkid", SUBSCRIPTION_ID);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testDeleteWorkflowFail() {
    AppConnectSaveWorkflowResponse appConnectSaveWorkflowResponse =
        new AppConnectSaveWorkflowResponse();
    String id = "response-id";
    appConnectSaveWorkflowResponse.setId(id);
    Mockito.when(wasHttpClient.httpResponse(any(WASHttpRequest.class)))
        .thenReturn(
            WASHttpResponse.builder()
                .status(HttpStatus.OK)
                .response(appConnectSaveWorkflowResponse)
                .isSuccess2xx(false)
                .build());
    appConnectService.deleteWorkflow("wkid", SUBSCRIPTION_ID);
    Mockito.verify(wasHttpClient).httpResponse(any(WASHttpRequest.class));
  }

  @Test
  public void testDeleteWorkflowWithOfflineTicket() {
    AppConnectSaveWorkflowResponse appConnectSaveWorkflowResponse =
        new AppConnectSaveWorkflowResponse();
    String id = "response-id";
    appConnectSaveWorkflowResponse.setId(id);
    Mockito.when(wasHttpClient.httpResponse(any(WASHttpRequest.class)))
        .thenReturn(
            WASHttpResponse.builder()
                .status(HttpStatus.OK)
                .response(appConnectSaveWorkflowResponse)
                .isSuccess2xx(true)
                .build());
    appConnectService.deleteWorkflow("wkid", SUBSCRIPTION_ID, AuthDetails.builder().subscriptionId("123").build());
    Mockito.verify(wasHttpClient).httpResponse(any(WASHttpRequest.class));
  }

  @Test
  public void testGetWorkflowWithOfflineTicket() {
    List<AppConnectWorkflowResponse> appConnectSaveWorkflowResponse =
        Arrays.asList(AppConnectWorkflowResponse.builder().id("response-id").build());
    Mockito.when(wasHttpClient.httpResponse(any(WASHttpRequest.class)))
        .thenReturn(
            WASHttpResponse.builder()
                .status(HttpStatus.OK)
                .response(appConnectSaveWorkflowResponse)
                .isSuccess2xx(true)
                .build());
    List<AppConnectWorkflowResponse> response =
        appConnectService.getAppConnectWorkflows("123", AuthDetails.builder().build());
    Assert.assertNotNull(response);
  }

  @Test
  public void testUnsubscribeUserOffline() {
    AppConnectUnsubscribeResponse response = new AppConnectUnsubscribeResponse();
    response.setStatusMessage("unsubscribed");
    response.setSuccess("true");
    Mockito.when(authDetailsService.renewOfflineTicketAndUpdateDB(AuthDetails.builder().build()))
        .thenReturn(Authorization.INTUIT_AUTH_HEADER);
    Mockito.when(wasHttpClient.httpResponse(any(WASHttpRequest.class)))
        .thenReturn(
            WASHttpResponse.builder()
                .status(HttpStatus.OK)
                .isSuccess2xx(true)
                .response(response)
                .build());

    AppConnectUnsubscribeResponse resp =
        appConnectService.unsubscribe(
            AuthDetails.builder().subscriptionId(SUBSCRIPTION_ID).build(), true);
    Assert.assertNotNull(resp);
    Assert.assertEquals("true", resp.getSuccess());
  }

  @SuppressWarnings("unchecked")
  @Test(expected = WorkflowGeneralException.class)
  public void testUnsubscribeExceptionUserOffline() {
    Mockito.when(wasHttpClient.httpResponse(any(WASHttpRequest.class)))
        .thenReturn(WASHttpResponse.builder().status(HttpStatus.OK).isSuccess2xx(false).build());

    appConnectService.unsubscribe(
        AuthDetails.builder().subscriptionId(SUBSCRIPTION_ID).build(), true);
  }

  @Test
  public void testDeleteWorkflowsEmptyList() {
    List<String> workflowIds = new ArrayList<>();
    try {
      appConnectService.deleteWorkflows(workflowIds, AuthDetails.builder().subscriptionId(SUBSCRIPTION_ID).build());
    } catch (Exception e) {
      Assert.fail();
    }
  }

  @Test
  public void testDeleteWorkflowsNonEmptyList() {
    List<String> workflowIds = new ArrayList<>();
    workflowIds.add("123");
    AppConnectSaveWorkflowResponse appConnectSaveWorkflowResponse =
            new AppConnectSaveWorkflowResponse();
    String id = "response-id";
    appConnectSaveWorkflowResponse.setId(id);
    Mockito.when(wasHttpClient.httpResponse(any(WASHttpRequest.class)))
            .thenReturn(
                    WASHttpResponse.builder()
                            .status(HttpStatus.OK)
                            .response(appConnectSaveWorkflowResponse)
                            .isSuccess2xx(true)
                            .build());
    try {
      appConnectService.deleteWorkflows(workflowIds, AuthDetails.builder().subscriptionId(SUBSCRIPTION_ID).build());
    } catch (Exception e) {
      Assert.fail();
    }
  }

  @Test
  public void testCreateSubscriptionForAppWithIdempotencyKey() {
    Mockito.when(appConnectConfig.isEnableIdempotency()).thenReturn(false);
    CreateSubscriptionResponse response = new CreateSubscriptionResponse();
    String id = "123";
    response.setId(id);
    Mockito.when(wasHttpClient.httpResponse(any(WASHttpRequest.class)))
        .thenReturn(
            WASHttpResponse.builder()
                .status(HttpStatus.OK)
                .response(response)
                .isSuccess2xx(true)
                .build());
    String createSubscriptionResponseId = appConnectService.createSubscriptionForApp(REALM_ID);
    Assert.assertNotNull(createSubscriptionResponseId);
    Assert.assertEquals(id, createSubscriptionResponseId);
  }
}
