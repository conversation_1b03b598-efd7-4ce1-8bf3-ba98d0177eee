package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants.DEF_ID;
import static org.mockito.ArgumentMatchers.any;

import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.AuthDetailsService;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.AppConnectService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.UnsubscribeAppConnectTask;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.AuthDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.AuthDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import com.intuit.async.execution.request.State;
import com.intuit.v4.workflows.Definition;
import java.util.HashMap;
import java.util.Map;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

/** <AUTHOR> */
public class WASUnsubscribeHandlerTest {

  @InjectMocks WASUnsubscribeHandler WASUnsubscribeHandler;

  @Mock private AppConnectService appConnectService;
  @Mock private AuthDetailsService authDetailsService;
  @Mock private ProcessDetailsRepository processDetailsRepository;
  @Mock private DefinitionDetailsRepository definitionDetailsRepository;
  @Mock private AuthDetailsRepository authDetailsRepository;
  @Mock private UnsubscribeAppConnectTask unsubscribeAppConnectTask;
  @Mock private MetricLogger metricLogger;

  private Definition definition = TestHelper.mockDefinitionEntity();
  @Mock private TemplateDetails bpmnTemplateDetail;
  private static Map<String, String> schema = new HashMap<>();
  private static final String parametersSchema =
      TestHelper.readResourceAsString("schema/testData/parameters.json");

  static {
    schema.put(WorkFlowVariables.PARAMETERS_KEY.getName(), parametersSchema);
    schema.put(WorkflowConstants.INTUIT_REALMID, "123");
  }

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
    ReflectionTestUtils.setField(WASUnsubscribeHandler, "metricLogger", metricLogger);
  }

  @Test
  public void testSuccess() {
    final State state = new State();
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, "123");
    state.addValue(AsyncTaskConstants.IS_OFFLINE_KEY, true);
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .ownerId(123L)
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId("hId")
            .build();
    definition.setId(TestHelper.getGlobalId(DEF_ID));
    Mockito.when(authDetailsService.getAuthDetailsFromRealmId("123"))
        .thenReturn(AuthDetails.builder().subscriptionId("sub_id").build());

    Assert.assertNotNull(WASUnsubscribeHandler.executeAction(workerActionRequest));
  }

  /**
   * Testing AppConnect Unsubscribe Idempotence here. No Error if workflow is already unsubscribed
   */
  @Test
  public void testIdempotenceBehaviourSuccess() {
    AuthDetails authDetails = AuthDetails.builder().subscriptionId("sub_id").build();
    final State state = new State();
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, "123");
    state.addValue(AsyncTaskConstants.IS_OFFLINE_KEY, true);
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .ownerId(123L)
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId("hId")
            .build();
    definition.setId(TestHelper.getGlobalId(DEF_ID));
    Mockito.when(authDetailsService.getAuthDetailsFromRealmId("123")).thenReturn(authDetails);
    Mockito.when(appConnectService.unsubscribe(authDetails, true))
        .thenThrow(
            new WorkflowGeneralException(
                WorkflowError.UNSUBSCRIBE_WORKFLOW_FAIL,
                "{\"errors\":[{\"details\":\" RESOURCE_NOT_FOUND\"}]}"));
    Mockito.doThrow(WorkflowGeneralException.class).when(unsubscribeAppConnectTask).execute(state);

    Map<String, Object> resp = WASUnsubscribeHandler.executeAction(workerActionRequest);
    Assert.assertEquals(
        true,
        resp.get(
            workerActionRequest.getActivityId()
                + WorkflowConstants.UNDERSCORE
                + WorkFlowVariables.RESPONSE.getName()));
  }

  @Test
  public void testExceptionScenario() {
    final State state = new State();
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, 123L);
    state.addValue(AsyncTaskConstants.IS_OFFLINE_KEY, true);
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .ownerId(123L)
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId("hId")
            .build();
    definition.setId(TestHelper.getGlobalId(DEF_ID));
    Mockito.when(authDetailsService.getAuthDetailsFromRealmId("123"))
        .thenReturn(AuthDetails.builder().subscriptionId("sub_id").build());

    Mockito.when(unsubscribeAppConnectTask.execute(state))
        .thenThrow(WorkflowGeneralException.class);

    try {
      WASUnsubscribeHandler.executeAction(workerActionRequest);
    } catch (Exception ex) {
      Assert.fail();
    }
  }

  @Test
  public void testGetName() {
    Assert.assertEquals(
        "was_unsubscribeAuthDelete", WASUnsubscribeHandler.getName().getTaskHandlerName());
  }

  @Test
  public void testLogMetric() {
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .ownerId(123L)
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId("hId")
            .build();
    definition.setId(TestHelper.getGlobalId(DEF_ID));
    Mockito.when(authDetailsService.getAuthDetailsFromRealmId("123"))
        .thenThrow(WorkflowGeneralException.class);
    try {
      WASUnsubscribeHandler.execute(workerActionRequest);
      Assert.fail("Exception should be thrown");
    } catch (WorkflowGeneralException workflowGeneralException) {
      Mockito.verify(metricLogger, Mockito.times(1)).logErrorMetric(any(), any(), any());
    }
  }
}
