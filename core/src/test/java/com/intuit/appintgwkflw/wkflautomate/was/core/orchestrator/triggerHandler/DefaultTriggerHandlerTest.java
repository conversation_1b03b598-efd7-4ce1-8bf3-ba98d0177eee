package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ProcessStatus.ACTIVE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ProcessStatus.ERROR;
import static java.util.Arrays.asList;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.TemplateService;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ProcessStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.ResponseStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.TriggerStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowGenericResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowTriggerResponse;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter.TransactionEntity;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.NoSuchElementException;
import java.util.Optional;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DefaultTriggerHandlerTest {

  /** */
  private static final String UPDATED_EVENT = "updated";
  /** */
  private static final String DELETED_EVENT_TYPE = "deleted";

  private static final String authHeader =
      "Intuit_IAM_Authentication intuit_appid=Intuit.appintgwkflw.wkflautomate.workflowappconnect,intuit_app_secret=xxxx,intuit_token=xxxx,intuit_userid=9130347715753436,intuit_token_type=IAM-Ticket,intuit_realmid=9130347798120106";
  private static final String REALM_ID = "9130347798120106";
  private static final String TEMPLATE_NAME = "invoiceapproval";

  @Mock private ProcessDetailsRepository processDetailsRepository;

  @Mock private TemplateService templateService;

  @Mock private V3RunTimeHelper runtimeHelper;

  @Mock private V3SignalProcess v3SignalProcess;

  @Mock private V3StartProcess v3StartProcess;
  @Mock private WASContextHandler contextHandler;

  @Mock private BaseTriggerHandler baseTriggerHandler;

  @InjectMocks private DefaultTriggerHandler triggerHandler;

  private TemplateDetails templateDetails;

  private List<DefinitionDetails> definitionDetailsList;

  @Before
  public void prepareMockData() {
    MockitoAnnotations.initMocks(this);
    definitionDetailsList = new ArrayList<>();
    templateDetails = TriggerHandlerTestData.getBPMNTemplateDetails();
    definitionDetailsList.add(TriggerHandlerTestData.getDefinitionDetails(templateDetails));
    Mockito.doReturn(definitionDetailsList)
        .when(runtimeHelper)
        .getEligibleDefinitions(any(), anyBoolean());
    Mockito.doReturn(authHeader).when(contextHandler).get(WASContextEnums.AUTHORIZATION_HEADER);
    Mockito.doReturn(TriggerHandlerTestData.getDefaultTriggerResponseBuilder())
        .when(runtimeHelper)
        .getDefaultResponseBuilder(any());
  }

  @Test
  public void executeTriggerStartProcessTest() {
    Mockito.doReturn(TriggerHandlerTestData.getStartProcessResult())
        .when(v3StartProcess)
        .startProcess(any(), any(), any());
    Mockito.doCallRealMethod().when(runtimeHelper).getTriggerResponse(any(), any(), any());
    Mockito.when(runtimeHelper.fetchInitialStartEventActivityDetail(ArgumentMatchers.any(TransactionEntity.class)))
            .thenReturn(TestHelper.getTestStartEventActivityDetail());
    WorkflowGenericResponse startProcessResponse =
        triggerHandler.executeTrigger(
            TriggerHandlerTestData.prepareV3TriggerMessage(UPDATED_EVENT));
    Assert.assertEquals(ResponseStatus.SUCCESS, startProcessResponse.getStatus());
    WorkflowTriggerResponse triggerResp =
        (WorkflowTriggerResponse) startProcessResponse.getResponse();
    Assert.assertEquals(TriggerStatus.PROCESS_STARTED, triggerResp.getStatus());
    Mockito.verify(contextHandler, Mockito.atLeastOnce())
	.addKey(Mockito.eq(WASContextEnums.WORKFLOW), Mockito.anyString());
  }

  @Test
  public void executeTriggerStartProcessTestForBill() {
    definitionDetailsList.get(0).setRecordType(RecordType.BILL);
    Mockito.when(runtimeHelper.fetchInitialStartEventActivityDetail(ArgumentMatchers.any(TransactionEntity.class)))
            .thenReturn(TestHelper.getTestStartEventActivityDetail());
    Mockito.doReturn(TriggerHandlerTestData.getStartProcessResult())
        .when(v3StartProcess)
        .startProcess(any(), any(), any());
    Mockito.doCallRealMethod().when(runtimeHelper).getTriggerResponse(any(), any(), any());
    WorkflowGenericResponse startProcessResponse =
        triggerHandler.executeTrigger(
            TriggerHandlerTestData.prepareV3TriggerMessage("created", "bill"));
    Assert.assertEquals(ResponseStatus.SUCCESS, startProcessResponse.getStatus());
    WorkflowTriggerResponse triggerResp =
        (WorkflowTriggerResponse) startProcessResponse.getResponse();
    Assert.assertEquals(TriggerStatus.PROCESS_STARTED, triggerResp.getStatus());
    Mockito.verify(contextHandler, Mockito.atLeastOnce())
	.addKey(Mockito.eq(WASContextEnums.WORKFLOW), Mockito.anyString());
  }

  @Test
  public void executeTriggerSignalProcessTest() {
    Mockito.when(runtimeHelper.fetchInitialStartEventActivityDetail(ArgumentMatchers.any(TransactionEntity.class)))
            .thenReturn(TestHelper.getTestStartEventActivityDetail());
    TransactionEntity transactionEntity =
		TransactionEntityFactory.getInstanceOf(
				TriggerHandlerTestData.prepareV3TriggerMessage(DELETED_EVENT_TYPE).getTriggerMessage(),
				contextHandler);

    Optional<List<ProcessDetails>> processDetailsOptionalList =
        Optional.ofNullable(
            TriggerHandlerTestData.getProcessDetailsList(
                transactionEntity.getEntityId(), definitionDetailsList.stream().findFirst().get()));
    String entityId = transactionEntity.getEntityId();
    long realmId = Long.parseLong(REALM_ID);

    Mockito.when(
            processDetailsRepository
                .findByRecordIdAndOwnerIdAndProcessStatusInAndInternalStatusIsNullAndDefinitionDetailsInOrderByCreatedDateDesc(
                    entityId, realmId, Arrays.asList(ACTIVE, ERROR), definitionDetailsList))
        .thenReturn(processDetailsOptionalList);

    Mockito.doReturn(true).when(v3SignalProcess).signalProcess(any(), any(), any(), any());

    Mockito
        .doCallRealMethod()
        .when(runtimeHelper)
        .mergeTriggerResponse(
            Mockito.any(), Mockito.any()
        );

    Mockito
        .doCallRealMethod()
        .when(runtimeHelper)
        .getWorkflowTriggerResponse(
            Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()
        );

    Mockito
        .doCallRealMethod()
        .when(runtimeHelper)
        .getTriggerResponse(
            Mockito.any(), Mockito.any(), Mockito.any()
        );

    WorkflowGenericResponse triggerProcessResponse =
        triggerHandler.executeTrigger(
            TriggerHandlerTestData.prepareV3TriggerMessage(DELETED_EVENT_TYPE));
    Assert.assertEquals(ResponseStatus.SUCCESS, triggerProcessResponse.getStatus());
    WorkflowTriggerResponse triggerResp =
        (WorkflowTriggerResponse) triggerProcessResponse.getResponse();
    Assert.assertEquals(TriggerStatus.PROCESS_SIGNALLED, triggerResp.getStatus());
    Mockito.verify(contextHandler, Mockito.atLeastOnce())
	.addKey(Mockito.eq(WASContextEnums.WORKFLOW), Mockito.anyString());
  }

  @Test
  public void executeTriggerSignalErrorProcessProcessTest() {
    Mockito.when(runtimeHelper.fetchInitialStartEventActivityDetail(ArgumentMatchers.any(TransactionEntity.class)))
            .thenReturn(TestHelper.getTestStartEventActivityDetail());
    TransactionEntity transactionEntity =
		TransactionEntityFactory.getInstanceOf(
				TriggerHandlerTestData.prepareV3TriggerMessage(DELETED_EVENT_TYPE).getTriggerMessage(),
				contextHandler);

    List<ProcessDetails> processDetaisList =
        TriggerHandlerTestData.getProcessDetailsList(
            transactionEntity.getEntityId(), definitionDetailsList.stream().findFirst().get());

    processDetaisList.stream().findFirst().get().setProcessStatus(ProcessStatus.ERROR);

    // since it supports only single enabled definition so process details list will always be one
    Assert.assertEquals(1, processDetaisList.size());
    Optional<List<ProcessDetails>> processDetailsOptionalList =
        Optional.ofNullable(processDetaisList);

    String entityId = transactionEntity.getEntityId();
    long realmId = Long.parseLong(REALM_ID);

    Mockito.when(
            processDetailsRepository
                .findByRecordIdAndOwnerIdAndProcessStatusInAndInternalStatusIsNullAndDefinitionDetailsInOrderByCreatedDateDesc(
                    entityId, realmId, asList(ACTIVE, ERROR), definitionDetailsList))
        .thenReturn(processDetailsOptionalList);

    Mockito.doReturn(true).when(v3SignalProcess).signalProcess(any(), any(), any(), any());

    Mockito
        .doCallRealMethod()
        .when(runtimeHelper)
        .mergeTriggerResponse(
            Mockito.any(), Mockito.any()
        );

    Mockito
        .doCallRealMethod()
        .when(runtimeHelper)
        .getWorkflowTriggerResponse(
            Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()
        );

    Mockito
        .doCallRealMethod()
        .when(runtimeHelper)
        .getTriggerResponse(
            Mockito.any(), Mockito.any(), Mockito.any()
        );

    WorkflowGenericResponse triggerProcessResponse =
        triggerHandler.executeTrigger(
            TriggerHandlerTestData.prepareV3TriggerMessage(DELETED_EVENT_TYPE));
    Assert.assertEquals(ResponseStatus.SUCCESS, triggerProcessResponse.getStatus());
    WorkflowTriggerResponse triggerResp =
        (WorkflowTriggerResponse) triggerProcessResponse.getResponse();
    Assert.assertEquals(TriggerStatus.PROCESS_SIGNALLED, triggerResp.getStatus());
    Mockito.verify(contextHandler, Mockito.atLeastOnce())
	.addKey(Mockito.eq(WASContextEnums.WORKFLOW), Mockito.anyString());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void executeTriggerSignalExceptionTest() {
    Mockito.when(runtimeHelper.fetchInitialStartEventActivityDetail(ArgumentMatchers.any(TransactionEntity.class)))
            .thenReturn(TestHelper.getTestStartEventActivityDetail());
    TransactionEntity transactionEntity =
		TransactionEntityFactory.getInstanceOf(
				TriggerHandlerTestData.prepareV3TriggerMessage(DELETED_EVENT_TYPE).getTriggerMessage(),
				contextHandler);

    List<ProcessDetails> processDetailsList =
        TriggerHandlerTestData.getProcessDetailsList(
            transactionEntity.getEntityId(), definitionDetailsList.stream().findFirst().get());

    processDetailsList.stream().findFirst().get().setProcessStatus(ProcessStatus.ERROR);

    // since it supports only single enabled definition so process details list will always be one
    Assert.assertEquals(1, processDetailsList.size());
    Optional<List<ProcessDetails>> processDetailsOptionalList =
        Optional.ofNullable(processDetailsList);

    String entityId = transactionEntity.getEntityId();
    long realmId = Long.parseLong(REALM_ID);

    Mockito.when(
            processDetailsRepository
                .findByRecordIdAndOwnerIdAndProcessStatusInAndInternalStatusIsNullAndDefinitionDetailsInOrderByCreatedDateDesc(
                    entityId, realmId, asList(ACTIVE, ERROR), definitionDetailsList))
        .thenReturn(processDetailsOptionalList);

    Mockito.doThrow(new WorkflowGeneralException("Error"))
        .when(v3SignalProcess)
        .signalProcess(any(), any(), any(), any());

    try {
      triggerHandler.executeTrigger(
          TriggerHandlerTestData.prepareV3TriggerMessage(DELETED_EVENT_TYPE));
    } catch (Exception e) {
      Mockito.verify(contextHandler, Mockito.atLeastOnce())
  	.addKey(Mockito.eq(WASContextEnums.WORKFLOW), Mockito.anyString());
      throw e;
    }
  }

  @SuppressWarnings("unchecked")
  @Test(expected = WorkflowGeneralException.class)
  public void executeTriggerSignalExceptionProcessNotMarkedInErrorStateTest() {
    Mockito.when(runtimeHelper.fetchInitialStartEventActivityDetail(ArgumentMatchers.any(TransactionEntity.class)))
            .thenReturn(TestHelper.getTestStartEventActivityDetail());
    TriggerProcessDetails prepareV3TriggerMessage =
        TriggerHandlerTestData.prepareV3TriggerMessage(DELETED_EVENT_TYPE);
    ((Map<String, Object>) (prepareV3TriggerMessage.getTriggerMessage().get("eventHeaders")))
        .put("blockProcessOnSignalFailure", false);

    TransactionEntity transactionEntity =
		TransactionEntityFactory.getInstanceOf(prepareV3TriggerMessage.getTriggerMessage(),
			contextHandler);

    List<ProcessDetails> processDetailsList =
        TriggerHandlerTestData.getProcessDetailsList(
            transactionEntity.getEntityId(), definitionDetailsList.stream().findFirst().get());

    processDetailsList.stream().findFirst().get().setProcessStatus(ProcessStatus.ERROR);

    // since it supports only single enabled definition so process details list will always be one
    Assert.assertEquals(1, processDetailsList.size());
    Optional<List<ProcessDetails>> processDetailsOptionalList =
        Optional.ofNullable(processDetailsList);

    String entityId = transactionEntity.getEntityId();
    long realmId = Long.parseLong(REALM_ID);

    Mockito.when(
            processDetailsRepository
                .findByRecordIdAndOwnerIdAndProcessStatusInAndInternalStatusIsNullAndDefinitionDetailsInOrderByCreatedDateDesc(
                    entityId, realmId, asList(ACTIVE, ERROR), definitionDetailsList))
        .thenReturn(processDetailsOptionalList);

    Mockito.doThrow(new WorkflowGeneralException("Error"))
        .when(v3SignalProcess)
        .signalProcess(any(), any(), any(), any());

    try {
      triggerHandler.executeTrigger(
          prepareV3TriggerMessage);
    } catch (Exception e) {
      Mockito.verify(processDetailsRepository, Mockito.times(0))
          .updateProcessStatusForProcesses(Mockito.any(), Mockito.any());
      Mockito.verify(contextHandler, Mockito.atLeastOnce())
  	.addKey(Mockito.eq(WASContextEnums.WORKFLOW), Mockito.anyString());
      throw e;
    }
  }

  @Test
  public void executeTriggerSignalMultipleProcessDetail() {
    Mockito.when(runtimeHelper.fetchInitialStartEventActivityDetail(ArgumentMatchers.any(TransactionEntity.class)))
            .thenReturn(TestHelper.getTestStartEventActivityDetail());
    TransactionEntity transactionEntity =
		TransactionEntityFactory.getInstanceOf(
				TriggerHandlerTestData.prepareV3TriggerMessage(DELETED_EVENT_TYPE).getTriggerMessage(),
				contextHandler);

    List<ProcessDetails> processDetaisList =
        TriggerHandlerTestData.getProcessDetailsList(
            transactionEntity.getEntityId(), definitionDetailsList.stream().findFirst().get());

    processDetaisList.stream().findFirst().get().setProcessStatus(ProcessStatus.ERROR);
    ProcessDetails processDetails =
        ProcessDetails.builder()
            .ownerId(Long.parseLong("9130347798120106"))
            .definitionDetails(definitionDetailsList.stream().findFirst().get())
            .processId("6fc09080-5565-11ea-8ce7-9e4a956f003c")
            .processStatus(ProcessStatus.ACTIVE)
            .recordId(transactionEntity.getEntityId())
            .build();
    processDetaisList.add(processDetails);

    // since it supports only single enabled definition so process details list will always be one
    Assert.assertEquals(2, processDetaisList.size());
    Optional<List<ProcessDetails>> processDetailsOptionalList =
        Optional.ofNullable(processDetaisList);

    String entityId = transactionEntity.getEntityId();
    long realmId = Long.parseLong(REALM_ID);
    Mockito.when(
            processDetailsRepository
                .findByRecordIdAndOwnerIdAndProcessStatusInAndInternalStatusIsNullAndDefinitionDetailsInOrderByCreatedDateDesc(
                    entityId, realmId, asList(ACTIVE, ERROR), definitionDetailsList))
        .thenReturn(processDetailsOptionalList);

    WorkflowGenericResponse triggerProcessResponse =
        triggerHandler.executeTrigger(
            TriggerHandlerTestData.prepareV3TriggerMessage(DELETED_EVENT_TYPE));
    Assert.assertEquals(ResponseStatus.FAILURE, triggerProcessResponse.getStatus());
    WorkflowTriggerResponse triggerResp =
        (WorkflowTriggerResponse) triggerProcessResponse.getResponse();
    Assert.assertNull(triggerResp.getStatus());
    Mockito.verify(contextHandler, Mockito.atLeastOnce())
	.addKey(Mockito.eq(WASContextEnums.WORKFLOW), Mockito.anyString());
  }

  @Test
  public void executeTriggerStartProcessNoActionTriggerStatusTest() {
    Mockito.doReturn(new HashMap<String, Object>())
            .when(v3StartProcess)
            .startProcess(any(), any(), any());
    Mockito.when(runtimeHelper.fetchInitialStartEventActivityDetail(ArgumentMatchers.any(TransactionEntity.class)))
            .thenReturn(TestHelper.getTestStartEventActivityDetail());
    Mockito.doCallRealMethod().when(runtimeHelper).getTriggerResponse(any(), any(), any());

    WorkflowGenericResponse startProcessResponse =
            triggerHandler.executeTrigger(
                    TriggerHandlerTestData.prepareV3TriggerMessage(UPDATED_EVENT));
    Assert.assertEquals(ResponseStatus.SUCCESS, startProcessResponse.getStatus());
    WorkflowTriggerResponse triggerResp =
            (WorkflowTriggerResponse) startProcessResponse.getResponse();
    Assert.assertEquals(TriggerStatus.NO_ACTION, triggerResp.getStatus());
    Mockito.verify(contextHandler, Mockito.atLeastOnce())
	.addKey(Mockito.eq(WASContextEnums.WORKFLOW), Mockito.anyString());
  }

  @Test
  public void executeTriggerSignalProcessNoActionTriggerTest() {
    Mockito.when(runtimeHelper.fetchInitialStartEventActivityDetail(ArgumentMatchers.any(TransactionEntity.class)))
            .thenReturn(TestHelper.getTestStartEventActivityDetail());
    TransactionEntity transactionEntity =
		TransactionEntityFactory.getInstanceOf(
				TriggerHandlerTestData.prepareV3TriggerMessage(DELETED_EVENT_TYPE).getTriggerMessage(),
				contextHandler);

    Optional<List<ProcessDetails>> processDetailsOptionalList =
            Optional.ofNullable(
                    TriggerHandlerTestData.getProcessDetailsList(
                            transactionEntity.getEntityId(), definitionDetailsList.stream().findFirst().get()));
    String entityId = transactionEntity.getEntityId();
    long realmId = Long.parseLong(REALM_ID);

    Mockito.when(
            processDetailsRepository
                    .findByRecordIdAndOwnerIdAndProcessStatusInAndInternalStatusIsNullAndDefinitionDetailsInOrderByCreatedDateDesc(
                            entityId, realmId, Arrays.asList(ACTIVE, ERROR), definitionDetailsList))
            .thenReturn(processDetailsOptionalList);

    Mockito.doReturn(false).when(v3SignalProcess).signalProcess(any(), any(), any(), any());

    Mockito
        .doCallRealMethod()
        .when(runtimeHelper)
        .mergeTriggerResponse(
            Mockito.any(), Mockito.any()
        );

    Mockito
        .doCallRealMethod()
        .when(runtimeHelper)
        .getWorkflowTriggerResponse(
            Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()
        );

    Mockito
        .doCallRealMethod()
        .when(runtimeHelper)
        .getTriggerResponse(
            Mockito.any(), Mockito.any(), Mockito.any()
        );

    WorkflowGenericResponse triggerProcessResponse =
            triggerHandler.executeTrigger(
                    TriggerHandlerTestData.prepareV3TriggerMessage(DELETED_EVENT_TYPE));
    Assert.assertEquals(ResponseStatus.SUCCESS, triggerProcessResponse.getStatus());
    WorkflowTriggerResponse triggerResp =
            (WorkflowTriggerResponse) triggerProcessResponse.getResponse();
    Assert.assertEquals(TriggerStatus.NO_ACTION, triggerResp.getStatus());
    Mockito.verify(contextHandler, Mockito.atLeastOnce())
	.addKey(Mockito.eq(WASContextEnums.WORKFLOW), Mockito.anyString());
  }

  @Test
  public void checkBusinessKeyPresent(){
    TriggerProcessDetails triggerProcessDetails =
        TriggerHandlerTestData.prepareV3TriggerMessage("created");
    ((Map<String, Object>)triggerProcessDetails.getTriggerMessage().get("eventHeaders")).put("businessKey", 1234);
    Exception exception = Assertions.assertThrows(WorkflowGeneralException.class,
        () -> triggerHandler.executeTrigger(triggerProcessDetails));
    Assert.assertEquals(WorkflowError.BUSINESS_KEY_NOT_ALLOWED.getErrorMessage(), exception.getMessage());
  }

  @Test
  public void testProcessBpmnParsing_SkipParsing_Failure() {

    TransactionEntity transactionEntity =
            TransactionEntityFactory.getInstanceOf(TriggerHandlerTestData.prepareV3TriggerMessage("created").getTriggerMessage(),
                    contextHandler);
    List<DefinitionDetails> definitionDetailsList1 = new ArrayList<>();
    definitionDetailsList1.add(TriggerHandlerTestData.getDefinitionDetails(templateDetails));
    transactionEntity.getEventHeaders().setEntityChangeType("update");
    definitionDetailsList1.get(0).setPlaceholderValue("{\n" +
            "\t\"user_variables\": {\n" +
            "\t\t\"startEvent\": {\n" +
            "\t\t\t\"selected\": true,\n" +
            "\t\t\t\"parameters\": {\n" +
            "\t\t\t\t\"entityOperation\": {\n" +
            "\t\t\t\t\t\"fieldValue\": [\"created\"]\n" +
            "\t\t\t\t}\n" +
            "\t\t\t}\n" +
            "\t\t}\n" +
            "\t}\n" +
            "}");
    Mockito.when(runtimeHelper.getEligibleDefinitions(ArgumentMatchers.any(TransactionEntity.class), ArgumentMatchers.anyBoolean())).thenReturn(definitionDetailsList1);
    Mockito.when(runtimeHelper.fetchInitialStartEventActivityDetail(ArgumentMatchers.any(TransactionEntity.class)))
          .thenReturn(TestHelper.getTestStartEventActivityDetail());

    // Here exception is expected bcoz the expected definiton is not found and we cannot proceed further without definition
    Exception exception = Assert.assertThrows(RuntimeException.class, () ->
            triggerHandler.executeTrigger(
                    TriggerHandlerTestData.prepareV3TriggerMessage(UPDATED_EVENT)));
    Assert.assertEquals(exception.getClass(), NoSuchElementException.class);
    Mockito.verify(contextHandler, Mockito.atLeastOnce())
            .addKey(Mockito.eq(WASContextEnums.WORKFLOW), Mockito.anyString());

    Mockito.verify(runtimeHelper, Mockito.times(1)).fetchInitialStartEventActivityDetail(ArgumentMatchers.any(TransactionEntity.class));
  }

  @Test
  public void testProcessBpmnParsing_SkipParsing() {
    Mockito.doReturn(TriggerHandlerTestData.getStartProcessResult())
            .when(v3StartProcess)
            .startProcess(any(), any(), any());
    Mockito.doCallRealMethod().when(runtimeHelper).getTriggerResponse(any(), any(), any());

    ActivityDetail activityDetail = TestHelper.getTestStartEventActivityDetail();

    Mockito.when(runtimeHelper.fetchInitialStartEventActivityDetail(ArgumentMatchers.any(TransactionEntity.class))).thenReturn(activityDetail);

    WorkflowGenericResponse startProcessResponse =
            triggerHandler.executeTrigger(
                    TriggerHandlerTestData.prepareV3TriggerMessage(UPDATED_EVENT));
    Assert.assertEquals(ResponseStatus.SUCCESS, startProcessResponse.getStatus());
    WorkflowTriggerResponse triggerResp =
            (WorkflowTriggerResponse) startProcessResponse.getResponse();
    Assert.assertEquals(TriggerStatus.PROCESS_STARTED, triggerResp.getStatus());
    Mockito.verify(contextHandler, Mockito.atLeastOnce())
            .addKey(Mockito.eq(WASContextEnums.WORKFLOW), Mockito.anyString());
    Mockito.verify(runtimeHelper, Mockito.times(1)).fetchInitialStartEventActivityDetail(ArgumentMatchers.any(TransactionEntity.class));
  }

  @Test
  public void getNameTest(){
    Assert.assertEquals("DefaultTriggerHandler",triggerHandler.getName());
  }
}
