package com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qbo.oinp.tasks;

import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qbo.oinp.constants.OinpBridgeConstants;
import com.intuit.async.execution.impl.RxExecutionChain;
import com.intuit.async.execution.request.State;
import java.util.HashMap;
import java.util.Map;
import org.junit.Assert;
import org.junit.Test;

public class ReplacePlaceholdersTaskTest {

  @Test
  public void replacePlaceHolderTest(){

    Map<String, String> input = new HashMap<>();
    input.put("TxnId", "1001");
    input.put("NotificationAction", "qb001://open/invoice/?id=[[TxnId]]&companyid=[[RealmId]]");
    input.put("RealmId", "1234567");

    State state = new State();
    state.addValue(OinpBridgeConstants.BRIDGE_INPUTS_MAP, input);

    state = new RxExecutionChain(state).next(new ReplacePlaceholdersTask()).execute();
    Assert.assertNotNull(state.getValue(OinpBridgeConstants.BRIDGE_INPUTS_MAP));
    Assert.assertNotNull(state.getValue(OinpBridgeConstants.BRIDGE_OUTPUT_MAP));
    Assert.assertEquals("qb001://open/invoice/?id=1001&companyid=1234567",
        ((Map)state.getValue(OinpBridgeConstants.BRIDGE_OUTPUT_MAP)).get("NotificationAction"));
  }

}
