package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables.MULTI_LEVEL;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.INTUIT_REALMID;

import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails.ParameterDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

/**
 * <AUTHOR>
 */
public class FieldValueFilterStrategyTest {

  @InjectMocks FieldValueFilterStrategy fieldValueFilterStrategy;

  private static Map<String, String> schema = new HashMap<>();

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
  }

  private static final String parametersSchema =
      TestHelper.readResourceAsString("schema/testData/parameters.json");

  static {
    schema.put(WorkFlowVariables.PARAMETERS_KEY.getName(), parametersSchema);
    schema.put(INTUIT_REALMID, "1234");
  }

  @Test
  public void testFieldValueFromParameterDetailFetching() {

    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("sendRejectNotification_txnApproval")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .ownerId(Long.valueOf("9999"))
            .inputVariables(schema)
            .handlerId("handlerId")
            .build();

    ParameterDetails parameterDetails = new ParameterDetails();
    parameterDetails.setFieldValue(List.of("value1"));
    parameterDetails.setHandlerFieldName("fieldName");

    String fieldValueAfterExtraction = fieldValueFilterStrategy.fetchFieldValueFromParameterDetail(workerActionRequest, Map.entry("key", parameterDetails));

    Assert.assertEquals(fieldValueAfterExtraction, "value1");
  }

  @Test
  public void testMultipleFieldValueFromParameterDetailFetching() {

    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("sendRejectNotification_txnApproval")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .ownerId(Long.valueOf("9999"))
            .inputVariables(schema)
            .handlerId("handlerId")
            .build();

    ParameterDetails parameterDetails = new ParameterDetails();
    parameterDetails.setFieldValue(List.of("value1", "value2"));
    parameterDetails.setHandlerFieldName("fieldName");

    String fieldValueAfterExtraction = fieldValueFilterStrategy.fetchFieldValueFromParameterDetail(workerActionRequest, Map.entry("key", parameterDetails));

    Assert.assertEquals(fieldValueAfterExtraction, "value1,value2");
  }

  @Test
  public void testMultiLevelStrategyFromProcessVariableFetching() {

    WorkerActionRequest workerActionRequest =
            WorkerActionRequest.builder()
                    .activityId("sendRejectNotification_txnApproval")
                    .processDefinitionId("dId")
                    .processInstanceId("iId")
                    .ownerId(Long.valueOf("9999"))
                    .inputVariables(Map.of("key", "value"))
                    .handlerId("handlerId")
                    .extensionProperties(Map.of(MULTI_LEVEL.getName(), "true"))
                    .build();

    ParameterDetails parameterDetails = new ParameterDetails();
    parameterDetails.setFieldValue(List.of("value1", "value2"));
    parameterDetails.setHandlerFieldName("fieldName");

    String fieldValueAfterExtraction = fieldValueFilterStrategy.fetchFieldValueFromParameterDetail(workerActionRequest, Map.entry("key", parameterDetails));

    Assert.assertEquals(fieldValueAfterExtraction, "value");
  }

  @Test
  public void testMultiLevelStrategyProcessVariableNotFoundFetching() {

    WorkerActionRequest workerActionRequest =
            WorkerActionRequest.builder()
                    .activityId("sendRejectNotification_txnApproval")
                    .processDefinitionId("dId")
                    .processInstanceId("iId")
                    .ownerId(Long.valueOf("9999"))
                    .inputVariables(schema)
                    .handlerId("handlerId")
                    .extensionProperties(Map.of(MULTI_LEVEL.getName(), "true"))
                    .build();

    ParameterDetails parameterDetails = new ParameterDetails();
    parameterDetails.setFieldValue(List.of("value1", "value2"));
    parameterDetails.setHandlerFieldName("fieldName");

    String fieldValueAfterExtraction = fieldValueFilterStrategy.fetchFieldValueFromParameterDetail(workerActionRequest, Map.entry("key", parameterDetails));

    Assert.assertEquals(fieldValueAfterExtraction, "value1,value2");
  }

  @Test
  public void testMultiLevelStrategySingleParameterFetching() {

    WorkerActionRequest workerActionRequest =
            WorkerActionRequest.builder()
                    .activityId("sendRejectNotification_txnApproval")
                    .processDefinitionId("dId")
                    .processInstanceId("iId")
                    .ownerId(Long.valueOf("9999"))
                    .inputVariables(Map.of("key", "value"))
                    .handlerId("handlerId")
                    .extensionProperties(Map.of(MULTI_LEVEL.getName(), "true"))
                    .build();

    ParameterDetails parameterDetails = new ParameterDetails();
    parameterDetails.setFieldValue(List.of("value1"));
    parameterDetails.setHandlerFieldName("fieldName");

    String fieldValueAfterExtraction = fieldValueFilterStrategy.fetchFieldValueFromParameterDetail(workerActionRequest, Map.entry("key", parameterDetails));

    Assert.assertEquals(fieldValueAfterExtraction, "value1");
  }
}
