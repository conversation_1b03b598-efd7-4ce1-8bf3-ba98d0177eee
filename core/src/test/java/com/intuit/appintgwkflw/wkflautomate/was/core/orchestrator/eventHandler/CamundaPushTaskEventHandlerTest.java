package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.eventHandler;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;
import com.google.common.base.Charsets;
import com.google.common.io.Resources;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.camunda.service.CamundaRunTimeServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.core.config.ExternalTaskConfiguration;
import com.intuit.appintgwkflw.wkflautomate.was.core.config.Worker;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler.PublishEventHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomConfigV2;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfigFactory;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.MigratedConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.OldCustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.taskcompletionhandler.TaskCompletionHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.taskcompletionhandler.TaskCompletionHandlers;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler.WorkflowTaskHandlers;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.impl.ProcessDetailsRepoService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.EventHeaderConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.TaskHandlerName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.externaltask.ExternalTaskCompleted;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.externaltask.ExternalTaskEvent;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.externaltask.ExternalTaskStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.ExtendExternalTask;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import com.intuit.appintgwkflw.wkflautomate.was.worker.HandlerExtractors.WorkerExecutorHelper;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.io.InputStream;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType.EXTERNALTASK;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class CamundaPushTaskEventHandlerTest {

    @Mock
    private CamundaRunTimeServiceRest camundaRest;

    @Mock
    private ExternalTaskConfiguration externalTaskConfiguration;

    @Mock
    private ExternalTaskEvent externalTaskEvent;

    @Mock private PublishEventHandler publishEventHandler;

    @Mock
    private ProcessDetailsRepoService processDetailsRepoService;
    @Spy
    CustomWorkflowConfig customWorkflowConfig = loadCustomConfig();
    @Mock
    private TaskCompletionHandler taskCompletionHandler;

    @InjectMocks
    private CamundaPushTaskEventHandler handler;
    private Map<String, String> headers;

    private static final Map<String, Object> schema = new HashMap<>();
    public static String DICTIONARY_PATH = "config.yaml";
    public static String YAML_KEY = "templateConfig";

    public static String readResourceAsString(String path) {
        try (InputStream stream = Resources.getResource(path).openStream()) {
            return IOUtils.toString(stream, Charsets.UTF_8);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static CustomWorkflowConfig loadCustomConfig() {

        try {
            OldCustomWorkflowConfig oldCustomWorkflowConfig =
                    new ObjectMapper(new YAMLFactory())
                            .readValue(
                                    readResourceAsString(DICTIONARY_PATH),
                                    new TypeReference<Map<String, OldCustomWorkflowConfig>>() {})
                            .get(YAML_KEY);
            oldCustomWorkflowConfig.afterPropertiesSet();
            CustomConfigV2 customConfigV2 = new CustomConfigV2();
            CustomWorkflowConfig customWorkflowConfig = new CustomWorkflowConfig();
            customWorkflowConfig.setCustomWorkflowConfigFactory(
                    new CustomWorkflowConfigFactory(
                            oldCustomWorkflowConfig, customConfigV2, new MigratedConfig(Set.of(), Set.of())));
            customWorkflowConfig.setOldConfig(oldCustomWorkflowConfig);
            customWorkflowConfig.getCustomWorkflowConfigFactory().afterPropertiesSet();
            return customWorkflowConfig;
        } catch (Exception e) {
        }
        return null;
    }

    static {
        Map<String, String> parametersSchema = new HashMap<>();
        parametersSchema.put("taskHandler", "appconnect");
        parametersSchema.put("actionName", "executeWorkflowAction");
        schema.put(
                WorkFlowVariables.HANDLER_DETAILS_KEY.getName(), ObjectConverter.toJson(parametersSchema));
    }

    @BeforeEach
    public void setUp() {
        TaskCompletionHandlers.addHandler(EventEntityType.EXTERNALTASK, taskCompletionHandler);
        WorkflowTaskHandlers.addHandler(TaskHandlerName.WAS_PUBLISH_EVENT_HANDLER, publishEventHandler);
        externalTaskEvent = new ExternalTaskEvent();
        externalTaskEvent.setProcessInstanceId("processInstanceId");
        externalTaskEvent.setWorkerId("externalTask");
        externalTaskEvent.setTopicName("externalTask");
        externalTaskEvent.setId("taskId");
        externalTaskEvent.setVariables(Map.of("Id", "test",
                "taskId", "1234",
                "handlerDetails", "{\"taskHandler\":\"was\",\"handlerId\":\"intuit-workflows/vep-int-project-service\",\"actionName\":\"publishEvent\",\"handlerScope\":\"test\"}"));
        headers = new HashMap<>();
        headers.put(EventHeaderConstants.ENTITY_ID, "entityId");
    }

    @Test
    public void testTransform() {
        String event = "{\"processInstanceId\":\"processInstanceId\"}";
        ExternalTaskEvent result = handler.transform(event);
        assertNotNull(result);
        assertEquals("processInstanceId", result.getProcessInstanceId());
    }

    @Test
    public void testTransformAndValidate() {
        String event = "{\"processInstanceId\":\"processInstanceId\"}";
        ExternalTaskEvent result = handler.transformAndValidate(event, headers);
        assertNotNull(result);
        assertEquals("processInstanceId", result.getProcessInstanceId());
    }

    @Test
    public void testTransformAndValidate_MissingHeader() {
        headers.remove(EventHeaderConstants.ENTITY_ID);
        String event = "{\"processInstanceId\":\"processInstanceId\"}";
        Exception exception = assertThrows(Exception.class, () -> {
            handler.transformAndValidate(event, headers);
        });
        assertTrue(exception.getMessage().contains("Unable to parse or missing Mandatory field entity_id in event header"));
    }

    @Test
    public void testExecute() {
        //This Test will execute entire lifecycle of this execute method
        Worker worker = new Worker();
        worker.setTopicName("externalTask");
        when(externalTaskConfiguration.getWorkers()).thenReturn(Map.of("externalTask",worker ));
        handler.execute(externalTaskEvent, headers);
        verify(camundaRest, times(0)).extendLock(any(ExtendExternalTask.class), eq("taskId"));
    }

    @Test
    public void testExecute_Exception_eventPublisher() {
        handler.execute(externalTaskEvent, headers);
        verify(camundaRest, times(0)).extendLock(any(ExtendExternalTask.class), eq("taskId"));
    }

    @Test
    public void testExecute_Exception() {
        handler.execute(externalTaskEvent, headers);
        verify(camundaRest, times(0)).extendLock(any(ExtendExternalTask.class), eq("taskId"));
    }

    @Test
    public void testExtractHandlerDetails() {

        Map<String, String>  inputVariablesMap = Map.of("Id", "test",
                "taskId", "1234",
                "handlerDetails", "{\"taskHandler\":\"was\",\"handlerId\":\"intuit-workflows/vep-int-project-service\",\"actionName\":\"publishEvent\",\"handlerScope\":\"test\"}");

        HandlerDetails handlerDetails = handler.extractHandlerDetails(inputVariablesMap, externalTaskEvent);
        assertNotNull(handlerDetails);
    }

    @Test
    public void testGetName() {
        assertEquals(EventEntityType.EXTERNAL_TASK_PUSH, handler.getName());
    }

    @Test
    public void testHandleFailure() {
        headers.put("test", "test");
        handler.handleFailure("externalTask", headers, new Exception("Test Exception"));
        verify(TaskCompletionHandlers.getHandler(EXTERNALTASK), times(1)).invokeFailure(null, headers);
    }
}