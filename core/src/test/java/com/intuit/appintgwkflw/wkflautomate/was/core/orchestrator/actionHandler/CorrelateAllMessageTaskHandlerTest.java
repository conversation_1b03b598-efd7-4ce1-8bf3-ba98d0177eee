package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CustomWorkflowType.APPROVAL;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CustomWorkflowType.REMINDER;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;

import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureManager;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.bpmnengineadapter.BPMNEngineRunTimeServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.OnDemandHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.impl.ProcessDetailsRepoService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CorrelationKeysEnum;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ProcessStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.CorrelateAllMessage;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import com.intuit.appintgwkflw.wkflautomate.was.workflowvariability.DefinitionPendingDeletion;
import com.intuit.v4.Authorization;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

/** <AUTHOR> */
public class CorrelateAllMessageTaskHandlerTest {

  @InjectMocks private CorrelateAllMessageTaskHandler correlateAllMessageTaskHandler;

  @Mock private ProcessDetailsRepoService processDetailsRepoService;
  @Mock private BPMNEngineRunTimeServiceRest bpmnEngineRunTimeServiceRest;
  @Mock private TemplateDetails bpmnTemplateDetail;
  @Mock private DefinitionDetailsRepository definitionDetailsRepository;
  @Mock private MetricLogger metricLogger;
  @Mock private FeatureManager featureManager;
  @Mock private OnDemandHelper onDemandHelper;

  private Authorization authorization = TestHelper.mockAuthorization("123");
  private static Map<String, String> schema = new HashMap<>();
  private static final String parametersSchema =
      TestHelper.readResourceAsString("schema/testData/parameters.json");

  static {
    schema.put(WorkFlowVariables.PARAMETERS_KEY.getName(), parametersSchema);
    schema.put(WorkflowConstants.INTUIT_REALMID, "123");
  }

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
    ReflectionTestUtils.setField(correlateAllMessageTaskHandler, "metricLogger", metricLogger);
  }

  @Test
  public void testUpdateStatusSuccess() {
    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail, authorization, "123");
    definitionDetail.setOwnerId(123L);
    ProcessDetails processDetails =
        buildProcessDetails(ProcessStatus.ACTIVE, 123L, definitionDetail, "pid");
    ProcessDetails processDetails1 =
        buildProcessDetails(ProcessStatus.ACTIVE, 123L, definitionDetail, "pid1");
    ProcessDetails processDetailsError =
        buildProcessDetails(ProcessStatus.ERROR, 123L, definitionDetail, "pid2");
    List<ProcessDetails> processDetailsList = new ArrayList<>();
    processDetailsList.add(processDetails);
    processDetailsList.add(processDetails1);
    processDetailsList.add(processDetailsError);
    String processInstanceID = "iId";
    Mockito.when(processDetailsRepoService.findByRealmIdAndProcessStatus(123L, ProcessStatus.ERROR))
        .thenReturn(Optional.of(processDetailsList));
    WorkerActionRequest workerActionRequest =
        buildWorkerActionRequest("aId", "dId", processInstanceID, 123L);
    CorrelateAllMessage correlateAllMessage =
        new CorrelateAllMessage(
            "deleted_voided_disable", String.valueOf(workerActionRequest.getOwnerId()), null);

    Mockito.when(bpmnEngineRunTimeServiceRest.correlateAllMessage(correlateAllMessage))
        .thenReturn(true);

    Map<String, Object> response =
        correlateAllMessageTaskHandler.executeAction(workerActionRequest);
    Assert.assertNotNull(response);
    Assert.assertEquals(response.size(), 1);
    Assert.assertEquals(
        response.entrySet().stream().findFirst().get().getValue(), Boolean.TRUE.toString());
  }

  @Test
  public void testUpdateStatusSuccess_withActiveSubscriptions() {
    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail, authorization, "123");
    definitionDetail.setOwnerId(123L);
    ProcessDetails processDetails =
        buildProcessDetails(ProcessStatus.ACTIVE, 123L, definitionDetail, "pid");
    ProcessDetails processDetails1 =
        buildProcessDetails(ProcessStatus.ACTIVE, 123L, definitionDetail, "pid1");
    ProcessDetails processDetailsError =
        buildProcessDetails(ProcessStatus.ERROR, 123L, definitionDetail, "pid2");
    List<ProcessDetails> processDetailsList = new ArrayList<>();
    processDetailsList.add(processDetails);
    processDetailsList.add(processDetails1);
    processDetailsList.add(processDetailsError);
    Mockito.when(
            processDetailsRepoService.findByDefinitionDetailsListAndProcessStatus(any(), any()))
        .thenReturn(Optional.of(processDetailsList));
    String processInstanceID = "iId";
    WorkerActionRequest workerActionRequest =
        buildWorkerActionRequest("aId", "dId", processInstanceID, 123L);
    workerActionRequest
        .getInputVariables()
        .put(
            WorkflowConstants.SUBSCRIPTIONS,
            "[{\"offeringId\": \"QBO\", \"offeringType\": \"PLUS\"}]");
    DefinitionPendingDeletion definitionPendingDeletion =
        DefinitionPendingDeletion.buildFromDefinitionDetails(definitionDetail);
    workerActionRequest
        .getInputVariables()
        .put(
            WorkflowConstants.DEFINITION_DETAILS_PENDING_DELETION,
            ObjectConverter.toJson(Arrays.asList(definitionPendingDeletion)));

    Map<String, CorrelateAllMessage.CorrelateKey> correlateKeysMap = new HashMap<>();
    correlateKeysMap.put(
        CorrelationKeysEnum.DEFINITION_KEY.getName(),
        new CorrelateAllMessage.CorrelateKey(
            definitionDetail.getDefinitionKey(), CorrelationKeysEnum.DEFINITION_KEY.getDataType()));
    CorrelateAllMessage correlateAllMessage =
        new CorrelateAllMessage(
            "deleted_voided_disable",
            String.valueOf(workerActionRequest.getOwnerId()),
            correlateKeysMap);

    Mockito.when(bpmnEngineRunTimeServiceRest.correlateAllMessage(correlateAllMessage))
        .thenReturn(true);

    Mockito.when(
        featureManager.getBoolean(
            eq(WorkflowConstants.WORKFLOW_VARIABILITY_DOWNGRADE_FF), anyString()
        )).thenReturn(true);

    Map<String, Object> response =
        correlateAllMessageTaskHandler.executeAction(workerActionRequest);
    Mockito.verify(processDetailsRepoService, Mockito.never())
        .findByRealmIdAndProcessStatus(any(), any());
    Mockito.verify(processDetailsRepoService, Mockito.times(1))
        .findByDefinitionDetailsListAndProcessStatus(any(), any());
    Mockito.verify(processDetailsRepoService, Mockito.times(1)).saveOrUpdateProcess(any());
    Assert.assertNotNull(response);
    Assert.assertEquals(response.size(), 1);
    Assert.assertEquals(
        response.entrySet().stream().findFirst().get().getValue(), Boolean.TRUE.toString());
  }


  @Test
  public void testUpdateStatusSuccess_withActiveSubscriptions_VariabilityFFDisabled() {
    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail, authorization, "123");
    definitionDetail.setOwnerId(123L);
    ProcessDetails processDetails =
        buildProcessDetails(ProcessStatus.ACTIVE, 123L, definitionDetail, "pid");
    ProcessDetails processDetails1 =
        buildProcessDetails(ProcessStatus.ACTIVE, 123L, definitionDetail, "pid1");
    ProcessDetails processDetailsError =
        buildProcessDetails(ProcessStatus.ERROR, 123L, definitionDetail, "pid2");
    List<ProcessDetails> processDetailsList = new ArrayList<>();
    processDetailsList.add(processDetails);
    processDetailsList.add(processDetails1);
    processDetailsList.add(processDetailsError);
    Mockito.when(
            processDetailsRepoService.findByDefinitionDetailsListAndProcessStatus(any(), any()))
        .thenReturn(Optional.of(processDetailsList));
    String processInstanceID = "iId";
    WorkerActionRequest workerActionRequest =
        buildWorkerActionRequest("aId", "dId", processInstanceID, 123L);
    workerActionRequest
        .getInputVariables()
        .put(
            WorkflowConstants.SUBSCRIPTIONS,
            "[{\"offeringId\": \"QBO\", \"offeringType\": \"PLUS\"}]");
    DefinitionPendingDeletion definitionPendingDeletion =
        DefinitionPendingDeletion.buildFromDefinitionDetails(definitionDetail);
    workerActionRequest
        .getInputVariables()
        .put(
            WorkflowConstants.DEFINITION_DETAILS_PENDING_DELETION,
            ObjectConverter.toJson(Arrays.asList(definitionPendingDeletion)));

    Map<String, CorrelateAllMessage.CorrelateKey> correlateKeysMap = new HashMap<>();
    correlateKeysMap.put(
        CorrelationKeysEnum.DEFINITION_KEY.getName(),
        new CorrelateAllMessage.CorrelateKey(
            definitionDetail.getDefinitionKey(), CorrelationKeysEnum.DEFINITION_KEY.getDataType()));
    CorrelateAllMessage correlateAllMessage =
        new CorrelateAllMessage(
            "deleted_voided_disable",
            String.valueOf(workerActionRequest.getOwnerId()),
            correlateKeysMap);

    Mockito.when(bpmnEngineRunTimeServiceRest.correlateAllMessage(correlateAllMessage))
        .thenReturn(true);

    Mockito.when(
        featureManager.getBoolean(
            eq(WorkflowConstants.WORKFLOW_VARIABILITY_DOWNGRADE_FF), anyString()
        )).thenReturn(false);

    Map<String, Object> response =
        correlateAllMessageTaskHandler.executeAction(workerActionRequest);
    Assert.assertNotNull(response);
    Assert.assertEquals(response.size(), 1);
    Assert.assertEquals(
        response.entrySet().stream().findFirst().get().getValue(), Boolean.TRUE.toString());
  }

  @Test
  public void testUpdateStatusSuccess_withActiveSubscriptions_NoDefinitions() {

    String processInstanceID = "iId";
    WorkerActionRequest workerActionRequest =
        buildWorkerActionRequest("aId", "dId", processInstanceID, 123L);
    workerActionRequest
        .getInputVariables()
        .put(
            WorkflowConstants.SUBSCRIPTIONS,
            "[{\"offeringId\": \"QBO\", \"offeringType\": \"PLUS\"}]");

    workerActionRequest
        .getInputVariables()
        .put(
            WorkflowConstants.DEFINITION_DETAILS_PENDING_DELETION,
            ObjectConverter.toJson(Collections.emptyList()));

    Mockito.when(
        featureManager.getBoolean(
            eq(WorkflowConstants.WORKFLOW_VARIABILITY_DOWNGRADE_FF), anyString()
        )).thenReturn(true);

    Map<String, Object> response =
        correlateAllMessageTaskHandler.executeAction(workerActionRequest);
    Mockito.verify(processDetailsRepoService, Mockito.never())
        .findByRealmIdAndProcessStatus(any(), any());
    Mockito.verify(processDetailsRepoService, Mockito.never()).saveOrUpdateProcess(any());
    Mockito.verify(processDetailsRepoService, Mockito.never())
        .findByDefinitionDetailsListAndProcessStatus(any(), any());

    Assert.assertNotNull(response);
    Assert.assertEquals(response.size(), 1);
    Assert.assertEquals(
        response.entrySet().stream().findFirst().get().getValue(), Boolean.TRUE.toString());
  }

  @Test
  public void testUpdateStatusSuccess_withActiveSubscriptions_NoDefinitions_VariabilityFFDisabled() {

    String processInstanceID = "iId";
    WorkerActionRequest workerActionRequest =
        buildWorkerActionRequest("aId", "dId", processInstanceID, 123L);
    workerActionRequest
        .getInputVariables()
        .put(
            WorkflowConstants.SUBSCRIPTIONS,
            "[{\"offeringId\": \"QBO\", \"offeringType\": \"PLUS\"}]");

    workerActionRequest
        .getInputVariables()
        .put(
            WorkflowConstants.DEFINITION_DETAILS_PENDING_DELETION,
            ObjectConverter.toJson(Collections.emptyList()));

    Mockito.when(
        featureManager.getBoolean(
            eq(WorkflowConstants.WORKFLOW_VARIABILITY_DOWNGRADE_FF), anyString()
        )).thenReturn(false);

    Map<String, Object> response =
        correlateAllMessageTaskHandler.executeAction(workerActionRequest);
    Mockito.verify(processDetailsRepoService, Mockito.never()).saveOrUpdateProcess(any());
    Mockito.verify(processDetailsRepoService, Mockito.never())
        .findByDefinitionDetailsListAndProcessStatus(any(), any());

    Assert.assertNotNull(response);
    Assert.assertEquals(response.size(), 1);
    Assert.assertEquals(
        response.entrySet().stream().findFirst().get().getValue(), Boolean.TRUE.toString());
  }

  @Test
  public void testUpdateStatusSuccess_withActiveSubscriptions_NoProcesses() {
    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail, authorization, "123");
    definitionDetail.setOwnerId(123L);
    Mockito.when(
            processDetailsRepoService.findByDefinitionDetailsListAndProcessStatus(
                Arrays.asList(definitionDetail), ProcessStatus.ERROR))
        .thenReturn(Optional.empty());
    String processInstanceID = "iId";
    WorkerActionRequest workerActionRequest =
        buildWorkerActionRequest("aId", "dId", processInstanceID, 123L);
    workerActionRequest
        .getInputVariables()
        .put(
            WorkflowConstants.SUBSCRIPTIONS,
            "[{\"offeringId\": \"QBO\", \"offeringType\": \"PLUS\"}]");
    DefinitionPendingDeletion definitionPendingDeletion =
        DefinitionPendingDeletion.buildFromDefinitionDetails(definitionDetail);
    workerActionRequest
        .getInputVariables()
        .put(
            WorkflowConstants.DEFINITION_DETAILS_PENDING_DELETION,
            ObjectConverter.toJson(Arrays.asList(definitionPendingDeletion)));

    Mockito.when(
        featureManager.getBoolean(
            eq(WorkflowConstants.WORKFLOW_VARIABILITY_DOWNGRADE_FF), anyString()
        )).thenReturn(true);

    Map<String, CorrelateAllMessage.CorrelateKey> correlateKeysMap = new HashMap<>();
    correlateKeysMap.put(
        CorrelationKeysEnum.DEFINITION_KEY.getName(),
        new CorrelateAllMessage.CorrelateKey(
            definitionDetail.getDefinitionKey(), CorrelationKeysEnum.DEFINITION_KEY.getDataType()));
    CorrelateAllMessage correlateAllMessage =
        new CorrelateAllMessage(
            "deleted_voided_disable",
            String.valueOf(workerActionRequest.getOwnerId()),
            correlateKeysMap);

    Mockito.when(bpmnEngineRunTimeServiceRest.correlateAllMessage(correlateAllMessage))
        .thenReturn(true);

    Map<String, Object> response =
        correlateAllMessageTaskHandler.executeAction(workerActionRequest);
    Mockito.verify(processDetailsRepoService, Mockito.never())
        .findByRealmIdAndProcessStatus(any(), any());
    Mockito.verify(processDetailsRepoService, Mockito.never()).saveOrUpdateProcess(any());
    Mockito.verify(processDetailsRepoService, Mockito.times(1))
        .findByDefinitionDetailsListAndProcessStatus(any(), any());
    Assert.assertNotNull(response);
    Assert.assertEquals(response.size(), 1);
    Assert.assertEquals(
        response.entrySet().stream().findFirst().get().getValue(), Boolean.TRUE.toString());
  }

  @Test
  public void testUpdateStatusSuccess_withActiveSubscriptions_NoProcesses_VariabilityFFDisabled() {
    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail, authorization, "123");
    definitionDetail.setOwnerId(123L);
    Mockito.when(
            processDetailsRepoService.findByDefinitionDetailsListAndProcessStatus(
                Arrays.asList(definitionDetail), ProcessStatus.ERROR))
        .thenReturn(Optional.empty());
    String processInstanceID = "iId";
    WorkerActionRequest workerActionRequest =
        buildWorkerActionRequest("aId", "dId", processInstanceID, 123L);
    workerActionRequest
        .getInputVariables()
        .put(
            WorkflowConstants.SUBSCRIPTIONS,
            "[{\"offeringId\": \"QBO\", \"offeringType\": \"PLUS\"}]");
    DefinitionPendingDeletion definitionPendingDeletion =
        DefinitionPendingDeletion.buildFromDefinitionDetails(definitionDetail);
    workerActionRequest
        .getInputVariables()
        .put(
            WorkflowConstants.DEFINITION_DETAILS_PENDING_DELETION,
            ObjectConverter.toJson(Arrays.asList(definitionPendingDeletion)));

    Mockito.when(
        featureManager.getBoolean(
            eq(WorkflowConstants.WORKFLOW_VARIABILITY_DOWNGRADE_FF), anyString()
        )).thenReturn(false);

    Map<String, CorrelateAllMessage.CorrelateKey> correlateKeysMap = new HashMap<>();
    correlateKeysMap.put(
        CorrelationKeysEnum.DEFINITION_KEY.getName(),
        new CorrelateAllMessage.CorrelateKey(
            definitionDetail.getDefinitionKey(), CorrelationKeysEnum.DEFINITION_KEY.getDataType()));
    CorrelateAllMessage correlateAllMessage =
        new CorrelateAllMessage(
            "deleted_voided_disable",
            String.valueOf(workerActionRequest.getOwnerId()),
            correlateKeysMap);

    Mockito.when(bpmnEngineRunTimeServiceRest.correlateAllMessage(correlateAllMessage))
        .thenReturn(true);

    Map<String, Object> response =
        correlateAllMessageTaskHandler.executeAction(workerActionRequest);
    Mockito.verify(processDetailsRepoService, Mockito.never()).saveOrUpdateProcess(any());
    Assert.assertNotNull(response);
    Assert.assertEquals(response.size(), 1);
    Assert.assertEquals(
        response.entrySet().stream().findFirst().get().getValue(), Boolean.TRUE.toString());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testMessageFail() {
    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail, authorization, "123");
    definitionDetail.setOwnerId(123L);
    ProcessDetails processDetails =
        buildProcessDetails(ProcessStatus.ACTIVE, 123L, definitionDetail, "pid");
    ProcessDetails processDetails1 =
        buildProcessDetails(ProcessStatus.ACTIVE, 123L, definitionDetail, "pid1");
    ProcessDetails processDetailsError =
        buildProcessDetails(ProcessStatus.ERROR, 123L, definitionDetail, "pid2");
    List<ProcessDetails> processDetailsList = new ArrayList<>();
    processDetailsList.add(processDetails);
    processDetailsList.add(processDetails1);
    processDetailsList.add(processDetailsError);
    String processInstanceID = "iId";

    WorkerActionRequest workerActionRequest =
        buildWorkerActionRequest("aId", "dId", processInstanceID, 123L);
    CorrelateAllMessage correlateAllMessage =
        new CorrelateAllMessage(
            "deleted_voided_disable", String.valueOf(workerActionRequest.getOwnerId()), null);
    Mockito.when(processDetailsRepoService.findByRealmIdAndProcessStatus(123L, ProcessStatus.ERROR))
        .thenReturn(Optional.of(processDetailsList));
    Mockito.doThrow(WorkflowGeneralException.class)
        .when(bpmnEngineRunTimeServiceRest)
        .correlateAllMessage(correlateAllMessage);

    bpmnEngineRunTimeServiceRest.correlateAllMessage(correlateAllMessage);

    correlateAllMessageTaskHandler.executeAction(workerActionRequest);
  }

  @Test
  public void testNoErrorProcessNotExistSuccess() {
    String processInstanceID = "iId";
    Mockito.when(processDetailsRepoService.findByRealmIdAndProcessStatus(123L, ProcessStatus.ERROR))
        .thenReturn(Optional.empty());
    WorkerActionRequest workerActionRequest =
        buildWorkerActionRequest("aId", "dId", processInstanceID, 123L);

    buildWorkerActionRequest("aId", "dId", processInstanceID, 123L);

    Map<String, Object> response =
        correlateAllMessageTaskHandler.executeAction(workerActionRequest);
    Assert.assertNotNull(response);
    Assert.assertEquals(response.size(), 1);
    Assert.assertEquals(
        response.entrySet().stream().findFirst().get().getValue(), Boolean.TRUE.toString());
  }

  @Test
  public void testNoErrorProcessExistSuccess() {
    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail, authorization, "123");
    ProcessDetails processDetails =
        ProcessDetails.builder()
            .definitionDetails(definitionDetail)
            .ownerId(123L)
            .processStatus(ProcessStatus.ACTIVE)
            .processId("pid")
            .build();
    List<ProcessDetails> processDetailsList = new ArrayList<>();
    processDetailsList.add(processDetails);
    String processInstanceID = "iId";
    Mockito.when(processDetailsRepoService.findByRealmIdAndProcessStatus(123L, ProcessStatus.ERROR))
        .thenReturn(Optional.ofNullable(processDetailsList));
    WorkerActionRequest workerActionRequest =
        buildWorkerActionRequest("aId", "dId", processInstanceID, 123L);
    HashMap<String, Object> messageResponse = new HashMap<>();
    CorrelateAllMessage correlateAllMessage =
        new CorrelateAllMessage(
            "deleted_voided_disable", String.valueOf(workerActionRequest.getOwnerId()), null);

    Map<String, Object> response =
        correlateAllMessageTaskHandler.executeAction(workerActionRequest);
    Assert.assertNotNull(response);
    Assert.assertEquals(response.size(), 1);
    Assert.assertEquals(
        response.entrySet().stream().findFirst().get().getValue(), Boolean.TRUE.toString());
  }

  private ProcessDetails buildProcessDetails(
      ProcessStatus processStatus,
      Long ownerId,
      DefinitionDetails definitionDetails,
      String processId) {
    return ProcessDetails.builder()
        .definitionDetails(definitionDetails)
        .ownerId(ownerId)
        .processStatus(processStatus)
        .processId(processId)
        .build();
  }

  private WorkerActionRequest buildWorkerActionRequest(
      String activityId, String processDefinitionId, String processInstanceID, Long ownerId) {
    return WorkerActionRequest.builder()
        .activityId("aId")
        .ownerId(ownerId)
        .processDefinitionId("dId")
        .processInstanceId(processInstanceID)
        .inputVariables(schema)
        .build();
  }

  @Test
  public void testGetName() {
    Assert.assertEquals(
        "was_correlateNonEndedProcess",
        correlateAllMessageTaskHandler.getName().getTaskHandlerName());
  }

  @Test
  public void test_LogErrorMetric() {
    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail, authorization, "123");
    ProcessDetails processDetails =
        ProcessDetails.builder()
            .definitionDetails(definitionDetail)
            .ownerId(123L)
            .processStatus(ProcessStatus.ACTIVE)
            .processId("pid")
            .build();
    List<ProcessDetails> processDetailsList = new ArrayList<>();
    processDetailsList.add(processDetails);
    String processInstanceID = "iId";
    Mockito.when(processDetailsRepoService.findByRealmIdAndProcessStatus(123L, ProcessStatus.ERROR))
        .thenThrow(WorkflowGeneralException.class);
    WorkerActionRequest workerActionRequest =
        buildWorkerActionRequest("aId", "dId", processInstanceID, 123L);
    try {
      correlateAllMessageTaskHandler.execute(workerActionRequest);
    } catch (WorkflowGeneralException workflowGeneralException) {
      Assert.assertEquals(workflowGeneralException.getWorkflowError(), WorkflowError.INVALID_INPUT);
      Mockito.verify(metricLogger, Mockito.times(1)).logErrorMetric(any(), any(), any());
    }
  }

  @Test
  public void testUpdateStatusSuccess_withActiveSubscriptions_ForOnDemandApproval_WithVariabilityFlagEnabled() {
    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail, authorization, "123");
    definitionDetail.setOwnerId(123L);
    definitionDetail.setDefinitionKey(REMINDER.getTemplateName());
    DefinitionPendingDeletion definitionPendingDeletion =
        DefinitionPendingDeletion.buildFromDefinitionDetails(definitionDetail);

    String processInstanceID = "downgradeProcessId";
    WorkerActionRequest workerActionRequest =
        buildWorkerActionRequest("aId", "downgradeDefinitionId", processInstanceID, 123L);
    workerActionRequest
        .getInputVariables()
        .put(
            WorkflowConstants.SUBSCRIPTIONS,
            "[{\"offeringId\": \"Intuit.sbe.salsa.default\", \"offeringType\": \"QBO_ADVANCED\", \"status\": \"INACTIVE\"}, {\"offeringId\": \"Intuit.core.billpay\", \"offeringType\": \"BILLPAY_PREMIUM\", \"status\": \"INACTIVE\"}, {\"offeringId\": \"Intuit.midmarket.ies\", \"offeringType\": \"IES_STANDARD\", \"status\": \"ACTIVE\"}, {\"offeringId\": \"Intuit.midmarket.ies.me\", \"offeringType\": \"IESME_STANDARD\", \"status\": \"ACTIVE\"}, {\"offeringId\": \"Intuit.expert.client.bookkeeping.onetime\", \"offeringType\": \"QBLIVE_EXPERT_SERVICES\", \"status\": \"ACTIVE\"}]"
        );

    workerActionRequest
        .getInputVariables()
        .put(
            WorkflowConstants.DEFINITION_DETAILS_PENDING_DELETION,
            ObjectConverter.toJson(Arrays.asList(definitionPendingDeletion)));

    DefinitionDetails onDemandApprovalDefinition = DefinitionDetails.builder()
        .definitionId("456")
        .recordType(null)
        .templateDetails(bpmnTemplateDetail)
        .workflowId("wkid")
        .definitionKey(APPROVAL.getTemplateName())
        .ownerId(Long.MIN_VALUE)
        .build();

    Mockito.when(onDemandHelper.fetchOnDemandApprovalDefinition()).thenReturn(List.of(onDemandApprovalDefinition));

    Mockito.when(
        featureManager.getBoolean(
            eq(WorkflowConstants.WORKFLOW_VARIABILITY_DOWNGRADE_FF), anyString()
        )).thenReturn(true);

    Map<String, Object> response =
        correlateAllMessageTaskHandler.executeAction(workerActionRequest);
    Assert.assertNotNull(response);
    Assert.assertEquals(response.size(), 1);
    Assert.assertEquals(
        response.entrySet().stream().findFirst().get().getValue(), Boolean.TRUE.toString());
  }
}
