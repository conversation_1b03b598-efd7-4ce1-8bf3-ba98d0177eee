package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services;

import com.intuit.appintgwkflw.wkflautomate.was.apollo.client.IdentityGraphqlClient;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.accounts.Persona;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Map;
import java.util.Set;

@RunWith(MockitoJUnitRunner.class)
public class IdentityServiceTest {

    @InjectMocks
    private IdentityService identityService;

    @Mock
    private IdentityGraphqlClient identityGraphqlClient;

    @Test
    public void test_GetPersona_ID20() {
        Mockito.when(identityGraphqlClient.getPersonaId(Mockito.any(), Mockito.any())).thenReturn("test");
        Assert.assertEquals("test", identityService.getPersonaId("123", "456"));
        Mockito.verify(identityGraphqlClient, Mockito.times(1)).getPersonaId(Mockito.any(), Mockito.any());
        Mockito.verify(identityGraphqlClient, Mockito.times(0)).getAccountantPersonaId(Mockito.any(), Mockito.any());
    }

    @Test
    public void test_GetAccountantPersona_ID20() {
        Mockito.when(identityGraphqlClient.getPersonaId(Mockito.any(), Mockito.any())).thenReturn(null);
        Mockito.when(identityGraphqlClient.getAccountantPersonaId(Mockito.any(), Mockito.any())).thenReturn("test");
        Assert.assertEquals("test", identityService.getPersonaId("123", "456"));
        Mockito.verify(identityGraphqlClient, Mockito.times(1)).getPersonaId(Mockito.any(), Mockito.any());
        Mockito.verify(identityGraphqlClient, Mockito.times(1)).getAccountantPersonaId(Mockito.any(), Mockito.any());
    }

    @Test
    public void test_GetRealmPersonas_ID20() {
        Map<String, Persona> map = Map.of("user1", Persona.builder().build());
        Mockito.when(identityGraphqlClient.getRealmPersonas(Mockito.any(), Mockito.any())).thenReturn(map);
        Assert.assertEquals(map, identityService.getRealmPersonas("1234", Set.of("123")));
    }

    @Test
    public void test_authorizeDelegation_ID20() {
        Mockito.doNothing().when(identityGraphqlClient).createTrustGrant(Mockito.any());
        identityService.authorizeDelegation("12345");
        Mockito.verify(identityGraphqlClient, Mockito.times(1)).createTrustGrant(Mockito.any());
    }

}
