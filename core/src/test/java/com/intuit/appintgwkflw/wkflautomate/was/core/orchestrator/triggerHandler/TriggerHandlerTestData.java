package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.BpmnProcessorUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.bpmn.BpmnStartElementUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TriggerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CreatorType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ModelType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ProcessStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.Status;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.TriggerType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.ResponseStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowEvaluateRuleResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowGenericResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowGenericResponse.WorkflowGenericResponseBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowTriggerResponse;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import org.apache.commons.io.IOUtils;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.StartEvent;

public class TriggerHandlerTestData {

  private static final String INVOICE_APPROVAL_BPMN =
      "src/test/resources/bpmn/invoiceapproval_triggerTest.bpmn";

  private static final String INVOICE_APPROVAL_BPMN_RELAXED_NON_ENTITY_PROCESS_VAR =
          "src/test/resources/bpmn/invoiceapproval_triggerTest_ignoreNonEntityProcessVariablesDetail.bpmn";

  private static final String INVOICE_APPROVAL_BPMN_RELAXED_PROCESS_VAR =
      "src/test/resources/bpmn/invoiceapproval_triggerTest_ignoreProcessVariablesDetail.bpmn";

  private static final String INVOICE_APPROVAL_BPMN_RELAXED_PROCESS_VAR_EMPTY_PROCESS_DETAILS =
      "src/test/resources/bpmn/invoiceapproval_triggerTest_ignoreProcessVariablesDetail_processVariablesAbsent.bpmn";

  private static final String BILL_APPROVAL_BPMN =
      "src/test/resources/bpmn/customApprovalDefinition.bpmn";

  private static final String CUSTOM_REMINDER_DMN =
      "src/test/resources/dmn/decision_singleCustomReminder.dmn";

  private static final String INVOICE_APPROVAL_DMN =
      "src/test/resources/dmn/decision_invoiceapproval.dmn";

  private static final String OFFER_ID = "offerId";
  private static final String OWNER_ID = "9130347798120106";
  private static final String CREATED_BY = "9130347715753436";
  private static final String PROCESS_ID = "6fc09080-5565-11ea-8ce7-9e4a956f003c";
  private static final String V3_PAYLOAD_DELETED = "schema/testData/V3PayloadDeleted.json";
  private static final String V3_PAYLOAD_UPDATED = "schema/testData/V3PayloadUpdated.json";
  private static final String V3_PAYLOAD_CREATED = "schema/testData/V3PayloadCreated.json";
  private static final String V3_PAYLOAD_INVOICE_EMPTY_STRING = "schema/testData/V3EmptyStringPayload.json";
  private static final String V3_PAYLOAD_CREATED_BILL = "schema/testData/V3CreatedBillPayload.json";
  private static final String V3_PAYLOAD_CREATED_CUSTOMER = "schema/testData/V3CreatedCustomerPayload.json";
  private static final String V3_PAYLOAD_CREATED_ENGAGEMENT = "schema/testData/V3CreatedCustomerPayload.json";
  private static final String V3_PAYLOAD_BUSINESS_KEY = "schema/testData/V3PayloadBusinessKey.json";
  private static final String V3_PAYLOAD_CREATED_PURCHASE_ORDER = "schema/testData/V3CreatePOPayload.json";
  private static final String V3_PAYLOAD_APPROVED_PURCHASE_ORDER = "schema/testData/V3ApprovePOPayload.json";
  private static final String V3_PAYLOAD_CREATED_ESTIMATE = "schema/testData/V3CreateEstimatePayload.json";
  private static final String V3_PAYLOAD_WITH_OBJECT_VARIABLE_TYPE = "schema/testData/V3ObjectVariableTypePayload.json";
  private static final String V3_PAYLOAD_WITH_TYPED_VARIABLES = "schema/testData/V3TypedLocalVariablesPayload.json";
  private static final String V3_PAYLOAD_WITH_OBJECT_TEMPLATE_TAG = "schema/testData/V3SystemTagPayload.json";
  private static final String V3_PAYLOAD_WITH_OBJECT_WITHOUT_TAG= "schema/testData/V3SystemTagWithoutVersion.json";
  private static final String V3_PAYLOAD_WITH_OBJECT_WITHOUT_TAG_KEY= "schema/testData/V3SystemTagTestTagMissing.json";

  private static final String TRIGGER_GLOBAL_SCOPING_PAYLOAD =
      "schema/testData/triggerGlobalScopingPayload.json";

  private static final String TRIGGER_LOCAL_SCOPING_PAYLOAD =
      "schema/testData/triggerLocalScopingPayload.json";

  private static final String TRIGGER_LOCAL_GLOBAL_SCOPING_PAYLOAD =
      "schema/testData/triggerLocalGlobalScopingPayload.json";

  private static final ObjectMapper mapper = new ObjectMapper();

  public static TriggerProcessDetails prepareV3TriggerMessage(String eventType, String recordType) {

    String triggerMessageStr;
    switch (eventType) {
      case "created":
        if (recordType != null && recordType.equalsIgnoreCase("bill")) {
          triggerMessageStr = TestHelper.readResourceAsString(V3_PAYLOAD_CREATED_BILL);
        } else if (recordType != null && recordType.equalsIgnoreCase("purchaseorder")) {
          triggerMessageStr = TestHelper.readResourceAsString(V3_PAYLOAD_CREATED_PURCHASE_ORDER);
        }else if (recordType != null && recordType.equalsIgnoreCase("estimate")) {
          triggerMessageStr = TestHelper.readResourceAsString(V3_PAYLOAD_CREATED_ESTIMATE);
        }else if (recordType != null && recordType.equalsIgnoreCase("customer")) {
          triggerMessageStr = TestHelper.readResourceAsString(V3_PAYLOAD_CREATED_CUSTOMER);
        }else if (recordType != null && recordType.equalsIgnoreCase("engagement")) {
          triggerMessageStr = TestHelper.readResourceAsString(V3_PAYLOAD_CREATED_CUSTOMER);
        } else {
          triggerMessageStr = TestHelper.readResourceAsString(V3_PAYLOAD_CREATED);
        }
        break;
      case "approved":
        triggerMessageStr = TestHelper.readResourceAsString(V3_PAYLOAD_APPROVED_PURCHASE_ORDER);
        break;
      case "emptyStringPayload":
        triggerMessageStr = TestHelper.readResourceAsString(V3_PAYLOAD_INVOICE_EMPTY_STRING);
        break;
      case "deleted":
        triggerMessageStr = TestHelper.readResourceAsString(V3_PAYLOAD_DELETED);
        break;
      case "globalScoping":
        triggerMessageStr = TestHelper.readResourceAsString(TRIGGER_GLOBAL_SCOPING_PAYLOAD);
        break;
      case "globalScopingWithEmptyApproverId":
        triggerMessageStr = TestHelper.readResourceAsString("schema/testData/triggerGlobalScopingPayloadWithEmptyApproverId.json");
        break;
      case "globalScopingWithNonEmptyApproverId":
        triggerMessageStr = TestHelper.readResourceAsString("schema/testData/triggerGlobalScopingPayloadWithNonEmptyApproverId.json");
        break;
      case "localScoping":
        triggerMessageStr = TestHelper.readResourceAsString(TRIGGER_LOCAL_SCOPING_PAYLOAD);
        break;
      case "localScopingWithEmptyApproverId":
        triggerMessageStr = TestHelper.readResourceAsString("schema/testData/triggerLocalScopingPayloadWithEmptyApproverId.json");
        break;
      case "localScopingWithNonEmptyApproverId":
        triggerMessageStr = TestHelper.readResourceAsString("schema/testData/triggerLocalScopingPayloadWithNonEmptyApproverId.json");
        break;
      case "localGlobalScoping":
        triggerMessageStr = TestHelper.readResourceAsString(TRIGGER_LOCAL_GLOBAL_SCOPING_PAYLOAD);
        break;
      case "businessKey":
        triggerMessageStr = TestHelper.readResourceAsString(V3_PAYLOAD_BUSINESS_KEY);
        break;
      case "objectVariableTypePayload":
        triggerMessageStr = TestHelper.readResourceAsString(V3_PAYLOAD_WITH_OBJECT_VARIABLE_TYPE);
        break;
      case "typedVariablesPayload":
        triggerMessageStr = TestHelper.readResourceAsString(V3_PAYLOAD_WITH_TYPED_VARIABLES);
        break;
      case "templateTagPayload":
        triggerMessageStr = TestHelper.readResourceAsString(V3_PAYLOAD_WITH_OBJECT_TEMPLATE_TAG);
        break;
      case "templateWithoutTagPayload":
        triggerMessageStr = TestHelper.readResourceAsString(V3_PAYLOAD_WITH_OBJECT_WITHOUT_TAG);
        break;
      case "templateWithoutTagKeyPayload":
        triggerMessageStr = TestHelper.readResourceAsString(V3_PAYLOAD_WITH_OBJECT_WITHOUT_TAG_KEY);
        break;
      default:
        triggerMessageStr = TestHelper.readResourceAsString(V3_PAYLOAD_UPDATED);
    }

    Map<String, Object> triggerMessage = null;
    try {
      triggerMessage = mapper
          .readValue(triggerMessageStr, new TypeReference<Map<String, Object>>() {
          });
    } catch (IOException e) {
      e.printStackTrace();
    }
    return TriggerProcessDetails.builder().triggerMessage(triggerMessage).build();
  }

  public static TriggerProcessDetails prepareV3TriggerMessage(String eventType) {
    return prepareV3TriggerMessage(eventType, null);
  }

  public static DefinitionDetails getDefinitionDetails(TemplateDetails templateDetails) {
    return DefinitionDetails.builder()
        .definitionId(UUID.randomUUID().toString())
        .templateDetails(templateDetails)
        .ownerId(Long.valueOf(OWNER_ID))
        .workflowId("workFlowId")
        .definitionKey("123")
        .recordType(templateDetails.getRecordType())
        .modelType(ModelType.BPMN)
        .createdByUserId(Long.parseLong(CREATED_BY))
        .status(Status.ENABLED)
        .build();
  }

  public static TemplateDetails getBPMNTemplateDetails(String recordType) {
    if(recordType!=null && recordType.equalsIgnoreCase("bill")) {
      return getTemplateDetails(ModelType.BPMN, null, BILL_APPROVAL_BPMN);
    }
    return getTemplateDetails(ModelType.BPMN, null, INVOICE_APPROVAL_BPMN);
  }

  public static TemplateDetails getBPMNTemplateDetails() {
    return getBPMNTemplateDetails(null);
  }

  public static TemplateDetails getDMNTemplateDetails(String parentId) {
    return getTemplateDetails(ModelType.DMN, parentId, INVOICE_APPROVAL_DMN);
  }

  public static TemplateDetails getBPMNTemplateDetailsWithAllowMultipleDefinitions() {

    return getTemplateDetailsWithAllowMultipleDefinitions(
        ModelType.BPMN, null, INVOICE_APPROVAL_BPMN);
  }

  public static TemplateDetails getDMNTemplateDetailsWithAllowMultipleDefinitions(String parentId) {

    return getTemplateDetailsWithAllowMultipleDefinitions(
        ModelType.DMN, parentId, INVOICE_APPROVAL_DMN);
  }

  public static TemplateDetails getTemplateDetails(
      ModelType modeltype, String parentId, String path) {

    File bpmnDmnFile = new File(path);
    FileInputStream fisBpmn = null;
    try {
      fisBpmn = new FileInputStream(bpmnDmnFile);
    } catch (FileNotFoundException e) {
      WorkflowLogger.error(
          () ->
              WorkflowLoggerRequest.builder()
                  .message("bpmn, dmn parsing error")
                  .stackTrace(e)
                  .downstreamComponentName(DownstreamComponentName.WAS)
                  .className(TriggerHandlerTestData.class.getSimpleName()));
    }
    TemplateDetails bpmnDetails = null;
    try {
      bpmnDetails =
          TemplateDetails.builder()
              .id(UUID.randomUUID().toString())
              .templateName(bpmnDmnFile.getName())
              .modelType(modeltype)
              .createdByUserId(Long.parseLong(CREATED_BY))
              .creatorType(CreatorType.SYSTEM)
              .offeringId(OFFER_ID)
              .ownerId(Long.parseLong(OWNER_ID))
              .recordType(RecordType.INVOICE)
              .version(1)
              .parentId(parentId)
              .templateData(IOUtils.toByteArray(fisBpmn))
              .status(Status.ENABLED)
              .allowMultipleDefinitions(false)
              .build();
    } catch (IOException e) {
      WorkflowLogger.error(
          () ->
              WorkflowLoggerRequest.builder()
                  .message("TemplateData build error")
                  .stackTrace(e)
                  .downstreamComponentName(DownstreamComponentName.WAS)
                  .className(TriggerHandlerTestData.class.getSimpleName()));
    }

    return bpmnDetails;
  }

  public static TemplateDetails getTemplateDetailsWithAllowMultipleDefinitions(
      ModelType modeltype, String parentId, String path) {

    File bpmnDmnFile = new File(path);
    FileInputStream fisBpmn = null;
    try {
      fisBpmn = new FileInputStream(bpmnDmnFile);
    } catch (FileNotFoundException e) {
      WorkflowLogger.error(
          () ->
              WorkflowLoggerRequest.builder()
                  .message("bpmn, dmn parsing error")
                  .stackTrace(e)
                  .downstreamComponentName(DownstreamComponentName.WAS)
                  .className(TriggerHandlerTestData.class.getSimpleName()));
    }
    TemplateDetails bpmnDetails = null;
    try {
      bpmnDetails =
          TemplateDetails.builder()
              .id(UUID.randomUUID().toString())
              .templateName(bpmnDmnFile.getName())
              .modelType(modeltype)
              .createdByUserId(Long.parseLong(CREATED_BY))
              .creatorType(CreatorType.SYSTEM)
              .offeringId(OFFER_ID)
              .ownerId(Long.parseLong(OWNER_ID))
              .recordType(RecordType.INVOICE)
              .version(1)
              .parentId(parentId)
              .templateData(IOUtils.toByteArray(fisBpmn))
              .status(Status.ENABLED)
              .allowMultipleDefinitions(true)
              .build();
    } catch (IOException e) {
      WorkflowLogger.error(
          () ->
              WorkflowLoggerRequest.builder()
                  .message("TemplateData build error")
                  .stackTrace(e)
                  .downstreamComponentName(DownstreamComponentName.WAS)
                  .className(TriggerHandlerTestData.class.getSimpleName()));
    }

    return bpmnDetails;
  }

  public static Map<String, Object> getStartProcessResponse() {

    String startPorcessResponseStr = "{\n"
        + "    \"status\": \"SUCCESS\",\n"
        + "    \"response\": {\n"
        + "        \"processId\": \"6fc09080-5565-11ea-8ce7-9e4a956f003c\",\n"
        + "        \"status\": \"PROCESS_STARTED\"\n"
        + "    }\n"
        + "}";

    Map<String, Object> startPorcessResponse = null;
    try {
      startPorcessResponse = mapper
          .readValue(startPorcessResponseStr, new TypeReference<Map<String, Object>>() {
          });
    } catch (IOException e) {
      e.printStackTrace();
    }
    return startPorcessResponse;

  }


  public static List<Map<String, Object>> getEvalautionResultOfCamunda() {

    String evaluationResultStr = "[\n"
        + "    {\n"
        + "        \"approvalRequired\": {\n"
        + "            \"type\": \"Boolean\",\n"
        + "            \"value\": false,\n"
        + "            \"valueInfo\": {}\n"
        + "        }\n"
        + "    }\n"
        + "]";

    List<Map<String, Object>> evaluationResult = null;
    try {
      evaluationResult = mapper
          .readValue(evaluationResultStr, new TypeReference<List<Map<String, Object>>>() {
          });
    } catch (IOException e) {
      e.printStackTrace();
    }

    return evaluationResult;

  }

  public static List<ProcessDetails> getProcessDetailsList(String entityId,
      DefinitionDetails definitionDetails) {

    ProcessDetails processDetails = ProcessDetails.builder()
        .ownerId(Long.parseLong(OWNER_ID))
        .definitionDetails(definitionDetails)
        .processId(PROCESS_ID)
        .processStatus(ProcessStatus.ACTIVE)
        .recordId(entityId)
        .build();
    List<ProcessDetails> processDetailsList = new ArrayList<>();
    processDetailsList.add(processDetails);
    return processDetailsList;

  }

  public static List<TriggerDetails> getTriggerDetaisList(TemplateDetails templateDetails,
      String triggerName) {

    TriggerDetails triggerDetails = TriggerDetails.builder()
        .recordType(RecordType.INVOICE)
        .templateDetails(templateDetails)
        .triggerName(triggerName)
        .triggerType(TriggerType.WFTASK)
        .build();
    List<TriggerDetails> triggerDetailsList = new ArrayList<>();
    triggerDetailsList.add(triggerDetails);
    return triggerDetailsList;

  }

  public static void getTriggerDetailsListWithSubstrings(TemplateDetails templateDetails,
                                                          String triggerName, List<TriggerDetails> triggerDetailsList) {

    TriggerDetails triggerDetails = TriggerDetails.builder()
        .recordType(RecordType.INVOICE)
        .templateDetails(templateDetails)
        .triggerName(triggerName)
        .triggerType(TriggerType.WFTASK)
        .build();

    triggerDetailsList.add(triggerDetails);
  }

  public static WorkflowGenericResponseBuilder getDefaultTriggerResponseBuilder() {

    return WorkflowGenericResponse.builder()
        .status(ResponseStatus.FAILURE)
        .response(WorkflowTriggerResponse.builder().build());
  }


  public static WorkflowGenericResponseBuilder getDefaultRuleResponseBuilder() {

    return WorkflowGenericResponse.builder()
        .status(ResponseStatus.FAILURE)
        .response(new WorkflowEvaluateRuleResponse());
  }

  public static Map<String, Object> getStartProcessResult() {

    String jsonStr = "{ \"id\": \"6fc09080-5565-11ea-8ce7-9e4a956f003c\"}";
    Map<String, Object> startPorcessResult = null;
    try {
      startPorcessResult = mapper
          .readValue(jsonStr, new TypeReference<Map<String, Object>>() {
          });
    } catch (IOException e) {
      e.printStackTrace();
    }
    return startPorcessResult;
  }

  public static Map<String, Object> getVariablesMap() {

    String jsonStr = "{\n"
        + "\t\"variables\": {\n"
        + "\t\t\"TotalAmt\": {\n"
        + "\t\t\t\"type\": \"Double\",\n"
        + "\t\t\t\"value\": 500\n"
        + "\t\t},\n"
        + "\t\t\"itemList\": {\n"
        + "\t\t\t\"type\": \"list\",\n"
        + "\t\t\t\"value\": [\"1\"]\n"
        + "\t\t},\n"
        + "\t\t\"CustomerRef_value\": {\n"
        + "\t\t\t\"type\": \"String\",\n"
        + "\t\t\t\"value\": \"1\"\n"
        + "\t\t},\n"
        + "\t\t\"DepartmentRef_value\": {\n"
        + "\t\t\t\"type\": \"String\",\n"
        + "\t\t\t\"value\": \"1\"\n"
        + "\t\t},\n"
        + "\t\t\"definitionKey\": {\n"
        + "\t\t\t\"type\": \"String\",\n"
        + "\t\t\t\"value\": \"123\"\n"
        + "\t\t}\n"
        + "\t}\n"
        + "}";
    Map<String, Object> variablesMap = null;
    try {
      variablesMap = mapper
          .readValue(jsonStr, new TypeReference<Map<String, Object>>() {
          });
    } catch (IOException e) {
      e.printStackTrace();
    }
    return variablesMap;

  }

  public static Map<String, Object> getInvalidVariablesMap() {

    String jsonStr = "{\"variableMap\":{\"TotalAmt\":{\"type\":\"Double\",\"value\":500},\"CustomerRef_value\":{\"type\":\"String\",\"value\":\"1\"}, \"DepartmentRef_value\":{\"type\":\"String\",\"value\":\"1\"}}}";
    Map<String, Object> variablesMap = null;
    try {
      variablesMap = mapper
          .readValue(jsonStr, new TypeReference<Map<String, Object>>() {
          });
    } catch (IOException e) {
      e.printStackTrace();
    }
    return variablesMap;

  }

  public static Collection<StartEvent> getStartEventOfTheTemplate() {
    BpmnModelInstance bpmnModelInstance = getBpmnModelInstanceForBPMN(INVOICE_APPROVAL_BPMN);
    return bpmnModelInstance.getModelElementsByType(StartEvent.class);
  }

  public static Map<String, Object> getStartEventExtensionPropertiesOfTheTemplate() {
    Collection<StartEvent> startEvents = getStartEventOfTheTemplate();
    StartEvent startEvent = BpmnStartElementUtil.fetchInitialStartEventForAProcess(startEvents);
    Map<String, String> initialStartEventExtensionProperties = BpmnProcessorUtil.getMapOfCamundaProperties(startEvent.getExtensionElements());
    return new HashMap<>(initialStartEventExtensionProperties);
  }

  public static Collection<StartEvent> getStartEventOfTheTemplateWithRelaxedProcessVar() {
    BpmnModelInstance bpmnModelInstance = getBpmnModelInstanceForBPMN(
        INVOICE_APPROVAL_BPMN_RELAXED_PROCESS_VAR);
    return bpmnModelInstance.getModelElementsByType(StartEvent.class);
  }

  public static Map<String, Object> getStartEventExtensionPropertiesOfTheTemplateWithRelaxedProcessVar() {
    Collection<StartEvent> startEvents = getStartEventOfTheTemplateWithRelaxedProcessVar();
    StartEvent startEvent = BpmnStartElementUtil.fetchInitialStartEventForAProcess(startEvents);
    Map<String, String> initialStartEventExtensionProperties = BpmnProcessorUtil.getMapOfCamundaProperties(startEvent.getExtensionElements());
    return new HashMap<>(initialStartEventExtensionProperties);
  }

  public static Collection<StartEvent> getStartEventOfTheTemplateWithRelaxedNonEntityProcessVar() {
    BpmnModelInstance bpmnModelInstance = getBpmnModelInstanceForBPMN(
        INVOICE_APPROVAL_BPMN_RELAXED_NON_ENTITY_PROCESS_VAR);
    return bpmnModelInstance.getModelElementsByType(StartEvent.class);
  }

  public static Map<String, Object> getStartEventExtensionPropertiesOfTheTemplateWithRelaxedNonEntityProcessVar() {
    Collection<StartEvent> startEvents = getStartEventOfTheTemplateWithRelaxedNonEntityProcessVar();
    StartEvent startEvent = BpmnStartElementUtil.fetchInitialStartEventForAProcess(startEvents);
    Map<String, String> initialStartEventExtensionProperties = BpmnProcessorUtil.getMapOfCamundaProperties(startEvent.getExtensionElements());
    return new HashMap<>(initialStartEventExtensionProperties);
  }

  public static Collection<StartEvent> getStartEventOfTheTemplateWithRelaxedProcessVarWithEmptyProcessDetails() {
    BpmnModelInstance bpmnModelInstance = getBpmnModelInstanceForBPMN(
        INVOICE_APPROVAL_BPMN_RELAXED_PROCESS_VAR_EMPTY_PROCESS_DETAILS);
    return bpmnModelInstance.getModelElementsByType(StartEvent.class);
  }

  public static Map<String, Object> getStartEventExtensionPropertiesOfTheTemplateWithRelaxedProcessVarWithEmptyProcessDetails() {
    Collection<StartEvent> startEvents = getStartEventOfTheTemplateWithRelaxedProcessVarWithEmptyProcessDetails();
    StartEvent startEvent = BpmnStartElementUtil.fetchInitialStartEventForAProcess(startEvents);
    Map<String, String> initialStartEventExtensionProperties = BpmnProcessorUtil.getMapOfCamundaProperties(startEvent.getExtensionElements());
    return new HashMap<>(initialStartEventExtensionProperties);
  }

  private static BpmnModelInstance getBpmnModelInstanceForBPMN(String bpmnFileName) {
    File bpmnDmnFile = new File(bpmnFileName);
    InputStream templateDataAsStream = null;
    try {
      FileInputStream fisBpmn = new FileInputStream(bpmnDmnFile);
      templateDataAsStream = new ByteArrayInputStream(IOUtils.toByteArray(fisBpmn));
    } catch (IOException e) {
      WorkflowLogger.logError(e, "bpmn parsing error");
    }
    return Bpmn.readModelFromStream(templateDataAsStream);
  }

}
