package com.intuit.appintgwkflw.wkflautomate.was.core.bpmnengineadapter;

import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.BpmnComponentType;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.Process;
import org.camunda.bpm.model.bpmn.instance.SubProcess;
import org.junit.Assert;
import org.junit.Test;

public class BpmnProcessorTest {

  private static final String INVOICE_APPROVAL_BPMN = "bpmn/invoiceapproval.bpmn";

  private static BpmnModelInstance modelInstance =
      Bpmn.readModelFromStream(
          BpmnToV4SchemaTest.class.getClassLoader().getResourceAsStream(INVOICE_APPROVAL_BPMN));

  @Test
  public void testProcessCount() {
    Collection<Process> processes = modelInstance.getModelElementsByType(Process.class);
    Assert.assertEquals(1, processes.size());
  }

  @Test
  public void testSubProcessCount() {
    Collection<SubProcess> processes = modelInstance.getModelElementsByType(SubProcess.class);
    Assert.assertEquals(1, processes.size());
  }

  @Test
  public void testServiceTasksCount() {
    Collection<Process> processes = modelInstance.getModelElementsByType(Process.class);
    final List<?>[] serviceTasks = new List<?>[1];
    processes.forEach(
        t -> {
          serviceTasks[0] =
              t.getFlowElements().stream()
                  .filter(
                      u ->
                          u.getElementType()
                              .getTypeName()
                              .equalsIgnoreCase(BpmnComponentType.SERVICE_TASK.getName()))
                  .collect(Collectors.toList());
        });
    Assert.assertEquals(7, serviceTasks[0].size());
  }

  @Test
  public void testBusinessRuleTaskCount() {
    Collection<Process> processes = modelInstance.getModelElementsByType(Process.class);
    final List<?>[] businessRuleTasks = new List<?>[1];
    processes.forEach(
        t -> {
          businessRuleTasks[0] =
              t.getFlowElements().stream()
                  .filter(
                      u ->
                          u.getElementType()
                              .getTypeName()
                              .equalsIgnoreCase(BpmnComponentType.BUSINESS_RULE_TASK.getName()))
                  .collect(Collectors.toList());
        });
    Assert.assertEquals(1, businessRuleTasks[0].size());
  }

  @Test
  public void testRecieveTaskCount() {
    Collection<Process> processes = modelInstance.getModelElementsByType(Process.class);
    final List<?>[] receiveTasks = new List<?>[1];
    processes.forEach(
        t -> {
          receiveTasks[0] =
              t.getFlowElements().stream()
                  .filter(
                      u ->
                          u.getElementType()
                              .getTypeName()
                              .equalsIgnoreCase(BpmnComponentType.RECEIVE_TASK.getName()))
                  .collect(Collectors.toList());
        });
    Assert.assertEquals(2, receiveTasks[0].size());
  }

  @Test
  public void testSendTaskCount() {
    Collection<Process> processes = modelInstance.getModelElementsByType(Process.class);
    final List<?>[] sendTasks = new List<?>[1];
    processes.forEach(
        t -> {
          sendTasks[0] =
              t.getFlowElements().stream()
                  .filter(
                      u ->
                          u.getElementType()
                              .getTypeName()
                              .equalsIgnoreCase(BpmnComponentType.SEND_TASK.getName()))
                  .collect(Collectors.toList());
        });
    Assert.assertEquals(2, sendTasks[0].size());
  }

  @Test
  public void testBoundaryEventCount() {
    Collection<Process> processes = modelInstance.getModelElementsByType(Process.class);
    final List<?>[] boundaryEvents = new List<?>[1];
    processes.forEach(
        t -> {
          boundaryEvents[0] =
              t.getFlowElements().stream()
                  .filter(
                      u ->
                          u.getElementType()
                              .getTypeName()
                              .equalsIgnoreCase(BpmnComponentType.BOUNDARY_EVENT.getName()))
                  .collect(Collectors.toList());
        });
    Assert.assertEquals(2, boundaryEvents[0].size());
  }
}
