package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.Constants;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionActivityDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.impl.ProcessDetailsRepoService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails.ParameterDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.service.TranslationService;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

public class OnDemandParameterDetailExtractorTest {
    @Mock
    private ProcessDetailsRepoService processDetailsRepoService;
    @Mock
    DefinitionActivityDetailsRepository definitionActivityDetailsRepository;
    @Mock
    ProcessDetailsRepository processDetailsRepository;
    @Mock
    ProcessDetailsRepoService getProcessDetailsRepoService;

    private MultiStepParameterDetailsExtractor multiStepParameterDetailsExtractor;

    private TranslationService translationService;

    private CustomWorkflowConfig customWorkflowConfig;
    private WorkerActionRequest workerActionRequest;
    private static final String parametersSchema =
            TestHelper.readResourceAsString("schema/testData/parameters.json");
    private static final Map<String, String> schema = new HashMap<>();

    static {
        schema.put(WorkFlowVariables.PARAMETERS_KEY.getName(), parametersSchema);
        schema.put(WorkFlowVariables.HANDLER_DETAILS_KEY.getName(), "{}");
        schema.put("entityType", "invoice");
        schema.put(WorkflowConstants.DEFINITION_KEY, "2121312213");
        schema.put(WorkflowConstants.INTUIT_REALMID, "362727827");
        schema.put(WorkflowConstants.ROOT_PROCESS_INSTANCE_ID, "1234");
        schema.put(WorkflowConstants.INTUIT_WAS_LOCALE,"fr_ca");
        schema.put("Assignee","1234");
    }

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);
        translationService =TestHelper.initTranslationService();
        customWorkflowConfig = TestHelper.loadCustomConfig();
        TemplateDetails templateDetails =
            TemplateDetails.builder()
                .id(DefinitionTestConstants.TEMPLATE_ID)
                .templateName("customApproval")
                .ownerId(Long.MIN_VALUE)
                .recordType(null)
                .allowMultipleDefinitions(true)
                .build();
        DefinitionDetails mockedDefinitionDetails = DefinitionDetails.builder()
            .templateDetails(templateDetails).placeholderValue(null).ownerId(Long.MIN_VALUE)
            .recordType(null).build();
        Mockito.when(processDetailsRepoService.findByProcessIdWithoutDefinitionData(any()))
                .thenReturn(Optional.of(mockedDefinitionDetails));
        multiStepParameterDetailsExtractor = new MultiStepParameterDetailsExtractor(
            customWorkflowConfig,
            definitionActivityDetailsRepository,
            processDetailsRepository,
            processDetailsRepoService
        );
    }

    @Test
    public void testGetParameterDetailsCreateTask() {
        String handlerId = "hid";
        workerActionRequest =
                WorkerActionRequest.builder()
                        .activityId("createTask")
                        .processDefinitionId("dId")
                        .processInstanceId("iId")
                        .definitionKey("sendForApproval")
                        .ownerId(Long.valueOf("9999"))
                        .inputVariables(schema)
                        .handlerId(handlerId)
                        .build();

        OnDemandParameterDetailExtractor onDemandParameterDetailExtractor = new OnDemandParameterDetailExtractor(processDetailsRepoService, customWorkflowConfig, multiStepParameterDetailsExtractor, translationService);
        Map<String, ParameterDetails> parameterDetailsMap = onDemandParameterDetailExtractor.getParameterDetails(workerActionRequest).get();
        assertNotNull(parameterDetailsMap);
        assertEquals("1234",parameterDetailsMap.get("Assignee").getFieldValue().get(0));
        assertEquals("QB_INVOICE", parameterDetailsMap.get(Constants.TASK_TYPE).getFieldValue().get(0));
        assertEquals("QB_INVOICE_APPROVAL",parameterDetailsMap.get(Constants.PROJECT_TYPE).getFieldValue().get(0));
        assertNotNull(parameterDetailsMap.get("TaskName").getFieldValue().get(0));
    }

    @Test
    public void testGetParameterDetailsSendCompanyEmail() {
        String handlerId = "hid";
        workerActionRequest =
            WorkerActionRequest.builder()
                .activityId("sendCompanyEmail")
                .processDefinitionId("dId")
                .processInstanceId("iId")
                .definitionKey("sendForApproval")
                .ownerId(Long.valueOf("9999"))
                .inputVariables(schema)
                .handlerId(handlerId)
                .build();

        OnDemandParameterDetailExtractor onDemandParameterDetailExtractor = new OnDemandParameterDetailExtractor(processDetailsRepoService, customWorkflowConfig, multiStepParameterDetailsExtractor, translationService);
        Map<String, ParameterDetails> parameterDetailsMap = onDemandParameterDetailExtractor.getParameterDetails(workerActionRequest).get();
        assertNotNull(parameterDetailsMap);
        assertEquals("true", parameterDetailsMap.get("IsEmail").getFieldValue().get(0));
        assertEquals("[[CompanyEmail]]",parameterDetailsMap.get("SendTo").getFieldValue().get(0));
        assertNotNull(parameterDetailsMap.get("Message").getFieldValue().get(0));
        assertNotNull(parameterDetailsMap.get("Subject").getFieldValue().get(0));
    }

    @Test
    public void testGetParameterDetails_nonLegalActivity_throwException() {
        String handlerId = "hid";
        workerActionRequest =
                WorkerActionRequest.builder()
                        .activityId("sendPushNotification")
                        .processDefinitionId("dId")
                        .processInstanceId("iId")
                        .definitionKey("customApproval")
                        .ownerId(Long.valueOf("9999"))
                        .inputVariables(schema)
                        .handlerId(handlerId)
                        .build();

        try {
            OnDemandParameterDetailExtractor onDemandParameterDetailExtractor = new OnDemandParameterDetailExtractor(processDetailsRepoService, customWorkflowConfig, multiStepParameterDetailsExtractor, translationService);
            onDemandParameterDetailExtractor.getParameterDetails(workerActionRequest);
        } catch (WorkflowGeneralException e) {
            Assert.assertTrue(
                    e.getMessage().contains(WorkflowError.INVALID_ACTION_KEY.getErrorMessage()));
        }
    }
}