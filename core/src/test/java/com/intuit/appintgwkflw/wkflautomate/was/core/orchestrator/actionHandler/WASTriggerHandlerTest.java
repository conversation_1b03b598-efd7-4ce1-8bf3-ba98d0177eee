package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureManager;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.DefinitionServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.UpdateDefinitionStatusInDataStoreService;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.RunTimeService;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.TemplateDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.InternalStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.camunda.bpm.engine.variable.VariableMap;
import org.camunda.bpm.engine.variable.impl.VariableMapImpl;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

public class WASTriggerHandlerTest {

  @InjectMocks private WASTriggerHandler wasTriggerTaskHandler;

  @Mock private TemplateDetailsRepository templateDetailsRepository;

  @Mock private RunTimeService runTimeService;

  @Mock private WASContextHandler contextHandler;

  @Mock private UpdateDefinitionStatusInDataStoreService updateDefinitionStatusInDataStoreService;

  @Mock private DefinitionServiceHelper definitionServiceHelper;

  @Mock private MetricLogger metricLogger;

  @Mock private FeatureManager featureManager;

  private static Map<String, String> schema = new HashMap<>();
  private final static String OWNER_ID = "123";
  private final static String ACTIVITY_ID = "aId";
  private final static String DEFINITION_ID = "dId";
  private final static String HANDLER_ID = "hId";
  private final static String INSTANCE_ID = "iId";
  private final static long OWNERID = 123L;

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
    ReflectionTestUtils.setField(wasTriggerTaskHandler, "metricLogger", metricLogger);
  }

  private static final String parametersSchema =
      TestHelper.readResourceAsString("schema/testData/downgradeParameters.json");

  static {
    schema.put(WorkFlowVariables.PARAMETERS_KEY.getName(), parametersSchema);
    schema.put(WorkflowConstants.INTUIT_REALMID,"123");
  }

  @Test
  public void testExecuteTriggerByPass(){
    Mockito.when(templateDetailsRepository.findAllValidEligibleDefinitions(Mockito.anyList(),
            Mockito.any()))
        .thenReturn(TestHelper.createTemplateDetails());
    VariableMap variableMap = new VariableMapImpl();
    List<Map<String, String>> workflowFilters= List.of(Map.of("recordType","invoice","workflowType","approval"));
    variableMap.put(WorkflowConstants.WORKFLOW_FILTER,workflowFilters);
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .variableMap(variableMap)
            .handlerId("hId")
            .ownerId(123L)
            .build();
    Map<String, Object> resp = wasTriggerTaskHandler.executeAction(workerActionRequest);
    Mockito.verify(templateDetailsRepository, Mockito.times(0)).findAllValidEligibleDefinitions(any(), any());
    Assert.assertNotNull(resp);
    Assert.assertNotNull(resp);
  }

  @Test
  public void testExecuteTriggerSuccess() {
    VariableMap variableMap = new VariableMapImpl();
    Mockito.when(templateDetailsRepository.findAllValidEligibleDefinitions(Mockito.anyList(),
            Mockito.any()))
        .thenReturn(TestHelper.createTemplateDetails());
    Mockito.when(
            runTimeService.processTriggerMessageV2(TestHelper.getTriggerPayloadForDisconnection()))
        .thenReturn(null);
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("123");
    Mockito.when(definitionServiceHelper.getAllDefinitionList(Long.parseLong("123")))
        .thenReturn(null);
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .variableMap(variableMap)
            .handlerId("hId")
            .ownerId(123L)
            .build();
    Map<String, Object> resp = wasTriggerTaskHandler.executeAction(workerActionRequest);
    Mockito.verify(templateDetailsRepository, Mockito.times(1)).findAllValidEligibleDefinitions(any(), any());
    Assert.assertNotNull(resp);
  }

  @Test
  public void testNoTemplateFound() {
    Optional<List<TemplateDetails>> templateDetails = Optional.empty();
    VariableMap variableMap = new VariableMapImpl();
    List<Map<String, String>> workflowFilters= List.of(Map.of("recordType","invoice","workflowType","approval"));
    variableMap.put(WorkflowConstants.WORKFLOW_FILTER,workflowFilters);
    Mockito.when(templateDetailsRepository.findAllValidEligibleDefinitions(Mockito.anyList(),
            Mockito.any()))
            .thenReturn(templateDetails);
    Mockito.when(
            runTimeService.processTriggerMessageV2(TestHelper.getTriggerPayloadForDisconnection()))
            .thenReturn(null);
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn(OWNER_ID);
    Mockito.when(definitionServiceHelper.getAllDefinitionList(Long.parseLong(OWNER_ID)))
            .thenReturn(null);
    WorkerActionRequest workerActionRequest =
            WorkerActionRequest.builder()
                    .activityId(ACTIVITY_ID)
                    .processDefinitionId(DEFINITION_ID)
                    .processInstanceId(INSTANCE_ID)
                    .inputVariables(schema)
                .variableMap(variableMap)
                    .handlerId(HANDLER_ID)
                    .ownerId(OWNERID)
                    .build();
    Map<String, Object> resp = wasTriggerTaskHandler.executeAction(workerActionRequest);
    Assert.assertNotNull(resp);
  }
  @Test
  public void testGetName() {
    Assert.assertEquals(
        "was_triggerHandler",
        wasTriggerTaskHandler.getName().getTaskHandlerName());
  }

  @Test
  public void testLogErrorMetric() {
    Mockito.when(templateDetailsRepository.findAllValidEligibleDefinitions(Mockito.anyList(),
        Mockito.any()))
        .thenThrow(new WorkflowGeneralException(WorkflowError.INVALID_INPUT));
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId(ACTIVITY_ID)
            .processDefinitionId(DEFINITION_ID)
            .processInstanceId(INSTANCE_ID)
            .inputVariables(schema)
            .handlerId(HANDLER_ID)
            .ownerId(OWNERID)
            .build();
    try {
      wasTriggerTaskHandler.execute(workerActionRequest);
      Assert.fail("Exception should be thrown");
    } catch (WorkflowGeneralException workflowGeneralException) {
      Mockito.verify(metricLogger, Mockito.times(1)).logErrorMetric(any(), any(), any());
    }
  }

  @Test
  public void testSelectiveDefinitionDowngradeMarking() {

    Mockito.when(templateDetailsRepository.findAllValidEligibleDefinitions(Mockito.anyList(), Mockito.any()))
        .thenReturn(Optional.of(new ArrayList<>()));
    VariableMap variableMap = new VariableMapImpl();

    schema.put(
        WorkflowConstants.SUBSCRIPTIONS,
        "[{\"offeringId\": \"Intuit.sbe.salsa.default\", \"offeringType\": \"QBO_PLUS\"}, {\"offeringId\": \"Intuit.core.billpay\", \"offeringType\": \"BILLPAY_ELITE\"}]"
    );

    Mockito.when(
        featureManager.getBoolean(
            eq(WorkflowConstants.WORKFLOW_VARIABILITY_DOWNGRADE_FF), anyString()
        )).thenReturn(true);

    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId(ACTIVITY_ID)
            .processDefinitionId(DEFINITION_ID)
            .processInstanceId(INSTANCE_ID)
            .inputVariables(schema)
            .variableMap(variableMap)
            .handlerId(HANDLER_ID)
            .ownerId(OWNERID)
            .build();

    wasTriggerTaskHandler.execute(workerActionRequest);
    Mockito.verify(definitionServiceHelper, Mockito.times(1)).updateInternalStatusAndPublishDomainEvent(any(), eq(
        InternalStatus.MARKED_FOR_DOWNGRADE));
  }

  @Test
  public void testSelectiveDefinitionDowngradeMarking_VariabilityFFDisabled() {

    VariableMap variableMap = new VariableMapImpl();

    Mockito.when(templateDetailsRepository.findAllValidEligibleDefinitions(Mockito.anyList(), Mockito.any()))
        .thenReturn(Optional.of(new ArrayList<>()));

    schema.put(
        WorkflowConstants.SUBSCRIPTIONS,
        "[{\"offeringId\": \"Intuit.sbe.salsa.default\", \"offeringType\": \"QBO_PLUS\"}, {\"offeringId\": \"Intuit.core.billpay\", \"offeringType\": \"BILLPAY_ELITE\"}]"
    );

    Mockito.when(
        featureManager.getBoolean(
            eq(WorkflowConstants.WORKFLOW_VARIABILITY_DOWNGRADE_FF), anyString()
        )).thenReturn(false);

    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId(ACTIVITY_ID)
            .processDefinitionId(DEFINITION_ID)
            .processInstanceId(INSTANCE_ID)
            .inputVariables(schema)
            .handlerId(HANDLER_ID)
            .variableMap(variableMap)
            .ownerId(OWNERID)
            .build();

    Mockito.doNothing().when(updateDefinitionStatusInDataStoreService).updateInternalStatusForDowngrade(any(), any());

    wasTriggerTaskHandler.execute(workerActionRequest);
    Mockito.verify(updateDefinitionStatusInDataStoreService, Mockito.times(1)).updateInternalStatusForDowngrade(any(), any());
  }

  @Test
  public void testAllDefinitionDowngradeMarking() {

    VariableMap variableMap = new VariableMapImpl();
    List<Map<String, String>> workflowFilters= List.of(Map.of("recordType","invoice","workflowType","approval"));
    variableMap.put(WorkflowConstants.WORKFLOW_FILTER,workflowFilters);
    Mockito.when(templateDetailsRepository.findAllValidEligibleDefinitions(Mockito.anyList(), Mockito.any()))
        .thenReturn(Optional.of(new ArrayList<>()));

    schema.remove(WorkflowConstants.SUBSCRIPTIONS);

    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId(ACTIVITY_ID)
            .processDefinitionId(DEFINITION_ID)
            .processInstanceId(INSTANCE_ID)
            .inputVariables(schema)
            .variableMap(variableMap)
            .handlerId(HANDLER_ID)
            .ownerId(OWNERID)
            .build();

    wasTriggerTaskHandler.execute(workerActionRequest);
    Mockito.verify(definitionServiceHelper, Mockito.times(0)).updateInternalStatusAndPublishDomainEvent(any(), eq(
        InternalStatus.MARKED_FOR_DOWNGRADE));
  }
}
