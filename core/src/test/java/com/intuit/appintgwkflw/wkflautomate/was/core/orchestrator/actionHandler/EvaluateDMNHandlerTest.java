package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.INTUIT_REALMID;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.SingleDefinitionDmnEvaluator;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionActivityDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.impl.ProcessDetailsRepoService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.EvaluateRuleRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import org.camunda.bpm.engine.variable.VariableMap;
import org.camunda.bpm.engine.variable.Variables;
import org.camunda.commons.utils.IoUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

public class EvaluateDMNHandlerTest {

  @Mock private DefinitionDetailsRepository definitionDetailsRepository;
  @Mock private ProcessDetailsRepoService processDetailsRepoService;
  @Mock private DefinitionActivityDetailsRepository definitionActivityDetailsRepository;

  @Mock
  private SingleDefinitionDmnEvaluator singleDefinitionDmnEvaluator;
  @Mock
  private MetricLogger metricLogger;

  @InjectMocks private EvaluateDMNHandler evaluateDMNHandler;

  private static final Map<String, Object> schema = new HashMap<>();
  private static final String handlersSchema =
      TestHelper.readResourceAsString("schema/testData/handlersDmn.json");

  private static final String BYO_DMN = "dmn/customWorkflow.dmn";
  private byte[] dmnData =
      IoUtil.inputStreamAsByteArray(
          Objects.requireNonNull(
              EvaluateDMNHandler.class.getClassLoader().getResourceAsStream(BYO_DMN)));

  private static final String BYO_BPMN = "bpmn/customWorkflowDefinition.bpmn";
  private byte[] bpmnData =
      IoUtil.inputStreamAsByteArray(
          Objects.requireNonNull(
              EvaluateDMNHandler.class.getClassLoader().getResourceAsStream(BYO_BPMN)));

  static {
    schema.put(WorkFlowVariables.HANDLER_DETAILS_KEY.getName(), handlersSchema);
    schema.put(INTUIT_REALMID, "1234");
    schema.put(WorkflowConstants.DEFINITION_KEY, "abcd_1234");
  }

  @Before
  public void setUp() {
    MockitoAnnotations.initMocks(this);
    ReflectionTestUtils.setField(evaluateDMNHandler, "metricLogger", metricLogger);
  }

  @Test
  public void testExecuteAction_Success() {
    VariableMap variableMap = Variables.fromMap(schema);
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .handlerId("hId")
            .ownerId(1234L)
            .variableMap(variableMap)
            .build();
    DefinitionDetails dmnDefinitionDetails =
        DefinitionDetails.builder()
            .definitionData(dmnData)
            .definitionId("123")
            .definitionKey("key")
            .build();
    DefinitionDetails bmpnDefinitionDetails =
        DefinitionDetails.builder().definitionData(bpmnData).build();
    Mockito.when(processDetailsRepoService.findByProcessIdWithoutDefinitionData(Mockito.any()))
        .thenReturn(Optional.of(bmpnDefinitionDetails));
    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();
    definitionDetailsList.add(dmnDefinitionDetails);
    Mockito.when(definitionDetailsRepository.findByParentId(Mockito.any()))
        .thenReturn(Optional.of(definitionDetailsList));
    Map<String, Object> map = new HashMap<>();
    LinkedHashMap<String, Boolean> nestedMap = new LinkedHashMap<>();
    nestedMap.put("value", Boolean.TRUE);
    map.put("decisionResult", nestedMap);
//    map.put("value", true);
    when(singleDefinitionDmnEvaluator.evaluateDMN(dmnData, new EvaluateRuleRequest(dmnDefinitionDetails.getDefinitionId(), workerActionRequest.getVariableMap()), true))
        .thenReturn(Collections.singletonList(map));
    when(singleDefinitionDmnEvaluator.dmnResponseConvertor(any())).thenReturn(Collections.singletonList(map));
    Map<String, Object> response = evaluateDMNHandler.executeAction(workerActionRequest);
    Assert.assertNotNull(response);
    Mockito.verify(processDetailsRepoService).findByProcessIdWithoutDefinitionData(any());
    Mockito.verify(definitionDetailsRepository).findByParentId(any());
  }

  @Test
  public void testExecuteActionForMultiStep_Success() {
    VariableMap variableMap = Variables.fromMap(schema);
    WorkerActionRequest workerActionRequest =
            WorkerActionRequest.builder()
                    .activityId("aId")
                    .processDefinitionId("dId")
                    .processInstanceId("iId")
                    .handlerId("hId")
                    .ownerId(1234L)
                    .variableMap(variableMap)
                    .build();
    DefinitionDetails dmnDefinitionDetails =
            DefinitionDetails.builder()
                    .definitionData(dmnData)
                    .definitionId("123")
                    .definitionKey("key")
                    .build();
    DefinitionDetails bmpnDefinitionDetails =
            DefinitionDetails.builder().definitionData(bpmnData).build();
    Mockito.when(processDetailsRepoService.findByProcessIdWithoutDefinitionData(Mockito.any()))
            .thenReturn(Optional.of(bmpnDefinitionDetails));
    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();
    definitionDetailsList.add(dmnDefinitionDetails);
    definitionDetailsList.add(dmnDefinitionDetails);
    Mockito.when(definitionDetailsRepository.findByParentId(Mockito.any()))
            .thenReturn(Optional.of(definitionDetailsList));

    DefinitionActivityDetail parentActivityDetail = TestHelper.mockDefinitionActivityDetails(
        "decisionElement",
        null
    );
    parentActivityDetail.setDefinitionDetails(dmnDefinitionDetails);

    Mockito.when(
            definitionActivityDetailsRepository.findByDefinitionDetailsInAndActivityId(Mockito.any(),
                Mockito.any()))
        .thenReturn(Optional.of(parentActivityDetail));
    Map<String, Object> map = new HashMap<>();
    LinkedHashMap<String, Boolean> nestedMap = new LinkedHashMap<>();
    nestedMap.put("value", Boolean.TRUE);
    map.put("decisionResult", "true");
//    map.put("value", true);
    when(singleDefinitionDmnEvaluator.evaluateDMN(dmnData, new EvaluateRuleRequest(dmnDefinitionDetails.getDefinitionId(), workerActionRequest.getVariableMap()), true))
            .thenReturn(Collections.singletonList(map));
    map.put("decisionResult", nestedMap);
    when(singleDefinitionDmnEvaluator.dmnResponseConvertor(any())).thenReturn(Collections.singletonList(map));
    Map<String, Object> response = evaluateDMNHandler.executeAction(workerActionRequest);
    Assert.assertNotNull(response);
    Mockito.verify(processDetailsRepoService).findByProcessIdWithoutDefinitionData(any());
    Mockito.verify(definitionDetailsRepository).findByParentId(any());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testExecuteActionForMultiStep_Failure() {
    VariableMap variableMap = Variables.fromMap(schema);
    WorkerActionRequest workerActionRequest =
            WorkerActionRequest.builder()
                    .activityId("aId")
                    .processDefinitionId("dId")
                    .processInstanceId("iId")
                    .handlerId("hId")
                    .ownerId(1234L)
                    .variableMap(variableMap)
                    .build();
    DefinitionDetails dmnDefinitionDetails =
            DefinitionDetails.builder()
                    .definitionData(dmnData)
                    .definitionId("123")
                    .definitionKey("key")
                    .build();
    DefinitionDetails bmpnDefinitionDetails =
            DefinitionDetails.builder().definitionData(bpmnData).build();
    Mockito.when(processDetailsRepoService.findByProcessIdWithoutDefinitionData(Mockito.any()))
            .thenReturn(Optional.of(bmpnDefinitionDetails));
    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();
    definitionDetailsList.add(dmnDefinitionDetails);
    definitionDetailsList.add(dmnDefinitionDetails);
    Mockito.when(definitionDetailsRepository.findByParentId(Mockito.any()))
            .thenReturn(Optional.of(definitionDetailsList));
    Map<String, Object> map = new HashMap<>();
    LinkedHashMap<String, Boolean> nestedMap = new LinkedHashMap<>();
    nestedMap.put("value", Boolean.TRUE);
    map.put("decisionResult", "true");
//    map.put("value", true);
    when(singleDefinitionDmnEvaluator.evaluateDMN(dmnData, new EvaluateRuleRequest(dmnDefinitionDetails.getDefinitionId(), workerActionRequest.getVariableMap()), true))
            .thenReturn(Collections.singletonList(map));
    map.put("decisionResult", nestedMap);
    Map<String, Object> response = evaluateDMNHandler.executeAction(workerActionRequest);
    Assert.assertNotNull(response);
    Mockito.verify(processDetailsRepoService).findByProcessIdWithoutDefinitionData(any());
    Mockito.verify(definitionDetailsRepository).findByParentId(any());
  }

  @Test
  public void testExecuteAction_failure() {
    VariableMap variableMap = Variables.fromMap(schema);
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .handlerId("hId")
            .ownerId(1234L)
            .variableMap(variableMap)
            .build();

    DefinitionDetails bmpnDefinitionDetails =
        DefinitionDetails.builder().definitionData(bpmnData).build();
    Mockito.when(processDetailsRepoService.findByProcessIdWithoutDefinitionData(Mockito.any()))
        .thenReturn(Optional.of(bmpnDefinitionDetails));
    Mockito.when(definitionDetailsRepository.findByParentId(Mockito.any()))
        .thenReturn(Optional.ofNullable(null));
    try {
      evaluateDMNHandler.executeAction(workerActionRequest);
      Assert.fail("The above method should throw exception");
    } catch (WorkflowGeneralException e) {
      Assert.assertTrue(e.getMessage().contains(WorkflowError.DMN_NOT_FOUND.getErrorMessage()));
    }
  }

  @Test
  public void testExecuteAction_failure_missing_definition_details() {
    VariableMap variableMap = Variables.fromMap(schema);
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .handlerId("hId")
            .ownerId(1234L)
            .variableMap(variableMap)
            .build();
    Mockito.when(processDetailsRepoService.findByProcessIdWithoutDefinitionData(Mockito.any()))
        .thenReturn(Optional.ofNullable(null));
    try {
      evaluateDMNHandler.executeAction(workerActionRequest);
      Assert.fail("The above method should throw exception");
    } catch (WorkflowGeneralException e) {
      Assert.assertTrue(
          e.getMessage().contains(WorkflowError.DEFINITION_NOT_FOUND.getErrorMessage()));
    }
  }


  @Test
  public void testLogMetric() {
    VariableMap variableMap = Variables.fromMap(schema);
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .handlerId("hId")
            .ownerId(1234L)
            .variableMap(variableMap)
            .build();
    Mockito.when(processDetailsRepoService.findByProcessIdWithoutDefinitionData(Mockito.any()))
        .thenThrow(new WorkflowGeneralException(WorkflowError.INVALID_INPUT));
    try {
      evaluateDMNHandler.execute(workerActionRequest);
      Assert.fail("The above method should throw exception");
    } catch (WorkflowGeneralException e) {
     Assert.assertEquals(e.getWorkflowError(), WorkflowError.INVALID_INPUT);
     Mockito.verify(metricLogger, Mockito.times(1)).logErrorMetric(any(), any(), any());
    }
  }

}