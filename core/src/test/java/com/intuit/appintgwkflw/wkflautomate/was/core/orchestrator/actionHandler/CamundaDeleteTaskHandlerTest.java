package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import static org.mockito.ArgumentMatchers.any;

import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.bpmnengineadapter.BPMNEngineDefinitionServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.impl.ProcessDetailsRepoService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ModelType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import com.intuit.async.execution.request.State;
import com.intuit.v4.Authorization;
import com.intuit.v4.workflows.Definition;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

/** <AUTHOR> */
public class CamundaDeleteTaskHandlerTest {

  @InjectMocks private CamundaDeleteTaskHandler camundaDeleteTaskHandler;

  @Mock private ProcessDetailsRepoService processDetailsRepoService;
  @Mock private DefinitionDetailsRepository definitionDetailsRepository;
  @Mock private BPMNEngineDefinitionServiceRest bpmnEngineDefinitionServiceRest;

  private Definition definition = TestHelper.mockDefinitionEntity();
  @Mock private TemplateDetails bpmnTemplateDetail;
  @Mock
  MetricLogger metricLogger;
  private Authorization authorization = TestHelper.mockAuthorization("123");
  private static Map<String, String> schema = new HashMap<>();
  private static final String parametersSchema =
      TestHelper.readResourceAsString("schema/testData/parameters.json");

  static {
    schema.put(WorkFlowVariables.PARAMETERS_KEY.getName(), parametersSchema);
    schema.put(WorkflowConstants.INTUIT_REALMID, "123");
  }

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
    ReflectionTestUtils.setField(camundaDeleteTaskHandler, "metricLogger", metricLogger);
  }

  @Test
  public void testSuccessNoWorkflow() {
    final State state = new State();
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, "123");
    String processInstanceID = "iId";
    Mockito.when(
            definitionDetailsRepository.findDistinctByDefinitionKeyAndOwnerIdAndModelType(
                123L, ModelType.BPMN))
        .thenReturn(null);
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .ownerId(123L)
            .processDefinitionId("dId")
            .processInstanceId(processInstanceID)
            .inputVariables(schema)
            .build();
    Map<String, Object> response = camundaDeleteTaskHandler.executeAction(workerActionRequest);
    Assert.assertNotNull(response);
    Assert.assertEquals(1, response.size());
    Assert.assertEquals(
        response.entrySet().stream().findFirst().get().getValue(), Boolean.TRUE.toString());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testFailureCase() {
    String processInstanceID = "iId";
    final State state = new State();
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, "123");
    Mockito.doThrow(WorkflowGeneralException.class)
        .when(definitionDetailsRepository)
        .findDistinctByDefinitionKeyAndOwnerIdAndModelType(123L, ModelType.BPMN);
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .ownerId(123L)
            .processDefinitionId("dId")
            .processInstanceId(processInstanceID)
            .inputVariables(schema)
            .build();

    camundaDeleteTaskHandler.executeAction(workerActionRequest);
  }

  @Test
  public void testSuccess() {
    String processInstanceID = "iId";
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .ownerId(123L)
            .processDefinitionId("dId")
            .processInstanceId(processInstanceID)
            .inputVariables(schema)
            .build();
    Mockito.when(
            definitionDetailsRepository.findDistinctByDefinitionKeyAndOwnerIdAndModelType(
                123L, ModelType.BPMN))
        .thenReturn(Arrays.asList(processInstanceID));
    Map<String, Object> response = camundaDeleteTaskHandler.executeAction(workerActionRequest);
    Assert.assertNotNull(response);
    Assert.assertEquals(1, response.size());
    Assert.assertEquals(
        response.entrySet().stream().findFirst().get().getValue(), Boolean.TRUE.toString());
  }

  @Test
  public void testGetName() {
    Assert.assertEquals(
        "was_camundaDeletion", camundaDeleteTaskHandler.getName().getTaskHandlerName());
  }

  @Test
  public void testLogMetric() {
    String processInstanceID = "iId";
    final State state = new State();
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, "123");
    Mockito.doThrow(WorkflowGeneralException.class)
        .when(definitionDetailsRepository)
        .findDistinctByDefinitionKeyAndOwnerIdAndModelType(123L, ModelType.BPMN);
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .ownerId(123L)
            .processDefinitionId("dId")
            .processInstanceId(processInstanceID)
            .inputVariables(schema)
            .build();
    try {
      camundaDeleteTaskHandler.execute(workerActionRequest);
      Assert.fail("Exception Should be thrown");
    } catch (WorkflowGeneralException workflowGeneralException) {
      Assert.assertEquals(workflowGeneralException.getWorkflowError(), WorkflowError.INVALID_INPUT);
      Mockito.verify(metricLogger, Mockito.times(1)).logErrorMetric(any(), any(), any());
    }
  }
}
