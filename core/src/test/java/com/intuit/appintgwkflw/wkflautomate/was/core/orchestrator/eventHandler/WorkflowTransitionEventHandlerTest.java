package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.eventHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.ENTITY_TYPE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType.INVOICE;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.CamundaRequestResponseLoggerConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowEventException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.helper.DomainEventService;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.StateTransitionConfig;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.WorkflowTaskConfig;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.EventHeaderConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ProcessStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventHeaderEntity;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.WorkflowMetaData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.events.ActivityMetaData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.events.WorkflowStateTransitionEvents;
import com.intuit.appintgwkflw.wkflautomate.was.event.api.EventPublisherCapability;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class WorkflowTransitionEventHandlerTest {

  private WorkflowTransitionEventHandler handler;
  @Mock private EventPublisherCapability eventPublisherCapability;
  @Mock private ProcessDetailsRepository processDetailsRepository;
  @Mock private WASContextHandler contextHandler;
  @Mock private DomainEventService domainEventService;
  @Mock private WorkflowTaskConfig workflowTaskConfig;
  @Mock private CamundaRequestResponseLoggerConfig camundaRequestResponseLoggerConfig;
  @Captor ArgumentCaptor<WorkflowStateTransitionEvents> workflowStateTransitionEventsCaptor;
  private Optional<ProcessDetails> processDetails;
  Map<String, String> headers = new HashMap<>();

  @Before
  public void init() {
    MockitoAnnotations.openMocks(this);
    TemplateDetails templateDetails =
        TemplateDetails.builder().templateName("templateName").build();
    DefinitionDetails definitionDetails =
        DefinitionDetails.builder()
            .recordType(INVOICE)
            .templateDetails(templateDetails)
            .definitionId("aa-bb-cc")
            .version(1)
            .definitionKey("testFlow")
            .build();
    processDetails =
        Optional.of(
            ProcessDetails.builder()
                .definitionDetails(definitionDetails)
                .processId("p11")
                .ownerId(123L)
                .recordId("rId")
                .entityVersion(0)
                .build());
    handler =
        new WorkflowTransitionEventHandler(
            eventPublisherCapability,
            processDetailsRepository,
            contextHandler,
            domainEventService,
            workflowTaskConfig);

    headers.put(EventHeaderConstants.ENTITY_ID, "taskId:workerId");
    headers.put(EventHeaderConstants.INTUIT_TID, "tid");
    headers.put(EventHeaderConstants.OWNER_ID, "ownerId");
  }

  @Test
  public void testTransform() {
    WorkflowStateTransitionEvents e =
        WorkflowStateTransitionEvents.builder()
            .businessEntityType("businessEntityType")
            .businessEntityId("businessEntityId")
            .activityType("activityType")
            .build();

    WorkflowStateTransitionEvents result = handler.transform(ObjectConverter.toJson(e));
    Assert.assertNotNull(result);
    Assert.assertEquals("businessEntityId", result.getBusinessEntityId());
    Assert.assertEquals("businessEntityType", result.getBusinessEntityType());
    Assert.assertEquals("activityType", result.getActivityType());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testTransformNull() {
    handler.transform(null);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testTransformIncorrectJson() {
    handler.transform("hello");
  }

  @Test
  public void testExecuteSuccess() {
    ActivityMetaData activityMetaData =
        ActivityMetaData.builder().activityId("activityId").activityName("activityName").build();
    WorkflowStateTransitionEvents event =
        WorkflowStateTransitionEvents.builder()
            .businessEntityType(null)
            .businessEntityId(null)
            .activityType("activityType")
            .activityMetadata(activityMetaData)
            .workflowMetadata(buildMetaData())
            .build();

    when(processDetailsRepository.findByIdWithoutDefinitionData(event.getWorkflowMetadata().getProcessInstanceId()))
        .thenReturn(processDetails);
    StateTransitionConfig stateTransitionResponse = new StateTransitionConfig();
    stateTransitionResponse.setEnableProcessEvents(new HashSet<>());
    when(workflowTaskConfig.getStateTransitionConfig()).thenReturn(stateTransitionResponse);
    handler.execute(event, headers);
    verify(eventPublisherCapability).publish(any(), any(), any());
    Assert.assertNotNull(event.getBusinessEntityType());
  }

  @Test
  public void testExecuteWithEmptyRecord() {
    ActivityMetaData activityMetaData =
        ActivityMetaData.builder().activityId("activityId").activityName("activityName").build();
    WorkflowStateTransitionEvents event =
        WorkflowStateTransitionEvents.builder()
            .businessEntityType(null)
            .businessEntityId(null)
            .activityType("activityType")
            .activityMetadata(activityMetaData)
            .workflowMetadata(buildMetaData())
            .build();

    processDetails.get().getDefinitionDetails().setRecordType(null);
    when(processDetailsRepository.findByIdWithoutDefinitionData(event.getWorkflowMetadata().getProcessInstanceId()))
        .thenReturn(processDetails);
    StateTransitionConfig stateTransitionResponse = new StateTransitionConfig();
    stateTransitionResponse.setEnableProcessEvents(new HashSet<>());
    when(workflowTaskConfig.getStateTransitionConfig()).thenReturn(stateTransitionResponse);
    handler.execute(event, headers);
    verify(eventPublisherCapability).publish(any(), any(), any());
    Assert.assertNull(event.getBusinessEntityType());
  }

  @Test(expected = WorkflowEventException.class)
  public void testExecuteException() {
    ActivityMetaData activityMetaData =
        ActivityMetaData.builder().activityId("activityId").activityName("activityName").build();
    WorkflowStateTransitionEvents event =
        WorkflowStateTransitionEvents.builder()
            .businessEntityType("businessEntityType")
            .businessEntityId("businessEntityId")
            .activityType("activityType")
            .activityMetadata(activityMetaData)
            .workflowMetadata(buildMetaData())
            .build();
    when(processDetailsRepository.findByIdWithoutDefinitionData(event.getWorkflowMetadata().getProcessInstanceId()))
        .thenReturn(processDetails);
    StateTransitionConfig stateTransitionResponse = new StateTransitionConfig();
    stateTransitionResponse.setEnableProcessEvents(new HashSet<>());
    when(workflowTaskConfig.getStateTransitionConfig()).thenReturn(stateTransitionResponse);
    doThrow(new WorkflowEventException("Error"))
        .when(eventPublisherCapability)
        .publish(any(EventHeaderEntity.class), any(WorkflowStateTransitionEvents.class), anyString());
    handler.execute(event, headers);
  }

  @Test
  public void testExecuteWithoutActivityMetaData() {
    WorkflowStateTransitionEvents event =
        WorkflowStateTransitionEvents.builder()
            .businessEntityType(null)
            .businessEntityId(null)
            .activityType("activityType")
            .workflowMetadata(buildMetaData())
            .build();

    when(processDetailsRepository.findByIdWithoutDefinitionData(event.getWorkflowMetadata().getProcessInstanceId()))
        .thenReturn(processDetails);
    StateTransitionConfig stateTransitionResponse = new StateTransitionConfig();
    stateTransitionResponse.setEnableProcessEvents(new HashSet<>());
    when(workflowTaskConfig.getStateTransitionConfig()).thenReturn(stateTransitionResponse);
    handler.execute(event, headers);
    verify(eventPublisherCapability).publish(any(), any(), any());
  }

  @Test
  public void shouldThrowExceptionMigrationEvent() {
    WorkflowMetaData workflowMetaData =
        WorkflowMetaData.builder()
            .workflowName("workflowName")
            .processInstanceId("processInstanceId")
            .processDefinitionId("bb-cc-dd")
            .build();
    WorkflowStateTransitionEvents event =
        WorkflowStateTransitionEvents.builder()
            .businessEntityType(null)
            .businessEntityId(null)
            .eventType("migrate")
            .workflowMetadata(workflowMetaData)
            .build();

    when(processDetailsRepository.findByIdWithoutDefinitionData(event.getWorkflowMetadata().getProcessInstanceId()))
        .thenReturn(processDetails);

    when(processDetailsRepository.updateDefinitionId(
            workflowMetaData.getProcessDefinitionId(), workflowMetaData.getProcessInstanceId()))
        .thenThrow(new RuntimeException("Repository Exception"));

    try {
      handler.execute(event, headers);
      Assert.fail();
    } catch (Exception e) {
      Assert.assertEquals("Repository Exception", e.getMessage());
    }

    verify(domainEventService, never()).publishMigrationEvent(Mockito.any());
  }

  @Test
  public void testMigrationEvent() {
    WorkflowMetaData workflowMetaData =
        WorkflowMetaData.builder()
            .workflowName("workflowName")
            .processInstanceId("processInstanceId")
            .processDefinitionId("bb-cc-dd")
            .build();
    WorkflowStateTransitionEvents event =
        WorkflowStateTransitionEvents.builder()
            .businessEntityType(null)
            .businessEntityId(null)
            .eventType("migrate")
            .workflowMetadata(workflowMetaData)
            .build();

    when(processDetailsRepository.findByIdWithoutDefinitionData(event.getWorkflowMetadata().getProcessInstanceId()))
        .thenReturn(processDetails);
    StateTransitionConfig stateTransitionResponse = new StateTransitionConfig();
    stateTransitionResponse.setEnableProcessEvents(new HashSet<>());
    when(workflowTaskConfig.getStateTransitionConfig()).thenReturn(stateTransitionResponse);
    when(eventPublisherCapability.publish(any(), workflowStateTransitionEventsCaptor.capture(), any()))
        .thenReturn(null);
    handler.execute(event, headers);

    verify(eventPublisherCapability).publish(any(), any(), any());
    verify(processDetailsRepository).updateDefinitionId(anyString(), anyString());

    Assert.assertNotNull(workflowStateTransitionEventsCaptor.getValue().getMigratedFrom());

    Assert.assertEquals(
        "aa-bb-cc",
        workflowStateTransitionEventsCaptor.getValue().getMigratedFrom().getProcessDefinitionId());

    Assert.assertEquals(
        1,
        workflowStateTransitionEventsCaptor
            .getValue()
            .getMigratedFrom()
            .getWorkflowVersion()
            .intValue());
  }

  @Test
  public void testMigrationEventWithDomainEvents() {
    WorkflowMetaData workflowMetaData =
        WorkflowMetaData.builder()
            .workflowName("workflowName")
            .processInstanceId("processInstanceId")
            .processDefinitionId("bb-cc-dd")
            .build();
    WorkflowStateTransitionEvents event =
        WorkflowStateTransitionEvents.builder()
            .businessEntityType(null)
            .businessEntityId(null)
            .eventType("migrate")
            .workflowMetadata(workflowMetaData)
            .build();

    when(processDetailsRepository.findByIdWithoutDefinitionData(event.getWorkflowMetadata().getProcessInstanceId()))
        .thenReturn(processDetails);
    when(processDetailsRepository.updateDefinitionId("bb-cc-dd", "processInstanceId"))
        .thenReturn(1);
    StateTransitionConfig stateTransitionResponse = new StateTransitionConfig();
    stateTransitionResponse.setEnableProcessEvents(new HashSet<>());
    when(workflowTaskConfig.getStateTransitionConfig()).thenReturn(stateTransitionResponse);
    when(eventPublisherCapability.publish(any(), workflowStateTransitionEventsCaptor.capture(), any()))
        .thenReturn(null);
    handler.execute(event, headers);

    verify(eventPublisherCapability).publish(any(), any(), any());
    verify(processDetailsRepository).updateDefinitionId(anyString(), anyString());

    Assert.assertNotNull(workflowStateTransitionEventsCaptor.getValue().getMigratedFrom());

    Assert.assertEquals(
        "aa-bb-cc",
        workflowStateTransitionEventsCaptor.getValue().getMigratedFrom().getProcessDefinitionId());

    Assert.assertEquals(
        1,
        workflowStateTransitionEventsCaptor
            .getValue()
            .getMigratedFrom()
            .getWorkflowVersion()
            .intValue());
  }

  @Test
  public void testDuplicateMigrationEvent() {
    WorkflowMetaData workflowMetaData =
        WorkflowMetaData.builder()
            .workflowName("workflowName")
            .processInstanceId("processInstanceId")
            .processDefinitionId("aa-bb-cc")
            .build();
    WorkflowStateTransitionEvents event =
        WorkflowStateTransitionEvents.builder()
            .businessEntityType(null)
            .businessEntityId(null)
            .eventType("migrate")
            .workflowMetadata(workflowMetaData)
            .build();

    when(processDetailsRepository.findByIdWithoutDefinitionData(event.getWorkflowMetadata().getProcessInstanceId()))
        .thenReturn(processDetails);
    handler.execute(event, headers);

    verify(eventPublisherCapability, Mockito.times(0)).publish(any(), any());

    verify(processDetailsRepository, Mockito.times(0)).updateDefinitionId(anyString(), anyString());
  }

  @Test(expected = RuntimeException.class)
  public void testMigrationEventWithDatabaseException() {
    WorkflowStateTransitionEvents event =
        WorkflowStateTransitionEvents.builder()
            .businessEntityType(null)
            .businessEntityId(null)
            .eventType("migrate")
            .workflowMetadata(buildMetaData())
            .build();

    when(processDetailsRepository.findByIdWithoutDefinitionData(event.getWorkflowMetadata().getProcessInstanceId()))
        .thenReturn(processDetails);
    handler.execute(event, headers);
  }

  @Test
  public void testMigrationEventWithPublishException() {
    WorkflowMetaData workflowMetaData =
        WorkflowMetaData.builder()
            .workflowName("workflowName")
            .processInstanceId("processInstanceId")
            .processDefinitionId("bb-cc-dd")
            .build();
    WorkflowStateTransitionEvents event =
        WorkflowStateTransitionEvents.builder()
            .businessEntityType(null)
            .businessEntityId(null)
            .eventType("migrate")
            .workflowMetadata(workflowMetaData)
            .build();

    when(processDetailsRepository.findByIdWithoutDefinitionData(event.getWorkflowMetadata().getProcessInstanceId()))
        .thenReturn(processDetails);
    StateTransitionConfig stateTransitionResponse = new StateTransitionConfig();
    stateTransitionResponse.setEnableProcessEvents(new HashSet<>());
    when(workflowTaskConfig.getStateTransitionConfig()).thenReturn(stateTransitionResponse);
    when(eventPublisherCapability.publish(any(), workflowStateTransitionEventsCaptor.capture(), any()))
        .thenThrow(new RuntimeException("Could not publish"));
    RuntimeException ex = null;
    try {
      handler.execute(event, headers);
    } catch (RuntimeException rex) {
      ex = rex;
    }

    verify(eventPublisherCapability).publish(any(), any(), any());
    verify(processDetailsRepository).updateDefinitionId(anyString(), anyString());

    Assert.assertNotNull(ex);
  }

  @Test
  public void testEventEntityType() {
    Assert.assertEquals(
        WorkflowConstants.WORKFLOW_TRANSITION_EVENTS, handler.getName().getEntityType());
  }

  private WorkflowMetaData buildMetaData() {
    return WorkflowMetaData.builder()
        .workflowName("workflowName")
        .processInstanceId("processInstanceId")
        .workflowOwnerId("workflowownerId")
        .build();
  }

  @Test
  public void testExecuteSuccessProcessCancelled() {
    WorkflowStateTransitionEvents event =
        WorkflowStateTransitionEvents.builder()
            .eventType("cancelled")
            .activityType("PROCESS")
            .workflowMetadata(buildMetaData())
            .build();

    when(processDetailsRepository.findByIdWithoutDefinitionData(event.getWorkflowMetadata().getProcessInstanceId()))
        .thenReturn(processDetails);
    StateTransitionConfig stateTransitionResponse = new StateTransitionConfig();
    stateTransitionResponse.setEnableProcessEvents(new HashSet<>());
    when(workflowTaskConfig.getStateTransitionConfig()).thenReturn(stateTransitionResponse);
    handler.execute(event, headers);
    verify(domainEventService).updateStatus(any(), any(), any(), any(), any());
  }

  @Test
  public void testExecuteSuccessProcessEnd() {
    WorkflowStateTransitionEvents event =
        WorkflowStateTransitionEvents.builder()
            .eventType("end")
            .businessEntityType(null)
            .businessEntityId(null)
            .activityType("PROCESS")
            .workflowMetadata(buildMetaData())
            .build();

    when(processDetailsRepository.findByIdWithoutDefinitionData(event.getWorkflowMetadata().getProcessInstanceId()))
        .thenReturn(processDetails);
    StateTransitionConfig stateTransitionResponse = new StateTransitionConfig();
    stateTransitionResponse.setEnableProcessEvents(new HashSet<>());
    when(workflowTaskConfig.getStateTransitionConfig()).thenReturn(stateTransitionResponse);
    handler.execute(event, headers);
    verify(domainEventService).updateStatus(any(), any(), any(), any(), any());
  }

  @Test
  public void shouldNotPublishDomainEvent() {
    WorkflowStateTransitionEvents event =
        WorkflowStateTransitionEvents.builder()
            .eventType("start")
            .businessEntityType(null)
            .businessEntityId(null)
            .activityType("PROCESS")
            .workflowMetadata(buildMetaData())
            .build();

    when(processDetailsRepository.findByIdWithoutDefinitionData(event.getWorkflowMetadata().getProcessInstanceId()))
        .thenReturn(processDetails);
    StateTransitionConfig stateTransitionResponse = new StateTransitionConfig();
    stateTransitionResponse.setEnableProcessEvents(new HashSet<>());
    when(workflowTaskConfig.getStateTransitionConfig()).thenReturn(stateTransitionResponse);
    handler.execute(event, headers);
    verify(domainEventService, times(0)).updateStatus(any(), any(), any(), any(), any());
  }

  @Test
  public void testExecuteSuccessActivityEnd() {
    WorkflowStateTransitionEvents event =
        WorkflowStateTransitionEvents.builder()
            .eventType("end")
            .businessEntityType(null)
            .businessEntityId(null)
            .activityType("startEvent")
            .workflowMetadata(buildMetaData())
            .build();

    when(processDetailsRepository.findByIdWithoutDefinitionData(event.getWorkflowMetadata().getProcessInstanceId()))
        .thenReturn(processDetails);
    StateTransitionConfig stateTransitionResponse = new StateTransitionConfig();
    stateTransitionResponse.setEnableProcessEvents(new HashSet<>());
    when(workflowTaskConfig.getStateTransitionConfig()).thenReturn(stateTransitionResponse);
    handler.execute(event, headers);
    verify(eventPublisherCapability).publish(any(), any(), any());
  }

  @Test
  public void testExecuteSuccessProcessEndEventEnabled() {
    WorkflowStateTransitionEvents event =
        WorkflowStateTransitionEvents.builder()
            .eventType("end")
            .businessEntityType(null)
            .businessEntityId(null)
            .activityType("startEvent")
            .workflowMetadata(buildMetaData())
            .build();

    when(processDetailsRepository.findByIdWithoutDefinitionData(event.getWorkflowMetadata().getProcessInstanceId()))
        .thenReturn(processDetails);
    StateTransitionConfig stateTransitionResponse = new StateTransitionConfig();
    stateTransitionResponse.setEnableProcessEvents(Set.of("end"));
    when(workflowTaskConfig.getStateTransitionConfig()).thenReturn(stateTransitionResponse);
    handler.execute(event, headers);
    verify(eventPublisherCapability).publish(any(), any(), any());
  }

  @Test
  public void testExecuteSuccessProcessEndEventEnabledAlreadyEnded() {
    ProcessDetails processDetails1 = processDetails.get();
    processDetails1.setProcessStatus(ProcessStatus.ENDED);
    Optional<ProcessDetails> processDetailsOptional = Optional.ofNullable(processDetails1);

    WorkflowStateTransitionEvents event =
        WorkflowStateTransitionEvents.builder()
            .eventType("end")
            .businessEntityType(null)
            .businessEntityId(null)
            .activityType("startEvent")
            .workflowMetadata(buildMetaData())
            .build();

    when(processDetailsRepository.findByIdWithoutDefinitionData(event.getWorkflowMetadata().getProcessInstanceId()))
        .thenReturn(processDetailsOptional);
    StateTransitionConfig stateTransitionResponse = new StateTransitionConfig();
    stateTransitionResponse.setEnableProcessEvents(Set.of("end"));
    when(workflowTaskConfig.getStateTransitionConfig()).thenReturn(stateTransitionResponse);
    handler.execute(event, headers);
    verify(eventPublisherCapability).publish(any(), any(), any());
  }

  @Test
  public void testExecuteSuccessProcessCancelledEventEnabled() {
    WorkflowStateTransitionEvents event =
        WorkflowStateTransitionEvents.builder()
            .eventType("cancelled")
            .businessEntityType(null)
            .businessEntityId(null)
            .activityType("startEvent")
            .workflowMetadata(buildMetaData())
            .build();

    when(processDetailsRepository.findByIdWithoutDefinitionData(event.getWorkflowMetadata().getProcessInstanceId()))
        .thenReturn(processDetails);
    StateTransitionConfig stateTransitionResponse = new StateTransitionConfig();
    stateTransitionResponse.setEnableProcessEvents(Set.of("cancelled"));
    when(workflowTaskConfig.getStateTransitionConfig()).thenReturn(stateTransitionResponse);
    handler.execute(event, headers);
    verify(eventPublisherCapability).publish(any(), any(), any());
  }

  @Test
  public void testDomainActivityRuntimeEventNotPublishOnCreated() {
    WorkflowStateTransitionEvents event =
        WorkflowStateTransitionEvents.builder()
            .eventType("created")
            .businessEntityType(null)
            .businessEntityId(null)
            .activityType("startEvent")
            .workflowMetadata(buildMetaData())
            .build();

    when(processDetailsRepository.findByIdWithoutDefinitionData(event.getWorkflowMetadata().getProcessInstanceId()))
        .thenReturn(processDetails);
    StateTransitionConfig stateTransitionResponse = new StateTransitionConfig();
    stateTransitionResponse.setEnableProcessEvents(Set.of("cancelled"));
    when(workflowTaskConfig.getStateTransitionConfig()).thenReturn(stateTransitionResponse);
    handler.execute(event, headers);
    verify(eventPublisherCapability).publish(any(), any(), any());
  }

  @Test
  public void testDomainActivityRuntimeEventPublishOnStart() {
    WorkflowStateTransitionEvents event =
        WorkflowStateTransitionEvents.builder()
            .eventType("start")
            .businessEntityType(null)
            .businessEntityId(null)
            .activityType("startEvent")
            .workflowMetadata(buildMetaData())
            .build();

    when(processDetailsRepository.findByIdWithoutDefinitionData(event.getWorkflowMetadata().getProcessInstanceId()))
        .thenReturn(processDetails);
    StateTransitionConfig stateTransitionResponse = new StateTransitionConfig();
    stateTransitionResponse.setEnableProcessEvents(Set.of("cancelled"));
    when(workflowTaskConfig.getStateTransitionConfig()).thenReturn(stateTransitionResponse);
    handler.execute(event, headers);
    verify(eventPublisherCapability).publish(any(), any(), any());
    verify(domainEventService).publishActivityRuntimeEvent(any(), any(), any());
  }

  @Test
  public void testDomainActivityRuntimeEventPublishOnStartWithStateTransitionConfigDisabled_Start() {
    WorkflowStateTransitionEvents event =
        WorkflowStateTransitionEvents.builder()
            .eventType("start")
            .businessEntityType(null)
            .businessEntityId(null)
            .activityType("startEvent")
            .workflowMetadata(buildMetaData())
            .build();

    when(processDetailsRepository.findByIdWithoutDefinitionData(event.getWorkflowMetadata().getProcessInstanceId()))
        .thenReturn(processDetails);
    StateTransitionConfig stateTransitionResponse = new StateTransitionConfig();
    stateTransitionResponse.setEnableProcessEvents(Set.of("cancelled"));
    stateTransitionResponse.setDisableActivityEvents(Set.of("start"));
    when(workflowTaskConfig.getStateTransitionConfig()).thenReturn(stateTransitionResponse);
    handler.execute(event, headers);
    verify(domainEventService).publishActivityRuntimeEvent(any(), any(), any());
  }

  @Test
  public void testDomainActivityRuntimeEventPublishOnStartWithStateTransitionConfigDisabled_End() {
    WorkflowStateTransitionEvents event =
            WorkflowStateTransitionEvents.builder()
                    .eventType("start")
                    .businessEntityType(null)
                    .businessEntityId(null)
                    .activityType("startEvent")
                    .workflowMetadata(buildMetaData())
                    .build();

    when(processDetailsRepository.findByIdWithoutDefinitionData(event.getWorkflowMetadata().getProcessInstanceId()))
            .thenReturn(processDetails);
    StateTransitionConfig stateTransitionResponse = new StateTransitionConfig();
    stateTransitionResponse.setEnableProcessEvents(Set.of("cancelled"));
    stateTransitionResponse.setDisableActivityEvents(Set.of("end"));
    when(workflowTaskConfig.getStateTransitionConfig()).thenReturn(stateTransitionResponse);
    handler.execute(event, headers);
    verify(domainEventService).publishActivityRuntimeEvent(any(), any(), any());
  }

  @Test
  public void
      testDomainActivityRuntimeEventPublishOnStartWithStateTransitionConfigDisabled_StartAndEnd() {
    WorkflowStateTransitionEvents event =
        WorkflowStateTransitionEvents.builder()
            .eventType("start")
            .businessEntityType(null)
            .businessEntityId(null)
            .activityType("startEvent")
            .workflowMetadata(buildMetaData())
            .build();

    when(processDetailsRepository.findByIdWithoutDefinitionData(event.getWorkflowMetadata().getProcessInstanceId()))
        .thenReturn(processDetails);
    StateTransitionConfig stateTransitionResponse = new StateTransitionConfig();
    stateTransitionResponse.setEnableProcessEvents(Set.of("cancelled"));
    stateTransitionResponse.setDisableActivityEvents(Set.of("end", "start"));
    when(workflowTaskConfig.getStateTransitionConfig()).thenReturn(stateTransitionResponse);
    handler.execute(event, headers);
    verify(domainEventService).publishActivityRuntimeEvent(any(), any(), any());
  }

  @Test
  public void testEnrichWorkflowStateTransitionEvent()
      throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
    Method method = WorkflowTransitionEventHandler.class.getDeclaredMethod("enrichWorkflowStateTransitionEvent", WorkflowStateTransitionEvents.class, ProcessDetails.class);
    method.setAccessible(true);

    ProcessDetails process = ProcessDetails.builder().recordId("123").ownerId(123L).definitionDetails(new DefinitionDetails()).build();

    Map<String, Object> variablesMap = new HashMap<>();
    variablesMap.put(ENTITY_TYPE, INVOICE.getRecordType());

    ActivityMetaData activityMetaData = ActivityMetaData.builder().variables(variablesMap).build();
    WorkflowStateTransitionEvents event = WorkflowStateTransitionEvents.builder().workflowMetadata(new WorkflowMetaData()).activityMetadata(activityMetaData).build();

    method.invoke(handler, event, process);

    assertEquals(INVOICE.getRecordType(), event.getBusinessEntityType());



  }
}
