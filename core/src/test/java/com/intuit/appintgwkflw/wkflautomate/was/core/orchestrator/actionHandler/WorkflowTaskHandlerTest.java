package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables.RESPONSE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.INTUIT_REALMID;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.UNDERSCORE;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.ExternalTaskRetryConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowNonRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.retry.WorkerRetryHelper;
import com.intuit.appintgwkflw.wkflautomate.was.common.retrystrategy.RetryStrategyName;
import com.intuit.appintgwkflw.wkflautomate.was.core.config.HandlerConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.common.IncidentTaskManager;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.request.WorkflowTaskHandlerInput;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.TaskHandlerName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Parameter;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.camunda.bpm.engine.variable.VariableMap;
import org.camunda.bpm.engine.variable.impl.VariableMapImpl;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(MockitoJUnitRunner.class)
public class WorkflowTaskHandlerTest {

  @Mock
  private MetricLogger metricLogger;

  @Mock
  private WorkerRetryHelper workerRetryHelper;

  @Mock
  private IncidentTaskManager incidentTaskManager;

  @Mock
  private HandlerConfig handlerConfig;

  private static Map<String, String> schema = new HashMap<>();

  private static Map<String, String> extensionProperties = new HashMap<>();

  static {
    schema.put(INTUIT_REALMID, "1234");
    
    extensionProperties.put(WorkFlowVariables.TASK_DETAILS_KEY.getName(), "{}");
  }

  private static class DummyWorkflowTaskHandler extends WorkflowTaskHandler {
    public DummyWorkflowTaskHandler(MetricLogger metricLogger,
                                    WorkerRetryHelper workerRetryHelper,
                                    HandlerConfig handlerConfig) {
      super(metricLogger, null, workerRetryHelper, handlerConfig);
    }

    @Override
    public TaskHandlerName getName() {
      return TaskHandlerName.DUMMY_ACTION_HANDLER;
    }

    @Override
    protected <T> Map<String, Object> executeAction(T inputRequest) {
      return new HashMap<>();
    }

    @Override
    protected void logErrorMetric(Exception exception, WorkerActionRequest workerActionRequest) {
      // No op for tests
    }
  }

  private DummyWorkflowTaskHandler workflowTaskHandler;

  @Before
  public void setup() {
    workflowTaskHandler = new DummyWorkflowTaskHandler(metricLogger, workerRetryHelper, handlerConfig);
  }

  /**
   * Non-Retriable exception to be re-thrown when taskDetails not present.
   */
  @Test(expected = WorkflowNonRetriableException.class)
  public void taskDetail_notPresent_NonRetriableException() {
    WorkflowTaskHandler taskHandler = new WorkflowTaskHandler() {

      @Override
      public TaskHandlerName getName() {
        // TODO Auto-generated method stub
        return null;
      }

      @Override
      protected <T> Map<String, Object> executeAction(T inputRequest) {
        // TODO Auto-generated method stub
        throw new WorkflowNonRetriableException(WorkflowError.ACTION_NOT_FOUND);
      }

      @Override
      protected void logErrorMetric(Exception exception,
          WorkerActionRequest workerActionRequest) {
        metricLogger.logErrorMetric(MetricName.INVOKE_DUZZIT, Type.APP_CONNECT_METRIC, exception);
      }
    };
    MetricLogger metricLogger = Mockito.mock(MetricLogger.class);
    Mockito.doNothing().when(metricLogger)
        .logErrorMetric(any(), any(), any());
    taskHandler.metricLogger = metricLogger;

    WorkerActionRequest actionRequest = WorkerActionRequest.builder()
        .extensionProperties(new HashMap<>()).inputVariables(new HashMap<>()).build();

    taskHandler.execute(actionRequest);
  }

  /**
   * General exception with actual WorkflowError re-thrown
   */
  @Test
  public void taskDetail_notPresent_GeneralException_WorkflowError() {
    WorkflowTaskHandler taskHandler = new WorkflowTaskHandler() {

      @Override
      public TaskHandlerName getName() {
        // TODO Auto-generated method stub
        return null;
      }

      @Override
      protected <T> Map<String, Object> executeAction(T inputRequest) {
        // TODO Auto-generated method stub
        throw new WorkflowRetriableException(WorkflowError.ACTION_NOT_FOUND);
      }

      @Override
      protected void logErrorMetric(Exception exception,
          WorkerActionRequest workerActionRequest) {
      metricLogger.logErrorMetric(MetricName.INVOKE_DUZZIT, Type.APP_CONNECT_METRIC, exception);
      }
    };
   MetricLogger metricLogger = Mockito.mock(MetricLogger.class);
    Mockito.doNothing().when(metricLogger)
        .logErrorMetric(any(), any(), any());
    taskHandler.metricLogger = metricLogger;

    WorkerActionRequest actionRequest = WorkerActionRequest.builder()
        .extensionProperties(new HashMap<>()).inputVariables(new HashMap<>()).build();

    try {
      taskHandler.execute(actionRequest);
    } catch (Exception ex) {
      Assert.assertTrue(ex instanceof WorkflowGeneralException);
      Assert.assertEquals(WorkflowError.ACTION_NOT_FOUND,
          ((WorkflowGeneralException) ex).getWorkflowError());
    }
  }

  /**
   * General exception with default WorkflowError re-thrown
   */
  @Test
  public void taskDetail_notPresent_GeneralException_WorkflowError_Default() {
    WorkflowTaskHandler taskHandler = new WorkflowTaskHandler() {

      @Override
      public TaskHandlerName getName() {
        // TODO Auto-generated method stub
        return null;
      }

      @Override
      protected <T> Map<String, Object> executeAction(T inputRequest) {
        // TODO Auto-generated method stub
        throw new RuntimeException("");
      }

      @Override
      protected void logErrorMetric(Exception exception,
          WorkerActionRequest workerActionRequest) {
        metricLogger.logErrorMetric(MetricName.INVOKE_DUZZIT, Type.APP_CONNECT_METRIC, exception);
      }
    };
    MetricLogger metricLogger = Mockito.mock(MetricLogger.class);
    Mockito.doNothing().when(metricLogger)
        .logErrorMetric(any(), any(), any());
    taskHandler.metricLogger = metricLogger;

    WorkerActionRequest actionRequest = WorkerActionRequest.builder()
        .extensionProperties(new HashMap<>()).inputVariables(new HashMap<>()).build();

    try {
      taskHandler.execute(actionRequest);
    } catch (Exception ex) {
      Assert.assertTrue(ex instanceof WorkflowGeneralException);
      Assert.assertEquals(WorkflowError.TASK_DETAILS_NOT_FOUND,
          ((WorkflowGeneralException) ex).getWorkflowError());
    }
  }


  /**
   * Non-Retriable exception to be re-thrown when fatal set true.
   */
  @Test(expected = WorkflowNonRetriableException.class)
  public void taskDetail_Present_fatal_true_nonRetriableException() {
    WorkflowTaskHandler taskHandler = new WorkflowTaskHandler(null, incidentTaskManager, workerRetryHelper, handlerConfig) {

      @Override
      public TaskHandlerName getName() {
        // TODO Auto-generated method stub
        return null;
      }

      @Override
      protected <T> Map<String, Object> executeAction(T inputRequest) {
        // TODO Auto-generated method stub
        throw new WorkflowNonRetriableException(WorkflowError.ACTION_NOT_FOUND);
      }

      @Override
      protected void logErrorMetric(Exception exception,
          WorkerActionRequest workerActionRequest) {
        metricLogger.logErrorMetric(MetricName.INVOKE_DUZZIT, Type.APP_CONNECT_METRIC, exception);
      }
    };
    MetricLogger metricLogger = Mockito.mock(MetricLogger.class);
    Mockito.doNothing().when(metricLogger)
        .logErrorMetric(any(), any(), any());
    taskHandler.metricLogger = metricLogger;
    Mockito.when(workerRetryHelper.getRetryConfig(any(), any(), any())).thenReturn(null);

    Map<String, String> extensionProperties = new HashMap<>();
    extensionProperties.put(WorkFlowVariables.TASK_DETAILS_KEY.getName(), "{\"fatal\":true}");
    WorkerActionRequest actionRequest = WorkerActionRequest.builder()
        .extensionProperties(extensionProperties).inputVariables(new HashMap<>()).build();
    taskHandler.execute(actionRequest);
  }

  /**
   * Retriable exception to be re-thrown when fatal is true.
   */
  @Test(expected = WorkflowRetriableException.class)
  public void taskDetail_Present_fatal_true_RetriableException() {

    WorkflowTaskHandler taskHandler = new WorkflowTaskHandler(null, incidentTaskManager, workerRetryHelper, handlerConfig) {

      @Override
      public TaskHandlerName getName() {
        // TODO Auto-generated method stub
        return null;
      }

      @Override
      protected <T> Map<String, Object> executeAction(T inputRequest) {
        // TODO Auto-generated method stub
        throw new WorkflowRetriableException(WorkflowError.ACTION_NOT_FOUND);
      }

      @Override
      protected void logErrorMetric(Exception exception,
          WorkerActionRequest workerActionRequest) {
        metricLogger.logErrorMetric(MetricName.INVOKE_DUZZIT, Type.APP_CONNECT_METRIC, exception);
      }
    };
    MetricLogger metricLogger = Mockito.mock(MetricLogger.class);
    Mockito.doNothing().when(metricLogger)
        .logErrorMetric(any(), any(), any());
    taskHandler.metricLogger = metricLogger;
    Mockito.when(workerRetryHelper.getRetryConfig(any(), any(), any())).thenReturn(null);

    Map<String, String> extensionProperties = new HashMap<>();
    extensionProperties.put(WorkFlowVariables.TASK_DETAILS_KEY.getName(), "{\"fatal\":true}");
    WorkerActionRequest actionRequest = WorkerActionRequest.builder()
        .extensionProperties(extensionProperties).inputVariables(new HashMap<>()).build();
    taskHandler.execute(actionRequest);
  }


  /**
   * Non-Retriable exception to be swallowed when fatal set false.
   */
  @Test
  public void taskDetail_Present_fatal_false_nonRetriableException() {

    WorkflowTaskHandler taskHandler = new WorkflowTaskHandler(null, incidentTaskManager, workerRetryHelper, handlerConfig) {

      @Override
      public TaskHandlerName getName() {
        // TODO Auto-generated method stub
        return null;
      }

      @Override
      protected <T> Map<String, Object> executeAction(T inputRequest) {
        // TODO Auto-generated method stub
        throw new WorkflowNonRetriableException(WorkflowError.ACTION_NOT_FOUND);
      }

      @Override
      protected void logErrorMetric(Exception exception,
          WorkerActionRequest workerActionRequest) {
        metricLogger.logErrorMetric(MetricName.INVOKE_DUZZIT, Type.APP_CONNECT_METRIC, exception);
      }
    };
    MetricLogger metricLogger = Mockito.mock(MetricLogger.class);
    Mockito.doNothing().when(metricLogger)
        .logErrorMetric(any(), any(), any());
    taskHandler.metricLogger = metricLogger;
    Mockito.when(workerRetryHelper.getRetryConfig(any(), any(), any())).thenReturn(null);
    IncidentTaskManager incidentTaskManager = Mockito.mock(IncidentTaskManager.class);
    Mockito.doNothing().when(incidentTaskManager).executeFailedCommand(any());
    ReflectionTestUtils.setField(taskHandler, "incidentTaskManager", incidentTaskManager);
    Map<String, String> extensionProperties = new HashMap<>();
    extensionProperties.put(WorkFlowVariables.TASK_DETAILS_KEY.getName(), "{\"fatal\":false}");
    WorkerActionRequest actionRequest = WorkerActionRequest.builder()
        .activityId("act1")
        .extensionProperties(extensionProperties).inputVariables(new HashMap<>()).build();
    Map<String, Object> responseMap = (Map) taskHandler.execute(actionRequest);
    Assert.assertNotNull(responseMap);
    Assert.assertEquals(1, responseMap.size());
  }

  /**
   * Retriable exception to be swallowed when fatal set false.
   */
  @Test
  public void taskDetail_Present_fatal_false_RetriableException() {

    WorkflowTaskHandler taskHandler = new WorkflowTaskHandler(null, incidentTaskManager, workerRetryHelper, handlerConfig) {

      @Override
      public TaskHandlerName getName() {
        // TODO Auto-generated method stub
        return null;
      }

      @Override
      protected <T> Map<String, Object> executeAction(T inputRequest) {
        // TODO Auto-generated method stub
        throw new WorkflowRetriableException(WorkflowError.ACTION_NOT_FOUND);
      }

      @Override
      protected void logErrorMetric(Exception exception,
          WorkerActionRequest workerActionRequest) {
        metricLogger.logErrorMetric(MetricName.INVOKE_DUZZIT, Type.APP_CONNECT_METRIC, exception);
      }
    };
    MetricLogger metricLogger = Mockito.mock(MetricLogger.class);
    Mockito.doNothing().when(metricLogger)
        .logErrorMetric(any(), any(), any());
    taskHandler.metricLogger = metricLogger;
    Mockito.when(workerRetryHelper.getRetryConfig(any(), any(), any())).thenReturn(null);
    IncidentTaskManager incidentTaskManager = Mockito.mock(IncidentTaskManager.class);
    Mockito.doNothing().when(incidentTaskManager).executeFailedCommand(any());
    ReflectionTestUtils.setField(taskHandler, "incidentTaskManager", incidentTaskManager);
    Map<String, String> extensionProperties = new HashMap<>();
    extensionProperties.put(WorkFlowVariables.TASK_DETAILS_KEY.getName(), "{\"fatal\":false}");
    WorkerActionRequest actionRequest = WorkerActionRequest.builder()
        .activityId("act1")
        .extensionProperties(extensionProperties).inputVariables(new HashMap<>()).build();
    Map<String, Object> responseMap = (Map) taskHandler.execute(actionRequest);
    Assert.assertNotNull(responseMap);
    Assert.assertEquals(1, responseMap.size());
  }

  @Test()
  public void test_throwExceptionOnExecuteAction_externalTaskRetryConfigNotPresent() {

    WorkflowTaskHandler taskHandler = new WorkflowTaskHandler(Mockito.mock(MetricLogger.class), incidentTaskManager, workerRetryHelper, handlerConfig) {

      @Override
      public TaskHandlerName getName() {
        return null;
      }

      @Override
      protected <T> Map<String, Object> executeAction(T inputRequest) {
        throw new WorkflowRetriableException(WorkflowError.UNKNOWN_ERROR);
      }

      @Override
      protected void logErrorMetric(
              Exception exception, WorkerActionRequest workerActionRequest) {
        metricLogger.logErrorMetric(
                MetricName.INVOKE_DUZZIT, Type.APP_CONNECT_METRIC, exception);
      }
    };

    String workflowName = "customReminder";
    String externalTaskName = "createTask";
    String ownerId = "1234";

    WorkerActionRequest workerActionRequest =
            WorkerActionRequest.builder()
                    .definitionKey(workflowName)
                    .activityId(externalTaskName)
                    .processDefinitionId("dId")
                    .processInstanceId("iId")
                    .inputVariables(schema)
                    .extensionProperties(extensionProperties)
                    .handlerId("hId")
                    .taskId("externalTaskId")
                    .ownerId(1234L)
                    .retries(2)
                    .build();

    
    Mockito.when(workerRetryHelper.getRetryConfig(workflowName, externalTaskName, ownerId)).thenReturn(null);

    Mockito.doNothing().when(incidentTaskManager).executeFailedCommand(workerActionRequest);

    Map<String, Object> response = taskHandler.execute(workerActionRequest);

    Mockito.verify(incidentTaskManager, Mockito.times(1))
            .executeFailedCommand(workerActionRequest);

    Assert.assertEquals(
            response.get(new StringBuilder(workerActionRequest.getActivityId())
                    .append(UNDERSCORE)
                    .append(RESPONSE.getName())
                    .toString()),
            "false"
    );
  }

  @Test(expected = WorkflowRetriableException.class)
  public void test_throwExceptionOnExecuteAction_isFatalOnRetryExhaust() {

    WorkflowTaskHandler taskHandler = new WorkflowTaskHandler(Mockito.mock(MetricLogger.class), incidentTaskManager, workerRetryHelper, handlerConfig) {

      @Override
      public TaskHandlerName getName() {
        return null;
      }

      @Override
      protected <T> Map<String, Object> executeAction(T inputRequest) {
        throw new WorkflowRetriableException(WorkflowError.UNKNOWN_ERROR);
      }

      @Override
      protected void logErrorMetric(
              Exception exception, WorkerActionRequest workerActionRequest) {
        metricLogger.logErrorMetric(
                MetricName.INVOKE_DUZZIT, Type.APP_CONNECT_METRIC, exception);
      }
    };

    String workflowName = "customReminder";
    String externalTaskName = "createTask";
    String ownerId = "1234";

    WorkerActionRequest workerActionRequest =
            WorkerActionRequest.builder()
                    .definitionKey(workflowName)
                    .activityId(externalTaskName)
                    .processDefinitionId("dId")
                    .processInstanceId("iId")
                    .inputVariables(schema)
                    .extensionProperties(extensionProperties)
                    .handlerId("hId")
                    .taskId("externalTaskId")
                    .ownerId(1234L)
                    .retries(2)
                    .build();
    
    Mockito.when(workerRetryHelper.getRetryConfig(workflowName, externalTaskName, ownerId)).thenReturn(
            getMockExternalTaskRetryConfig(
                    workflowName, 
                    externalTaskName, 
                    3, 
                    null, 
                    null, 
                    true
            )
    );
    taskHandler.execute(workerActionRequest);
  }

  @Test(expected = WorkflowRetriableException.class)
  public void test_throwRetriableExceptionOnAppConnectRest_workerActionRequestRetriesNull() {

    WorkflowTaskHandler taskHandler = new WorkflowTaskHandler(Mockito.mock(MetricLogger.class), incidentTaskManager, workerRetryHelper, handlerConfig) {

      @Override
      public TaskHandlerName getName() {
        return null;
      }

      @Override
      protected <T> Map<String, Object> executeAction(T inputRequest) {
        throw new WorkflowRetriableException(WorkflowError.UNKNOWN_ERROR);
      }

      @Override
      protected void logErrorMetric(
              Exception exception, WorkerActionRequest workerActionRequest) {
        metricLogger.logErrorMetric(
                MetricName.INVOKE_DUZZIT, Type.APP_CONNECT_METRIC, exception);
      }
    };

    String workflowName = "customReminder";
    String externalTaskName = "createTask";
    String ownerId = "1234";

    WorkerActionRequest workerActionRequest =
            WorkerActionRequest.builder()
                    .definitionKey(workflowName)
                    .activityId(externalTaskName)
                    .processDefinitionId("dId")
                    .processInstanceId("iId")
                    .inputVariables(schema)
                    .extensionProperties(extensionProperties)
                    .handlerId("hId")
                    .taskId("externalTaskId")
                    .ownerId(1234L)
                    .retries(null)
                    .build();

    Mockito.when(workerRetryHelper.getRetryConfig(workflowName, externalTaskName, ownerId)).thenReturn(
            getMockExternalTaskRetryConfig(
                    workflowName,
                    externalTaskName,
                    3,
                    null,
                    null,
                    false
            )
    );
    
    taskHandler.execute(workerActionRequest);
  }

  @Test(expected = WorkflowRetriableException.class)
  public void test_throwExceptionOnExecuteAction_workerActionRequestRetriesGreaterThan1() {
    WorkflowTaskHandler taskHandler = new WorkflowTaskHandler(Mockito.mock(MetricLogger.class), incidentTaskManager, workerRetryHelper, handlerConfig) {

      @Override
      public TaskHandlerName getName() {
        return null;
      }

      @Override
      protected <T> Map<String, Object> executeAction(T inputRequest) {
        throw new WorkflowRetriableException(WorkflowError.UNKNOWN_ERROR);
      }

      @Override
      protected void logErrorMetric(
              Exception exception, WorkerActionRequest workerActionRequest) {
        metricLogger.logErrorMetric(
                MetricName.INVOKE_DUZZIT, Type.APP_CONNECT_METRIC, exception);
      }
    };

    String workflowName = "customReminder";
    String externalTaskName = "createTask";
    String ownerId = "1234";

    WorkerActionRequest workerActionRequest =
            WorkerActionRequest.builder()
                    .definitionKey(workflowName)
                    .activityId(externalTaskName)
                    .processDefinitionId("dId")
                    .processInstanceId("iId")
                    .inputVariables(schema)
                    .extensionProperties(extensionProperties)
                    .handlerId("hId")
                    .taskId("externalTaskId")
                    .ownerId(1234L)
                    .retries(2)
                    .build();


    Mockito.when(workerRetryHelper.getRetryConfig(workflowName, externalTaskName, ownerId)).thenReturn(
            getMockExternalTaskRetryConfig(
                    workflowName,
                    externalTaskName,
                    3,
                    null,
                    null,
                    false
            )
    );
    taskHandler.execute(workerActionRequest);
  }

  @Test()
  public void test_throwExceptionOnExecuteAction_workerActionRequestRetriesEqualTo1() {
    
    WorkflowTaskHandler taskHandler = new WorkflowTaskHandler(Mockito.mock(MetricLogger.class), incidentTaskManager, workerRetryHelper, handlerConfig) {

      @Override
      public TaskHandlerName getName() {
        return null;
      }

      @Override
      protected <T> Map<String, Object> executeAction(T inputRequest) {
        throw new WorkflowRetriableException(WorkflowError.UNKNOWN_ERROR);
      }

      @Override
      protected void logErrorMetric(
              Exception exception, WorkerActionRequest workerActionRequest) {
        metricLogger.logErrorMetric(
                MetricName.INVOKE_DUZZIT, Type.APP_CONNECT_METRIC, exception);
      }
    };

    String workflowName = "customReminder";
    String externalTaskName = "createTask";
    String ownerId = "1234";

    WorkerActionRequest workerActionRequest =
            WorkerActionRequest.builder()
                    .definitionKey(workflowName)
                    .activityId(externalTaskName)
                    .processDefinitionId("dId")
                    .processInstanceId("iId")
                    .inputVariables(schema)
                    .extensionProperties(extensionProperties)
                    .handlerId("hId")
                    .taskId("externalTaskId")
                    .ownerId(1234L)
                    .retries(1)
                    .build();

    Mockito.when(workerRetryHelper.getRetryConfig(workflowName, externalTaskName, ownerId)).thenReturn(
            getMockExternalTaskRetryConfig(
                    workflowName,
                    externalTaskName,
                    3,
                    null,
                    null,
                    false
            )
    );

    Mockito.doNothing().when(incidentTaskManager).executeFailedCommand(workerActionRequest);

    Map<String, Object> response = taskHandler.execute(workerActionRequest);

    Mockito.verify(incidentTaskManager, Mockito.times(1))
            .executeFailedCommand(workerActionRequest);

    Assert.assertEquals(
            response.get(new StringBuilder(workerActionRequest.getActivityId())
                    .append(UNDERSCORE)
                    .append(RESPONSE.getName())
                    .toString()),
            "false"
    );
  }

  @Test()
  public void test_throwExceptionOnExecuteAction_NonRetriableExceptionThrown() {
    WorkflowTaskHandler taskHandler =
        new WorkflowTaskHandler(
            Mockito.mock(MetricLogger.class), incidentTaskManager, workerRetryHelper, handlerConfig) {

          @Override
          public TaskHandlerName getName() {
            return null;
          }

          @Override
          protected <T> Map<String, Object> executeAction(T inputRequest) {
            throw new WorkflowNonRetriableException(WorkflowError.UNKNOWN_ERROR);
          }

          @Override
          protected void logErrorMetric(
              Exception exception, WorkerActionRequest workerActionRequest) {
            metricLogger.logErrorMetric(
                MetricName.INVOKE_DUZZIT, Type.APP_CONNECT_METRIC, exception);
          }
        };

    String workflowName = "customReminder";
    String externalTaskName = "createTask";
    String ownerId = "1234";

    WorkerActionRequest workerActionRequest =
            WorkerActionRequest.builder()
                    .definitionKey(workflowName)
                    .activityId(externalTaskName)
                    .processDefinitionId("dId")
                    .processInstanceId("iId")
                    .inputVariables(schema)
                    .extensionProperties(extensionProperties)
                    .handlerId("hId")
                    .taskId("externalTaskId")
                    .ownerId(1234L)
                    .retries(2)
                    .build();
    
    Mockito.when(workerRetryHelper.getRetryConfig(workflowName, externalTaskName, ownerId)).thenReturn(
            getMockExternalTaskRetryConfig(
                    workflowName,
                    externalTaskName,
                    3,
                    null,
                    null,
                    false
            )
    );

    Mockito.doNothing().when(incidentTaskManager).executeFailedCommand(workerActionRequest);

    Map<String, Object> response = taskHandler.execute(workerActionRequest);

    Mockito.verify(incidentTaskManager, Mockito.times(1))
            .executeFailedCommand(workerActionRequest);

    Assert.assertEquals(
            response.get(new StringBuilder(workerActionRequest.getActivityId())
                    .append(UNDERSCORE)
                    .append(RESPONSE.getName())
                    .toString()),
            "false"
    );
  }

  private ExternalTaskRetryConfig getMockExternalTaskRetryConfig(String workflowName, String externalTaskName, Integer retryCount, Long backOffStepSize, RetryStrategyName retryStrategyName, boolean fatalOnRetryExhaust) {
    ExternalTaskRetryConfig externalTaskRetryConfig = new ExternalTaskRetryConfig();
    externalTaskRetryConfig.setWorkflowName(workflowName);
    externalTaskRetryConfig.setExternalTaskName(externalTaskName);
    externalTaskRetryConfig.setRetryCount(retryCount);
    externalTaskRetryConfig.setBackOffStepSize(backOffStepSize);
    externalTaskRetryConfig.setRetryStrategyName(retryStrategyName);
    externalTaskRetryConfig.setFatalOnRetryExhaust(fatalOnRetryExhaust);
    return externalTaskRetryConfig;
  }

  /**
   * Test a successful execution scenario where the executeAction method returns a valid result.
   */
  @Test
  public void test_executeAction_Successful() {
    Map<String, Object> expectedResult = Collections.singletonMap("key", "value");
    WorkflowTaskHandler handler = new WorkflowTaskHandler(metricLogger, incidentTaskManager, workerRetryHelper, handlerConfig) {
      @Override
      public TaskHandlerName getName() {
        // Return any valid TaskHandlerName if needed
        return TaskHandlerName.DUMMY_ACTION_HANDLER;
      }

      @Override
      protected <T> Map<String, Object> executeAction(T inputRequest) {
        return expectedResult;
      }

      @Override
      protected void logErrorMetric(Exception exception, WorkerActionRequest workerActionRequest) {
        // For success scenario, no error metric is logged.
      }
    };

    WorkerActionRequest request = WorkerActionRequest.builder()
            .extensionProperties(new HashMap<>(extensionProperties))
            .inputVariables(new HashMap<>(schema))
            .activityId("activity1")
            .build();

    // When no exception, the handler should directly return the expected result.
    Map<String, Object> actual = handler.execute(request);
    Assert.assertEquals(expectedResult, actual);

    // IncidentTaskManager should not be invoked.
    verify(incidentTaskManager, never()).executeFailedCommand(any());
  }

  /**
   * Test scenario where external task retry config is available,
   * and the workerActionRequest has retries equal to 1.
   * In this case, exception is swallowed and a response map with false flag is returned.
   */
  @Test
  public void test_executeAction_WithExternalRetryConfig_RetriesEqualOne_Swallowed() {

    WorkflowTaskHandler handler = new WorkflowTaskHandler(metricLogger, incidentTaskManager, workerRetryHelper, handlerConfig) {
      @Override
      public TaskHandlerName getName() {
        return TaskHandlerName.DUMMY_ACTION_HANDLER;
      }

      @Override
      protected <T> Map<String, Object> executeAction(T inputRequest) {
        throw new WorkflowRetriableException(WorkflowError.ACTION_NOT_FOUND);
      }

      @Override
      protected void logErrorMetric(Exception exception, WorkerActionRequest workerActionRequest) {
        metricLogger.logErrorMetric(MetricName.INVOKE_DUZZIT, Type.APP_CONNECT_METRIC, exception);
      }
    };

    // Simulate an external task retry config with retry count 3 and non-fatal.
    ExternalTaskRetryConfig retryConfig = new ExternalTaskRetryConfig();
    retryConfig.setWorkflowName("customReminder");
    retryConfig.setExternalTaskName("createTask");
    retryConfig.setRetryCount(3);
    retryConfig.setFatalOnRetryExhaust(false);
    when(workerRetryHelper.getRetryConfig(eq("customReminder"), eq("createTask"), eq("1234")))
            .thenReturn(retryConfig);

    // Ensure incidentTaskManager's failed command is executed.
    doNothing().when(incidentTaskManager).executeFailedCommand(any());

    WorkerActionRequest request = WorkerActionRequest.builder()
            .definitionKey("customReminder")
            .activityId("createTask")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(new HashMap<>(schema))
            .extensionProperties(new HashMap<>(extensionProperties))
            .handlerId("hId")
            .taskId("externalTaskId")
            .ownerId(1234L)
            .retries(1)
            .build();

    Map<String, Object> response = handler.execute(request);
    // Verify incident task command executed once.
    verify(incidentTaskManager, times(1)).executeFailedCommand(request);

    String responseKey = new StringBuilder(request.getActivityId())
            .append(UNDERSCORE)
            .append(RESPONSE.getName())
            .toString();
    Assert.assertEquals("false", response.get(responseKey));
  }

  /**
   * Test that when executeAction returns a valid map, the key based on activityId and RESPONSE is not altered.
   */
  @Test
  public void test_executeAction_ResponseKeyNotOverridden() {
    Map<String, Object> handlerResult = new HashMap<>();
    // Pre-populate the map with some result.
    handlerResult.put("customKey", "customValue");

    WorkflowTaskHandler handler = new WorkflowTaskHandler(metricLogger, incidentTaskManager, workerRetryHelper, handlerConfig) {
      @Override
      public TaskHandlerName getName() {
        return TaskHandlerName.DUMMY_ACTION_HANDLER;
      }

      @Override
      protected <T> Map<String, Object> executeAction(T inputRequest) {
        return handlerResult;
      }

      @Override
      protected void logErrorMetric(Exception exception, WorkerActionRequest workerActionRequest) {
        // no op for successful call.
      }
    };

    WorkerActionRequest request = WorkerActionRequest.builder()
            .extensionProperties(new HashMap<>(extensionProperties))
            .inputVariables(new HashMap<>(schema))
            .activityId("activity3")
            .build();

    Map<String, Object> response = handler.execute(request);
    // The response from executeAction should match as no exception was thrown.
    Assert.assertEquals("customValue", response.get("customKey"));
    // Ensure we haven't inadvertently injected a false response.
    String responseKey = new StringBuilder(request.getActivityId())
            .append(UNDERSCORE)
            .append(RESPONSE.getName())
            .toString();
    Assert.assertNull(response.get(responseKey));
  }

  @Test
  public void getTaskParametersFromConfig_Test(){

    Map<String, Object> handlerResult = new HashMap<>();
    // Pre-populate the map with some result.
    handlerResult.put("customKey", "customValue");

    Parameter parameter1 = new Parameter();
    parameter1.setName("TaskType");
    parameter1.setFieldValues(List.of("PROJECT"));
    Parameter parameter2 = new Parameter();
    parameter2.setName("Id");
    parameter2.setHandlerFieldName("TxnId");
    VariableMap variableMap = new VariableMapImpl();
    variableMap.put("Id", "123");
    variableMap.put("entityType", "Project");
    Map<String, List<Parameter>> parameters = Map.of("parameters", Arrays.asList(parameter1, parameter2));
    Map<String, Map<String, List<Parameter>>> project = Map.of("project", parameters);
    Map<String, Map<String, Map<String, List<Parameter>>>> handlers   = Map.of("intuit-workflows/taskmanager-update-task", project);
    Mockito.when(handlerConfig.getHandlers()).thenReturn(handlers);
    WorkerActionRequest workerActionRequest = WorkerActionRequest.builder().handlerId("intuit-workflows/taskmanager-update-task").variableMap(variableMap).build();
    List<WorkflowTaskHandlerInput> output = workflowTaskHandler.getTaskParametersFromConfig(RecordType.PROJECT, workerActionRequest);
    Assert.assertEquals(2, output.size());
    Assert.assertEquals("123", output.get(1).getValue());

  }

  @Test
  public void testGetTaskParametersFromConfig_withValidConfig_Select() {
    // Prepare parameters
    Parameter parameter1 = new Parameter();
    parameter1.setName("TaskType");
    parameter1.setFieldValues(List.of("PROJECT"));
    Parameter parameter2 = new Parameter();
    parameter2.setName("Id");
    parameter2.setHandlerFieldName("TxnId");

    // Prepare variable map with value for "Id"
    VariableMap variableMap = new VariableMapImpl();
    variableMap.put("Id", "123");
    variableMap.put("entityType", "Project");

    // Build parameters map structure
    Map<String, List<Parameter>> parametersMap = Map.of("parameters", Arrays.asList(parameter1, parameter2));
    Map<String, Map<String, List<Parameter>>> entityMap = Map.of(RecordType.PROJECT.getRecordType(), parametersMap);
    Map<String, Map<String, Map<String, List<Parameter>>>> handlersConfig =
            Map.of("intuit-workflows/taskmanager-update-task", entityMap);

    // When handlerConfig.getHandlers() is called, return the constructed configuration
    Mockito.when(handlerConfig.getHandlers()).thenReturn(handlersConfig);

    // Build a worker action request with handlerId matching our config and pass variableMap
    WorkerActionRequest workerActionRequest = WorkerActionRequest.builder()
            .handlerId("intuit-workflows/taskmanager-update-task")
            .variableMap(variableMap)
            .build();

    // Call the method under test using select scenario "valid config"
    List<WorkflowTaskHandlerInput> output = workflowTaskHandler.getTaskParametersFromConfig(RecordType.PROJECT, workerActionRequest);

    // Verify that two inputs are returned and the second input's value is "123"
    Assert.assertEquals(2, output.size());
    Assert.assertEquals("123", output.get(1).getValue());
  }

  @Test
  public void testGetTaskParametersFromConfig_withMissingConfig_Select() {
    // Simulate missing configuration by returning null from handlerConfig.getHandlers()
    Mockito.when(handlerConfig.getHandlers()).thenReturn(null);

    // Prepare a variable map (contents are irrelevant in this case)
    VariableMap variableMap = new VariableMapImpl();
    variableMap.put("Id", "456");

    WorkerActionRequest workerActionRequest = WorkerActionRequest.builder()
            .handlerId("non-existent-handler")
            .variableMap(variableMap)
            .build();

    // Call the method under test in a scenario where no configuration is found.
    List<WorkflowTaskHandlerInput> output = workflowTaskHandler.getTaskParametersFromConfig(RecordType.PROJECT, workerActionRequest);

    // Verify that an empty list is returned
    Assert.assertTrue(output.isEmpty());
  }

  @Test
  public void testExtractRecordTypeWithDefinitionDetails() {
    // Mock DefinitionDetails
    DefinitionDetails mockDefinitionDetails = mock(DefinitionDetails.class);
    when(mockDefinitionDetails.getRecordType()).thenReturn(RecordType.INVOICE);

    // Mock WorkerActionRequest
    WorkerActionRequest mockWorkerActionRequest = mock(WorkerActionRequest.class);

    // Assume the existence of extractRecordType method in some class
    Optional<RecordType> recordType = workflowTaskHandler.extractRecordType(mockDefinitionDetails, mockWorkerActionRequest);
    assertTrue(recordType.isPresent());
    assertEquals(RecordType.INVOICE, recordType.get());
  }

  @Test
  public void testExtractRecordTypeWithInputVariables() {
    // Mock DefinitionDetails
    DefinitionDetails mockDefinitionDetails = mock(DefinitionDetails.class);
    when(mockDefinitionDetails.getRecordType()).thenReturn(null);

    schema.put("onDemandApproval", "true");
    schema.put("entityType", "invoice");

    // Mock WorkerActionRequest
    WorkerActionRequest mockWorkerActionRequest =  WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId("handlerId")
            .taskId("externalTaskId")
            .ownerId(1234L)
            .build();


    // Assume the existence of extractRecordType method in some class
    Optional<RecordType> recordType = workflowTaskHandler.extractRecordType(mockDefinitionDetails, mockWorkerActionRequest);
    assertTrue(recordType.isPresent());
    assertEquals(RecordType.INVOICE, recordType.get());
  }

  @Test
  public void testExtractRecordTypeWithInputVariables_WithoutOndemandFlag() {
    // Mock DefinitionDetails
    DefinitionDetails mockDefinitionDetails = mock(DefinitionDetails.class);
    when(mockDefinitionDetails.getRecordType()).thenReturn(null);
    Map<String, String> schema1 = new HashMap<>();

    schema1.put("entityType", "invoice");

    // Mock WorkerActionRequest
    WorkerActionRequest mockWorkerActionRequest =  WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema1)
            .handlerId("handlerId")
            .taskId("externalTaskId")
            .ownerId(1234L)
            .build();


    // Assume the existence of extractRecordType method in some class
    Optional<RecordType> recordType = workflowTaskHandler.extractRecordType(mockDefinitionDetails, mockWorkerActionRequest);
    assertFalse(recordType.isPresent());
  }

  @Test
  public void testExtractRecordTypeWithoutRecord() {
    // Mock DefinitionDetails
    DefinitionDetails mockDefinitionDetails = mock(DefinitionDetails.class);
    when(mockDefinitionDetails.getRecordType()).thenReturn(null);

    Map<String, String> schema1 = new HashMap<>();

    schema1.put("entityType", "invoice");

    // Mock WorkerActionRequest
    WorkerActionRequest mockWorkerActionRequest =  WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema1)
            .handlerId("handlerId")
            .taskId("externalTaskId")
            .ownerId(1234L)
            .build();


    // Assume the existence of extractRecordType method in some class
    Optional<RecordType> recordType = workflowTaskHandler.extractRecordType(mockDefinitionDetails, mockWorkerActionRequest);
    assertFalse(recordType.isPresent());
  }

  @Test
  public void getTaskParametersFromConfig_Test1(){

    Map<String, Object> handlerResult = new HashMap<>();
    // Pre-populate the map with some result.
    handlerResult.put("customKey", "customValue");

    Parameter parameter1 = new Parameter();
    parameter1.setName("TaskType");
    parameter1.setFieldValues(List.of("PROJECT"));
    Parameter parameter2 = new Parameter();
    parameter2.setName("useTaskService");
    parameter2.setFieldValues(List.of("true"));
    VariableMap variableMap = new VariableMapImpl();
    variableMap.put("Id", "123");
    variableMap.put("entityType", "Project");
    Map<String, List<Parameter>> parameters = Map.of("parameters", Arrays.asList(parameter1, parameter2));
    Map<String, Map<String, List<Parameter>>> project = Map.of("project", parameters);
    Map<String, Map<String, Map<String, List<Parameter>>>> handlers   = Map.of("intuit-workflows/taskmanager-update-task", project);
    Mockito.when(handlerConfig.getHandlers()).thenReturn(handlers);

    WorkerActionRequest workerActionRequest = WorkerActionRequest.builder().handlerId("intuit-workflows/taskmanager-update-task").variableMap(variableMap).definitionKey("test").build();
    List<WorkflowTaskHandlerInput> output = workflowTaskHandler.getTaskParametersFromConfig(RecordType.PROJECT, workerActionRequest);
    Assert.assertEquals(2, output.size());
    Assert.assertEquals("PROJECT", output.get(0).getValue());
    Assert.assertEquals("TaskType", output.get(0).getName());
    Assert.assertEquals("true", output.get(1).getValue());
    Assert.assertEquals("useTaskService", output.get(1).getName());
  }

}
