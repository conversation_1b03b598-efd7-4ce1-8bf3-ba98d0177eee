package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.DEFINITION_NOT_FOUND;
import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.INVALID_PARAMETER_DETAILS;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.ENTITY_TYPE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.INTUIT_REALMID;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.config.HandlerConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.WorkerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.impl.ProcessDetailsRepoService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.request.AppConnectTaskHandlerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.request.WorkflowTaskHandlerAction;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.request.WorkflowTaskHandlerInput;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.TaskHandlerName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qbo.oinp.adapter.AppConnectOINPBridge;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.springframework.test.util.ReflectionTestUtils;

/**
 * <AUTHOR>
 */
public class AppConnectDuzzitRestExecutionHandlerTest {

  @Mock private AppconnectDuzzitRestExecutionHelper appconnectDuzzitRestExecutionHelper;

  @Mock private ProcessDetailsRepoService processDetailsRepoService;

  @Mock private AppConnectParameterDetailsExtractorHelper appConnectParameterDetailsExtractorHelper;

  @Mock private DefaultParameterDetailsExtractor defaultParameterDetailsExtractor;

  @Mock private MetricLogger metricLogger;
  @Mock private OnDemandParameterDetailExtractor onDemandParameterDetailExtractor;

  @InjectMocks AppConnectDuzzitRestExecutionHandler appConnectDuzzitRestExecutionHandler;

  @Mock private AppConnectOINPBridge appConnectOINPBridge;
  @Mock private MultiStepParameterDetailsExtractor multiStepParameterDetailsExtractor;

  @Mock private HandlerConfig handlerConfig;

  @Mock private WorkerUtil workerUtil;

  @Mock private AppConnectWorkflowTaskHandlerHelper appConnectWorkflowTaskHandlerHelper;

  @Spy
  CustomWorkflowConfig customWorkflowConfig;

  private static Map<String, String> schema = new HashMap<>();

  @Before
  public void init() throws Exception {
    customWorkflowConfig = TestHelper.loadCustomConfig();
    MockitoAnnotations.initMocks(this);
    ReflectionTestUtils.setField(appConnectDuzzitRestExecutionHandler, "metricLogger", metricLogger);
    ReflectionTestUtils.setField(appConnectDuzzitRestExecutionHandler, "handlerConfig", handlerConfig);
    Mockito.when(handlerConfig.getHandlers()).thenReturn(new HashMap<>());

  }

  private static final String parametersSchema =
      TestHelper.readResourceAsString("schema/testData/parameters.json");

  static {
    schema.put(WorkFlowVariables.PARAMETERS_KEY.getName(), parametersSchema);
    schema.put(ENTITY_TYPE,"bill");
    schema.put(INTUIT_REALMID, "1234");
    
  }

  @Test
  public void testGetName() {
    Assert.assertEquals(TaskHandlerName.APP_CONNECT_DUZZIT_REST_ACTION_HANDLER, appConnectDuzzitRestExecutionHandler.getName());
  }

  @Test
  public void testRestExecutionSuccess() {
    String handlerId = "hId";

    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId(handlerId)
            .taskId("externalTaskId")
            .ownerId(1234L)
            .build();

    Map<String, Object> res = new HashMap<>();
    res.put(
        workerActionRequest.getActivityId()
            + WorkflowConstants.UNDERSCORE
            + WorkFlowVariables.RESPONSE.getName(),
        "true");

    Mockito.when(processDetailsRepoService.findByProcessIdWithoutDefinitionData(any())).thenReturn(
        Optional.of(getMockDefinitionDetails()));

    Mockito.when(
        appConnectParameterDetailsExtractorHelper.getAppConnectParameterDetailExtractor(any(), any(), any(), any())
    ).thenReturn(
        defaultParameterDetailsExtractor
    );

    List<WorkflowTaskHandlerInput> workflowTaskHandlerInputs = new ArrayList<>();
    WorkflowTaskHandlerInput workflowTaskHandlerInput = new WorkflowTaskHandlerInput();
    workflowTaskHandlerInput.setName("name");
    workflowTaskHandlerInput.setValue("value");
    workflowTaskHandlerInputs.add(workflowTaskHandlerInput);
    WorkflowTaskHandlerAction workflowTaskHandlerAction = WorkflowTaskHandlerAction.builder()
        .inputs(workflowTaskHandlerInputs).build();
    AppConnectTaskHandlerRequest actionRequest =
        AppConnectTaskHandlerRequest.builder()
            .action(workflowTaskHandlerAction)
            .endpoint("endpoint")
            .instanceId(workerActionRequest.getProcessInstanceId())
            .providerAppId("AP123")
            .providerWorkflowId(workerActionRequest.getHandlerId())
            .workflowId(workerActionRequest.getProcessDefinitionId())
            .build();

    Mockito.when(appConnectWorkflowTaskHandlerHelper.prepareTaskHandlerRequest(Mockito.any(),Mockito.any(), Mockito.any()))
        .thenReturn(actionRequest);

    Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);

    Mockito.doCallRealMethod().when(defaultParameterDetailsExtractor).getParameterDetails(any());

    Mockito.when(appconnectDuzzitRestExecutionHelper.executeWorkflowActionRequest(any(), any(), any())).thenReturn(res);

    Map<String, Object> resp = appConnectDuzzitRestExecutionHandler.executeAction(workerActionRequest);

    Mockito.verify(defaultParameterDetailsExtractor, Mockito.times(1))
        .getParameterDetails(any());

    Assert.assertTrue(
        resp.get(
                workerActionRequest.getActivityId()
                    + WorkflowConstants.UNDERSCORE
                    + WorkFlowVariables.RESPONSE.getName())
            .equals("true"));

  }

  @Test
  public void testRestExecutionSuccessWithIncorrectHandlerId() {
    String handlerId = "intuit-workflows/hld";

    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId(handlerId)
            .taskId("externalTaskId")
            .ownerId(1234L)
            .build();

    Map<String, Object> res = new HashMap<>();
    res.put(
        workerActionRequest.getActivityId()
            + WorkflowConstants.UNDERSCORE
            + WorkFlowVariables.RESPONSE.getName(),
        "true");

    Mockito.when(processDetailsRepoService.findByProcessIdWithoutDefinitionData(any())).thenReturn(
        Optional.of(getMockDefinitionDetails()));

    Mockito.when(
        appConnectParameterDetailsExtractorHelper.getAppConnectParameterDetailExtractor(any(), any(), any(), any())
    ).thenReturn(
        defaultParameterDetailsExtractor
    );

    List<WorkflowTaskHandlerInput> workflowTaskHandlerInputs = new ArrayList<>();
    WorkflowTaskHandlerInput workflowTaskHandlerInput = new WorkflowTaskHandlerInput();
    workflowTaskHandlerInput.setName("name");
    workflowTaskHandlerInput.setValue("value");
    workflowTaskHandlerInputs.add(workflowTaskHandlerInput);
    WorkflowTaskHandlerAction workflowTaskHandlerAction = WorkflowTaskHandlerAction.builder()
        .inputs(workflowTaskHandlerInputs).build();
    AppConnectTaskHandlerRequest actionRequest =
        AppConnectTaskHandlerRequest.builder()
            .action(workflowTaskHandlerAction)
            .endpoint("endpoint")
            .instanceId(workerActionRequest.getProcessInstanceId())
            .providerAppId("AP123")
            .providerWorkflowId(workerActionRequest.getHandlerId())
            .workflowId(workerActionRequest.getProcessDefinitionId())
            .build();

    Mockito.when(appConnectWorkflowTaskHandlerHelper.prepareTaskHandlerRequest(Mockito.any(),Mockito.any(), Mockito.any()))
        .thenReturn(actionRequest);

    Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);

    Mockito.doCallRealMethod().when(defaultParameterDetailsExtractor).getParameterDetails(any());

    Mockito.when(appconnectDuzzitRestExecutionHelper.executeWorkflowActionRequest(any(), any(), any())).thenReturn(res);

    Map<String, Object> resp = appConnectDuzzitRestExecutionHandler.executeAction(workerActionRequest);

    Mockito.verify(defaultParameterDetailsExtractor, Mockito.times(1))
        .getParameterDetails(any());

    Assert.assertTrue(
        resp.get(
                workerActionRequest.getActivityId()
                    + WorkflowConstants.UNDERSCORE
                    + WorkFlowVariables.RESPONSE.getName())
            .equals("true"));

    Assert.assertTrue(workerActionRequest.getHandlerId().equals("/intuit-workflows/api/hld.json"));

  }

  @Test
  public void testRestExecutionSuccess_forOnDemand() {
    String handlerId = "hId";

    Map<String, String> schemaForOnDemand = new HashMap<>();
    schemaForOnDemand.put(WorkFlowVariables.PARAMETERS_KEY.getName(), parametersSchema);
    schemaForOnDemand.put(WorkflowConstants.ON_DEMAND_APPROVAL, "true");
    schemaForOnDemand.put(INTUIT_REALMID, "1234");
    schemaForOnDemand.put(ENTITY_TYPE,"bill");

    WorkerActionRequest workerActionRequest =
            WorkerActionRequest.builder()
                    .activityId("createTask")
                    .processDefinitionId("dId")
                    .processInstanceId("iId")
                    .inputVariables(schemaForOnDemand)
                    .handlerId(handlerId)
                    .definitionKey("customApproval")
                    .taskId("externalTaskId")
                    .ownerId(1234L)
                    .build();

    Map<String, Object> res = new HashMap<>();
    res.put(
            workerActionRequest.getActivityId()
                    + WorkflowConstants.UNDERSCORE
                    + WorkFlowVariables.RESPONSE.getName(),
            "true");

    Mockito.when(processDetailsRepoService.findByProcessIdWithoutDefinitionData(any())).thenReturn(
            Optional.of(getMockDefinitionDetails()));

    Mockito.when(
        appConnectParameterDetailsExtractorHelper.getAppConnectParameterDetailExtractor(any(), any(), any(), any())).thenReturn(onDemandParameterDetailExtractor);

    List<WorkflowTaskHandlerInput> workflowTaskHandlerInputs = new ArrayList<>();
    WorkflowTaskHandlerInput workflowTaskHandlerInput = new WorkflowTaskHandlerInput();
    workflowTaskHandlerInput.setName("name");
    workflowTaskHandlerInput.setValue("value");
    workflowTaskHandlerInputs.add(workflowTaskHandlerInput);
    WorkflowTaskHandlerAction workflowTaskHandlerAction = WorkflowTaskHandlerAction.builder()
            .inputs(workflowTaskHandlerInputs).build();
    AppConnectTaskHandlerRequest actionRequest =
            AppConnectTaskHandlerRequest.builder()
                    .action(workflowTaskHandlerAction)
                    .endpoint("endpoint")
                    .instanceId(workerActionRequest.getProcessInstanceId())
                    .providerAppId("AP123")
                    .providerWorkflowId(workerActionRequest.getHandlerId())
                    .workflowId(workerActionRequest.getProcessDefinitionId())
                    .build();

    Mockito.when(appConnectWorkflowTaskHandlerHelper.prepareTaskHandlerRequest(Mockito.any(),Mockito.any(), Mockito.any()))
            .thenReturn(actionRequest);

    Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);

    HandlerDetails.ParameterDetails obj = new HandlerDetails.ParameterDetails();
    obj.setHandlerFieldName("abc");
    Optional<Map<String, HandlerDetails.ParameterDetails>> paramterOpt = Optional.of(Map.of("abc",obj));
    
    Mockito.when(onDemandParameterDetailExtractor.getParameterDetails(any())).thenReturn(paramterOpt);

    Mockito.when(appconnectDuzzitRestExecutionHelper.executeWorkflowActionRequest(any(), any(), any()))
    	.thenReturn(res);

    Map<String, Object> resp = appConnectDuzzitRestExecutionHandler.executeAction(workerActionRequest);
    Mockito.verify(onDemandParameterDetailExtractor, Mockito.times(1))
        .getParameterDetails(any());
    Assert.assertTrue(
        resp.get(
                workerActionRequest.getActivityId()
                    + WorkflowConstants.UNDERSCORE
                    + WorkFlowVariables.RESPONSE.getName())
            .equals("true"));

  }

  @Test
  public void testRestExecutionFailure() {
    String handlerId = "hId";

    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId(handlerId)
            .taskId("externalTaskId")
            .ownerId(1234L)
            .build();

    Map<String, Object> res = new HashMap<>();
    res.put(
        workerActionRequest.getActivityId()
            + WorkflowConstants.UNDERSCORE
            + WorkFlowVariables.RESPONSE.getName(),
        "false");

    List<WorkflowTaskHandlerInput> workflowTaskHandlerInputs = new ArrayList<>();
    WorkflowTaskHandlerInput workflowTaskHandlerInput = new WorkflowTaskHandlerInput();
    workflowTaskHandlerInput.setName("name");
    workflowTaskHandlerInput.setValue("value");
    workflowTaskHandlerInputs.add(workflowTaskHandlerInput);
    WorkflowTaskHandlerAction workflowTaskHandlerAction = WorkflowTaskHandlerAction.builder()
        .inputs(workflowTaskHandlerInputs).build();
    AppConnectTaskHandlerRequest actionRequest =
        AppConnectTaskHandlerRequest.builder()
            .action(workflowTaskHandlerAction)
            .endpoint("endpoint")
            .instanceId(workerActionRequest.getProcessInstanceId())
            .providerAppId("AP123")
            .providerWorkflowId(workerActionRequest.getHandlerId())
            .workflowId(workerActionRequest.getProcessDefinitionId())
            .build();

    Mockito.when(appConnectWorkflowTaskHandlerHelper.prepareTaskHandlerRequest(Mockito.any(),Mockito.any(), Mockito.any()))
        .thenReturn(actionRequest);

    Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);

    Mockito.when(processDetailsRepoService.findByProcessIdWithoutDefinitionData(any())).thenReturn(
        Optional.of(getMockDefinitionDetails()));

    Mockito.when(
        appConnectParameterDetailsExtractorHelper.getAppConnectParameterDetailExtractor(any(), any(), any(), any())
    ).thenReturn(
        defaultParameterDetailsExtractor
    );

    Mockito.doCallRealMethod().when(defaultParameterDetailsExtractor).getParameterDetails(any());

    Mockito.when(appconnectDuzzitRestExecutionHelper.executeWorkflowActionRequest(any(), any(), any())).thenReturn(res);

    Map<String, Object> resp = appConnectDuzzitRestExecutionHandler.executeAction(workerActionRequest);

    Assert.assertTrue(
        resp.get(
                workerActionRequest.getActivityId()
                    + WorkflowConstants.UNDERSCORE
                    + WorkFlowVariables.RESPONSE.getName())
            .equals("false"));

  }

  @Test(expected = Exception.class)
  public void testExecuteAppActionExecutionFailure() {

    Mockito.when(processDetailsRepoService.findByProcessIdWithoutDefinitionData(any())).thenReturn(
        Optional.of(getMockDefinitionDetails()));

    Mockito.when(
        appConnectParameterDetailsExtractorHelper.getAppConnectParameterDetailExtractor(any(), any(), any(), any())
    ).thenReturn(
        defaultParameterDetailsExtractor
    );

    Mockito.doCallRealMethod().when(defaultParameterDetailsExtractor).getParameterDetails(any());

    Mockito.when(appconnectDuzzitRestExecutionHelper.executeWorkflowActionRequest(any(), any())).thenThrow(new Exception());

    appConnectDuzzitRestExecutionHandler.executeAction(
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId("hId")
            .taskId("externalTaskId")
            .ownerId(1234L)
            .build()
    );
  }

  @Test
  public void testDefinitionDetailsNotFound() {

    Mockito.when(processDetailsRepoService.findByProcessIdWithoutDefinitionData(any())).thenReturn(
        Optional.empty());

    try {
      appConnectDuzzitRestExecutionHandler.executeAction(
          WorkerActionRequest.builder()
              .activityId("aId")
              .processDefinitionId("dId")
              .processInstanceId("iId")
              .inputVariables(new HashMap<>())
              .handlerId("hId")
              .taskId("externalTaskId")
              .ownerId(1234L)
              .build()
      );
      Assert.fail("The above method should throw exception");
    } catch (WorkflowGeneralException workflowGeneralException) {
      Assert.assertTrue(
          workflowGeneralException.getWorkflowError().equals(DEFINITION_NOT_FOUND)
      );
    }
  }

  @Test
  public void testInvalidParameterDetails() {
    String handlerId = "hId";

    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(Map.of(ENTITY_TYPE,"bill"))
            .handlerId(handlerId)
            .taskId("externalTaskId")
            .ownerId(1234L)
            .build();

    Map<String, Object> res = new HashMap<>();
    res.put(
        workerActionRequest.getActivityId()
            + WorkflowConstants.UNDERSCORE
            + WorkFlowVariables.RESPONSE.getName(),
        "false");

    Mockito.when(processDetailsRepoService.findByProcessIdWithoutDefinitionData(any())).thenReturn(
        Optional.of(getMockDefinitionDetails()));

    Mockito.when(
        appConnectParameterDetailsExtractorHelper.getAppConnectParameterDetailExtractor(any(), any(), any(), any())
    ).thenReturn(
        defaultParameterDetailsExtractor
    );

    Mockito.doCallRealMethod().when(defaultParameterDetailsExtractor).getParameterDetails(any());

    try {
      appConnectDuzzitRestExecutionHandler.executeAction(workerActionRequest);
      Assert.fail("The above method should throw exception");
    } catch (WorkflowGeneralException workflowGeneralException) {
      Assert.assertTrue(
          workflowGeneralException.getWorkflowError().equals(INVALID_PARAMETER_DETAILS)
      );
    }
  }

  private DefinitionDetails getMockDefinitionDetails() {
    return DefinitionDetails.builder()
            .templateDetails(
                TemplateDetails.builder()
                    .templateName("sampleTemplate")
                    .build()
            )
            .build();
  }

  @Test
  public void test_execute_logMetric() {
    Mockito.when(processDetailsRepoService.findByProcessIdWithoutDefinitionData(any())).thenReturn(
        Optional.of(getMockDefinitionDetails()));

    Mockito.when(
        appConnectParameterDetailsExtractorHelper.getAppConnectParameterDetailExtractor(any(), any(), any(), any())
    ).thenReturn(
        defaultParameterDetailsExtractor
    );

    Mockito.doCallRealMethod().when(defaultParameterDetailsExtractor).getParameterDetails(any());
    Mockito.when(appconnectDuzzitRestExecutionHelper.executeWorkflowActionRequest(any(), any())).thenThrow(new WorkflowGeneralException(WorkflowError.INVALID_INPUT));
    try {
      appConnectDuzzitRestExecutionHandler.execute(
          WorkerActionRequest.builder()
              .activityId("aId")
              .processDefinitionId("dId")
              .processInstanceId("iId")
              .inputVariables(schema)
              .handlerId("hId")
              .taskId("externalTaskId")
              .ownerId(1234L)
              .build()
      );
      Assert.fail();
    } catch (WorkflowGeneralException workflowGeneralException) {
      Assert.assertEquals(WorkflowError.INVALID_INPUT, workflowGeneralException.getWorkflowError());
      Mockito.verify(metricLogger, Mockito.times(1))
          .logErrorMetric(eq(MetricName.INVOKE_DUZZIT), any(), any());
    }
  }

  @Test
  public void test_execute_logMetricCalledProcess() {
    Mockito.when(processDetailsRepoService.findByProcessIdWithoutDefinitionData(any())).thenReturn(
        Optional.of(getMockDefinitionDetails()));

    Mockito.when(
        appConnectParameterDetailsExtractorHelper.getAppConnectParameterDetailExtractor(any(), any(), any(), any())
    ).thenReturn(
        defaultParameterDetailsExtractor
    );

    Mockito.doCallRealMethod().when(defaultParameterDetailsExtractor).getParameterDetails(any());
    Mockito.when(appconnectDuzzitRestExecutionHelper.executeWorkflowActionRequest(any(), any())).thenThrow(new WorkflowGeneralException(WorkflowError.INVALID_INPUT));
    try {
      appConnectDuzzitRestExecutionHandler.execute(
          WorkerActionRequest.builder()
              .activityId("aId")
              .rootProcessInstanceId("rId")
              .isCalledProcess(true)
              .processDefinitionId("dId")
              .processInstanceId("iId")
              .inputVariables(schema)
              .handlerId("hId")
              .taskId("externalTaskId")
              .ownerId(1234L)
              .build()
      );
      Assert.fail();
    } catch (WorkflowGeneralException workflowGeneralException) {
      Assert.assertEquals(WorkflowError.INVALID_INPUT, workflowGeneralException.getWorkflowError());
      Mockito.verify(metricLogger, Mockito.times(1))
          .logErrorMetric(eq(MetricName.MULTI_STEP_INVOKE_DUZZIT), any(), any());
    }
  }

  @Test
  public void testExecuteAction_oinpBridge_Success() {
    String handlerId = "hId";

    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId(handlerId)
            .taskId("externalTaskId")
            .ownerId(1234L)
            .build();

    String responseKey = workerActionRequest.getActivityId()
        + WorkflowConstants.UNDERSCORE
        + WorkFlowVariables.RESPONSE.getName();
    ArgumentCaptor<Map<String, String>> argumentCaptor = ArgumentCaptor.forClass(Map.class);
    Mockito.when(appConnectOINPBridge.initiateBridge(any(), any())).thenReturn(true);
    Mockito.when(appConnectOINPBridge.executeNotificationAction(any(), argumentCaptor.capture()))
        .thenReturn(new HashMap<>() {{
          put(responseKey, "true");
        }});

    List<WorkflowTaskHandlerInput> workflowTaskHandlerInputs = new ArrayList<>();
    WorkflowTaskHandlerInput workflowTaskHandlerInput = new WorkflowTaskHandlerInput();
    workflowTaskHandlerInput.setName("name");
    workflowTaskHandlerInput.setValue("value");
    workflowTaskHandlerInputs.add(workflowTaskHandlerInput);
    WorkflowTaskHandlerAction workflowTaskHandlerAction = WorkflowTaskHandlerAction.builder()
        .inputs(workflowTaskHandlerInputs).build();
    AppConnectTaskHandlerRequest actionRequest =
        AppConnectTaskHandlerRequest.builder()
            .action(workflowTaskHandlerAction)
            .endpoint("endpoint")
            .instanceId(workerActionRequest.getProcessInstanceId())
            .providerAppId("AP123")
            .providerWorkflowId(workerActionRequest.getHandlerId())
            .workflowId(workerActionRequest.getProcessDefinitionId())
            .build();

    Mockito.when(
            appConnectWorkflowTaskHandlerHelper.prepareTaskHandlerRequest(Mockito.any(), Mockito.any(),
                Mockito.any()))
        .thenReturn(actionRequest);

    Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);

    Mockito.when(processDetailsRepoService.findByProcessIdWithoutDefinitionData(any())).thenReturn(
        Optional.of(getMockDefinitionDetails()));

    Mockito.when(
        appConnectParameterDetailsExtractorHelper.getAppConnectParameterDetailExtractor(any(),
            any(), any(), any())
    ).thenReturn(
        defaultParameterDetailsExtractor
    );

    Mockito.doCallRealMethod().when(defaultParameterDetailsExtractor).getParameterDetails(any());

    Map<String, Object> resp = appConnectDuzzitRestExecutionHandler.executeAction(
        workerActionRequest);

    Assert.assertEquals("value", argumentCaptor.getValue().get("name"));

    Mockito.verify(appConnectWorkflowTaskHandlerHelper, Mockito.times(0))
        .executeAction(any(), any());

    Assert.assertEquals("true", resp.get(responseKey));

  }

  @Test
  public void testExecuteAction_oinpBridge_Failure() {
    String handlerId = "hId";

    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId(handlerId)
            .taskId("externalTaskId")
            .ownerId(1234L)
            .build();

    Mockito.when(appConnectOINPBridge.initiateBridge(any(), any())).thenReturn(true);
    Mockito.when(appConnectOINPBridge.executeNotificationAction(any(), any()))
        .thenThrow(new RuntimeException("Gone"));

    List<WorkflowTaskHandlerInput> workflowTaskHandlerInputs = new ArrayList<>();
    WorkflowTaskHandlerInput workflowTaskHandlerInput = new WorkflowTaskHandlerInput();
    workflowTaskHandlerInput.setName("name");
    workflowTaskHandlerInput.setValue("value");
    workflowTaskHandlerInputs.add(workflowTaskHandlerInput);
    WorkflowTaskHandlerAction workflowTaskHandlerAction = WorkflowTaskHandlerAction.builder()
        .inputs(workflowTaskHandlerInputs).build();
    AppConnectTaskHandlerRequest actionRequest =
        AppConnectTaskHandlerRequest.builder()
            .action(workflowTaskHandlerAction)
            .endpoint("endpoint")
            .instanceId(workerActionRequest.getProcessInstanceId())
            .providerAppId("AP123")
            .providerWorkflowId(workerActionRequest.getHandlerId())
            .workflowId(workerActionRequest.getProcessDefinitionId())
            .build();

    Mockito.when(processDetailsRepoService.findByProcessIdWithoutDefinitionData(any())).thenReturn(
        Optional.of(getMockDefinitionDetails()));

    Mockito.when(
        appConnectParameterDetailsExtractorHelper.getAppConnectParameterDetailExtractor(any(),
            any(), any(), any())
    ).thenReturn(
        defaultParameterDetailsExtractor
    );

    Mockito.doCallRealMethod().when(defaultParameterDetailsExtractor).getParameterDetails(any());
    Mockito.when(
            appConnectWorkflowTaskHandlerHelper.prepareTaskHandlerRequest(Mockito.any(), Mockito.any(),
                Mockito.any()))
        .thenReturn(actionRequest);

    Mockito.when(workerUtil.validate(workerActionRequest)).thenReturn(workerActionRequest);
    Exception e = Assertions.assertThrows(RuntimeException.class,
        () -> appConnectDuzzitRestExecutionHandler.executeAction(workerActionRequest));
    Assert.assertEquals("Gone", e.getMessage());

    Mockito.verify(appConnectWorkflowTaskHandlerHelper, Mockito.times(0))
        .executeAction(any(), any());
  }
}
