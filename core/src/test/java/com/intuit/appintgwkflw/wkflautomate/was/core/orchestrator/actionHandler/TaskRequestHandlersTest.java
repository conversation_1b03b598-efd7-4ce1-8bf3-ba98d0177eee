package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
/**
 * Class acts as factory to return task request handlers
 *
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class TaskRequestHandlersTest {

  @Mock TaskRequestModifier taskRequestModifier;

  @Test
  public void testGetTaskRequest() {
    TaskRequestHandlers.addHandler(TaskType.NOTIFICATION_TASK, taskRequestModifier);
    Assert.assertEquals(
        taskRequestModifier, TaskRequestHandlers.getHandler(TaskType.NOTIFICATION_TASK));
    Assert.assertEquals(true, TaskRequestHandlers.contains(TaskType.NOTIFICATION_TASK));
  }
}
