package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.impl;

import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.AppConnectConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowEventException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.httpClient.AppConnectWASClient;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.AuthDetailsService;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.DefinitionEventDetails;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.helpers.DefinitionEventProcessorHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.AuthDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.response.AppConnectFetchTransactionsResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.EventHeaderConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DefinitionEventType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import com.intuit.appintgwkflw.wkflautomate.was.event.api.EventPublisherCapability;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.HttpStatus;
import org.springframework.test.util.ReflectionTestUtils;

/** <AUTHOR> */
@RunWith(MockitoJUnitRunner.class)
public class CustomReminderFetchTransactionsProcessorTest {
  @InjectMocks CustomReminderFetchTransactionsProcessor customReminderFetchTransactionsProcessor;
  @Mock AuthDetailsService authDetailsService;
  @Mock AppConnectWASClient wasHttpClient;
  @Mock AppConnectConfig appConnectConfig;
  @Mock DefinitionEventProcessorHelper definitionEventProcessorHelper;
  @Mock EventPublisherCapability eventPublisherCapability;

  private static final String CUSTOM_REMINDER_WORKFLOW_DEF =
      TestHelper.readResourceAsString("bpmn/customWorkflowDefinition.bpmn");

  private static final String CUSTOM_REMINDER_WORKFLOW =
      TestHelper.readResourceAsString("bpmn/customReminderTest.bpmn");
  private DefinitionEventDetails definitionEventDetails;

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
    ReflectionTestUtils.setField(
        customReminderFetchTransactionsProcessor,
        "definitionEventProcessorHelper",
        definitionEventProcessorHelper);
    ReflectionTestUtils.setField(
        customReminderFetchTransactionsProcessor, "authDetailsService", authDetailsService);
    ReflectionTestUtils.setField(
        customReminderFetchTransactionsProcessor, "appConnectConfig", appConnectConfig);
    ReflectionTestUtils.setField(
        customReminderFetchTransactionsProcessor, "wasHttpClient", wasHttpClient);

    ReflectionTestUtils.setField(
        customReminderFetchTransactionsProcessor,
        "eventPublisherCapability",
        eventPublisherCapability);
    definitionEventDetails =
        DefinitionEventDetails.builder()
            .metaData(
                Map.of(WorkflowConstants.ENTITY_CHANGE_TYPE, WorkflowConstants.NEW_CUSTOM_START))
            .definitionDetails(getDefinitionDetails(CUSTOM_REMINDER_WORKFLOW_DEF))
            .build();
    Mockito.when(appConnectConfig.getConnectorEndpoint())
        .thenReturn("https://e2e.api.intuit.com/appconnect/connector");
    Mockito.when(authDetailsService.getAuthDetailsFromRealmId("123")).thenReturn(getAuthDetails());
    Mockito.when(
            definitionEventProcessorHelper.getHandlerId(
                ArgumentMatchers.any(), ArgumentMatchers.any()))
        .thenReturn("/intuit-workflows/api/custom-reminder-start-process.json");
  }

  @Test
  public void testDefinitionEventName() {
    Assert.assertEquals(
        DefinitionEventType.CUSTOM_REMINDER_FETCH_TRANSACTIONS,
        customReminderFetchTransactionsProcessor.getName());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testProcessEventsWithNull() {
    customReminderFetchTransactionsProcessor.processEvents(null);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testProcessEventsWithEmptyDefinitionDetails() {
    customReminderFetchTransactionsProcessor.processEvents(
        DefinitionEventDetails.builder().build());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testProcessEventsWithNullMetaData() {
    customReminderFetchTransactionsProcessor.processEvents(
        DefinitionEventDetails.builder()
            .definitionDetails(getDefinitionDetails(CUSTOM_REMINDER_WORKFLOW_DEF))
            .build());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testProcessEventsWithEmptyMetaData() {
    customReminderFetchTransactionsProcessor.processEvents(
        DefinitionEventDetails.builder()
            .definitionDetails(getDefinitionDetails(CUSTOM_REMINDER_WORKFLOW_DEF))
            .metaData(Map.of())
            .build());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testProcessEventsWithEmptyParameterDetails() {
    customReminderFetchTransactionsProcessor.processEvents(
        DefinitionEventDetails.builder()
            .definitionDetails(getDefinitionDetails(CUSTOM_REMINDER_WORKFLOW))
            .metaData(
                Map.of(WorkflowConstants.ENTITY_CHANGE_TYPE, WorkflowConstants.NEW_CUSTOM_START))
            .build());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testProcessEventsWithNullDefinitionEventTypes() {
    Mockito.when(
            definitionEventProcessorHelper.getHandlerId(
                ArgumentMatchers.any(), ArgumentMatchers.any()))
        .thenThrow(new WorkflowGeneralException("Test"));
    customReminderFetchTransactionsProcessor.processEvents(
        DefinitionEventDetails.builder()
            .definitionDetails(getDefinitionDetails(CUSTOM_REMINDER_WORKFLOW_DEF))
            .metaData(
                Map.of(WorkflowConstants.ENTITY_CHANGE_TYPE, WorkflowConstants.NEW_CUSTOM_START))
            .build());
  }

  @Test
  public void testProcessCustomStartEventsSuccess() {
    Mockito.when(
            definitionEventProcessorHelper.getHandlerId(
                ArgumentMatchers.any(), ArgumentMatchers.any()))
        .thenReturn("/intuit-workflows/api/custom-reminder-start-process.json");
    AppConnectFetchTransactionsResponse handlerResponse = new AppConnectFetchTransactionsResponse();
    handlerResponse.setSuccess("true");
    List<Map<String, String>> output = new ArrayList<>();
    Map<String, String> data = new HashMap<>();
    data.put("ID", "123");
    output.add(data);
    handlerResponse.setOutput(output);
    WASHttpResponse<Object> response =
        WASHttpResponse.<Object>builder()
            .status(HttpStatus.OK)
            .response(handlerResponse)
            .isSuccess2xx(true)
            .build();
    Mockito.when(wasHttpClient.httpResponse(ArgumentMatchers.any())).thenReturn(response);
    customReminderFetchTransactionsProcessor.processEvents(
        DefinitionEventDetails.builder()
            .definitionDetails(getDefinitionDetails(CUSTOM_REMINDER_WORKFLOW_DEF))
            .metaData(
                Map.of(WorkflowConstants.ENTITY_CHANGE_TYPE, WorkflowConstants.NEW_CUSTOM_START))
            .build());
    // asset auth serive call
    verify(authDetailsService, times(1)).getAuthDetailsFromRealmId("123");
    verify(wasHttpClient, times(1)).httpResponse(ArgumentMatchers.any());
    // assert washttp call
    verify(eventPublisherCapability, times(1))
        .publish(ArgumentMatchers.any(), ArgumentMatchers.any());
  }

  @Test
  public void testProcessCustomStartEventsSuccessCustOffering() {
    AppConnectFetchTransactionsResponse handlerResponse = new AppConnectFetchTransactionsResponse();
    handlerResponse.setSuccess("true");
    List<Map<String, String>> output = new ArrayList<>();
    Map<String, String> data = new HashMap<>();
    data.put("ID", "123");
    output.add(data);
    handlerResponse.setOutput(output);
    WASHttpResponse<Object> response =
        WASHttpResponse.<Object>builder()
            .status(HttpStatus.OK)
            .response(handlerResponse)
            .isSuccess2xx(true)
            .build();
    Mockito.when(wasHttpClient.httpResponse(ArgumentMatchers.any())).thenReturn(response);
    customReminderFetchTransactionsProcessor.processEvents(
        DefinitionEventDetails.builder()
            .definitionDetails(getDefinitionDetails(CUSTOM_REMINDER_WORKFLOW_DEF))
            .metaData(
                Map.of(
                    WorkflowConstants.ENTITY_CHANGE_TYPE,
                    WorkflowConstants.NEW_CUSTOM_START,
                    EventHeaderConstants.OFFERING_ID,
                    "off123"))
            .build());
    // asset auth serive call
    verify(authDetailsService, times(1)).getAuthDetailsFromRealmId("123");
    verify(wasHttpClient, times(1)).httpResponse(ArgumentMatchers.any());
    // assert washttp call
    verify(eventPublisherCapability, times(1))
        .publish(ArgumentMatchers.any(), ArgumentMatchers.any());
  }

  @Test
  public void testProcessCustomStartEventsSuccessWithoutResponse() {
    AppConnectFetchTransactionsResponse handlerResponse = new AppConnectFetchTransactionsResponse();
    handlerResponse.setSuccess("true");
    WASHttpResponse<Object> response =
        WASHttpResponse.<Object>builder()
            .status(HttpStatus.OK)
            .response(handlerResponse)
            .isSuccess2xx(true)
            .build();
    Mockito.when(wasHttpClient.httpResponse(ArgumentMatchers.any())).thenReturn(response);
    customReminderFetchTransactionsProcessor.processEvents(
        DefinitionEventDetails.builder()
            .definitionDetails(getDefinitionDetails(CUSTOM_REMINDER_WORKFLOW_DEF))
            .metaData(
                Map.of(WorkflowConstants.ENTITY_CHANGE_TYPE, WorkflowConstants.NEW_CUSTOM_START))
            .build());
    // asset auth serive call
    verify(authDetailsService, times(1)).getAuthDetailsFromRealmId("123");
    verify(wasHttpClient, times(1)).httpResponse(ArgumentMatchers.any());
  }

  @Test
  public void testProcessCustomWaitEventsSuccess() {
    AppConnectFetchTransactionsResponse handlerResponse = new AppConnectFetchTransactionsResponse();
    handlerResponse.setSuccess("true");
    List<Map<String, String>> output = new ArrayList<>();
    Map<String, String> data = new HashMap<>();
    data.put("ID", "123");
    output.add(data);
    handlerResponse.setOutput(output);
    WASHttpResponse<Object> response =
        WASHttpResponse.<Object>builder()
            .status(HttpStatus.OK)
            .response(handlerResponse)
            .isSuccess2xx(true)
            .build();
    Mockito.when(wasHttpClient.httpResponse(ArgumentMatchers.any())).thenReturn(response);
    customReminderFetchTransactionsProcessor.processEvents(
        DefinitionEventDetails.builder()
            .definitionDetails(getDefinitionDetails(CUSTOM_REMINDER_WORKFLOW_DEF))
            .metaData(
                Map.of(WorkflowConstants.ENTITY_CHANGE_TYPE, WorkflowConstants.CUSTOM_WAIT_EVENT))
            .build());
    // asset auth serive call
    verify(authDetailsService, times(1)).getAuthDetailsFromRealmId("123");
    verify(wasHttpClient, times(1)).httpResponse(ArgumentMatchers.any());
    // assert washttp call
    verify(eventPublisherCapability, times(1))
        .publish(ArgumentMatchers.any(), ArgumentMatchers.any());
  }

  @Test(expected = WorkflowEventException.class)
  public void testProcessCustomStartEventsSuccessPublishTrowException() {
    AppConnectFetchTransactionsResponse handlerResponse = new AppConnectFetchTransactionsResponse();
    handlerResponse.setSuccess("true");
    List<Map<String, String>> output = new ArrayList<>();
    Map<String, String> data = new HashMap<>();
    data.put("ID", "123");
    output.add(data);
    handlerResponse.setOutput(output);
    WASHttpResponse<Object> response =
        WASHttpResponse.<Object>builder()
            .status(HttpStatus.OK)
            .response(handlerResponse)
            .isSuccess2xx(true)
            .build();
    Mockito.when(wasHttpClient.httpResponse(ArgumentMatchers.any())).thenReturn(response);
    Mockito.when(eventPublisherCapability.publish(ArgumentMatchers.any(), ArgumentMatchers.any()))
        .thenThrow(new WorkflowEventException("Publish failed"));
    customReminderFetchTransactionsProcessor.processEvents(
        DefinitionEventDetails.builder()
            .definitionDetails(getDefinitionDetails(CUSTOM_REMINDER_WORKFLOW_DEF))
            .metaData(
                Map.of(WorkflowConstants.ENTITY_CHANGE_TYPE, WorkflowConstants.NEW_CUSTOM_START))
            .build());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testProcessCustomStartEventsSuccess2xxFalse() {
    AppConnectFetchTransactionsResponse handlerResponse = new AppConnectFetchTransactionsResponse();
    handlerResponse.setSuccess("true");
    List<Map<String, String>> output = new ArrayList<>();
    Map<String, String> data = new HashMap<>();
    data.put("ID", "123");
    output.add(data);
    handlerResponse.setOutput(output);
    WASHttpResponse<Object> response =
        WASHttpResponse.<Object>builder()
            .status(HttpStatus.OK)
            .response(handlerResponse)
            .isSuccess2xx(false)
            .build();
    Mockito.when(wasHttpClient.httpResponse(ArgumentMatchers.any())).thenReturn(response);
    customReminderFetchTransactionsProcessor.processEvents(
        DefinitionEventDetails.builder()
            .definitionDetails(getDefinitionDetails(CUSTOM_REMINDER_WORKFLOW_DEF))
            .metaData(
                Map.of(WorkflowConstants.ENTITY_CHANGE_TYPE, WorkflowConstants.NEW_CUSTOM_START))
            .build());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testProcessCustomStartEventsResponseNull() {
    WASHttpResponse<Object> response =
        WASHttpResponse.<Object>builder()
            .status(HttpStatus.OK)
            .response(null)
            .isSuccess2xx(true)
            .build();
    Mockito.when(wasHttpClient.httpResponse(ArgumentMatchers.any())).thenReturn(response);
    customReminderFetchTransactionsProcessor.processEvents(
        DefinitionEventDetails.builder()
            .definitionDetails(getDefinitionDetails(CUSTOM_REMINDER_WORKFLOW_DEF))
            .metaData(
                Map.of(WorkflowConstants.ENTITY_CHANGE_TYPE, WorkflowConstants.NEW_CUSTOM_START))
            .build());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testProcessCustomStartEventsSuccess2xxTrueResponseSuccessFalse() {
    AppConnectFetchTransactionsResponse handlerResponse = new AppConnectFetchTransactionsResponse();
    handlerResponse.setSuccess("false");
    List<Map<String, String>> output = new ArrayList<>();
    Map<String, String> data = new HashMap<>();
    data.put("ID", "123");
    output.add(data);
    handlerResponse.setOutput(output);
    WASHttpResponse<Object> response =
        WASHttpResponse.<Object>builder()
            .status(HttpStatus.OK)
            .response(handlerResponse)
            .isSuccess2xx(false)
            .build();
    Mockito.when(wasHttpClient.httpResponse(ArgumentMatchers.any())).thenReturn(response);
    customReminderFetchTransactionsProcessor.processEvents(
        DefinitionEventDetails.builder()
            .definitionDetails(getDefinitionDetails(CUSTOM_REMINDER_WORKFLOW_DEF))
            .metaData(
                Map.of(WorkflowConstants.ENTITY_CHANGE_TYPE, WorkflowConstants.NEW_CUSTOM_START))
            .build());
  }

  private DefinitionDetails getDefinitionDetails(String file) {
    DefinitionDetails definitionDetails = new DefinitionDetails();
    definitionDetails.setDefinitionId("def123");
    definitionDetails.setOwnerId(123L);
    definitionDetails.setDefinitionData(file.getBytes());
    TemplateDetails templateDetails = new TemplateDetails();
    templateDetails.setOfferingId("off123");
    definitionDetails.setTemplateDetails(templateDetails);
    return definitionDetails;
  }

  private AuthDetails getAuthDetails() {
    AuthDetails authDetails = new AuthDetails();
    authDetails.setSubscriptionId("1234");
    return authDetails;
  }
}
