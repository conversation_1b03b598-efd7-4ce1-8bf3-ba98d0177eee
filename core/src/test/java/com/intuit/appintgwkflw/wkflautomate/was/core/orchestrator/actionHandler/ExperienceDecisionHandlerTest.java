package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.IXPManager;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import com.intuit.identity.exptplatform.assignment.entities.EntityID;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.Map;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables.DECISION;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

public class ExperienceDecisionHandlerTest {
    @Mock private IXPManager ixpManager;
    @Mock private WASContextHandler contextHandler;

    @InjectMocks private ExperienceDecisionHandler experienceDecisionHandler;

    @Test
    public void testExecuteAction_StringVariation_EntityIdIsNull() {
        MockitoAnnotations.initMocks(this);
        WorkerActionRequest workerActionRequest =
                WorkerActionRequest.builder()
                        .activityId("decisionFlag")
                        .processDefinitionId("pdId")
                        .processInstanceId("pId")
                        .handlerId("dummy")
                        .ownerId(1234L)
                        .inputVariables(Map.of("decisionDetails",
                                "{\"contextMap\":null,\"defaultValue\":\"VariationC\",\"featureFlag\":\"SBSEG-wkflatmnsvc-qbo-ab-flow\",\"variationType\":\"String\"}"))
                        .build();
        when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("1234");
        when(ixpManager.getString(any(), any(), any(), anyLong())).thenReturn("VariationC");

        Map<String, Object> result = experienceDecisionHandler.executeAction(workerActionRequest);

        assertNotNull(result);
        assertEquals("VariationC", result.get("decisionFlag_" + DECISION.getName()));
    }

    @Test
    public void testExecuteAction_StringVariation_EntityIdIsNotNull() {
        MockitoAnnotations.initMocks(this);
        WorkerActionRequest workerActionRequest =
                WorkerActionRequest.builder()
                        .activityId("decisionFlag")
                        .processDefinitionId("pdId")
                        .processInstanceId("pId")
                        .handlerId("dummy")
                        .ownerId(1234L)
                        .inputVariables(Map.of("decisionDetails",
                                "{\"contextMap\":\"{\\\"id\\\": \\\"123\\\"}\",\"defaultValue\":\"VariationC\",\"entityId\":\"{\\\"ns\\\": \\\"IXPService\\\"}\",\"featureFlag\":\"SBSEG-wkflatmnsvc-qbo-ab-flow\",\"variationType\":\"String\"}"))
                        .build();
        when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("1234");
        when(ixpManager.getString(any(), any(), any(), anyMap())).thenReturn("VariationC");

        Map<String, Object> result = experienceDecisionHandler.executeAction(workerActionRequest);

        assertNotNull(result);
        assertEquals("VariationC", result.get("decisionFlag_" + DECISION.getName()));
    }

    @Test
    public void testExecuteAction_StringVariation_EntityIdDoesNotHaveNamespace() {
        MockitoAnnotations.initMocks(this);
        WorkerActionRequest workerActionRequest =
            WorkerActionRequest.builder()
                .activityId("decisionFlag")
                .processDefinitionId("pdId")
                .processInstanceId("pId")
                .handlerId("dummy")
                .ownerId(1234L)
                .inputVariables(Map.of("decisionDetails",
                    "{\"contextMap\":\"{\\\"id\\\": \\\"123\\\"}\",\"defaultValue\":\"VariationC\",\"entityId\":\"{\\\"accountId\\\": \\\"********\\\"}\",\"featureFlag\":\"SBSEG-wkflatmnsvc-qbo-ab-flow\",\"variationType\":\"String\"}"))
                .build();
        when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("1234");
        when(ixpManager.getString(any(), any(), any(), anyMap())).thenReturn("VariationC");

        Map<String, Object> result = experienceDecisionHandler.executeAction(workerActionRequest);

        assertNotNull(result);
        assertEquals("VariationC", result.get("decisionFlag_" + DECISION.getName()));
    }

    @Test
    public void testExecuteAction_BooleanVariation_EntityIdIsNull() {
        MockitoAnnotations.initMocks(this);
        WorkerActionRequest workerActionRequest =
                WorkerActionRequest.builder()
                        .activityId("decisionFlag")
                        .processDefinitionId("pdId")
                        .processInstanceId("pId")
                        .handlerId("dummy")
                        .ownerId(1234L)
                        .inputVariables(Map.of("decisionDetails",
                                "{\"contextMap\":\"{\\\"id\\\": \\\"123\\\"}\",\"defaultValue\":\"true\",\"featureFlag\":\"SBSEG-wkflatmnsvc-qbo-ab-flow\",\"variationType\":\"Boolean\"}"))
                        .build();
        when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("1234");
        when(ixpManager.getBoolean(anyString(), anyBoolean(), Mockito.anyMap(), anyLong())).thenReturn(true);

        Map<String, Object> result = experienceDecisionHandler.executeAction(workerActionRequest);

        assertNotNull(result);
        assertEquals(true, result.get("decisionFlag_" + DECISION.getName()));
    }

    @Test
    public void testExecuteAction_BooleanVariation_EntityIdIsNotNull() {
        MockitoAnnotations.initMocks(this);
        WorkerActionRequest workerActionRequest =
                WorkerActionRequest.builder()
                        .activityId("decisionFlag")
                        .processDefinitionId("pdId")
                        .processInstanceId("pId")
                        .handlerId("dummy")
                        .ownerId(1234L)
                        .inputVariables(Map.of("decisionDetails",
                                "{\"contextMap\":\"{\\\"id\\\": \\\"123\\\"}\",\"defaultValue\":\"true\",\"entityId\":\"{\\\"ns\\\": \\\"IXPService\\\"}\",\"featureFlag\":\"SBSEG-wkflatmnsvc-qbo-ab-flow\",\"variationType\":\"Boolean\"}"))
                        .build();
        when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("1234");
        when(ixpManager.getString(anyString(), anyString(), any(), anyMap())).thenReturn("false");

        Map<String, Object> result = experienceDecisionHandler.executeAction(workerActionRequest);

        assertNotNull(result);
        assertEquals(false, result.get("decisionFlag_" + DECISION.getName()));
    }

    @Test
    public void testGetIXPEntityID() {
        MockitoAnnotations.initMocks(this);
        Map<String, String> entityId = Map.of("realmOrCompanyId", "1235", "accountId", "5678");
        String ownerId = "1234";

        EntityID entityID = experienceDecisionHandler.getIXPEntityID(entityId, ownerId);

        assertNotNull(entityID);
        assertEquals("1235", entityID.getRealmOrCompanyId()); // realmOrCompanyId
        assertEquals("IXPService", entityID.getEntityDescription()); // namespace
    }
}
