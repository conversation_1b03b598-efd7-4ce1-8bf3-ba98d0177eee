package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.when;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.ActivityInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.DefinitionServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionActivityDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.DeployDefinitionResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CustomWorkflowType;
import com.intuit.async.execution.request.State;
import com.intuit.v4.Authorization;
import com.intuit.v4.GlobalId;
import com.intuit.v4.workflows.Definition;
import java.nio.charset.Charset;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import org.apache.commons.io.IOUtils;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.dmn.Dmn;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class MultiStepSaveDefinitionDataStoreTaskTest {

  @InjectMocks
  private MultiStepSaveDefinitionDataStoreTask multiStepSaveDefinitionDataStoreTask;

  @Mock
  private DefinitionServiceHelper definitionServiceHelper;

  @Mock
  private DefinitionActivityDetailsRepository definitionActivityDetailsRepository;

  private State state = new State();

  private Authorization authorization = new Authorization();

  private DefinitionInstance definitionInstance;
  private Definition multiConditionDefinition;
  private DmnModelInstance dmnModelInstance;
  private BpmnModelInstance multiConditionBpmnModelInstance;
  private TemplateDetails templateDetails;
  private String PARENT_ACTIVITY_ID = "action-1";
  private String CHILD_ACTIVITY_ID = "createTask";
  private String DMN_ACTIVITY_ID = "decisionElement";
  private String REALM_ID = "1234";
  private String LOCAL_ID = "localId";
  private String DEFINITION_KEY = "definitionKey";


  private static final String MULTI_CONDITION_WORKFLOW_BPMN_XML =
      TestHelper.readResourceAsString("bpmn/customApproval_multiCondition.bpmn");

  private static final String CUSTOM_WORKFLOW_DMN_XML =
      TestHelper.readResourceAsString("dmn/customWorkflow.dmn");

  @Before
  public void setUp() {
    authorization.putRealm("realm");
    authorization.putAuthId("auth");
    multiStepSaveDefinitionDataStoreTask =
        new MultiStepSaveDefinitionDataStoreTask(definitionActivityDetailsRepository);
    state.addValue(
        AsyncTaskConstants.BPMN_ENGINE_DEPLOY_RESPONSE_KEY,
        Mockito.mock(DeployDefinitionResponse.class));

    multiConditionDefinition = TestHelper.mockMultiConditionDefinitionEntity();
    multiConditionBpmnModelInstance =
        Bpmn.readModelFromStream(
            IOUtils.toInputStream(MULTI_CONDITION_WORKFLOW_BPMN_XML, Charset.defaultCharset()));
    dmnModelInstance =
        Dmn.readModelFromStream(
            IOUtils.toInputStream(CUSTOM_WORKFLOW_DMN_XML, Charset.defaultCharset()));

    templateDetails = TemplateDetails.builder().id("template-id")
        .templateName(CustomWorkflowType.APPROVAL.getTemplateName()).build();

    definitionInstance = new DefinitionInstance(multiConditionDefinition,
        multiConditionBpmnModelInstance,
        Collections.singletonList(dmnModelInstance), templateDetails);

    state.addValue(AsyncTaskConstants.DEFINITION_INSTANCE, definitionInstance);
    state.addValue(AsyncTaskConstants.AUTHORIZATION_KEY, authorization);
    state.addValue(AsyncTaskConstants.DEFINITION_SERVICE_HELPER_OBJECT, definitionServiceHelper);
    state.addValue(AsyncTaskConstants.IS_UPDATE, Boolean.FALSE);
  }

  @Test
  public void test_executeSuccessForCreateDefinition() {
    when(definitionServiceHelper.saveUpdateDefinitionDetails(any(), any(), any(), anyBoolean()))
        .thenReturn(multiConditionDefinition);
    Map<String, ActivityInstance> activityInstanceMap = createActivityInstanceMapForDmn();
    Map<String, ActivityInstance> callActivityInstanceMap = createActivityInstanceMapForCallActivity();
    activityInstanceMap.putAll(callActivityInstanceMap);
    definitionInstance.setActivityInstanceMap(activityInstanceMap);
    DefinitionActivityDetail definitionActivityDetail =
        DefinitionActivityDetail.builder().id(UUID.randomUUID().toString())
            .activityId(PARENT_ACTIVITY_ID).build();
    List<DefinitionActivityDetail> activityDetailList = Collections.singletonList(
        definitionActivityDetail);
    Mockito.when(definitionActivityDetailsRepository.saveAll(any()))
        .thenReturn(activityDetailList);
    state.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, "defIdKey");
    multiStepSaveDefinitionDataStoreTask.execute(state);
    Mockito.verify(definitionActivityDetailsRepository, times(1))
        .saveAll(any());
    Assert.assertEquals("defIdKey", state.getValue(AsyncTaskConstants.DEFINITION_ID_KEY));
  }

  @Test
  public void test_executeSuccessForUpdateDefinition() {
    when(definitionServiceHelper.saveUpdateDefinitionDetails(any(), any(), any(), anyBoolean()))
        .thenReturn(multiConditionDefinition);
    Map<String, ActivityInstance> activityInstanceMap = createActivityInstanceMapForDmn();
    Map<String, ActivityInstance> callActivityInstanceMap = createActivityInstanceMapForCallActivity();
    activityInstanceMap.putAll(callActivityInstanceMap);
    Optional<List<TemplateDetails>> templateDetails = TestHelper.createTemplateDetails();
    Authorization authorization = TestHelper.mockAuthorization(REALM_ID);
    multiConditionDefinition.setId(GlobalId.create(REALM_ID, LOCAL_ID));
    DefinitionDetails definitionDetails = TestHelper.mockDefinitionDetails(multiConditionDefinition,
        templateDetails.get().get(0), authorization);
    definitionDetails.setDefinitionKey(DEFINITION_KEY);
    definitionInstance.setDefinitionDetails(definitionDetails);
    definitionInstance.setActivityInstanceMap(activityInstanceMap);
    DefinitionActivityDetail definitionActivityDetail =
        DefinitionActivityDetail.builder().id(UUID.randomUUID().toString())
            .activityId(PARENT_ACTIVITY_ID).build();
    List<DefinitionActivityDetail> activityDetailList = Collections.singletonList(
        definitionActivityDetail);
    Mockito.when(definitionActivityDetailsRepository.saveAll(any()))
        .thenReturn(activityDetailList);
    multiStepSaveDefinitionDataStoreTask.execute(state);
    Mockito.verify(definitionActivityDetailsRepository, times(1))
        .saveAll(any());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void test_execute_throwsExceptionWhileSavingActivityDetails() {
    when(definitionServiceHelper.saveUpdateDefinitionDetails(any(), any(), any(), anyBoolean()))
        .thenReturn(multiConditionDefinition);
    Map<String, ActivityInstance> activityInstanceMap = createActivityInstanceMapForDmn();
    Map<String, ActivityInstance> callActivityInstanceMap = createActivityInstanceMapForCallActivity();
    activityInstanceMap.putAll(callActivityInstanceMap);
    definitionInstance.setActivityInstanceMap(activityInstanceMap);
    Mockito.when(definitionActivityDetailsRepository.saveAll(any()))
        .thenThrow(
            new WorkflowGeneralException(WorkflowError.MULTI_STEP_DEFINITION_SAVE_EXCEPTION));
    multiStepSaveDefinitionDataStoreTask.execute(state);
  }

  private Map<String, ActivityInstance> createActivityInstanceMapForDmn() {
    Map<String, ActivityInstance> activityInstanceMap = new HashMap<>();
    ActivityInstance decisionActivityInstance = ActivityInstance.builder()
        .dmnModelInstance(dmnModelInstance).build();
    activityInstanceMap.put(DMN_ACTIVITY_ID, decisionActivityInstance);
    return activityInstanceMap;
  }

  private Map<String, ActivityInstance> createActivityInstanceMapForCallActivity() {
    Map<String, ActivityInstance> activityInstanceMap = new HashMap<>();
    ActivityInstance childCallActivityInstance = ActivityInstance.builder().build();
    ActivityInstance parentCallActivityInstance = ActivityInstance.builder()
        .childActivityInstances(
            Collections.singletonMap(CHILD_ACTIVITY_ID, childCallActivityInstance)).build();
    activityInstanceMap.put(PARENT_ACTIVITY_ID, parentCallActivityInstance);
    return activityInstanceMap;
  }
}