package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.ENTITY_TYPE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.HANDLER_DETAILS;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.ON_DEMAND_APPROVAL;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.TASK_DETAILS;
import static org.mockito.ArgumentMatchers.any;

import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WASContext;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import com.intuit.v4.Authorization;
import com.intuit.v4.workflows.Template;
import java.util.HashMap;
import java.util.Map;
import junit.framework.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;

public class AppConnectParameterDetailsExtractorHelperTest {

  @Mock private FeatureFlagManager featureFlagManager;

  @InjectMocks
  private AppConnectParameterDetailsExtractorHelper appConnectParameterDetailsExtractorHelper;

  @Mock
  private PrecannedWorkflowParameterDetailsExtractor precannedWorkflowParameterDetailsExtractor;

  @Mock private DefaultParameterDetailsExtractor defaultParameterDetailsExtractor;

  @Mock private CustomWorkflowUserConfiguredParameterDetailsExtractor customWorkflowUserConfiguredParameterDetailsExtractor;

  @Mock
  private CustomWorkflowNonUserConfiguredParameterDetailsExtractor
      customWorkflowNonUserConfiguredParameterDetailsExtractor;

  @Mock
  private MultiStepParameterDetailsExtractor multiStepParameterDetailsExtractor;

  @Spy
  private CustomWorkflowConfig customWorkflowConfig;
  @Mock
  private OnDemandParameterDetailExtractor onDemandParameterDetailExtractor;

  private DefinitionDetails definitionDetails;

  @Before
  public void init() throws Exception {
    customWorkflowConfig = TestHelper.loadCustomConfig();
    MockitoAnnotations.openMocks(this);
    String PLACEHOLDER_VALUES_PATH = "placeholder/appconnect_test_placeholder.json";
    definitionDetails =
        DefinitionDetails.builder()
            .placeholderValue(TestHelper.readResourceAsString(PLACEHOLDER_VALUES_PATH))
            .ownerId(1234L)
            .build();
    setContext();
  }

  @Test
  public void testGetPrecannedAppConnectParameterExtractorFlagOn() {
    Mockito.when(featureFlagManager.getBoolean(any(String.class), any(Boolean.class), any(String.class))).thenReturn(true);
    Template templateDetails = new Template().name("depositbankreminder");
    definitionDetails.setTemplateDetails(TemplateDetails.builder().templateName("depositbankreminder").build());
    AppConnectParameterDetailExtractor appConnectParameterDetailExtractor =
        appConnectParameterDetailsExtractorHelper.getAppConnectParameterDetailExtractor(
            templateDetails, definitionDetails, "{}",WorkerActionRequest.builder().inputVariables(Map.of(ENTITY_TYPE,"bill")).build());
    Assert.assertTrue(
        appConnectParameterDetailExtractor instanceof PrecannedWorkflowParameterDetailsExtractor);
  }

  @Test
  public void testGetPrecannedAppConnectParameterExtractorFlagOnWithContext() {
    Mockito.when(featureFlagManager.getBoolean(any(String.class), any(Boolean.class), any(String.class))).thenReturn(true);
    Template templateDetails = new Template().name("depositbankreminder");
    definitionDetails.setTemplateDetails(TemplateDetails.builder().templateName("depositbankreminder").build());
    AppConnectParameterDetailExtractor appConnectParameterDetailExtractor =
        appConnectParameterDetailsExtractorHelper.getAppConnectParameterDetailExtractor(
            templateDetails, definitionDetails,"{}", WorkerActionRequest.builder().inputVariables(Map.of(ENTITY_TYPE,"bill")).build());
    Assert.assertTrue(
        appConnectParameterDetailExtractor instanceof PrecannedWorkflowParameterDetailsExtractor);
  }

  @Test
  public void testGetCustomAppConnectParameterExtractorFlagOn() {

    Mockito.when(featureFlagManager.getBoolean(any(String.class), any(Boolean.class), any(String.class))).thenReturn(true);
    Template templateDetails = new Template().name("customReminder");
    definitionDetails.setTemplateDetails(TemplateDetails.builder().templateName("depositbankreminder").build());
    Map<String, String> inputVariables = new HashMap<>();
    inputVariables.put(ENTITY_TYPE,"bill");
    inputVariables.put(HANDLER_DETAILS,"{}");
    inputVariables.put(TASK_DETAILS,"{}");
    AppConnectParameterDetailExtractor appConnectParameterDetailExtractor =
        appConnectParameterDetailsExtractorHelper.getAppConnectParameterDetailExtractor(
            templateDetails, definitionDetails,"{}", WorkerActionRequest.builder().inputVariables(inputVariables).build());
    Assert.assertTrue(
        appConnectParameterDetailExtractor instanceof CustomWorkflowUserConfiguredParameterDetailsExtractor);
  }

  @Test
  public void testGetMultiStepParameterDetailsExtractorFlagOn() {

    Mockito.when(featureFlagManager.getBoolean(any(String.class), any(Boolean.class), any(String.class))).thenReturn(true);
    Template templateDetails = new Template().name("customReminder");
    definitionDetails.setTemplateDetails(TemplateDetails.builder().templateName("depositbankreminder").build());
    Map<String, String> inputVariables = new HashMap<>();
    inputVariables.put(ENTITY_TYPE,"bill");
    inputVariables.put(HANDLER_DETAILS,"{}");
    inputVariables.put(TASK_DETAILS,"{}");
    AppConnectParameterDetailExtractor appConnectParameterDetailExtractor =
        appConnectParameterDetailsExtractorHelper.getAppConnectParameterDetailExtractor(
            templateDetails, definitionDetails,"{}", WorkerActionRequest.builder().inputVariables(inputVariables).isCalledProcess(true).build());
    Assert.assertTrue(
        appConnectParameterDetailExtractor instanceof MultiStepParameterDetailsExtractor);
  }

  @Test
  public void testGetPrecannedAppConnectParameterExtractorFlagOnDefinitionDetailEmpty() {
    definitionDetails = DefinitionDetails.builder().placeholderValue(null).build();
    Mockito.when(featureFlagManager.getBoolean(any(String.class), any(Boolean.class), any(String.class)))
        .thenReturn(true);
    Template templateDetails = new Template().name("depositbankreminder");
    AppConnectParameterDetailExtractor appConnectParameterDetailExtractor =
        appConnectParameterDetailsExtractorHelper.getAppConnectParameterDetailExtractor(
            templateDetails, definitionDetails,"{}", WorkerActionRequest.builder().inputVariables(Map.of(ENTITY_TYPE,"bill")).build());
    Assert.assertTrue(
        appConnectParameterDetailExtractor instanceof DefaultParameterDetailsExtractor);
  }

  @Test
  public void testGetCustomAppConnectParameterExtractorFlagOnDefinitionDetailEmpty() {
    definitionDetails = DefinitionDetails.builder().placeholderValue(null).build();
    Mockito.when(featureFlagManager.getBoolean(any(String.class), any(Boolean.class), any(String.class), any(Long.class)))
        .thenReturn(true);
    Template templateDetails = new Template().name("customReminder");
    AppConnectParameterDetailExtractor appConnectParameterDetailExtractor =
        appConnectParameterDetailsExtractorHelper.getAppConnectParameterDetailExtractor(
            templateDetails, definitionDetails,"{}", WorkerActionRequest.builder().inputVariables(Map.of(ENTITY_TYPE,"bill")).build());
    Assert.assertTrue(
        appConnectParameterDetailExtractor instanceof DefaultParameterDetailsExtractor);
  }

  @Test
  public void testGetCustomParameterExtractorEmptyHandlerDetailEmpty() {
    Mockito.when(featureFlagManager.getBoolean(any(String.class), any(Boolean.class), any(String.class)))
        .thenReturn(true);
    Template templateDetails = new Template().name("customReminder");
    definitionDetails.setTemplateDetails(TemplateDetails.builder().templateName("depositbankreminder").build());
    Map<String, String> inputVariables = new HashMap<>();
    inputVariables.put(ENTITY_TYPE,"bill");
    inputVariables.put(HANDLER_DETAILS,"{\"taskHandler\":\"appconnect\",\"actionName\":\"executeWorkflowAction\",\"handlerId\":\"intuit-workflows/was-send-notification\",\"recordType\":null,\"responseFields\":null,\"handlerScope\":null}");
    inputVariables.put(TASK_DETAILS,"{}");
    AppConnectParameterDetailExtractor appConnectParameterDetailExtractor =
        appConnectParameterDetailsExtractorHelper.getAppConnectParameterDetailExtractor(
            templateDetails,
            definitionDetails,
            "{\"taskHandler\":\"appconnect\",\"actionName\":\"executeWorkflowAction\",\"handlerId\":\"intuit-workflows/was-send-notification\",\"recordType\":null,\"responseFields\":null,\"handlerScope\":null}",
    WorkerActionRequest.builder().inputVariables(inputVariables).build());
    Assert.assertTrue(
        appConnectParameterDetailExtractor instanceof DefaultParameterDetailsExtractor);
  }

  @Test
  public void testGetCustomParameterExtractorWithTaskDetailsTrue() {
    Mockito.when(
            featureFlagManager.getBoolean(any(String.class), any(Boolean.class), any(String.class)))
        .thenReturn(true);
    Template templateDetails = new Template().name("customApproval");
    definitionDetails.setTemplateDetails(
        TemplateDetails.builder().templateName("invoiceApproval").build());
    Map<String,String> inputVariables = new HashMap<>();
    inputVariables.put(WorkflowConstants.HANDLER_DETAILS,"{\"taskHandler\":\"appconnect\",\"actionName\":\"executeWorkflowAction\",\"handlerId\":\"intuit-workflows/was-send-notification\",\"recordType\":null,\"responseFields\":null,\"handlerScope\":null}");
    inputVariables.put(WorkflowConstants.TASK_DETAILS,getTaskDetails(true));
    inputVariables.put(ENTITY_TYPE,"bill");
    AppConnectParameterDetailExtractor appConnectParameterDetailExtractor =
        appConnectParameterDetailsExtractorHelper.getAppConnectParameterDetailExtractor(
            templateDetails,
            definitionDetails,
            "{\"taskHandler\":\"appconnect\",\"actionName\":\"executeWorkflowAction\",\"handlerId\":\"intuit-workflows/was-send-notification\",\"recordType\":null,\"responseFields\":null,\"handlerScope\":null}",
    WorkerActionRequest.builder().inputVariables(inputVariables).build());
    Assert.assertTrue(
        appConnectParameterDetailExtractor instanceof DefaultParameterDetailsExtractor);
  }

  @Test
  public void testGetCustomParameterExtractorWithTaskDetailsFalse() {
    Mockito.when(
            featureFlagManager.getBoolean(any(String.class), any(Boolean.class), any(String.class)))
        .thenReturn(true);
    Template templateDetails = new Template().name("customApproval");
    definitionDetails.setTemplateDetails(
        TemplateDetails.builder().templateName("invoiceApproval").build());
    Map<String, String> inputVariables = new HashMap<>();
    inputVariables.put(WorkflowConstants.HANDLER_DETAILS,
        "{\"taskHandler\":\"appconnect\",\"actionName\":\"executeWorkflowAction\",\"handlerId\":\"intuit-workflows/was-send-notification\",\"recordType\":null,\"responseFields\":null,\"handlerScope\":null}");
    inputVariables.put(WorkflowConstants.TASK_DETAILS, getTaskDetails(false));
    inputVariables.put(ENTITY_TYPE,"bill");
    AppConnectParameterDetailExtractor appConnectParameterDetailExtractor =
        appConnectParameterDetailsExtractorHelper.getAppConnectParameterDetailExtractor(
            templateDetails,
            definitionDetails,
            "{\"taskHandler\":\"appconnect\",\"actionName\":\"executeWorkflowAction\",\"handlerId\":\"intuit-workflows/was-send-notification\",\"recordType\":null,\"responseFields\":null,\"handlerScope\":null}",
    WorkerActionRequest.builder().inputVariables(inputVariables).build());
    Assert.assertTrue(
        appConnectParameterDetailExtractor
            instanceof CustomWorkflowNonUserConfiguredParameterDetailsExtractor);
  }

  @Test
  public void testgetAppConnectParameterDetailExtractorForOnDemandApproval(){
    Map<String, String> inputVariables = new HashMap<>();
    inputVariables.put(ENTITY_TYPE,"bill");
    inputVariables.put(ON_DEMAND_APPROVAL,"true");
    inputVariables.put(WorkflowConstants.TASK_DETAILS,"{}");

    Template templateDetails = new Template().name("customApproval");
    String handlerDetails = "{\"taskHandler\":\"appconnect\",\"actionName\":\"executeWorkflowAction\",\"handlerId\":\"intuit-workflows/was-send-notification\",\"recordType\":null,\"responseFields\":null,\"handlerScope\":null}";
    AppConnectParameterDetailExtractor appConnectParameterDetailExtractor =
        appConnectParameterDetailsExtractorHelper.getAppConnectParameterDetailExtractor(
            templateDetails,
            definitionDetails,handlerDetails,
            WorkerActionRequest.builder().inputVariables(inputVariables).activityId("createTask").build());
    Assertions.assertTrue(appConnectParameterDetailExtractor instanceof OnDemandParameterDetailExtractor);

    // Non on demand task
     appConnectParameterDetailExtractor =
        appConnectParameterDetailsExtractorHelper.getAppConnectParameterDetailExtractor(
            templateDetails,
            definitionDetails,handlerDetails,
            WorkerActionRequest.builder().inputVariables(inputVariables).activityId("closeTask").build());
    Assertions.assertTrue(appConnectParameterDetailExtractor instanceof DefaultParameterDetailsExtractor);
  }
  @Test
  public void testGetCustomParameterExtractorWithTaskDetailsWithoutData() {
    Mockito.when(
            featureFlagManager.getBoolean(any(String.class), any(Boolean.class), any(String.class)))
        .thenReturn(true);
    Template templateDetails = new Template().name("customApproval");
    definitionDetails.setTemplateDetails(
        TemplateDetails.builder().templateName("invoiceApproval").build());
    Map<String, String> inputVariables = new HashMap<>();
    inputVariables.put(WorkflowConstants.HANDLER_DETAILS,
        "{\"taskHandler\":\"appconnect\",\"actionName\":\"executeWorkflowAction\",\"handlerId\":\"intuit-workflows/was-send-notification\",\"recordType\":null,\"responseFields\":null,\"handlerScope\":null}");
    inputVariables.put(WorkflowConstants.TASK_DETAILS, getTaskDetails(null));
    inputVariables.put(ENTITY_TYPE,"bill");
    AppConnectParameterDetailExtractor appConnectParameterDetailExtractor =
        appConnectParameterDetailsExtractorHelper.getAppConnectParameterDetailExtractor(
            templateDetails,
            definitionDetails,
            "{\"taskHandler\":\"appconnect\",\"actionName\":\"executeWorkflowAction\",\"handlerId\":\"intuit-workflows/was-send-notification\",\"recordType\":null,\"responseFields\":null,\"handlerScope\":null}",
            WorkerActionRequest.builder().inputVariables(inputVariables).build());
    Assert.assertTrue(
        appConnectParameterDetailExtractor instanceof DefaultParameterDetailsExtractor);
  }

  private void setContext() {
    Authorization authorization = new Authorization();
    authorization.putRealm(Long.toString(definitionDetails.getOwnerId()));
    WASContext.setAuthContext(authorization);
  }

  private String getTaskDetails(Boolean required) {
    return "{\n" + "  \"required\":" + "\"" + required + "\"" + "}";
  }
}
