package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ComputedVariableConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.ComputedVariableType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.ParameterDetailsValueType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import java.util.HashMap;
import java.util.Map;
import org.apache.commons.lang3.BooleanUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class FeatureFlagComputedVariableEvaluatorTest {

  @Mock private FeatureFlagManager featureFlagManager;
  @Mock private WASContextHandler contextHandler;

  @InjectMocks private FeatureFlagComputedVariableEvaluator featureFlagComputedVariableEvaluator;

  @Before
  public void init() {
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("1000");
  }

  @Test
  public void testEvaluateValueWhenFFTrue() {
    Mockito.when(
            featureFlagManager.getBoolean(
                Mockito.eq("sample-ff-name"), Mockito.eq(false), Mockito.any(), Mockito.any()))
        .thenReturn(true);

    String computedVariableResult =
        featureFlagComputedVariableEvaluator.evaluateValue(
            getWorkerActionRequest(), getParameterDetailsEntry());

    Assert.assertEquals(BooleanUtils.TRUE, computedVariableResult);
  }

  @Test
  public void testEvaluateValueWhenFFFalse() {
    Mockito.when(
            featureFlagManager.getBoolean(
                Mockito.eq("sample-ff-name"), Mockito.eq(false), Mockito.any(), Mockito.any()))
        .thenReturn(false);

    String computedVariableResult =
        featureFlagComputedVariableEvaluator.evaluateValue(
            getWorkerActionRequest(), getParameterDetailsEntry());

    Assert.assertEquals(BooleanUtils.FALSE, computedVariableResult);
  }

  @Test
  public void testEvaluateValueWhenFFDoesntExist() {
    String computedVariableResult =
        featureFlagComputedVariableEvaluator.evaluateValue(
            getWorkerActionRequest(), getParameterDetailsEntry());
    Assert.assertEquals(BooleanUtils.FALSE, computedVariableResult);
  }

  private Map.Entry<String, HandlerDetails.ParameterDetails> getParameterDetailsEntry() {
    HandlerDetails.ParameterDetails parameterDetails = new HandlerDetails.ParameterDetails();
    parameterDetails.setValueType(ParameterDetailsValueType.COMPUTED_VARIABLE);
    parameterDetails.setComputedVariableType(ComputedVariableType.FEATURE_FLAG);
    Map<String, String> computedVariableDetails = new HashMap<>();
    computedVariableDetails.put(ComputedVariableConstants.FEATURE_FLAG_NAME, "sample-ff-name");
    parameterDetails.setComputedVariableDetails(computedVariableDetails);
    return Map.entry("parameter1", parameterDetails);
  }

  private WorkerActionRequest getWorkerActionRequest() {
    return WorkerActionRequest.builder().definitionKey("sendForApproval").build();
  }
}
