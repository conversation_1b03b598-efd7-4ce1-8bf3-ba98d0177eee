package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.DEFINITION_NOT_FOUND;
import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.INVALID_PARAMETER_DETAILS;
import static com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler.PrecannedWorkflowParameterDetailsExtractorTest.PLACEHOLDER_VALUES_PATH;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ComputedVariableConstants.FEATURE_FLAG_NAME;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.ParameterDetailsValueType.COMPUTED_VARIABLE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.ParameterDetailsValueType.PROCESS_VARIABLE;
import static org.mockito.ArgumentMatchers.any;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.AppConnectConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.common.retry.WorkerRetryHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.camunda.template.schema.SchemaDecoder;
import com.intuit.appintgwkflw.wkflautomate.was.core.config.HandlerConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.common.IncidentTaskManager;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.WorkerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.impl.ProcessDetailsRepoService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.request.AppConnectTaskHandlerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.request.WorkflowTaskHandlerAction;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.response.WorkflowTaskHandlerResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.TaskHandlerName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.RetryHandlerName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.ComputedVariableType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails.ParameterDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qbo.oinp.adapter.AppConnectOINPBridge;
import org.apache.commons.lang3.BooleanUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.util.ReflectionTestUtils;

/** <AUTHOR> */
public class AppConnectWorkflowTaskHandlerHelperTest {

  @Mock private AppConnectConfig appConnectConfig;

  @Mock private DefinitionDetailsRepository definitionDetailsRepository;

  @Mock private RestAction appconnectWorkflowHeaderAction;
  @Mock private FeatureFlagManager featureFlagManager;
  @Mock private FieldValueFilterStrategy fieldValueFilterStrategy;
  
  @Mock private WASContextHandler wasContextHandler;

  @Mock private ParameterDetailsExtractorHelper parameterDetailsExtractorHelper;
  
  @InjectMocks private FeatureFlagComputedVariableEvaluator featureFlagComputedVariableEvaluator;

  @InjectMocks private AppConnectWorkflowTaskHandlerHelper appConnectActionHandlerHelper;

  private static final String parametersSchema =
      TestHelper.readResourceAsString("schema/testData/parameters.json");

  private static final String handlersSchema =
      TestHelper.readResourceAsString("schema/testData/handlers.json");

  private static Map<String, String> schema = new HashMap<>();
  private static final String MESSAGE = "Message";
  private static final String ACTIVITY_ID_SINGLE = "aId";
  private static final String ACTIVITY_ID_USER = "aId_123_f27bb550-d2d7-4018-9a0a-1f297fdaa49b";
  private static final String DEFINITION_ID = "123";
  private static final String INSTANCE_ID = "pId";
  private static final String HANDLER_ID = "hanlderId";
  private static final long OWNER_ID = 123l;
  private static final String WORKFLOW_ID = "wid";
  private static final String SUBJECT = "Subject";
  private static final int FIRST_VALUE = 0;

  static {
    schema.put(WorkFlowVariables.PARAMETERS_KEY.getName(), parametersSchema);
    schema.put(WorkFlowVariables.HANDLER_DETAILS_KEY.getName(), handlersSchema);
    schema.put(WorkflowConstants.INTUIT_REALMID, "123");
    schema.put(WorkflowConstants.DEFINITION_KEY, "customReminder_123_f27bb550-d2d7-4018-9a0a-1f297fdaa49b");
    schema.put(WorkFlowVariables.TASK_DETAILS_KEY.getName(), "{ \"required\": true}");
  }

  @InjectMocks
  private AppConnectWorkflowTaskHandler actionHandler;

  @Mock
  private AppConnectOINPBridge appConnectOINPBridge;

  @Mock
  private WorkerUtil workerUtil;

  @Mock
  private ProcessDetailsRepoService processDetailsRepoService;

  @Mock
  private MetricLogger metricLogger;

  @Mock
  private IncidentTaskManager incidentTaskManager;

  @Mock
  private WorkerRetryHelper workerRetryHelper;

  @Mock
  private HandlerConfig handlerConfig;

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
    Mockito.when(appConnectConfig.getProviderAppId()).thenReturn("TEST");
    Mockito.when(appConnectConfig.getTaskHandler()).thenReturn("/workflow-task-handler");

    Mockito.doCallRealMethod().when(fieldValueFilterStrategy).fetchFieldValueFromParameterDetail(Mockito.any(), Mockito.any());

    DefinitionDetails def = new DefinitionDetails();
    def.setWorkflowId("w123");
    Mockito.when(definitionDetailsRepository.findById(Long.parseLong("123")))
        .thenReturn(Optional.ofNullable(def));

    ComputedVariableEvaluators.addEvaluator(ComputedVariableType.FEATURE_FLAG, featureFlagComputedVariableEvaluator);
    Mockito.when(wasContextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("1000000");


    // Setup a default DefinitionDetails with non-null TemplateDetails.
    TemplateDetails templateDetails = TemplateDetails.builder()
            .id("template1")
            .allowMultipleDefinitions(true)
            .build();
    DefinitionDetails defaultDefinitionDetails = DefinitionDetails.builder()
            .templateDetails(templateDetails)
            .build();
    Mockito.when(processDetailsRepoService.findByProcessIdWithoutDefinitionData(any()))
            .thenReturn(Optional.of(defaultDefinitionDetails));
    // By default, simulate OINP Bridge returns false.
    Mockito.when(appConnectOINPBridge.initiateBridge(any(), any())).thenReturn(false);
    // Set fields via Reflection
    ReflectionTestUtils.setField(actionHandler, "metricLogger", metricLogger);
    ReflectionTestUtils.setField(actionHandler, "incidentTaskManager", incidentTaskManager);
    ReflectionTestUtils.setField(actionHandler, "workerRetryHelper", workerRetryHelper);
    ReflectionTestUtils.setField(actionHandler, "handlerConfig", handlerConfig);

  }

  @Test
  public void test() {
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("123")
            .processInstanceId("pId")
            .inputVariables(schema)
            .handlerId("hanlderId")
            .ownerId(123l)
            .workflowId("wid")
            .build();

    Optional<Map<String, ParameterDetails>> parameterDetailsMap =
        SchemaDecoder.getParametersForHandler(workerActionRequest.getInputVariables());

    DefinitionDetails definitionDetails = new DefinitionDetails();
    definitionDetails.setWorkflowId(WORKFLOW_ID);
    definitionDetails.setOwnerId(OWNER_ID);
    Mockito.when(definitionDetailsRepository.findByDefinitionId(DEFINITION_ID))
            .thenReturn(Optional.of(definitionDetails));

    AppConnectTaskHandlerRequest actionRequest =
            appConnectActionHandlerHelper.prepareTaskHandlerRequest(
                    workerActionRequest, parameterDetailsMap, definitionDetails);

    Assert.assertNotNull(actionRequest);
    Assert.assertNotNull(actionRequest.getEndpoint());
    Assert.assertNotNull(actionRequest.getInstanceId());
    Assert.assertNotNull(actionRequest.getProviderWorkflowId());
    Assert.assertNotNull(actionRequest.getWorkflowId());
    Assert.assertNotNull(actionRequest.getAction());
    WorkflowTaskHandlerAction action = actionRequest.getAction();
    Assert.assertNotNull(action.getInputs());
    Assert.assertNotNull(action.getInputs().get(FIRST_VALUE));
    Assert.assertNotNull(action.getInputs().get(FIRST_VALUE).getName());
    Assert.assertNotNull(action.getInputs().get(FIRST_VALUE).getValue());
  }

  @Test
  public void testFetchParamProcessVariablesForSingleDefinition() {
    schema.put(MESSAGE, parametersSchema);
    HandlerDetails handlerDetails = SchemaDecoder.getHandlerDetails(schema).get();

    WorkerActionRequest workerActionRequest =
            WorkerActionRequest.builder()
                    .activityId(ACTIVITY_ID_SINGLE)
                    .processDefinitionId(DEFINITION_ID)
                    .processInstanceId(INSTANCE_ID)
                    .inputVariables(schema)
                    .handlerId(HANDLER_ID)
                    .ownerId(123l)
                    .workflowId(WORKFLOW_ID)
                    .handlerDetails(handlerDetails)
                    .build();

    Optional<Map<String, ParameterDetails>> parameterDetailsMap =
            SchemaDecoder.getParametersForHandler(workerActionRequest.getInputVariables());
    parameterDetailsMap.get().get(MESSAGE).setValueType(PROCESS_VARIABLE);
    parameterDetailsMap.get().get(SUBJECT).setValueType(null);
    parameterDetailsMap.get().get(SUBJECT).setFieldValue(null);


    DefinitionDetails definitionDetails = new DefinitionDetails();
    definitionDetails.setWorkflowId(WORKFLOW_ID);
    definitionDetails.setOwnerId(OWNER_ID);
    TemplateDetails tempDetails = TemplateDetails.builder().templateName("invoiceApproval").build();
    definitionDetails.setTemplateDetails(tempDetails);
    definitionDetails.setPlaceholderValue(TestHelper.readResourceAsString(PLACEHOLDER_VALUES_PATH));
            Mockito.when(featureFlagManager.getBoolean(Mockito.anyString(), Mockito.anyBoolean(),Mockito.any(),
                Mockito.anyLong())).thenReturn(Boolean.TRUE);
    AppConnectTaskHandlerRequest actionRequest =
        appConnectActionHandlerHelper.prepareTaskHandlerRequest(
            workerActionRequest, parameterDetailsMap, definitionDetails);

    Assert.assertNotNull(actionRequest);
    Assert.assertNotNull(actionRequest.getEndpoint());
    Assert.assertNotNull(actionRequest.getInstanceId());
    Assert.assertNotNull(actionRequest.getProviderWorkflowId());
    Assert.assertNotNull(actionRequest.getWorkflowId());
    Assert.assertNotNull(actionRequest.getAction());
    WorkflowTaskHandlerAction action = actionRequest.getAction();
    Assert.assertNotNull(action.getInputs());
    Assert.assertNotNull(action.getInputs().get(FIRST_VALUE));
    Assert.assertNotNull(action.getInputs().get(FIRST_VALUE).getName());
    Assert.assertNotNull(action.getInputs().get(FIRST_VALUE).getValue());
  }

  @Test
  public void testFetchParamProcessVariablesForUserDefiniton() {
    schema.put(MESSAGE, parametersSchema);
    HandlerDetails handlerDetails = SchemaDecoder.getHandlerDetails(schema).get();

    WorkerActionRequest workerActionRequest =
            WorkerActionRequest.builder()
                    .activityId(ACTIVITY_ID_USER)
                    .processDefinitionId(DEFINITION_ID)
                    .processInstanceId(INSTANCE_ID)
                    .inputVariables(schema)
                    .handlerId(HANDLER_ID)
                    .ownerId(123l)
                    .workflowId(WORKFLOW_ID)
                    .handlerDetails(handlerDetails)
                    .build();

    Optional<Map<String, ParameterDetails>> parameterDetailsMap =
            SchemaDecoder.getParametersForHandler(workerActionRequest.getInputVariables());
    parameterDetailsMap.get().get(MESSAGE).setValueType(PROCESS_VARIABLE);
    parameterDetailsMap.get().get(SUBJECT).setValueType(null);
    parameterDetailsMap.get().get(SUBJECT).setFieldValue(null);

    DefinitionDetails definitionDetails = new DefinitionDetails();
    definitionDetails.setWorkflowId(WORKFLOW_ID);
    definitionDetails.setOwnerId(OWNER_ID);
    Mockito.when(definitionDetailsRepository.findByDefinitionId(DEFINITION_ID))
            .thenReturn(Optional.of(definitionDetails));

    AppConnectTaskHandlerRequest actionRequest =
            appConnectActionHandlerHelper.prepareTaskHandlerRequest(
                    workerActionRequest, parameterDetailsMap, definitionDetails);

    Assert.assertNotNull(actionRequest);
    Assert.assertNotNull(actionRequest.getEndpoint());
    Assert.assertNotNull(actionRequest.getInstanceId());
    Assert.assertNotNull(actionRequest.getProviderWorkflowId());
    Assert.assertNotNull(actionRequest.getWorkflowId());
    Assert.assertNotNull(actionRequest.getAction());
    WorkflowTaskHandlerAction action = actionRequest.getAction();
    Assert.assertNotNull(action.getInputs());
    Assert.assertNotNull(action.getInputs().get(FIRST_VALUE));
    Assert.assertNotNull(action.getInputs().get(FIRST_VALUE).getName());
    Assert.assertNotNull(action.getInputs().get(FIRST_VALUE).getValue());
  }

  @Test
  public void testFetchParamComputeVariables() {
    schema.put(MESSAGE, parametersSchema);
    HandlerDetails handlerDetails = SchemaDecoder.getHandlerDetails(schema).get();

    WorkerActionRequest workerActionRequest =
            WorkerActionRequest.builder()
                    .activityId(ACTIVITY_ID_USER)
                    .processDefinitionId(DEFINITION_ID)
                    .processInstanceId(INSTANCE_ID)
                    .inputVariables(schema)
                    .handlerId(HANDLER_ID)
                    .ownerId(123l)
                    .workflowId(WORKFLOW_ID)
                    .handlerDetails(handlerDetails)
                    .build();

    Optional<Map<String, ParameterDetails>> parameterDetailsMap =
            SchemaDecoder.getParametersForHandler(workerActionRequest.getInputVariables());
    parameterDetailsMap.get().get(MESSAGE).setValueType(COMPUTED_VARIABLE);
    parameterDetailsMap.get().get(MESSAGE).setComputedVariableType(ComputedVariableType.FEATURE_FLAG);
    parameterDetailsMap.get().get(MESSAGE).setComputedVariableDetails(Map.of(FEATURE_FLAG_NAME, "sample-ff-name"));

    DefinitionDetails definitionDetails = new DefinitionDetails();
    definitionDetails.setWorkflowId(WORKFLOW_ID);
    definitionDetails.setOwnerId(OWNER_ID);
    Mockito.when(definitionDetailsRepository.findByDefinitionId(DEFINITION_ID))
            .thenReturn(Optional.of(definitionDetails));
    
    Mockito.when(featureFlagManager.getBoolean(Mockito.eq("sample-ff-name"), Mockito.eq(false), Mockito.any(), Mockito.any())).thenReturn(true);

    AppConnectTaskHandlerRequest actionRequest =
            appConnectActionHandlerHelper.prepareTaskHandlerRequest(
                    workerActionRequest, parameterDetailsMap, definitionDetails);

    Assert.assertNotNull(actionRequest);
    Assert.assertNotNull(actionRequest.getEndpoint());
    Assert.assertNotNull(actionRequest.getInstanceId());
    Assert.assertNotNull(actionRequest.getProviderWorkflowId());
    Assert.assertNotNull(actionRequest.getWorkflowId());
    Assert.assertNotNull(actionRequest.getAction());
    WorkflowTaskHandlerAction action = actionRequest.getAction();
    Assert.assertNotNull(action.getInputs());
    Assert.assertNotNull(action.getInputs().get(FIRST_VALUE));
    Assert.assertNotNull(action.getInputs().get(FIRST_VALUE).getName());
    Assert.assertNotNull(action.getInputs().get(FIRST_VALUE).getValue());
    Assert.assertEquals(MESSAGE, action.getInputs().get(FIRST_VALUE).getName());
    Assert.assertEquals(BooleanUtils.TRUE, action.getInputs().get(FIRST_VALUE).getValue());
  }


  @Test
  public void testPrepareTaskHandlerResponse() {
    HandlerDetails handlerDetails = SchemaDecoder.getHandlerDetails(schema).get();
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .inputVariables(schema)
            .handlerDetails(handlerDetails)
            .activityId("123")
            .build();
    Map<String, Object> data = new HashMap<>();
    data.put("taskId", "123");
    data.put("projectId", "345");
    WorkflowTaskHandlerResponse handlerResponse = new WorkflowTaskHandlerResponse();
    handlerResponse.setSuccess("true");
    handlerResponse.setData(data);
    WASHttpResponse<WorkflowTaskHandlerResponse> response =
        WASHttpResponse.<WorkflowTaskHandlerResponse>builder()
            .status(HttpStatus.OK)
            .response(handlerResponse)
            .isSuccess2xx(true)
            .build();
    Map<String, Object> result =
        appConnectActionHandlerHelper.prepareTaskHandlerResponse(workerActionRequest, response);
    Assert.assertNotNull(result);
    Assert.assertEquals("123", result.get("taskId"));
    Assert.assertEquals("345", result.get("projectId"));
  }

  @Test
  public void testPrepareTaskHandlerNoDataResponse() {
    HandlerDetails handlerDetails = SchemaDecoder.getHandlerDetails(schema).get();
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .inputVariables(schema)
            .handlerDetails(handlerDetails)
            .activityId("123")
            .build();
    WorkflowTaskHandlerResponse handlerResponse = new WorkflowTaskHandlerResponse();
    handlerResponse.setSuccess("true");
    WASHttpResponse<WorkflowTaskHandlerResponse> response =
        WASHttpResponse.<WorkflowTaskHandlerResponse>builder()
            .status(HttpStatus.OK)
            .response(handlerResponse)
            .isSuccess2xx(true)
            .build();
    Map<String, Object> result =
        appConnectActionHandlerHelper.prepareTaskHandlerResponse(workerActionRequest, response);
    Assert.assertNotNull(result);
  }

  @Test
  public void testPrepareTaskHandlerResponse_noResponse() {
    Map<String, String> schema = new HashMap<>();
    schema.put(WorkFlowVariables.PARAMETERS_KEY.getName(), parametersSchema);
    schema.put(
            WorkFlowVariables.HANDLER_DETAILS_KEY.getName(),
            TestHelper.readResourceAsString("schema/testData/handlersNoResponse.json"));
    HandlerDetails handlerDetails = SchemaDecoder.getHandlerDetails(schema).get();
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .inputVariables(schema)
            .handlerDetails(handlerDetails)
            .activityId(ACTIVITY_ID_USER)
            .build();

    WorkflowTaskHandlerResponse handlerResponse = new WorkflowTaskHandlerResponse();
    handlerResponse.setSuccess("true");
    WASHttpResponse<WorkflowTaskHandlerResponse> response =
        WASHttpResponse.<WorkflowTaskHandlerResponse>builder()
            .status(HttpStatus.OK)
            .response(handlerResponse)
            .isSuccess2xx(true)
            .build();
    Map<String, Object> result =
        appConnectActionHandlerHelper.prepareTaskHandlerResponse(workerActionRequest, response);
    Assert.assertNotNull(result);
    Assert.assertEquals("true", result.get("aId_123_f27bb550-d2d7-4018-9a0a-1f297fdaa49b_response"));
  }

  @Test
  public void testPrepareTaskHandlerInput_ParameterDetailsNotFound() {
    Map<String, String> schema = new HashMap<>();
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder().inputVariables(schema).activityId("123").build();
    Optional<Map<String, ParameterDetails>> parameterDetailsMap =
        SchemaDecoder.getParametersForHandler(workerActionRequest.getInputVariables());

    try {
      appConnectActionHandlerHelper.prepareTaskHandlerInputs(
          workerActionRequest, parameterDetailsMap);
      Assert.fail();
    } catch (WorkflowGeneralException e) {
      Assert.assertEquals(
          WorkflowError.INVALID_PARAMETER_DETAILS.getErrorMessage(), e.getMessage());
    }
  }

  @Test(expected = WorkflowRetriableException.class)
  public void testExecute202AcceptedForRetryScenario() {
    String url ="https://e2e.api.intuit.com/appconnect/connector/workflow-task-handler";
    WorkflowTaskHandlerResponse handlerResponse = new WorkflowTaskHandlerResponse();
    handlerResponse.setSuccess("true");
    WASHttpResponse<WorkflowTaskHandlerResponse> response =
        WASHttpResponse.<WorkflowTaskHandlerResponse>builder()
            .status(HttpStatus.ACCEPTED)
            .response(handlerResponse)
            .isSuccess2xx(true)
            .build();
    final WorkflowTaskHandlerAction workflowTaskHandlerAction =
        WorkflowTaskHandlerAction.builder().bpmnTaskId("bpmnID").build();
    final AppConnectTaskHandlerRequest actionRequest =
        AppConnectTaskHandlerRequest.builder()
            .workflowId("TEST")
            .instanceId("iID")
            .providerAppId("pId")
            .providerWorkflowId("6788")
            .realmId("968787")
            .endpoint(url)
            .action(workflowTaskHandlerAction)
            .build();

    final HttpHeaders requestHeaders = new HttpHeaders();
    requestHeaders.setContentType(MediaType.APPLICATION_JSON);
    requestHeaders.set("CONTENT_TYPE", "WORKFLOW");
    final WASHttpRequest<AppConnectTaskHandlerRequest, WorkflowTaskHandlerResponse> wasHttpRequest =
        WASHttpRequest.<AppConnectTaskHandlerRequest, WorkflowTaskHandlerResponse>builder()
            .url(url)
            .request(actionRequest)
            .httpMethod(HttpMethod.POST)
            .requestHeaders(requestHeaders)
            .retryHandler(RetryHandlerName.STATUS_CODE)
            .build();
    Mockito.when(appconnectWorkflowHeaderAction.execute(wasHttpRequest)).thenReturn(response);
    appConnectActionHandlerHelper.executeAction(
        wasHttpRequest, "hid");
  }

  @Test
  public void testExecuteAction() {
    String url = "https://e2e.api.intuit.com/appconnect/connector/workflow-task-handler";
    WorkflowTaskHandlerResponse handlerResponse = new WorkflowTaskHandlerResponse();
    handlerResponse.setSuccess("true");
    WASHttpResponse<WorkflowTaskHandlerResponse> response =
        WASHttpResponse.<WorkflowTaskHandlerResponse>builder()
            .status(HttpStatus.OK)
            .response(handlerResponse)
            .isSuccess2xx(true)
            .build();
    final WorkflowTaskHandlerAction workflowTaskHandlerAction =
        WorkflowTaskHandlerAction.builder().bpmnTaskId("bpmnID").build();
    final AppConnectTaskHandlerRequest actionRequest =
        AppConnectTaskHandlerRequest.builder()
            .workflowId("TEST")
            .instanceId("iID")
            .providerAppId("pId")
            .providerWorkflowId("6788")
            .realmId("968787")
            .endpoint(url)
            .action(workflowTaskHandlerAction)
            .build();

    final HttpHeaders requestHeaders = new HttpHeaders();
    requestHeaders.setContentType(MediaType.APPLICATION_JSON);
    requestHeaders.set("CONTENT_TYPE", "WORKFLOW");
    final WASHttpRequest<AppConnectTaskHandlerRequest, WorkflowTaskHandlerResponse> wasHttpRequest =
        WASHttpRequest.<AppConnectTaskHandlerRequest, WorkflowTaskHandlerResponse>builder()
            .url(url)
            .request(actionRequest)
            .httpMethod(HttpMethod.POST)
            .requestHeaders(requestHeaders)
            .retryHandler(RetryHandlerName.STATUS_CODE)
            .build();
    Mockito.when(appconnectWorkflowHeaderAction.execute(wasHttpRequest)).thenReturn(response);
    try {
      appConnectActionHandlerHelper.executeAction(wasHttpRequest, "hid");
    } catch (Exception e) {
      Assert.fail("The above method should not fail");
    }
  }

  @Test
  public void test_getAppConnectDuzzitException() {
    WorkflowTaskHandlerResponse handlerResponse = new WorkflowTaskHandlerResponse();
    handlerResponse.setSuccess("false");
    handlerResponse.setError("error");
    WASHttpResponse<WorkflowTaskHandlerResponse> response =
        WASHttpResponse.<WorkflowTaskHandlerResponse>builder()
            .status(HttpStatus.OK)
            .response(handlerResponse)
            .error("error")
            .build();
    String handlerId = "hid";
    Assert.assertTrue(
        appConnectActionHandlerHelper
            .getAppConnectDuzzitException(handlerId, response)
            .getErrorCode()
            .contains("EXTERNAL_TASK_HANDLER_ERROR"));
  }

  @Test
  public void test_getAppConnectDuzzitExceptionAccessFailure() {
    WorkflowTaskHandlerResponse handlerResponse = new WorkflowTaskHandlerResponse();
    handlerResponse.setSuccess("false");
    handlerResponse.setError("error");
    WASHttpResponse<WorkflowTaskHandlerResponse> response =
            WASHttpResponse.<WorkflowTaskHandlerResponse>builder()
                    .status(HttpStatus.OK)
                    .response(handlerResponse)
                    .error("IAC-001002")
                    .build();
    String handlerId = "hid";
    Assert.assertTrue(
            appConnectActionHandlerHelper
                    .getAppConnectDuzzitException(handlerId, response)
                    .getErrorCode()
                    .contains("EXTERNAL_TASK_ACCESS_FAILURE"));
  }

  @Test
  public void test_formatHandlerId() {
    String handlerId = "intuit-workflows/was-update-task";
    Assert.assertEquals(
        "WAS_UPDATE_TASK", appConnectActionHandlerHelper.formatHandlerId(handlerId));
  }

  // ----- Test for invalid parameter details -----
  @Test
  public void testInvalidParameterDetails() {
    // Build a schema with handlerDetails and invalid/missing parameter details.
    Map<String, String> testSchema = new HashMap<>();
    testSchema.put("handlerDetails", "{\"taskHandler\":\"appconnect\",\"actionName\":\"executeWorkflowAction\",\"handlerId\":\"intuit-workflows/was-send-notification\"}");
    testSchema.put(WorkflowConstants.TASK_DETAILS, "{}");
    testSchema.put(WorkflowConstants.DEFINITION_KEY, "2121312213");
    testSchema.put(WorkflowConstants.INTUIT_REALMID, "362727827");
    WorkerActionRequest request = WorkerActionRequest.builder()
            .activityId("act1")
            .processDefinitionId("def1")
            .processInstanceId("proc1")
            .inputVariables(testSchema)
            .build();
    // Simulate valid workerUtil validation.
    Mockito.when(workerUtil.validate(request)).thenReturn(request);
    // Simulate extraction returning empty parameter details => triggers INVALID_PARAMETER_DETAILS.
//    Mockito.when(parameterDetailsExtractorHelper.getParameterDetails(any(), any()))
//            .thenReturn(Optional.empty());
    try {
      actionHandler.executeAction(request);
      Assert.fail("Should throw WorkflowGeneralException");
    } catch (WorkflowGeneralException ex) {
      Assert.assertTrue(ex.getMessage().contains(INVALID_PARAMETER_DETAILS.getErrorMessage()));
    }
  }

  // ----- Test for invalid process details -----
  @Test
  public void testInvalidProcessDetails() {
    // Simulate processDetailsRepoService returns empty.
    Mockito.when(processDetailsRepoService.findByProcessIdWithoutDefinitionData(any()))
            .thenReturn(Optional.empty());
    WorkerActionRequest request = WorkerActionRequest.builder()
            .activityId("act2")
            .processDefinitionId("def2")
            .processInstanceId("proc2")
            .inputVariables(schema)
            .build();
    Mockito.when(workerUtil.validate(request)).thenReturn(request);
    // For parameter extraction, simulate non-empty parameter details.
    Mockito.when(parameterDetailsExtractorHelper.getParameterDetails(any(), any()))
            .thenReturn(Optional.of(Collections.emptyMap()));
    try {
      actionHandler.executeAction(request);
      Assert.fail("Should throw WorkflowGeneralException");
    } catch (WorkflowGeneralException ex) {
      Assert.assertTrue(ex.getMessage().contains(DEFINITION_NOT_FOUND.getErrorMessage()));
    }
  }


  // ----- Test for getName() method -----
  @Test
  public void testGetName() {
    Assert.assertEquals(TaskHandlerName.APP_CONNECT_WORKFLOW_TASK_ACTION_HANDLER, actionHandler.getName());
  }

}
