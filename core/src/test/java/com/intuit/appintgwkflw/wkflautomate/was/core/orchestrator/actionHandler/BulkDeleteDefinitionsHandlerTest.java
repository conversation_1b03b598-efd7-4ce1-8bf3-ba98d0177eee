package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import static org.mockito.ArgumentMatchers.any;

import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.UserContributionService;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.DefinitionServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.TaskHandlerName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.intuit.appintgwkflw.wkflautomate.was.workflowvariability.DefinitionPendingDeletion;
import com.intuit.v4.Authorization;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

public class BulkDeleteDefinitionsHandlerTest {
  @InjectMocks private BulkDeleteDefinitionsHandler bulkDeleteDefinitionsHandler;
  @Mock private UserContributionService userContributionService;
  @Mock private DefinitionServiceHelper definitionServiceHelper;
  @Mock private MetricLogger metricLogger;
  @Mock private TemplateDetails bpmnTemplateDetail;
  private Authorization authorization = TestHelper.mockAuthorization("123");

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
    ReflectionTestUtils.setField(bulkDeleteDefinitionsHandler, "metricLogger", metricLogger);
  }

  @Test
  public void testGetName() {
    Assert.assertEquals(
        TaskHandlerName.BULK_DELETE_DEFINITIONS_HANDLER, bulkDeleteDefinitionsHandler.getName());
  }

  @Test
  public void testMetricLogger() {
    Map<String, String> testSchema = new HashMap<>();
    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail, authorization, "123");
    DefinitionPendingDeletion definitionPendingDeletion =
        DefinitionPendingDeletion.buildFromDefinitionDetails(definitionDetail);
    testSchema.put(
        WorkflowConstants.DEFINITION_DETAILS_PENDING_DELETION,
        ObjectConverter.toJson(Arrays.asList(definitionPendingDeletion)));
    Mockito.doThrow(WorkflowGeneralException.class)
        .when(definitionServiceHelper)
        .updateInternalStatusAndPublishDomainEvent(any(), any());
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId(null)
            .processInstanceId("iId")
            .inputVariables(testSchema)
            .handlerId("hId")
            .ownerId(1234L)
            .build();

    try {
      bulkDeleteDefinitionsHandler.execute(workerActionRequest);
      Assert.fail();
    } catch (WorkflowGeneralException e) {
      Mockito.verify(metricLogger, Mockito.times(1)).logErrorMetric(any(), any(), any());
    }
  }

  @Test
  public void testExecuteAction_Success() {
    Map<String, String> testSchema = new HashMap<>();
    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail, authorization, "123");
    DefinitionPendingDeletion definitionPendingDeletion =
        DefinitionPendingDeletion.buildFromDefinitionDetails(definitionDetail);
    testSchema.put(
        WorkflowConstants.DEFINITION_DETAILS_PENDING_DELETION,
        ObjectConverter.toJson(Arrays.asList(definitionPendingDeletion)));
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId(null)
            .processInstanceId("iId")
            .inputVariables(testSchema)
            .handlerId("hId")
            .ownerId(1234L)
            .build();

    Map<String, Object> response = bulkDeleteDefinitionsHandler.execute(workerActionRequest);
    Mockito.verify(userContributionService, Mockito.times(1)).deleteAllPublishedTemplates(any(),any(List.class));

    Mockito.verify(definitionServiceHelper, Mockito.times(1))
        .updateInternalStatusAndPublishDomainEvent(any(), any());
    Assert.assertEquals(response.get("aId_response"), Boolean.TRUE.toString());
  }
}
