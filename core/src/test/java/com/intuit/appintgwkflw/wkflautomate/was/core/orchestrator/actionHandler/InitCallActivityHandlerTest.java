package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper.loadCustomConfig;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.ENTITY_TYPE;
import static org.mockito.ArgumentMatchers.any;

import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.MultiStepConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowNonRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionActivityDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.impl.ProcessDetailsRepoService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import com.intuit.async.execution.request.State;
import com.intuit.v4.Authorization;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.springframework.test.util.ReflectionTestUtils;

/**
 * <AUTHOR>
 */
public class InitCallActivityHandlerTest {

  @InjectMocks
  private InitCallActivityHandler initCallActivityHandler;

  @Mock
  private ProcessDetailsRepoService processDetailsRepoService;

  @Mock
  private DefinitionActivityDetailsRepository definitionActivityDetailsRepository;

  @Mock
  private TemplateDetails bpmnTemplateDetail;

  @Mock
  private MultiStepConfig multiStepConfig;

  @Spy
  CustomWorkflowConfig customWorkflowConfig;

  @Mock
  private MetricLogger metricLogger;
  private final static String OWNER_ID = "123";
  private final static String DEFINITION_ID = "dId";
  Authorization authorization = TestHelper.mockAuthorization(OWNER_ID);

  private static Map<String, String> schema = new HashMap<>();

  @Before
  public void init() throws Exception {
    customWorkflowConfig = loadCustomConfig();
    MockitoAnnotations.initMocks(this);
    ReflectionTestUtils.setField(initCallActivityHandler, "metricLogger", metricLogger);
  }

  private static final String parametersSchema =
      TestHelper.readResourceAsString("schema/testData/downgradeParameters.json");

  static {
    schema.put(WorkFlowVariables.PARAMETERS_KEY.getName(), parametersSchema);
    schema.put(WorkflowConstants.INTUIT_REALMID, "123");
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testMissingInputVariables() {
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .handlerId("hId")
            .ownerId(123L)
            .build();
    initCallActivityHandler.executeAction(workerActionRequest);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testMissingActivityId() {
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId("hId")
            .ownerId(123L)
            .build();
    initCallActivityHandler.executeAction(workerActionRequest);
  }

  @Test
  public void testIfOnDemandWorkflow() {
    Map<String, String> inputVariables = new HashMap<>();
    inputVariables.put(WorkflowConstants.ON_DEMAND_APPROVAL, Boolean.TRUE.toString());
    inputVariables.put(ENTITY_TYPE, "bill");
    WorkerActionRequest workerActionRequest =
            WorkerActionRequest.builder()
                    .activityId("aId")
                    .processDefinitionId("dId")
                    .processInstanceId("iId")
                    .inputVariables(inputVariables)
                    .definitionKey("customApproval")
                    .handlerId("hId")
                    .ownerId(123L)
                    .build();
    Map<String, Object> stringObjectMap = initCallActivityHandler.executeAction(workerActionRequest);
    Assertions.assertEquals(2, stringObjectMap.size());

    inputVariables.put(ENTITY_TYPE, null);
    Assertions.assertThrows(WorkflowNonRetriableException.class, ()-> initCallActivityHandler.executeAction(workerActionRequest));
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testMissingRootProcessInstanceId() {
    schema.put(WorkflowConstants.ACTIVITY_ID, "action-2");
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId("hId")
            .ownerId(123L)
            .build();
    initCallActivityHandler.executeAction(workerActionRequest);
  }

  @Test
  public void testExecuteAction_Success() {
    schema.put(WorkflowConstants.ACTIVITY_ID, "action-2");
    schema.put(WorkflowConstants.ROOT_PROCESS_INSTANCE_ID, "rip-1");
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId("hId")
            .ownerId(123L)
            .build();
    State state = new State();
    state.addValue(WorkflowConstants.ACTIVITY_ID, "action-2");
    state.addValue(WorkflowConstants.ROOT_PROCESS_INSTANCE_ID, "rpi-1");

    DefinitionDetails definitionDetails = TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail,
        authorization, DEFINITION_ID);

    Mockito.when(processDetailsRepoService.findByProcessIdWithoutDefinitionData(
            Mockito.any()))
        .thenReturn(Optional.of(definitionDetails));

    DefinitionActivityDetail parentDefinitionActivityDetail = TestHelper.mockDefinitionActivityDetails(
        "action-1",
        null
    );

    List<DefinitionActivityDetail> childDefinitionActivityDetails = new ArrayList<>();

    childDefinitionActivityDetails.add(
        TestHelper.mockDefinitionActivityDetails(
            "createTask",
            TestHelper.getMockUserAttributes(true)
        )
    );
    childDefinitionActivityDetails.add(
        TestHelper.mockDefinitionActivityDetails(
            "sendCompanyEmail",
            TestHelper.getMockUserAttributes(false)
        )
    );
    childDefinitionActivityDetails.add(
        TestHelper.mockDefinitionActivityDetails(
            "sendPushNotification",
            TestHelper.getMockUserAttributes(true)
        )
    );
    parentDefinitionActivityDetail.setChildActivityDetails(childDefinitionActivityDetails);

    Mockito.when(definitionActivityDetailsRepository.
            findDefinitionActivityDetailByActivityIdAndDefinitionDetails_DefinitionId(
                Mockito.any(), Mockito.any()))
        .thenReturn(Optional.of(parentDefinitionActivityDetail));

    Map<String, Object> expectedSequenceMap = new HashMap<>();
    expectedSequenceMap.put("createTask", "true");
    expectedSequenceMap.put("sendCompanyEmail", "false");
    expectedSequenceMap.put("sendPushNotification", "true");

    Map<String, Object> actualSequenceMap = initCallActivityHandler.executeAction(
        workerActionRequest);
    Assert.assertEquals(expectedSequenceMap, actualSequenceMap);
  }


  @Test
  public void expectRootProcessIdInWorkflowActionRequest() {
    String rootProcessInstanceId = "rootProcessInstanceId";

    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("sendRejectNotification_txnApproval")
            .processDefinitionId("dId")
            .rootProcessInstanceId(rootProcessInstanceId)
            .processInstanceId("iId")
            .ownerId(Long.valueOf("9999"))
            .inputVariables(Map.of())
            .handlerId("handlerId")
            .build();

    Assert.assertEquals(workerActionRequest.fetchParentProcessInstanceId(), rootProcessInstanceId);
  }

  @Test
  public void expectProcessIdInWorkflowActionRequest() {
    String processInstanceId = "processId";

    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("sendRejectNotification_txnApproval")
            .processDefinitionId("dId")
            .processInstanceId(processInstanceId)
            .ownerId(Long.valueOf("9999"))
            .inputVariables(Map.of())
            .handlerId("handlerId")
            .build();

    Assert.assertEquals(workerActionRequest.fetchParentProcessInstanceId(), processInstanceId);
  }

  @Test
  public void testGetName() {
    Assert.assertEquals(
        "was_initCallActivity",
        initCallActivityHandler.getName().getTaskHandlerName());
  }

  @Test
  public void test_logErrorMetric() {
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .handlerId("hId")
            .ownerId(123L)
            .build();
    try {
      initCallActivityHandler.execute(workerActionRequest);
      Assert.fail("Exception should be thrown");
    } catch (WorkflowGeneralException workflowGeneralException) {
      Mockito.verify(metricLogger, Mockito.times(1)).logErrorMetric(any(), any(), any());
    }
  }
}
