package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants.REALM_ID_KEY;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.SchedulingService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.CorrelateAllMessageTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.StartRecurringProcessTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.EventScheduleHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TriggerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.TriggerDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CorrelationKeysEnum;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.CorrelateAllMessage;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.respone.SchedulingSvcResponse;
import com.intuit.async.execution.request.State;
import com.intuit.v4.workflows.Definition;

import java.util.*;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class TriggerRecurringProcessHelperTest {
  @Mock private TriggerDetailsRepository triggerDetailsRepository;
  @InjectMocks private TriggerRecurringProcessHelper triggerRecurringProcessHelper;
  @Mock private WASContextHandler contextHandler;
  @Mock private EventScheduleHelper eventScheduleHelper;
  @Mock private SchedulingService schedulingService;
  @Mock private FeatureFlagManager featureFlagManager;

  @Test
  public void testGetStartRecurringProcessTask() {
    DefinitionInstance definitionInstance = mock(DefinitionInstance.class);
    Definition definition = TestHelper.mockDefinitionEntity();
    when(definitionInstance.getDefinition()).thenReturn(definition);
    TemplateDetails templateDetails = new TemplateDetails();
    templateDetails.setTemplateName("customScheduledActions");
    templateDetails.setTemplateCategory("CUSTOM");
    when(definitionInstance.getTemplateDetails()).thenReturn(templateDetails);
    State state = new State();
    StartRecurringProcessTask startRecurringProcessTask =
        triggerRecurringProcessHelper.getStartRecurringProcessTask(definitionInstance, state);
    Assert.assertNotNull(startRecurringProcessTask);
    Assert.assertNotNull(state.getValue(AsyncTaskConstants.TRIGGER_TRANSACTION_ENTITY));
  }

  @Test
  public void testGetCorrelateRecurringProcessTask() {
    DefinitionInstance definitionInstance = mock(DefinitionInstance.class);
    TemplateDetails templateDetails = new TemplateDetails();
    DefinitionDetails definitionDetails = new DefinitionDetails();
    definitionDetails.setDefinitionKey("def-key");
    templateDetails.setTemplateName("customScheduledActions");
    when(definitionInstance.getTemplateDetails()).thenReturn(templateDetails);
    when(definitionInstance.getDefinitionDetails()).thenReturn(definitionDetails);
    when(triggerDetailsRepository.findByTemplateDetails(any()))
        .thenReturn(
            Optional.of(
                Collections.singletonList(
                    TriggerDetails.builder().triggerName("deleted_voided_disable").build())));
    State state = new State();
    state.addValue(REALM_ID_KEY, "123456");
    CorrelateAllMessageTask correlateAllMessageTask =
        triggerRecurringProcessHelper.prepareCorrelateMessageTask(
            definitionInstance, state);
    Assert.assertNotNull(correlateAllMessageTask);
    Map<String, CorrelateAllMessage.CorrelateKey> correlateKeysMap =
        state.getValue(AsyncTaskConstants.CORRELATE_KEYS);
    Assert.assertNotNull(correlateKeysMap);
    CorrelateAllMessage.CorrelateKey key =
        correlateKeysMap.get(CorrelationKeysEnum.DEFINITION_KEY.getName());
    Assert.assertEquals("def-key", key.getValue());
  }

  @Test
  public void testGetCorrelateRecurringProcessTask_EmptyTrigger_CustomTemplate() {
    DefinitionInstance definitionInstance = mock(DefinitionInstance.class);
    TemplateDetails templateDetails = new TemplateDetails();
    templateDetails.setTemplateCategory("CUSTOM");
    DefinitionDetails definitionDetails = new DefinitionDetails();
    definitionDetails.setDefinitionKey("def-key");
    templateDetails.setTemplateName("customScheduledActions");
    when(definitionInstance.getTemplateDetails()).thenReturn(templateDetails);
    when(definitionInstance.getDefinitionDetails()).thenReturn(definitionDetails);
    when(triggerDetailsRepository.findByTemplateDetails(any()))
        .thenReturn(Optional.of(Collections.EMPTY_LIST));
    State state = new State();
    state.addValue(REALM_ID_KEY, "123456");
    CorrelateAllMessageTask correlateAllMessageTask =
        triggerRecurringProcessHelper.prepareCorrelateMessageTask(
            definitionInstance, state);
    Assert.assertNotNull(correlateAllMessageTask);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testGetCorrelateRecurringProcessTask_EmptyTrigger() {
    DefinitionInstance definitionInstance = mock(DefinitionInstance.class);
    TemplateDetails templateDetails = new TemplateDetails();
    templateDetails.setTemplateCategory("HUB");
    DefinitionDetails definitionDetails = new DefinitionDetails();
    definitionDetails.setDefinitionKey("def-key");
    templateDetails.setTemplateName("customScheduledActions");
    when(definitionInstance.getTemplateDetails()).thenReturn(templateDetails);
    when(definitionInstance.getDefinitionDetails()).thenReturn(definitionDetails);
    when(triggerDetailsRepository.findByTemplateDetails(any()))
        .thenReturn(Optional.of(Collections.EMPTY_LIST));
    State state = new State();
    state.addValue(REALM_ID_KEY, "123456");
    CorrelateAllMessageTask correlateAllMessageTask =
        triggerRecurringProcessHelper.prepareCorrelateMessageTask(definitionInstance, state);
  }

  @Test
  public void testIsDefinitionSupportingRecurringProcess_True_NoSchedulesFound_CustomReminder() {
    DefinitionDetails definitionDetails = mock(DefinitionDetails.class);
    when(definitionDetails.getDefinitionId()).thenReturn("def-id");
    when(definitionDetails.getDefinitionKey()).thenReturn("defKey");
    TemplateDetails templateDetails = mock(TemplateDetails.class);
    when(templateDetails.getTemplateName()).thenReturn("customReminder");
    when(definitionDetails.getTemplateDetails()).thenReturn(templateDetails);
    when (eventScheduleHelper.getScheduleIdsForDefinition(any())).thenReturn(List.of());
    Assert.assertTrue(triggerRecurringProcessHelper.isDefinitionSupportingRecurringProcess(definitionDetails));
    verify(eventScheduleHelper, times(1)).getScheduleIdsForDefinition(any());
    verify(schedulingService, times(1)).getSchedules(any(), any());
  }

  @Test
  public void testIsDefinitionSupportingRecurringProcess_True_NoSchedulesFound_ScheduledActions() {
    DefinitionDetails definitionDetails = mock(DefinitionDetails.class);
    when(definitionDetails.getDefinitionId()).thenReturn("def-id");
    when(definitionDetails.getDefinitionKey()).thenReturn("defKey");
    TemplateDetails templateDetails = mock(TemplateDetails.class);
    when(templateDetails.getTemplateName()).thenReturn("customScheduledActions");
    when(definitionDetails.getTemplateDetails()).thenReturn(templateDetails);
    when (eventScheduleHelper.getScheduleIdsForDefinition(any())).thenReturn(List.of());
    Assert.assertTrue(triggerRecurringProcessHelper.isDefinitionSupportingRecurringProcess(definitionDetails));
    verify(eventScheduleHelper, times(1)).getScheduleIdsForDefinition(any());
    verify(schedulingService, times(1)).getSchedules(any(), any());
  }

  @Test
  public void testIsDefinitionSupportingRecurringProcess_True_SchedulesFound_ScheduledActions() {
    DefinitionDetails definitionDetails = mock(DefinitionDetails.class);
    when(definitionDetails.getDefinitionId()).thenReturn("def-id");
    TemplateDetails templateDetails = mock(TemplateDetails.class);
    when(templateDetails.getTemplateName()).thenReturn("customScheduledActions");
    when(definitionDetails.getDefinitionKey()).thenReturn("def-key");
    when(definitionDetails.getTemplateDetails()).thenReturn(templateDetails);
    when(schedulingService.getSchedules(any(),any())).thenReturn(new ArrayList<>());
    when (eventScheduleHelper.getScheduleIdsForDefinition(any())).thenReturn(List.of());
    when(schedulingService.getSchedules(any(), any())).thenReturn(Collections.singletonList(new SchedulingSvcResponse()));
    Assert.assertFalse(triggerRecurringProcessHelper.isDefinitionSupportingRecurringProcess(definitionDetails));
    verify(eventScheduleHelper, times(1)).getScheduleIdsForDefinition(any());
    verify(schedulingService, times(1)).getSchedules(any(), any());
  }

  @Test
  public void testIsDefinitionSupportingRecurringProcess_True_SchedulesFound_CustomReminder() {
    DefinitionDetails definitionDetails = mock(DefinitionDetails.class);
    when(definitionDetails.getDefinitionId()).thenReturn("def-id");
    TemplateDetails templateDetails = mock(TemplateDetails.class);
    when(templateDetails.getTemplateName()).thenReturn("customReminder");
    when(definitionDetails.getDefinitionKey()).thenReturn("def-key");
    when(definitionDetails.getTemplateDetails()).thenReturn(templateDetails);
    when(schedulingService.getSchedules(any(),any())).thenReturn(new ArrayList<>());
    when (eventScheduleHelper.getScheduleIdsForDefinition(any())).thenReturn(List.of());
    when(schedulingService.getSchedules(any(), any())).thenReturn(Collections.singletonList(new SchedulingSvcResponse()));

    Assert.assertFalse(triggerRecurringProcessHelper.isDefinitionSupportingRecurringProcess(definitionDetails));
    verify(eventScheduleHelper, times(1)).getScheduleIdsForDefinition(any());
    verify(schedulingService, times(1)).getSchedules(any(), any());
  }

  @Test
  public void testIsDefinitionSupportingRecurringProcess_True_SchedulesFound_otherWorkflows() {
    DefinitionDetails definitionDetails = mock(DefinitionDetails.class);
    when(definitionDetails.getDefinitionId()).thenReturn("def-id");
    TemplateDetails templateDetails = mock(TemplateDetails.class);
    when(templateDetails.getTemplateName()).thenReturn("customSend");
    when(definitionDetails.getTemplateDetails()).thenReturn(templateDetails);
    when (eventScheduleHelper.getScheduleIdsForDefinition(any())).thenReturn(List.of());

    Assert.assertTrue(triggerRecurringProcessHelper.isDefinitionSupportingRecurringProcess(definitionDetails));
    verify(eventScheduleHelper, times(1)).getScheduleIdsForDefinition(any());
    verify(schedulingService, times(0)).getSchedules(any(), any());
  }

  @Test
  public void testIsDefinitionSupportingRecurringProcess_False_NullDefinitionDetails() {
    DefinitionDetails definitionDetails = null;
    Assert.assertFalse(triggerRecurringProcessHelper.isDefinitionSupportingRecurringProcess(definitionDetails));
  }

  @Test
  public void testIsDefinitionSupportingRecurringProcess_False_ActiveSchedulesFound() {
    DefinitionDetails definitionDetails = new DefinitionDetails();
    TemplateDetails templateDetails = mock(TemplateDetails.class);
    when(templateDetails.getTemplateName()).thenReturn("test");
    definitionDetails.setTemplateDetails(templateDetails);
    definitionDetails.setDefinitionId("def-id");
    when (eventScheduleHelper.getScheduleIdsForDefinition(any())).thenReturn(List.of("schedule1"));
    Assert.assertFalse(triggerRecurringProcessHelper.isDefinitionSupportingRecurringProcess(definitionDetails));
  }
}
