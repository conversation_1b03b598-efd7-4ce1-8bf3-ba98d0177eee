<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns="http://maven.apache.org/POM/4.0.0"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <!-- Created by Intuit JSK V4 Initialzr 0.5.1.BUILD on 2019-09-06 -->
    <!-- https://jira.intuit.com/projects/ISP/issues/ -->

    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.intuit.appintgwkflw.wkflautomate</groupId>
        <artifactId>workflow-automation-service-aggregator</artifactId>
        <version>1.1.20</version>
    </parent>

    <artifactId>was-app</artifactId>
    <packaging>jar</packaging>

    <properties>
        <start-class>com.intuit.appintgwkflw.wkflautomate.was.Application</start-class>
        <spring-security-config.version>5.7.11</spring-security-config.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.intuit.platform.jsk.instrument</groupId>
            <artifactId>jsk-micrometer-instrument</artifactId>
        </dependency>

        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <version>${postgresql.version}</version>
        </dependency>

        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>was-worker</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>was-core</artifactId>
            <version>${project.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>tomcat-embed-core</artifactId>
                    <groupId>org.apache.tomcat.embed</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>was-batch</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>was-eventconsumer</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>was-graphql-client</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>tomcat-embed-core</artifactId>
                    <groupId>org.apache.tomcat.embed</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-core</artifactId>
            <version>9.0.99</version>
        </dependency>
        <dependency>
            <groupId>com.intuit.platform.jsk</groupId>
            <artifactId>jsk-spring-boot-starter-v4-servlet</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.hibernate.javax.persistence</groupId>
                    <artifactId>hibernate-jpa-2.0-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.hibernate.validator</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>iamticket-client</artifactId>
                    <groupId>com.intuit.platform.integration.iamticket.client</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--  Added this dependency to override the version of the dependency on this same library that
         jsk-spring-boot-starter-v4-servlet brings, but which currently has a CVE. -->
        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
        </dependency>
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.intuit.platform.jsk</groupId>
            <artifactId>jsk-spring-security</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.security</groupId>
                    <artifactId>spring-security-config</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
					
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-config</artifactId>
            <version>${spring-security-config.version}</version>
        </dependency>

        <dependency>
            <groupId>com.intuit.platform.jsk.spring</groupId>
            <artifactId>jsk-spring-boot-starter-version-metrics</artifactId>
        </dependency>

        <dependency>
            <groupId>com.intuit.platform.jsk.spring</groupId>
            <artifactId>jsk-spring-boot-starter-k8s-metrics</artifactId>
        </dependency>

        <!-- Adding V4 Schema -->

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
            <version>5.3.32</version>
        </dependency>


        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.data</groupId>
                    <artifactId>spring-data-jpa</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- Using version 2.2.12.RELEASE instead of the latest one that comes with spring-boot-starter-data-jpa
         due to change in the return format and thus backward incompatibility of the latest version -->
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-jpa</artifactId>
            <version>2.2.12.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>org.camunda.bpm</groupId>
            <artifactId>camunda-engine</artifactId>
            <scope>provided</scope>
	    <!-- xalan and log4j in camunda engine have vulnerability -->
            <exclusions>
                <exclusion>
                    <groupId>xalan</groupId>
                    <artifactId>xalan</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.camunda.bpm</groupId>
            <artifactId>camunda-engine-cdi</artifactId>
        </dependency>
        <dependency>
            <groupId>org.camunda.spin</groupId>
            <artifactId>camunda-spin-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.camunda.spin</groupId>
            <artifactId>camunda-spin-dataformat-json-jackson</artifactId>
        </dependency>
        <dependency>
            <groupId>org.camunda.bpm</groupId>
            <artifactId>camunda-engine-plugin-spin</artifactId>
        </dependency>
        <dependency>
            <groupId>org.liquibase</groupId>
            <artifactId>liquibase-core</artifactId>
        </dependency>
        <!--SignalSciences dependencies -->
        <dependency>
            <groupId>com.signalsciences.release</groupId>
            <artifactId>sigsci-module-java</artifactId>
        </dependency>

        <dependency>
            <groupId>org.msgpack</groupId>
            <artifactId>msgpack-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.jnr</groupId>
            <artifactId>jnr-unixsocket</artifactId>
        </dependency>
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-all</artifactId>
            <scope>provided</scope>
            <version>4.1.100.Final</version>
        </dependency>
        <!--SignalSciences dependencies -->

        <dependency>
            <groupId>io.opentracing.contrib</groupId>
            <artifactId>opentracing-spring-jaeger-web-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.camunda.bpm.extension.dmn</groupId>
            <artifactId>dmn-xlsx-converter</artifactId>
            <version>0.3.0</version>
            <exclusions>
                <exclusion>
                    <groupId>xalan</groupId>
                    <artifactId>xalan</artifactId>
                    </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>2.13.4.2</version>
        </dependency>


        <dependency>
            <groupId>com.intuit.platform.jsk</groupId>
            <artifactId>jsk-health</artifactId>
        </dependency>

        <dependency>
             <groupId>de.codecentric</groupId>
             <artifactId>chaos-monkey-spring-boot</artifactId>
         </dependency>
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-ui</artifactId>
        </dependency>
        <dependency>    <!-- Bootstrap has been disabled by default: https://spring.io/blog/2020/12/22/spring-cloud-2020-0-0-aka-ilford-is-available -->
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.bouncycastle</groupId>     <!-- brprov-jdk15on brought in by spring-cloud-starter ⇒ spring-security-rsa ⇒ bcpkix-jdk15on -->
                    <artifactId>bcprov-jdk15on</artifactId> <!-- Intuit apps must use bc-fips instead; you should add a dependency for it. -->
                </exclusion>
                <exclusion>
                    <groupId>org.bouncycastle</groupId>
                    <artifactId>bcprov-ext-jdk15on</artifactId> <!-- replaced by bc-fips, below -->
                </exclusion>
                <exclusion>
                    <groupId>org.bouncycastle</groupId>
                    <artifactId>bcpkix-jdk15on</artifactId> <!-- replaced by bc-fips, below -->
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bc-fips</artifactId>                <!-- API-1496: jsk-servlet-logging-filter does not use BouncyCastle directly, but we must exclude bcprov-* (above) so must provide an alternative implementation for spring-cloud-starter-bootstrap -->
        </dependency>

        <dependency>
            <groupId>com.github.tomakehurst</groupId>
            <artifactId>
                wiremock-jre8</artifactId>
            <version>2.35.0</version>
            <scope>test</scope>
        </dependency>

        <!-- https://mvnrepository.com/artifact/org.springframework/spring-webflux -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-webflux</artifactId>
            <scope>test</scope>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.6.0</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${maven-springboot-plugin.version}</version>
                <executions>
                    <execution>
                        <id>repackage</id>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                        <configuration>
                            <classifier>exec</classifier>
                        </configuration>
                    </execution>

                    <execution>
                        <phase>install</phase>
                        <goals>
                            <goal>repackage</goal>
                            <goal>build-info</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <executable>true</executable>
                    <finalName>${project.artifactId}</finalName>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
            </plugin>


            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <executions>
                    <execution>
                        <id>default-deploy</id>
                        <phase>never</phase>
                    </execution>
                </executions>
                <version>${maven-deploy-plugin.version}</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-release-plugin</artifactId>
                <configuration>
                    <preparationGoals>clean</preparationGoals>
                    <arguments>-s settings.xml</arguments>
                    <goals>clean deploy</goals>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>